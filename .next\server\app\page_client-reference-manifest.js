globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/src/components/WalletProvider.tsx <module evaluation>":{"id":"[project]/src/components/WalletProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40solflare-wallet_732400b2._.js","/_next/static/chunks/node_modules_%40noble_curves_esm_d476331b._.js","/_next/static/chunks/node_modules_%40solana_web3_js_lib_index_browser_esm_88d65ea0.js","/_next/static/chunks/node_modules_%40solana_d1281bcc._.js","/_next/static/chunks/node_modules_%40solana-mobile_67f20ee4._.js","/_next/static/chunks/node_modules_53fd1ed3._.js","/_next/static/chunks/src_13020ecf._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/components/WalletProvider.tsx":{"id":"[project]/src/components/WalletProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40solflare-wallet_732400b2._.js","/_next/static/chunks/node_modules_%40noble_curves_esm_d476331b._.js","/_next/static/chunks/node_modules_%40solana_web3_js_lib_index_browser_esm_88d65ea0.js","/_next/static/chunks/node_modules_%40solana_d1281bcc._.js","/_next/static/chunks/node_modules_%40solana-mobile_67f20ee4._.js","/_next/static/chunks/node_modules_53fd1ed3._.js","/_next/static/chunks/src_13020ecf._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>":{"id":"[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40solflare-wallet_732400b2._.js","/_next/static/chunks/node_modules_%40noble_curves_esm_d476331b._.js","/_next/static/chunks/node_modules_%40solana_web3_js_lib_index_browser_esm_88d65ea0.js","/_next/static/chunks/node_modules_%40solana_d1281bcc._.js","/_next/static/chunks/node_modules_%40solana-mobile_67f20ee4._.js","/_next/static/chunks/node_modules_53fd1ed3._.js","/_next/static/chunks/src_13020ecf._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/react-hot-toast/dist/index.mjs":{"id":"[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40solflare-wallet_732400b2._.js","/_next/static/chunks/node_modules_%40noble_curves_esm_d476331b._.js","/_next/static/chunks/node_modules_%40solana_web3_js_lib_index_browser_esm_88d65ea0.js","/_next/static/chunks/node_modules_%40solana_d1281bcc._.js","/_next/static/chunks/node_modules_%40solana-mobile_67f20ee4._.js","/_next/static/chunks/node_modules_53fd1ed3._.js","/_next/static/chunks/src_13020ecf._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/app/page.tsx <module evaluation>":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40solflare-wallet_732400b2._.js","/_next/static/chunks/node_modules_%40noble_curves_esm_d476331b._.js","/_next/static/chunks/node_modules_%40solana_web3_js_lib_index_browser_esm_88d65ea0.js","/_next/static/chunks/node_modules_%40solana_d1281bcc._.js","/_next/static/chunks/node_modules_%40solana-mobile_67f20ee4._.js","/_next/static/chunks/node_modules_53fd1ed3._.js","/_next/static/chunks/src_13020ecf._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/src_35f9f0b9._.js","/_next/static/chunks/node_modules_%40solana_e4b1b9e6._.js","/_next/static/chunks/src_app_page_tsx_c29afa78._.js"],"async":false},"[project]/src/app/page.tsx":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40solflare-wallet_732400b2._.js","/_next/static/chunks/node_modules_%40noble_curves_esm_d476331b._.js","/_next/static/chunks/node_modules_%40solana_web3_js_lib_index_browser_esm_88d65ea0.js","/_next/static/chunks/node_modules_%40solana_d1281bcc._.js","/_next/static/chunks/node_modules_%40solana-mobile_67f20ee4._.js","/_next/static/chunks/node_modules_53fd1ed3._.js","/_next/static/chunks/src_13020ecf._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/src_35f9f0b9._.js","/_next/static/chunks/node_modules_%40solana_e4b1b9e6._.js","/_next/static/chunks/src_app_page_tsx_c29afa78._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/src/components/WalletProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/WalletProvider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_@solflare-wallet_89939424._.js","server/chunks/ssr/node_modules_@noble_curves_esm_46e37e61._.js","server/chunks/ssr/node_modules_tr46_3d1b2cf9._.js","server/chunks/ssr/node_modules_ws_04231ca3._.js","server/chunks/ssr/node_modules_@solana_web3_js_lib_index_esm_8e506e22.js","server/chunks/ssr/node_modules_@solana_75ac7935._.js","server/chunks/ssr/node_modules_@solana-mobile_cfc9e909._.js","server/chunks/ssr/node_modules_8bf0d1e1._.js","server/chunks/ssr/[root-of-the-server]__e5286eb6._.js"],"async":false}},"[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_@solflare-wallet_89939424._.js","server/chunks/ssr/node_modules_@noble_curves_esm_46e37e61._.js","server/chunks/ssr/node_modules_tr46_3d1b2cf9._.js","server/chunks/ssr/node_modules_ws_04231ca3._.js","server/chunks/ssr/node_modules_@solana_web3_js_lib_index_esm_8e506e22.js","server/chunks/ssr/node_modules_@solana_75ac7935._.js","server/chunks/ssr/node_modules_@solana-mobile_cfc9e909._.js","server/chunks/ssr/node_modules_8bf0d1e1._.js","server/chunks/ssr/[root-of-the-server]__e5286eb6._.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_@solflare-wallet_89939424._.js","server/chunks/ssr/node_modules_@noble_curves_esm_46e37e61._.js","server/chunks/ssr/node_modules_tr46_3d1b2cf9._.js","server/chunks/ssr/node_modules_ws_04231ca3._.js","server/chunks/ssr/node_modules_@solana_web3_js_lib_index_esm_8e506e22.js","server/chunks/ssr/node_modules_@solana_75ac7935._.js","server/chunks/ssr/node_modules_@solana-mobile_cfc9e909._.js","server/chunks/ssr/node_modules_8bf0d1e1._.js","server/chunks/ssr/[root-of-the-server]__e5286eb6._.js","server/chunks/ssr/src_190ef081._.js","server/chunks/ssr/node_modules_@solana_5b4f91dd._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/components/WalletProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/WalletProvider.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/react-hot-toast/dist/index.mjs (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__9ce94736._.css","inlined":false}],"[project]/src/app/page":[{"path":"static/chunks/[root-of-the-server]__9ce94736._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"[project]/src/app/layout":["static/chunks/node_modules_@solflare-wallet_732400b2._.js","static/chunks/node_modules_@noble_curves_esm_d476331b._.js","static/chunks/node_modules_@solana_web3_js_lib_index_browser_esm_88d65ea0.js","static/chunks/node_modules_@solana_d1281bcc._.js","static/chunks/node_modules_@solana-mobile_67f20ee4._.js","static/chunks/node_modules_53fd1ed3._.js","static/chunks/src_13020ecf._.js","static/chunks/src_app_layout_tsx_c0237562._.js"],"[project]/src/app/page":["static/chunks/node_modules_@solflare-wallet_732400b2._.js","static/chunks/node_modules_@noble_curves_esm_d476331b._.js","static/chunks/node_modules_@solana_web3_js_lib_index_browser_esm_88d65ea0.js","static/chunks/node_modules_@solana_d1281bcc._.js","static/chunks/node_modules_@solana-mobile_67f20ee4._.js","static/chunks/node_modules_53fd1ed3._.js","static/chunks/src_13020ecf._.js","static/chunks/src_app_layout_tsx_c0237562._.js","static/chunks/src_35f9f0b9._.js","static/chunks/node_modules_@solana_e4b1b9e6._.js","static/chunks/src_app_page_tsx_c29afa78._.js"]}}
