/**
 * Chain Selector Component
 * Reusable component for selecting multiple blockchains
 */

'use client';

import React from 'react';
import { SupportedChain, getChainConfig } from '@/config/chains';

interface ChainSelectorProps {
  selectedChains: SupportedChain[];
  onSelectionChange: (chains: SupportedChain[]) => void;
  maxSelections?: number;
  minSelections?: number;
  showCosts?: boolean;
  showFeatures?: boolean;
  layout?: 'grid' | 'list';
  size?: 'small' | 'medium' | 'large';
}

export function ChainSelector({
  selectedChains,
  onSelectionChange,
  maxSelections,
  minSelections = 1,
  showCosts = true,
  showFeatures = true,
  layout = 'grid',
  size = 'medium',
}: ChainSelectorProps) {
  const chainIcons = {
    [SupportedChain.SOLANA]: '◎',
    [SupportedChain.BNB_CHAIN]: '🟡',
    [SupportedChain.AVALANCHE]: '🔺',
    [SupportedChain.POLKADOT]: '⚫',
  };

  const chainColors = {
    [SupportedChain.SOLANA]: 'from-purple-500 to-blue-500',
    [SupportedChain.BNB_CHAIN]: 'from-yellow-500 to-orange-500',
    [SupportedChain.AVALANCHE]: 'from-red-500 to-pink-500',
    [SupportedChain.POLKADOT]: 'from-pink-500 to-purple-500',
  };

  const estimatedCosts = {
    [SupportedChain.SOLANA]: '~0.2 SOL', // Raydium Standard AMM
    [SupportedChain.BNB_CHAIN]: '~0.05 BNB', // PancakeSwap V2
    [SupportedChain.AVALANCHE]: '~0.5 AVAX', // Trader Joe
    [SupportedChain.POLKADOT]: '~2 DOT', // HydraDX/Basilisk
  };

  const handleChainToggle = (chain: SupportedChain) => {
    const isSelected = selectedChains.includes(chain);
    
    if (isSelected) {
      // Remove chain (but respect minimum)
      if (selectedChains.length > minSelections) {
        onSelectionChange(selectedChains.filter(c => c !== chain));
      }
    } else {
      // Add chain (but respect maximum)
      if (!maxSelections || selectedChains.length < maxSelections) {
        onSelectionChange([...selectedChains, chain]);
      }
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          container: 'p-4',
          icon: 'text-2xl',
          title: 'text-lg',
          subtitle: 'text-xs',
          feature: 'text-xs',
        };
      case 'large':
        return {
          container: 'p-8',
          icon: 'text-5xl',
          title: 'text-2xl',
          subtitle: 'text-base',
          feature: 'text-base',
        };
      default: // medium
        return {
          container: 'p-6',
          icon: 'text-4xl',
          title: 'text-xl',
          subtitle: 'text-sm',
          feature: 'text-sm',
        };
    }
  };

  const sizeClasses = getSizeClasses();
  const gridCols = layout === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-6' : 'space-y-4';

  return (
    <div className="space-y-6">
      {/* Selection info */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-white">
            Select Blockchains ({selectedChains.length})
          </h3>
          <p className="text-gray-400 text-sm">
            {minSelections > 1 && `Minimum ${minSelections} required. `}
            {maxSelections && `Maximum ${maxSelections} allowed.`}
          </p>
        </div>
        
        {selectedChains.length > 0 && (
          <div className="flex items-center gap-2">
            {selectedChains.map((chain) => (
              <div
                key={chain}
                className="flex items-center gap-1 bg-gray-800 border border-gray-600 rounded-lg px-2 py-1"
              >
                <span className="text-sm">{chainIcons[chain]}</span>
                <span className="text-xs text-gray-300">{getChainConfig(chain).displayName}</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Chain options */}
      <div className={gridCols}>
        {Object.values(SupportedChain).map((chain) => {
          const config = getChainConfig(chain);
          const isSelected = selectedChains.includes(chain);
          const isDisabled = !isSelected && maxSelections && selectedChains.length >= maxSelections;
          const canDeselect = selectedChains.length > minSelections;

          return (
            <div
              key={chain}
              onClick={() => !isDisabled && (isSelected ? canDeselect && handleChainToggle(chain) : handleChainToggle(chain))}
              className={`
                relative ${sizeClasses.container} rounded-2xl border-2 transition-all duration-300 
                ${isDisabled 
                  ? 'cursor-not-allowed opacity-50' 
                  : 'cursor-pointer hover:scale-105'
                }
                ${isSelected 
                  ? `bg-gradient-to-r ${chainColors[chain]} border-white/30 shadow-xl` 
                  : 'bg-gray-800/50 border-gray-600/50 hover:border-gray-500/50'
                }
              `}
            >
              {/* Selection indicator */}
              <div className="absolute top-4 right-4">
                <div className={`
                  w-6 h-6 rounded-full border-2 flex items-center justify-center
                  ${isSelected ? 'bg-white border-white' : 'border-gray-400'}
                `}>
                  {isSelected && (
                    <svg className="w-4 h-4 text-gray-800" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>

              {/* Chain info */}
              <div className="flex items-center space-x-4 mb-4">
                <div className={sizeClasses.icon}>{chainIcons[chain]}</div>
                <div>
                  <h4 className={`${sizeClasses.title} font-bold ${isSelected ? 'text-white' : 'text-white'}`}>
                    {config.displayName}
                  </h4>
                  <p className={`${sizeClasses.subtitle} ${isSelected ? 'text-white/80' : 'text-gray-400'}`}>
                    {config.features.tokenStandard} Token
                  </p>
                </div>
              </div>

              {/* Features */}
              {showFeatures && (
                <div className="space-y-2 mb-4">
                  <div className={`flex items-center space-x-2 ${sizeClasses.feature} ${isSelected ? 'text-white/90' : 'text-gray-300'}`}>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span>Fast deployment</span>
                  </div>
                  <div className={`flex items-center space-x-2 ${sizeClasses.feature} ${isSelected ? 'text-white/90' : 'text-gray-300'}`}>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                    <span>DEX integration</span>
                  </div>
                  {showCosts && (
                    <div className={`flex items-center space-x-2 ${sizeClasses.feature} ${isSelected ? 'text-white/90' : 'text-gray-300'}`}>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>{estimatedCosts[chain]}</span>
                    </div>
                  )}
                </div>
              )}

              {/* Bridge support */}
              <div className="pt-3 border-t border-white/20">
                <p className={`text-xs ${isSelected ? 'text-white/70' : 'text-gray-500'} mb-1`}>
                  Bridge Support:
                </p>
                <div className="flex flex-wrap gap-1">
                  {config.features.bridgeSupport.slice(0, 2).map((bridge) => (
                    <span
                      key={bridge}
                      className={`px-2 py-1 rounded-md text-xs font-medium ${
                        isSelected 
                          ? 'bg-white/20 text-white' 
                          : 'bg-gray-700 text-gray-300'
                      }`}
                    >
                      {bridge}
                    </span>
                  ))}
                  {config.features.bridgeSupport.length > 2 && (
                    <span className={`px-2 py-1 rounded-md text-xs ${isSelected ? 'text-white/70' : 'text-gray-500'}`}>
                      +{config.features.bridgeSupport.length - 2}
                    </span>
                  )}
                </div>
              </div>

              {/* Disabled overlay */}
              {isDisabled && (
                <div className="absolute inset-0 bg-gray-900/50 rounded-2xl flex items-center justify-center">
                  <div className="bg-gray-800 border border-gray-600 rounded-lg px-3 py-1">
                    <span className="text-gray-400 text-sm font-medium">Max reached</span>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Quick selection buttons */}
      <div className="flex flex-wrap gap-2">
        <button
          type="button"
          onClick={() => onSelectionChange([SupportedChain.SOLANA])}
          className="px-3 py-1 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white text-sm rounded-lg transition-all duration-300"
        >
          Solana Only
        </button>
        <button
          type="button"
          onClick={() => onSelectionChange([SupportedChain.SOLANA, SupportedChain.BNB_CHAIN])}
          className="px-3 py-1 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white text-sm rounded-lg transition-all duration-300"
        >
          Solana + BNB
        </button>
        <button
          type="button"
          onClick={() => onSelectionChange(Object.values(SupportedChain))}
          className="px-3 py-1 bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 text-white text-sm rounded-lg transition-all duration-300"
        >
          All Chains
        </button>
        <button
          type="button"
          onClick={() => onSelectionChange([])}
          disabled={minSelections > 0}
          className="px-3 py-1 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white text-sm rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Clear All
        </button>
      </div>
    </div>
  );
}
