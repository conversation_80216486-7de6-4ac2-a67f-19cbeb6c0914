/**
 * Token Launch Modal Component
 * Modal for launching tokens from tweets
 */

'use client';

import React, { useState, useEffect } from 'react';
import { EnrichedTweet, TweetToTokenParams, TokenLaunchResult } from '@/types/twitter';
import { aiImageService, TokenArtworkRequest, TokenArtworkResult } from '@/services/aiImageService';

interface TokenLaunchModalProps {
  isOpen: boolean;
  onClose: () => void;
  tweet: EnrichedTweet | null;
  onLaunch: (params: TweetToTokenParams) => Promise<TokenLaunchResult>;
}

export function TokenLaunchModal({ isOpen, onClose, tweet, onLaunch }: TokenLaunchModalProps) {
  const [tokenName, setTokenName] = useState('');
  const [tokenSymbol, setTokenSymbol] = useState('');
  const [tokenDescription, setTokenDescription] = useState('');
  const [initialLiquidity, setInitialLiquidity] = useState(0.2);
  const [totalSupply, setTotalSupply] = useState('1000000000');
  const [decimals, setDecimals] = useState(9);
  const [isLaunching, setIsLaunching] = useState(false);
  const [launchResult, setLaunchResult] = useState<TokenLaunchResult | null>(null);
  const [step, setStep] = useState<'form' | 'generating' | 'launching' | 'success' | 'error'>('form');
  const [generatedArtwork, setGeneratedArtwork] = useState<TokenArtworkResult | null>(null);
  const [isGeneratingArt, setIsGeneratingArt] = useState(false);
  const [artworkStyle, setArtworkStyle] = useState<'meme' | 'abstract' | 'cartoon' | 'minimalist' | 'cyberpunk'>('meme');

  // Reset form when tweet changes
  useEffect(() => {
    if (tweet && isOpen) {
      setTokenName(tweet.token_potential?.name_suggestion || '');
      setTokenSymbol(tweet.token_potential?.symbol_suggestion || '');
      setTokenDescription(tweet.token_potential?.description_suggestion || tweet.text.substring(0, 100));
      setStep('form');
      setLaunchResult(null);
      setGeneratedArtwork(null);
      setIsGeneratingArt(false);
    }
  }, [tweet, isOpen]);

  const generateArtwork = async () => {
    if (!tweet) return;

    setIsGeneratingArt(true);
    setStep('generating');

    try {
      const artworkRequest: TokenArtworkRequest = {
        tweet,
        tokenName,
        tokenSymbol,
        style: artworkStyle,
        size: '1024x1024'
      };

      const result = await aiImageService.generateTokenArtwork(artworkRequest);
      setGeneratedArtwork(result);

      if (result.success) {
        setStep('form');
      } else {
        setStep('error');
      }
    } catch (error) {
      console.error('Artwork generation failed:', error);
      setStep('error');
    } finally {
      setIsGeneratingArt(false);
    }
  };

  const handleLaunch = async () => {
    if (!tweet) return;

    setIsLaunching(true);
    setStep('launching');

    try {
      const params: TweetToTokenParams = {
        tweet,
        token_name: tokenName,
        token_symbol: tokenSymbol,
        token_description: tokenDescription,
        initial_liquidity: initialLiquidity,
        total_supply: totalSupply,
        decimals,
        creator_wallet: 'placeholder' // This would come from wallet connection
      };

      const result = await onLaunch(params);
      setLaunchResult(result);
      
      if (result.success) {
        setStep('success');
      } else {
        setStep('error');
      }
    } catch (error) {
      console.error('Launch error:', error);
      setLaunchResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      setStep('error');
    } finally {
      setIsLaunching(false);
    }
  };

  const handleClose = () => {
    if (!isLaunching) {
      onClose();
      setTimeout(() => {
        setStep('form');
        setLaunchResult(null);
      }, 300);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  if (!isOpen || !tweet) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 border border-gray-700 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white">
            {step === 'form' && 'Launch Token from Tweet'}
            {step === 'generating' && 'Generating AI Artwork...'}
            {step === 'launching' && 'Launching Token...'}
            {step === 'success' && 'Token Launched Successfully! 🎉'}
            {step === 'error' && 'Launch Failed'}
          </h2>
          <button
            onClick={handleClose}
            disabled={isLaunching}
            className="text-gray-400 hover:text-white transition-colors disabled:opacity-50"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6">
          {/* Tweet Preview */}
          <div className="bg-gray-800/50 border border-gray-600/50 rounded-xl p-4 mb-6">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold">
                  {tweet.author.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p className="text-white font-semibold">{tweet.author.name}</p>
                <p className="text-gray-400 text-sm">@{tweet.author.username}</p>
              </div>
            </div>
            <p className="text-gray-200 text-sm leading-relaxed">{tweet.text}</p>
          </div>

          {/* Form Step */}
          {step === 'form' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Token Name
                  </label>
                  <input
                    type="text"
                    value={tokenName}
                    onChange={(e) => setTokenName(e.target.value)}
                    placeholder="e.g., Bonk Inu"
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Token Symbol
                  </label>
                  <input
                    type="text"
                    value={tokenSymbol}
                    onChange={(e) => setTokenSymbol(e.target.value.toUpperCase())}
                    placeholder="e.g., BONK"
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Token Description
                </label>
                <textarea
                  value={tokenDescription}
                  onChange={(e) => setTokenDescription(e.target.value)}
                  placeholder="Describe your token..."
                  rows={3}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300 resize-none"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Initial Liquidity (SOL)
                  </label>
                  <input
                    type="number"
                    value={initialLiquidity}
                    onChange={(e) => setInitialLiquidity(parseFloat(e.target.value) || 0)}
                    min="0.1"
                    step="0.1"
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                  />
                  <p className="text-gray-500 text-xs mt-1">Minimum 0.2 SOL recommended</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Total Supply
                  </label>
                  <input
                    type="text"
                    value={totalSupply}
                    onChange={(e) => setTotalSupply(e.target.value)}
                    placeholder="1000000000"
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Decimals
                  </label>
                  <select
                    value={decimals}
                    onChange={(e) => setDecimals(parseInt(e.target.value))}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white transition-all duration-300"
                  >
                    <option value={6}>6</option>
                    <option value={9}>9</option>
                  </select>
                </div>
              </div>

              {/* AI Artwork Generation */}
              <div className="bg-pink-900/20 border border-pink-500/30 rounded-xl p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-pink-400 font-semibold">AI Token Artwork</h4>
                  <div className="flex items-center space-x-2">
                    <select
                      value={artworkStyle}
                      onChange={(e) => setArtworkStyle(e.target.value as any)}
                      className="bg-gray-800 border border-gray-600 rounded-lg px-2 py-1 text-white text-xs"
                    >
                      <option value="meme">Meme Style</option>
                      <option value="abstract">Abstract</option>
                      <option value="cartoon">Cartoon</option>
                      <option value="minimalist">Minimalist</option>
                      <option value="cyberpunk">Cyberpunk</option>
                    </select>
                    <button
                      type="button"
                      onClick={generateArtwork}
                      disabled={isGeneratingArt || !tokenName || !tokenSymbol}
                      className="bg-pink-600 hover:bg-pink-700 disabled:bg-gray-600 text-white text-xs font-medium py-1 px-3 rounded-lg transition-colors disabled:cursor-not-allowed"
                    >
                      {isGeneratingArt ? 'Generating...' : 'Generate'}
                    </button>
                  </div>
                </div>

                {generatedArtwork && generatedArtwork.success ? (
                  <div className="space-y-2">
                    <div className="relative">
                      <img
                        src={generatedArtwork.imageUrl}
                        alt={`${tokenName} token artwork`}
                        className="w-full h-32 object-cover rounded-lg border border-gray-600"
                      />
                      <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                        AI Generated
                      </div>
                    </div>
                    <p className="text-gray-400 text-xs">
                      Generated in {generatedArtwork.generationTime ? Math.round(generatedArtwork.generationTime / 1000) : 0}s
                    </p>
                  </div>
                ) : (
                  <div className="text-center py-4 border-2 border-dashed border-gray-600 rounded-lg">
                    <svg className="w-8 h-8 text-gray-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p className="text-gray-500 text-xs">Click Generate to create AI artwork</p>
                  </div>
                )}
              </div>

              {/* Cost Estimation */}
              <div className="bg-purple-900/20 border border-purple-500/30 rounded-xl p-4">
                <h4 className="text-purple-400 font-semibold mb-2">Estimated Costs</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Token Creation:</span>
                    <span className="text-white ml-2">~0.01 SOL</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Pool Creation:</span>
                    <span className="text-white ml-2">~0.2 SOL</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Initial Liquidity:</span>
                    <span className="text-white ml-2">{initialLiquidity} SOL</span>
                  </div>
                  <div>
                    <span className="text-gray-400">AI Artwork:</span>
                    <span className="text-white ml-2">$0.02</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-400">Total Cost:</span>
                    <span className="text-cyan-400 ml-2 font-semibold">~{(initialLiquidity + 0.21).toFixed(2)} SOL + $0.02</span>
                  </div>
                </div>
              </div>

              <button
                onClick={handleLaunch}
                disabled={!tokenName || !tokenSymbol || initialLiquidity < 0.1}
                className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:to-gray-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 disabled:cursor-not-allowed border border-orange-400/30 hover:border-orange-300/50 disabled:border-gray-600/30"
              >
                Launch Token on Raydium 🚀
              </button>
            </div>
          )}

          {/* Generating Step */}
          {step === 'generating' && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="animate-spin w-8 h-8 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Generating AI Artwork...</h3>
              <p className="text-gray-400 mb-4">Creating unique token artwork based on the tweet content</p>
              <div className="bg-gray-800/50 rounded-lg p-4 max-w-md mx-auto">
                <p className="text-sm text-gray-300 mb-2">Style: <span className="text-pink-400 capitalize">{artworkStyle}</span></p>
                <p className="text-sm text-gray-300">Token: <span className="text-cyan-400">${tokenSymbol}</span></p>
              </div>
            </div>
          )}

          {/* Launching Step */}
          {step === 'launching' && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="animate-spin w-8 h-8 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Launching Your Token...</h3>
              <p className="text-gray-400 mb-4">This may take a few moments. Please don't close this window.</p>
              {generatedArtwork && generatedArtwork.success && (
                <div className="bg-gray-800/50 rounded-lg p-4 max-w-md mx-auto">
                  <img
                    src={generatedArtwork.imageUrl}
                    alt={`${tokenName} artwork`}
                    className="w-16 h-16 object-cover rounded-lg mx-auto mb-2"
                  />
                  <p className="text-sm text-gray-300">Including AI-generated artwork</p>
                </div>
              )}
            </div>
          )}

          {/* Success Step */}
          {step === 'success' && launchResult && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Token Launched Successfully!</h3>

              {/* Show generated artwork */}
              {generatedArtwork && generatedArtwork.success && (
                <div className="mb-6">
                  <img
                    src={generatedArtwork.imageUrl}
                    alt={`${tokenName} token artwork`}
                    className="w-32 h-32 object-cover rounded-xl mx-auto border-2 border-green-500/30"
                  />
                  <p className="text-gray-400 text-sm mt-2">AI-Generated Token Artwork</p>
                </div>
              )}
              
              <div className="space-y-4 mb-6">
                {launchResult.token_address && (
                  <div className="bg-gray-800/50 border border-gray-600/50 rounded-xl p-4">
                    <p className="text-gray-400 text-sm mb-1">Token Address</p>
                    <div className="flex items-center space-x-2">
                      <code className="text-cyan-400 text-sm font-mono">{launchResult.token_address}</code>
                      <button
                        onClick={() => copyToClipboard(launchResult.token_address!)}
                        className="text-gray-400 hover:text-white"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                )}
                
                {launchResult.solscan_url && (
                  <a
                    href={launchResult.solscan_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors"
                  >
                    View on Solscan
                  </a>
                )}
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => copyToClipboard(`Buy $${tokenSymbol} now! ${launchResult.solscan_url || ''}`)}
                  className="w-full bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300"
                >
                  Copy Share Message
                </button>
                <button
                  onClick={handleClose}
                  className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          )}

          {/* Error Step */}
          {step === 'error' && launchResult && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Launch Failed</h3>
              <p className="text-gray-400 mb-6">{launchResult.error || 'An unknown error occurred'}</p>
              
              <div className="space-y-3">
                <button
                  onClick={() => setStep('form')}
                  className="w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors"
                >
                  Try Again
                </button>
                <button
                  onClick={handleClose}
                  className="w-full bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
