"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[878],{4878:(e,t,i)=>{let n;i.r(t),i.d(t,{StandardSolflareMetaMaskWalletAccount:()=>x,default:()=>j});var r,s,a,o,c,l,d=i(3570),h=i(5484),u=i(6922),f=i.n(u);let m={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},p=new Uint8Array(16),_=[];for(let e=0;e<256;++e)_.push((e+256).toString(16).slice(1));let g=function(e,t,i){if(m.randomUUID&&!t&&!e)return m.randomUUID();let r=(e=e||{}).random||(e.rng||function(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(p)})();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){i=i||0;for(let e=0;e<16;++e)t[i+e]=r[e];return t}return function(e,t=0){return _[e[t+0]]+_[e[t+1]]+_[e[t+2]]+_[e[t+3]]+"-"+_[e[t+4]]+_[e[t+5]]+"-"+_[e[t+6]]+_[e[t+7]]+"-"+_[e[t+8]]+_[e[t+9]]+"-"+_[e[t+10]]+_[e[t+11]]+_[e[t+12]]+_[e[t+13]]+_[e[t+14]]+_[e[t+15]]}(r)};function v(e){return void 0===e.version}function y(e){return v(e)?e.serialize({verifySignatures:!1,requireAllSignatures:!1}):e.serialize()}i(4134).Buffer;var w=function(e,t,i,n){return new(i||(i=Promise))(function(r,s){function a(e){try{c(n.next(e))}catch(e){s(e)}}function o(e){try{c(n.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?r(e.value):((t=e.value)instanceof i?t:new i(function(e){e(t)})).then(a,o)}c((n=n.apply(e,t||[])).next())})};function b(e){return w(this,void 0,void 0,function*(){try{return yield e.request({method:"wallet_getSnaps"}),!0}catch(e){return!1}})}var E=i(7308),M=i(6803),K=i(5936);let A=["solana:mainnet","solana:devnet","solana:testnet","solana:localnet"];function S(e){return A.includes(e)}var I=function(e,t,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(e):n?n.value:t.get(e)},T=function(e,t,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,i):r?r.value=i:t.set(e,i),i};let k=[E.R,M.q,K.F];class x{get address(){return I(this,r,"f")}get publicKey(){return I(this,s,"f").slice()}get chains(){return I(this,a,"f").slice()}get features(){return I(this,o,"f").slice()}get label(){return I(this,c,"f")}get icon(){return I(this,l,"f")}constructor({address:e,publicKey:t,label:i,icon:n}){r.set(this,void 0),s.set(this,void 0),a.set(this,void 0),o.set(this,void 0),c.set(this,void 0),l.set(this,void 0),new.target===x&&Object.freeze(this),T(this,r,e,"f"),T(this,s,t,"f"),T(this,a,A,"f"),T(this,o,k,"f"),T(this,c,i,"f"),T(this,l,n,"f")}}r=new WeakMap,s=new WeakMap,a=new WeakMap,o=new WeakMap,c=new WeakMap,l=new WeakMap;var C=function(e,t,i,n){return new(i||(i=Promise))(function(r,s){function a(e){try{c(n.next(e))}catch(e){s(e)}}function o(e){try{c(n.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?r(e.value):((t=e.value)instanceof i?t:new i(function(e){e(t)})).then(a,o)}c((n=n.apply(e,t||[])).next())})};class z extends h.A{constructor(e){super(),this._network="mainnet-beta",this._iframeParams={},this._element=null,this._iframe=null,this._publicKey=null,this._account=null,this._isConnected=!1,this._connectHandler=null,this._messageHandlers={},this._handleEvent=e=>{var t,i;switch(e.type){case"connect":this._collapseIframe(),(null==(t=e.data)?void 0:t.publicKey)?(this._publicKey=e.data.publicKey,this._isConnected=!0,this._connectHandler&&(this._connectHandler.resolve(),this._connectHandler=null),this._connected()):(this._connectHandler&&(this._connectHandler.reject(),this._connectHandler=null),this._disconnected());return;case"disconnect":this._connectHandler&&(this._connectHandler.reject(),this._connectHandler=null),this._disconnected();return;case"accountChanged":(null==(i=e.data)?void 0:i.publicKey)?(this._publicKey=e.data.publicKey,this.emit("accountChanged",this.publicKey),this._standardConnected()):(this.emit("accountChanged",void 0),this._standardDisconnected());return;default:return}},this._handleResize=e=>{"full"===e.resizeMode?"fullscreen"===e.params.mode?this._expandIframe():"hide"===e.params.mode&&this._collapseIframe():"coordinates"===e.resizeMode&&this._resizeIframe(e.params)},this._handleMessage=e=>{var t;if((null==(t=e.data)?void 0:t.channel)!=="solflareIframeToWalletAdapter")return;let i=e.data.data||{};if("event"===i.type)this._handleEvent(i.event);else if("resize"===i.type)this._handleResize(i);else if("response"===i.type&&this._messageHandlers[i.id]){let{resolve:e,reject:t}=this._messageHandlers[i.id];delete this._messageHandlers[i.id],i.error?t(i.error):e(i.result)}},this._removeElement=()=>{this._element&&(this._element.remove(),this._element=null)},this._removeDanglingElements=()=>{for(let e of document.getElementsByClassName("solflare-metamask-wallet-adapter-iframe"))e.parentElement&&e.remove()},this._injectElement=()=>{this._removeElement(),this._removeDanglingElements();let e=Object.assign(Object.assign({},this._iframeParams),{mm:!0,v:1,cluster:this._network||"mainnet-beta",origin:window.location.origin||"",title:document.title||""}),t=Object.keys(e).map(t=>`${t}=${encodeURIComponent(e[t])}`).join("&"),i=`${z.IFRAME_URL}?${t}`;this._element=document.createElement("div"),this._element.className="solflare-metamask-wallet-adapter-iframe",this._element.innerHTML=`
      <iframe src='${i}' style='position: fixed; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; border: none; border-radius: 0; z-index: 99999; color-scheme: auto;' allowtransparency='true'></iframe>
    `,document.body.appendChild(this._element),this._iframe=this._element.querySelector("iframe"),window.addEventListener("message",this._handleMessage,!1)},this._collapseIframe=()=>{this._iframe&&(this._iframe.style.top="",this._iframe.style.right="",this._iframe.style.height="2px",this._iframe.style.width="2px")},this._expandIframe=()=>{this._iframe&&(this._iframe.style.top="0px",this._iframe.style.bottom="0px",this._iframe.style.left="0px",this._iframe.style.right="0px",this._iframe.style.width="100%",this._iframe.style.height="100%")},this._resizeIframe=e=>{this._iframe&&(this._iframe.style.top=isFinite(e.top)?`${e.top}px`:"",this._iframe.style.bottom=isFinite(e.bottom)?`${e.bottom}px`:"",this._iframe.style.left=isFinite(e.left)?`${e.left}px`:"",this._iframe.style.right=isFinite(e.right)?`${e.right}px`:"",this._iframe.style.width=isFinite(e.width)?`${e.width}px`:e.width,this._iframe.style.height=isFinite(e.height)?`${e.height}px`:e.height)},this._sendIframeMessage=e=>{if(!this.connected||!this.publicKey)throw Error("Wallet not connected");return new Promise((t,i)=>{var n,r;let s=g();this._messageHandlers[s]={resolve:t,reject:i},null==(r=null==(n=this._iframe)?void 0:n.contentWindow)||r.postMessage({channel:"solflareWalletAdapterToIframe",data:Object.assign({id:s},e)},"*")})},this._connected=()=>{this._isConnected=!0,this.emit("connect",this.publicKey),this._standardConnected()},this._disconnected=()=>{this._publicKey=null,this._isConnected=!1,window.removeEventListener("message",this._handleMessage,!1),this._removeElement(),this.emit("disconnect"),this._standardDisconnected()},this._standardConnected=()=>{if(!this.publicKey)return;let e=this.publicKey.toString();this._account&&this._account.address===e||(this._account=new x({address:e,publicKey:this.publicKey.toBytes()}),this.emit("standard_change",{accounts:this.standardAccounts}))},this._standardDisconnected=()=>{this._account&&(this._account=null,this.emit("standard_change",{accounts:this.standardAccounts}))},(null==e?void 0:e.network)&&(this._network=null==e?void 0:e.network),window.SolflareMetaMaskParams&&(this._iframeParams=Object.assign(Object.assign({},this._iframeParams),window.SolflareMetaMaskParams)),(null==e?void 0:e.params)&&(this._iframeParams=Object.assign(Object.assign({},this._iframeParams),null==e?void 0:e.params))}get publicKey(){return this._publicKey?new d.J3(this._publicKey):null}get standardAccount(){return this._account}get standardAccounts(){return this._account?[this._account]:[]}get isConnected(){return this._isConnected}get connected(){return this.isConnected}get autoApprove(){return!1}connect(){return C(this,void 0,void 0,function*(){this.connected||(this._injectElement(),yield new Promise((e,t)=>{this._connectHandler={resolve:e,reject:t}}))})}disconnect(){return C(this,void 0,void 0,function*(){yield this._sendIframeMessage({method:"disconnect"}),this._disconnected()})}signTransaction(e){var t;return C(this,void 0,void 0,function*(){if(!this.connected||!this.publicKey)throw Error("Wallet not connected");try{let t=y(e),{transaction:i}=yield this._sendIframeMessage({method:"signTransactionV2",params:{transaction:f().encode(t)}});return v(e)?d.ZX.from(f().decode(i)):d.Kt.deserialize(f().decode(i))}catch(e){throw Error((null==(t=null==e?void 0:e.toString)?void 0:t.call(e))||"Failed to sign transaction")}})}signAllTransactions(e){var t;return C(this,void 0,void 0,function*(){if(!this.connected||!this.publicKey)throw Error("Wallet not connected");try{let t=e.map(e=>y(e)),{transactions:i}=yield this._sendIframeMessage({method:"signAllTransactionsV2",params:{transactions:t.map(e=>f().encode(e))}});return i.map((t,i)=>v(e[i])?d.ZX.from(f().decode(t)):d.Kt.deserialize(f().decode(t)))}catch(e){throw Error((null==(t=null==e?void 0:e.toString)?void 0:t.call(e))||"Failed to sign transactions")}})}signAndSendTransaction(e,t){var i;return C(this,void 0,void 0,function*(){if(!this.connected||!this.publicKey)throw Error("Wallet not connected");try{let i=y(e),{signature:n}=yield this._sendIframeMessage({method:"signAndSendTransaction",params:{transaction:f().encode(i),options:t}});return n}catch(e){throw Error((null==(i=null==e?void 0:e.toString)?void 0:i.call(e))||"Failed to sign and send transaction")}})}signMessage(e,t="utf8"){var i;return C(this,void 0,void 0,function*(){if(!this.connected||!this.publicKey)throw Error("Wallet not connected");try{let{signature:i}=yield this._sendIframeMessage({method:"signMessage",params:{data:f().encode(e),display:t}});return Uint8Array.from(f().decode(i))}catch(e){throw Error((null==(i=null==e?void 0:e.toString)?void 0:i.call(e))||"Failed to sign message")}})}sign(e,t="utf8"){return C(this,void 0,void 0,function*(){return yield this.signMessage(e,t)})}static isSupported(){return C(this,void 0,void 0,function*(){return!!(yield function(){return w(this,void 0,void 0,function*(){try{let e=window.ethereum;if(!e)return null;if(e.providers&&Array.isArray(e.providers)){for(let t of e.providers)if(yield b(t))return t}if(e.detected&&Array.isArray(e.detected)){for(let t of e.detected)if(yield b(t))return t}if(yield b(e))return e;return null}catch(e){return console.error(e),null}})}())})}standardSignAndSendTransaction(...e){return C(this,void 0,void 0,function*(){if(!this.connected)throw Error("not connected");let t=[];if(1===e.length){let{transaction:i,account:n,chain:r,options:s}=e[0],{minContextSlot:a,preflightCommitment:o,skipPreflight:c,maxRetries:l}=s||{};if(n!==this._account)throw Error("invalid account");if(!S(r))throw Error("invalid chain");let h=yield this.signAndSendTransaction(d.Kt.deserialize(i),{preflightCommitment:o,minContextSlot:a,maxRetries:l,skipPreflight:c});t.push({signature:f().decode(h)})}else if(e.length>1)for(let i of e)t.push(...yield this.standardSignAndSendTransaction(i));return t})}standardSignTransaction(...e){return C(this,void 0,void 0,function*(){if(!this.connected)throw Error("not connected");let t=[];if(1===e.length){let{transaction:i,account:n,chain:r}=e[0];if(n!==this._account)throw Error("invalid account");if(r&&!S(r))throw Error("invalid chain");let s=yield this.signTransaction(d.Kt.deserialize(i));t.push({signedTransaction:s.serialize()})}else if(e.length>1){let i;for(let t of e){if(t.account!==this._account)throw Error("invalid account");if(t.chain){if(!S(t.chain))throw Error("invalid chain");if(i){if(t.chain!==i)throw Error("conflicting chain")}else i=t.chain}}let n=e.map(({transaction:e})=>d.Kt.deserialize(e)),r=yield this.signAllTransactions(n);t.push(...r.map(e=>({signedTransaction:e.serialize()})))}return t})}standardSignMessage(...e){return C(this,void 0,void 0,function*(){if(!this.connected)throw Error("not connected");let t=[];if(1===e.length){let{message:i,account:n}=e[0];if(n!==this._account)throw Error("invalid account");let r=yield this.signMessage(i);t.push({signedMessage:i,signature:r})}else if(e.length>1)for(let i of e)t.push(...yield this.standardSignMessage(i));return t})}}z.IFRAME_URL="https://widget.solflare.com/";let j=z}}]);