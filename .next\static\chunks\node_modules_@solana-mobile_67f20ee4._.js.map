{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solana-mobile/mobile-wallet-adapter-protocol/lib/esm/index.js"], "sourcesContent": ["import { createSignInMessageText } from '@solana/wallet-standard-util';\n\n// Typescript `enums` thwart tree-shaking. See https://bargsten.org/jsts/enums/\nconst SolanaMobileWalletAdapterErrorCode = {\n    ERROR_ASSOCIATION_PORT_OUT_OF_RANGE: 'ERROR_ASSOCIATION_PORT_OUT_OF_RANGE',\n    ERROR_REFLECTOR_ID_OUT_OF_RANGE: 'ERROR_REFLECTOR_ID_OUT_OF_RANGE',\n    ERROR_FORBIDDEN_WALLET_BASE_URL: 'ERROR_FORBIDDEN_WALLET_BASE_URL',\n    ERROR_SECURE_CONTEXT_REQUIRED: 'ERROR_SECURE_CONTEXT_REQUIRED',\n    ERROR_SESSION_CLOSED: 'ERROR_SESSION_CLOSED',\n    ERROR_SESSION_TIMEOUT: 'ERROR_SESSION_TIMEOUT',\n    ERROR_WALLET_NOT_FOUND: 'ERROR_WALLET_NOT_FOUND',\n    ERROR_INVALID_PROTOCOL_VERSION: 'ERROR_INVALID_PROTOCOL_VERSION',\n    ERROR_BROWSER_NOT_SUPPORTED: 'ERROR_BROWSER_NOT_SUPPORTED',\n};\nclass SolanaMobileWalletAdapterError extends Error {\n    constructor(...args) {\n        const [code, message, data] = args;\n        super(message);\n        this.code = code;\n        this.data = data;\n        this.name = 'SolanaMobileWalletAdapterError';\n    }\n}\n// Typescript `enums` thwart tree-shaking. See https://bargsten.org/jsts/enums/\nconst SolanaMobileWalletAdapterProtocolErrorCode = {\n    // Keep these in sync with `mobilewalletadapter/common/ProtocolContract.java`.\n    ERROR_AUTHORIZATION_FAILED: -1,\n    ERROR_INVALID_PAYLOADS: -2,\n    ERROR_NOT_SIGNED: -3,\n    ERROR_NOT_SUBMITTED: -4,\n    ERROR_TOO_MANY_PAYLOADS: -5,\n    ERROR_ATTEST_ORIGIN_ANDROID: -100,\n};\nclass SolanaMobileWalletAdapterProtocolError extends Error {\n    constructor(...args) {\n        const [jsonRpcMessageId, code, message, data] = args;\n        super(message);\n        this.code = code;\n        this.data = data;\n        this.jsonRpcMessageId = jsonRpcMessageId;\n        this.name = 'SolanaMobileWalletAdapterProtocolError';\n    }\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\n\nfunction encode(input) {\n    return window.btoa(input);\n}\nfunction fromUint8Array(byteArray, urlsafe) {\n    const base64 = window.btoa(String.fromCharCode.call(null, ...byteArray));\n    if (urlsafe) {\n        return base64\n            .replace(/\\+/g, '-')\n            .replace(/\\//g, '_')\n            .replace(/=+$/, '');\n    }\n    else\n        return base64;\n}\nfunction toUint8Array(base64EncodedByteArray) {\n    return new Uint8Array(window\n        .atob(base64EncodedByteArray)\n        .split('')\n        .map((c) => c.charCodeAt(0)));\n}\n\nfunction createHelloReq(ecdhPublicKey, associationKeypairPrivateKey) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const publicKeyBuffer = yield crypto.subtle.exportKey('raw', ecdhPublicKey);\n        const signatureBuffer = yield crypto.subtle.sign({ hash: 'SHA-256', name: 'ECDSA' }, associationKeypairPrivateKey, publicKeyBuffer);\n        const response = new Uint8Array(publicKeyBuffer.byteLength + signatureBuffer.byteLength);\n        response.set(new Uint8Array(publicKeyBuffer), 0);\n        response.set(new Uint8Array(signatureBuffer), publicKeyBuffer.byteLength);\n        return response;\n    });\n}\n\nfunction createSIWSMessage(payload) {\n    return createSignInMessageText(payload);\n}\nfunction createSIWSMessageBase64(payload) {\n    return encode(createSIWSMessage(payload));\n}\n\n// optional features\nconst SolanaSignTransactions = 'solana:signTransactions';\nconst SolanaCloneAuthorization = 'solana:cloneAuthorization';\nconst SolanaSignInWithSolana = 'solana:signInWithSolana';\n\n/**\n * Creates a {@link MobileWallet} proxy that handles backwards compatibility and API to RPC conversion.\n *\n * @param protocolVersion the protocol version in use for this session/request\n * @param protocolRequestHandler callback function that handles sending the RPC request to the wallet endpoint.\n * @returns a {@link MobileWallet} proxy\n */\nfunction createMobileWalletProxy(protocolVersion, protocolRequestHandler) {\n    return new Proxy({}, {\n        get(target, p) {\n            // Wrapping a Proxy in a promise results in the Proxy being asked for a 'then' property so must \n            // return null if 'then' is called on this proxy to let the 'resolve()' call know this is not a promise.\n            // see: https://stackoverflow.com/a/53890904\n            //@ts-ignore\n            if (p === 'then') {\n                return null;\n            }\n            if (target[p] == null) {\n                target[p] = function (inputParams) {\n                    return __awaiter(this, void 0, void 0, function* () {\n                        const { method, params } = handleMobileWalletRequest(p, inputParams, protocolVersion);\n                        const result = yield protocolRequestHandler(method, params);\n                        // if the request tried to sign in but the wallet did not return a sign in result, fallback on message signing\n                        if (method === 'authorize' && params.sign_in_payload && !result.sign_in_result) {\n                            result['sign_in_result'] = yield signInFallback(params.sign_in_payload, result, protocolRequestHandler);\n                        }\n                        return handleMobileWalletResponse(p, result, protocolVersion);\n                    });\n                };\n            }\n            return target[p];\n        },\n        defineProperty() {\n            return false;\n        },\n        deleteProperty() {\n            return false;\n        },\n    });\n}\n/**\n * Handles all {@link MobileWallet} API requests and determines the correct MWA RPC method and params to call.\n * This handles backwards compatibility, based on the provided @protocolVersion.\n *\n * @param methodName the name of {@link MobileWallet} method that was called\n * @param methodParams the parameters that were passed to the method\n * @param protocolVersion the protocol version in use for this session/request\n * @returns the RPC request method and params that should be sent to the wallet endpoint\n */\nfunction handleMobileWalletRequest(methodName, methodParams, protocolVersion) {\n    let params = methodParams;\n    let method = methodName\n        .toString()\n        .replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`)\n        .toLowerCase();\n    switch (methodName) {\n        case 'authorize': {\n            let { chain } = params;\n            if (protocolVersion === 'legacy') {\n                switch (chain) {\n                    case 'solana:testnet': {\n                        chain = 'testnet';\n                        break;\n                    }\n                    case 'solana:devnet': {\n                        chain = 'devnet';\n                        break;\n                    }\n                    case 'solana:mainnet': {\n                        chain = 'mainnet-beta';\n                        break;\n                    }\n                    default: {\n                        chain = params.cluster;\n                    }\n                }\n                params.cluster = chain;\n            }\n            else {\n                switch (chain) {\n                    case 'testnet':\n                    case 'devnet': {\n                        chain = `solana:${chain}`;\n                        break;\n                    }\n                    case 'mainnet-beta': {\n                        chain = 'solana:mainnet';\n                        break;\n                    }\n                }\n                params.chain = chain;\n            }\n        }\n        case 'reauthorize': {\n            const { auth_token, identity } = params;\n            if (auth_token) {\n                switch (protocolVersion) {\n                    case 'legacy': {\n                        method = 'reauthorize';\n                        params = { auth_token: auth_token, identity: identity };\n                        break;\n                    }\n                    default: {\n                        method = 'authorize';\n                        break;\n                    }\n                }\n            }\n            break;\n        }\n    }\n    return { method, params };\n}\n/**\n * Handles all {@link MobileWallet} API responses and modifies the response for backwards compatibility, if needed\n *\n * @param method the {@link MobileWallet} method that was called\n * @param response the original response that was returned by the method call\n * @param protocolVersion the protocol version in use for this session/request\n * @returns the possibly modified response\n */\nfunction handleMobileWalletResponse(method, response, protocolVersion) {\n    switch (method) {\n        case 'getCapabilities': {\n            const capabilities = response;\n            switch (protocolVersion) {\n                case 'legacy': {\n                    const features = [SolanaSignTransactions];\n                    if (capabilities.supports_clone_authorization === true) {\n                        features.push(SolanaCloneAuthorization);\n                    }\n                    return Object.assign(Object.assign({}, capabilities), { features: features });\n                }\n                case 'v1': {\n                    return Object.assign(Object.assign({}, capabilities), { supports_sign_and_send_transactions: true, supports_clone_authorization: capabilities.features.includes(SolanaCloneAuthorization) });\n                }\n            }\n        }\n    }\n    return response;\n}\nfunction signInFallback(signInPayload, authorizationResult, protocolRequestHandler) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n        const domain = (_a = signInPayload.domain) !== null && _a !== void 0 ? _a : window.location.host;\n        const address = authorizationResult.accounts[0].address;\n        const siwsMessage = createSIWSMessageBase64(Object.assign(Object.assign({}, signInPayload), { domain, address }));\n        const signMessageResult = yield protocolRequestHandler('sign_messages', {\n            addresses: [address],\n            payloads: [siwsMessage]\n        });\n        const signInResult = {\n            address: address,\n            signed_message: siwsMessage,\n            signature: signMessageResult.signed_payloads[0].slice(siwsMessage.length)\n        };\n        return signInResult;\n    });\n}\n\nconst SEQUENCE_NUMBER_BYTES = 4;\nfunction createSequenceNumberVector(sequenceNumber) {\n    if (sequenceNumber >= **********) {\n        throw new Error('Outbound sequence number overflow. The maximum sequence number is 32-bytes.');\n    }\n    const byteArray = new ArrayBuffer(SEQUENCE_NUMBER_BYTES);\n    const view = new DataView(byteArray);\n    view.setUint32(0, sequenceNumber, /* littleEndian */ false);\n    return new Uint8Array(byteArray);\n}\n\nconst INITIALIZATION_VECTOR_BYTES = 12;\nconst ENCODED_PUBLIC_KEY_LENGTH_BYTES = 65;\nfunction encryptMessage(plaintext, sequenceNumber, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const sequenceNumberVector = createSequenceNumberVector(sequenceNumber);\n        const initializationVector = new Uint8Array(INITIALIZATION_VECTOR_BYTES);\n        crypto.getRandomValues(initializationVector);\n        const ciphertext = yield crypto.subtle.encrypt(getAlgorithmParams(sequenceNumberVector, initializationVector), sharedSecret, new TextEncoder().encode(plaintext));\n        const response = new Uint8Array(sequenceNumberVector.byteLength + initializationVector.byteLength + ciphertext.byteLength);\n        response.set(new Uint8Array(sequenceNumberVector), 0);\n        response.set(new Uint8Array(initializationVector), sequenceNumberVector.byteLength);\n        response.set(new Uint8Array(ciphertext), sequenceNumberVector.byteLength + initializationVector.byteLength);\n        return response;\n    });\n}\nfunction decryptMessage(message, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const sequenceNumberVector = message.slice(0, SEQUENCE_NUMBER_BYTES);\n        const initializationVector = message.slice(SEQUENCE_NUMBER_BYTES, SEQUENCE_NUMBER_BYTES + INITIALIZATION_VECTOR_BYTES);\n        const ciphertext = message.slice(SEQUENCE_NUMBER_BYTES + INITIALIZATION_VECTOR_BYTES);\n        const plaintextBuffer = yield crypto.subtle.decrypt(getAlgorithmParams(sequenceNumberVector, initializationVector), sharedSecret, ciphertext);\n        const plaintext = getUtf8Decoder().decode(plaintextBuffer);\n        return plaintext;\n    });\n}\nfunction getAlgorithmParams(sequenceNumber, initializationVector) {\n    return {\n        additionalData: sequenceNumber,\n        iv: initializationVector,\n        name: 'AES-GCM',\n        tagLength: 128, // 16 byte tag => 128 bits\n    };\n}\nlet _utf8Decoder;\nfunction getUtf8Decoder() {\n    if (_utf8Decoder === undefined) {\n        _utf8Decoder = new TextDecoder('utf-8');\n    }\n    return _utf8Decoder;\n}\n\nfunction generateAssociationKeypair() {\n    return __awaiter(this, void 0, void 0, function* () {\n        return yield crypto.subtle.generateKey({\n            name: 'ECDSA',\n            namedCurve: 'P-256',\n        }, false /* extractable */, ['sign'] /* keyUsages */);\n    });\n}\n\nfunction generateECDHKeypair() {\n    return __awaiter(this, void 0, void 0, function* () {\n        return yield crypto.subtle.generateKey({\n            name: 'ECDH',\n            namedCurve: 'P-256',\n        }, false /* extractable */, ['deriveKey', 'deriveBits'] /* keyUsages */);\n    });\n}\n\n// https://stackoverflow.com/a/9458996/802047\nfunction arrayBufferToBase64String(buffer) {\n    let binary = '';\n    const bytes = new Uint8Array(buffer);\n    const len = bytes.byteLength;\n    for (let ii = 0; ii < len; ii++) {\n        binary += String.fromCharCode(bytes[ii]);\n    }\n    return window.btoa(binary);\n}\n\nfunction getRandomAssociationPort() {\n    return assertAssociationPort(49152 + Math.floor(Math.random() * (65535 - 49152 + 1)));\n}\nfunction assertAssociationPort(port) {\n    if (port < 49152 || port > 65535) {\n        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_ASSOCIATION_PORT_OUT_OF_RANGE, `Association port number must be between 49152 and 65535. ${port} given.`, { port });\n    }\n    return port;\n}\n\nfunction getStringWithURLUnsafeCharactersReplaced(unsafeBase64EncodedString) {\n    return unsafeBase64EncodedString.replace(/[/+=]/g, (m) => ({\n        '/': '_',\n        '+': '-',\n        '=': '.',\n    }[m]));\n}\n\nconst INTENT_NAME = 'solana-wallet';\nfunction getPathParts(pathString) {\n    return (pathString\n        // Strip leading and trailing slashes\n        .replace(/(^\\/+|\\/+$)/g, '')\n        // Return an array of directories\n        .split('/'));\n}\nfunction getIntentURL(methodPathname, intentUrlBase) {\n    let baseUrl = null;\n    if (intentUrlBase) {\n        try {\n            baseUrl = new URL(intentUrlBase);\n        }\n        catch (_a) { } // eslint-disable-line no-empty\n        if ((baseUrl === null || baseUrl === void 0 ? void 0 : baseUrl.protocol) !== 'https:') {\n            throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, 'Base URLs supplied by wallets must be valid `https` URLs');\n        }\n    }\n    baseUrl || (baseUrl = new URL(`${INTENT_NAME}:/`));\n    const pathname = methodPathname.startsWith('/')\n        ? // Method is an absolute path. Replace it wholesale.\n            methodPathname\n        : // Method is a relative path. Merge it with the existing one.\n            [...getPathParts(baseUrl.pathname), ...getPathParts(methodPathname)].join('/');\n    return new URL(pathname, baseUrl);\n}\nfunction getAssociateAndroidIntentURL(associationPublicKey, putativePort, associationURLBase, protocolVersions = ['v1']) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const associationPort = assertAssociationPort(putativePort);\n        const exportedKey = yield crypto.subtle.exportKey('raw', associationPublicKey);\n        const encodedKey = arrayBufferToBase64String(exportedKey);\n        const url = getIntentURL('v1/associate/local', associationURLBase);\n        url.searchParams.set('association', getStringWithURLUnsafeCharactersReplaced(encodedKey));\n        url.searchParams.set('port', `${associationPort}`);\n        protocolVersions.forEach((version) => {\n            url.searchParams.set('v', version);\n        });\n        return url;\n    });\n}\nfunction getRemoteAssociateAndroidIntentURL(associationPublicKey, hostAuthority, reflectorId, associationURLBase, protocolVersions = ['v1']) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const exportedKey = yield crypto.subtle.exportKey('raw', associationPublicKey);\n        const encodedKey = arrayBufferToBase64String(exportedKey);\n        const url = getIntentURL('v1/associate/remote', associationURLBase);\n        url.searchParams.set('association', getStringWithURLUnsafeCharactersReplaced(encodedKey));\n        url.searchParams.set('reflector', `${hostAuthority}`);\n        url.searchParams.set('id', `${fromUint8Array(reflectorId, true)}`);\n        protocolVersions.forEach((version) => {\n            url.searchParams.set('v', version);\n        });\n        return url;\n    });\n}\n\nfunction encryptJsonRpcMessage(jsonRpcMessage, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const plaintext = JSON.stringify(jsonRpcMessage);\n        const sequenceNumber = jsonRpcMessage.id;\n        return encryptMessage(plaintext, sequenceNumber, sharedSecret);\n    });\n}\nfunction decryptJsonRpcMessage(message, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const plaintext = yield decryptMessage(message, sharedSecret);\n        const jsonRpcMessage = JSON.parse(plaintext);\n        if (Object.hasOwnProperty.call(jsonRpcMessage, 'error')) {\n            throw new SolanaMobileWalletAdapterProtocolError(jsonRpcMessage.id, jsonRpcMessage.error.code, jsonRpcMessage.error.message);\n        }\n        return jsonRpcMessage;\n    });\n}\n\nfunction parseHelloRsp(payloadBuffer, // The X9.62-encoded wallet endpoint ephemeral ECDH public keypoint.\nassociationPublicKey, ecdhPrivateKey) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const [associationPublicKeyBuffer, walletPublicKey] = yield Promise.all([\n            crypto.subtle.exportKey('raw', associationPublicKey),\n            crypto.subtle.importKey('raw', payloadBuffer.slice(0, ENCODED_PUBLIC_KEY_LENGTH_BYTES), { name: 'ECDH', namedCurve: 'P-256' }, false /* extractable */, [] /* keyUsages */),\n        ]);\n        const sharedSecret = yield crypto.subtle.deriveBits({ name: 'ECDH', public: walletPublicKey }, ecdhPrivateKey, 256);\n        const ecdhSecretKey = yield crypto.subtle.importKey('raw', sharedSecret, 'HKDF', false /* extractable */, ['deriveKey'] /* keyUsages */);\n        const aesKeyMaterialVal = yield crypto.subtle.deriveKey({\n            name: 'HKDF',\n            hash: 'SHA-256',\n            salt: new Uint8Array(associationPublicKeyBuffer),\n            info: new Uint8Array(),\n        }, ecdhSecretKey, { name: 'AES-GCM', length: 128 }, false /* extractable */, ['encrypt', 'decrypt']);\n        return aesKeyMaterialVal;\n    });\n}\n\nfunction parseSessionProps(message, sharedSecret) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const plaintext = yield decryptMessage(message, sharedSecret);\n        const jsonProperties = JSON.parse(plaintext);\n        let protocolVersion = 'legacy';\n        if (Object.hasOwnProperty.call(jsonProperties, 'v')) {\n            switch (jsonProperties.v) {\n                case 1:\n                case '1':\n                case 'v1':\n                    protocolVersion = 'v1';\n                    break;\n                case 'legacy':\n                    protocolVersion = 'legacy';\n                    break;\n                default:\n                    throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_INVALID_PROTOCOL_VERSION, `Unknown/unsupported protocol version: ${jsonProperties.v}`);\n            }\n        }\n        return ({\n            protocol_version: protocolVersion\n        });\n    });\n}\n\n// Typescript `enums` thwart tree-shaking. See https://bargsten.org/jsts/enums/\nconst Browser = {\n    Firefox: 0,\n    Other: 1,\n};\nfunction assertUnreachable(x) {\n    return x;\n}\nfunction getBrowser() {\n    return navigator.userAgent.indexOf('Firefox/') !== -1 ? Browser.Firefox : Browser.Other;\n}\nfunction getDetectionPromise() {\n    // Chrome and others silently fail if a custom protocol is not supported.\n    // For these, we wait to see if the browser is navigated away from in\n    // a reasonable amount of time (ie. the native wallet opened).\n    return new Promise((resolve, reject) => {\n        function cleanup() {\n            clearTimeout(timeoutId);\n            window.removeEventListener('blur', handleBlur);\n        }\n        function handleBlur() {\n            cleanup();\n            resolve();\n        }\n        window.addEventListener('blur', handleBlur);\n        const timeoutId = setTimeout(() => {\n            cleanup();\n            reject();\n        }, 3000);\n    });\n}\nlet _frame = null;\nfunction launchUrlThroughHiddenFrame(url) {\n    if (_frame == null) {\n        _frame = document.createElement('iframe');\n        _frame.style.display = 'none';\n        document.body.appendChild(_frame);\n    }\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    _frame.contentWindow.location.href = url.toString();\n}\nfunction launchAssociation(associationUrl) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (associationUrl.protocol === 'https:') {\n            // The association URL is an Android 'App Link' or iOS 'Universal Link'.\n            // These are regular web URLs that are designed to launch an app if it\n            // is installed or load the actual target webpage if not.\n            window.location.assign(associationUrl);\n        }\n        else {\n            // The association URL has a custom protocol (eg. `solana-wallet:`)\n            try {\n                const browser = getBrowser();\n                switch (browser) {\n                    case Browser.Firefox:\n                        // If a custom protocol is not supported in Firefox, it throws.\n                        launchUrlThroughHiddenFrame(associationUrl);\n                        // If we reached this line, it's supported.\n                        break;\n                    case Browser.Other: {\n                        const detectionPromise = getDetectionPromise();\n                        window.location.assign(associationUrl);\n                        yield detectionPromise;\n                        break;\n                    }\n                    default:\n                        assertUnreachable(browser);\n                }\n            }\n            catch (e) {\n                throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_WALLET_NOT_FOUND, 'Found no installed wallet that supports the mobile wallet protocol.');\n            }\n        }\n    });\n}\nfunction startSession(associationPublicKey, associationURLBase) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const randomAssociationPort = getRandomAssociationPort();\n        const associationUrl = yield getAssociateAndroidIntentURL(associationPublicKey, randomAssociationPort, associationURLBase);\n        yield launchAssociation(associationUrl);\n        return randomAssociationPort;\n    });\n}\n\nconst WEBSOCKET_CONNECTION_CONFIG = {\n    /**\n     * 300 milliseconds is a generally accepted threshold for what someone\n     * would consider an acceptable response time for a user interface\n     * after having performed a low-attention tapping task. We set the initial\n     * interval at which we wait for the wallet to set up the websocket at\n     * half this, as per the Nyquist frequency, with a progressive backoff\n     * sequence from there. The total wait time is 30s, which allows for the\n     * user to be presented with a disambiguation dialog, select a wallet, and\n     * for the wallet app to subsequently start.\n     */\n    retryDelayScheduleMs: [150, 150, 200, 500, 500, 750, 750, 1000],\n    timeoutMs: 30000,\n};\nconst WEBSOCKET_PROTOCOL_BINARY = 'com.solana.mobilewalletadapter.v1';\nconst WEBSOCKET_PROTOCOL_BASE64 = 'com.solana.mobilewalletadapter.v1.base64';\nfunction assertSecureContext() {\n    if (typeof window === 'undefined' || window.isSecureContext !== true) {\n        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SECURE_CONTEXT_REQUIRED, 'The mobile wallet adapter protocol must be used in a secure context (`https`).');\n    }\n}\nfunction assertSecureEndpointSpecificURI(walletUriBase) {\n    let url;\n    try {\n        url = new URL(walletUriBase);\n    }\n    catch (_a) {\n        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, 'Invalid base URL supplied by wallet');\n    }\n    if (url.protocol !== 'https:') {\n        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, 'Base URLs supplied by wallets must be valid `https` URLs');\n    }\n}\nfunction getSequenceNumberFromByteArray(byteArray) {\n    const view = new DataView(byteArray);\n    return view.getUint32(0, /* littleEndian */ false);\n}\nfunction decodeVarLong(byteArray) {\n    var bytes = new Uint8Array(byteArray), l = byteArray.byteLength, limit = 10, value = 0, offset = 0, b;\n    do {\n        if (offset >= l || offset > limit)\n            throw new RangeError('Failed to decode varint');\n        b = bytes[offset++];\n        value |= (b & 0x7F) << (7 * offset);\n    } while (b >= 0x80);\n    return { value, offset };\n}\nfunction getReflectorIdFromByteArray(byteArray) {\n    let { value: length, offset } = decodeVarLong(byteArray);\n    return new Uint8Array(byteArray.slice(offset, offset + length));\n}\nfunction transact(callback, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        assertSecureContext();\n        const associationKeypair = yield generateAssociationKeypair();\n        const sessionPort = yield startSession(associationKeypair.publicKey, config === null || config === void 0 ? void 0 : config.baseUri);\n        const websocketURL = `ws://localhost:${sessionPort}/solana-wallet`;\n        let connectionStartTime;\n        const getNextRetryDelayMs = (() => {\n            const schedule = [...WEBSOCKET_CONNECTION_CONFIG.retryDelayScheduleMs];\n            return () => (schedule.length > 1 ? schedule.shift() : schedule[0]);\n        })();\n        let nextJsonRpcMessageId = 1;\n        let lastKnownInboundSequenceNumber = 0;\n        let state = { __type: 'disconnected' };\n        return new Promise((resolve, reject) => {\n            let socket;\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const jsonRpcResponsePromises = {};\n            const handleOpen = () => __awaiter(this, void 0, void 0, function* () {\n                if (state.__type !== 'connecting') {\n                    console.warn('Expected adapter state to be `connecting` at the moment the websocket opens. ' +\n                        `Got \\`${state.__type}\\`.`);\n                    return;\n                }\n                socket.removeEventListener('open', handleOpen);\n                // previous versions of this library and walletlib incorrectly implemented the MWA session \n                // establishment protocol for local connections. The dapp is supposed to wait for the \n                // APP_PING message before sending the HELLO_REQ. Instead, the dapp was sending the HELLO_REQ \n                // immediately upon connection to the websocket server regardless of wether or not an \n                // APP_PING was sent by the wallet/websocket server. We must continue to support this behavior \n                // in case the user is using a wallet that has not updated their walletlib implementation. \n                const { associationKeypair } = state;\n                const ecdhKeypair = yield generateECDHKeypair();\n                socket.send(yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey));\n                state = {\n                    __type: 'hello_req_sent',\n                    associationPublicKey: associationKeypair.publicKey,\n                    ecdhPrivateKey: ecdhKeypair.privateKey,\n                };\n            });\n            const handleClose = (evt) => {\n                if (evt.wasClean) {\n                    state = { __type: 'disconnected' };\n                }\n                else {\n                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session dropped unexpectedly (${evt.code}: ${evt.reason}).`, { closeEvent: evt }));\n                }\n                disposeSocket();\n            };\n            const handleError = (_evt) => __awaiter(this, void 0, void 0, function* () {\n                disposeSocket();\n                if (Date.now() - connectionStartTime >= WEBSOCKET_CONNECTION_CONFIG.timeoutMs) {\n                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_TIMEOUT, `Failed to connect to the wallet websocket at ${websocketURL}.`));\n                }\n                else {\n                    yield new Promise((resolve) => {\n                        const retryDelayMs = getNextRetryDelayMs();\n                        retryWaitTimeoutId = window.setTimeout(resolve, retryDelayMs);\n                    });\n                    attemptSocketConnection();\n                }\n            });\n            const handleMessage = (evt) => __awaiter(this, void 0, void 0, function* () {\n                const responseBuffer = yield evt.data.arrayBuffer();\n                switch (state.__type) {\n                    case 'connecting':\n                        if (responseBuffer.byteLength !== 0) {\n                            throw new Error('Encountered unexpected message while connecting');\n                        }\n                        const ecdhKeypair = yield generateECDHKeypair();\n                        socket.send(yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey));\n                        state = {\n                            __type: 'hello_req_sent',\n                            associationPublicKey: associationKeypair.publicKey,\n                            ecdhPrivateKey: ecdhKeypair.privateKey,\n                        };\n                        break;\n                    case 'connected':\n                        try {\n                            const sequenceNumberVector = responseBuffer.slice(0, SEQUENCE_NUMBER_BYTES);\n                            const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);\n                            if (sequenceNumber !== (lastKnownInboundSequenceNumber + 1)) {\n                                throw new Error('Encrypted message has invalid sequence number');\n                            }\n                            lastKnownInboundSequenceNumber = sequenceNumber;\n                            const jsonRpcMessage = yield decryptJsonRpcMessage(responseBuffer, state.sharedSecret);\n                            const responsePromise = jsonRpcResponsePromises[jsonRpcMessage.id];\n                            delete jsonRpcResponsePromises[jsonRpcMessage.id];\n                            responsePromise.resolve(jsonRpcMessage.result);\n                        }\n                        catch (e) {\n                            if (e instanceof SolanaMobileWalletAdapterProtocolError) {\n                                const responsePromise = jsonRpcResponsePromises[e.jsonRpcMessageId];\n                                delete jsonRpcResponsePromises[e.jsonRpcMessageId];\n                                responsePromise.reject(e);\n                            }\n                            else {\n                                throw e;\n                            }\n                        }\n                        break;\n                    case 'hello_req_sent': {\n                        // if we receive an APP_PING message (empty message), resend the HELLO_REQ (see above)\n                        if (responseBuffer.byteLength === 0) {\n                            const ecdhKeypair = yield generateECDHKeypair();\n                            socket.send(yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey));\n                            state = {\n                                __type: 'hello_req_sent',\n                                associationPublicKey: associationKeypair.publicKey,\n                                ecdhPrivateKey: ecdhKeypair.privateKey,\n                            };\n                            break;\n                        }\n                        const sharedSecret = yield parseHelloRsp(responseBuffer, state.associationPublicKey, state.ecdhPrivateKey);\n                        const sessionPropertiesBuffer = responseBuffer.slice(ENCODED_PUBLIC_KEY_LENGTH_BYTES);\n                        const sessionProperties = sessionPropertiesBuffer.byteLength !== 0\n                            ? yield (() => __awaiter(this, void 0, void 0, function* () {\n                                const sequenceNumberVector = sessionPropertiesBuffer.slice(0, SEQUENCE_NUMBER_BYTES);\n                                const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);\n                                if (sequenceNumber !== (lastKnownInboundSequenceNumber + 1)) {\n                                    throw new Error('Encrypted message has invalid sequence number');\n                                }\n                                lastKnownInboundSequenceNumber = sequenceNumber;\n                                return parseSessionProps(sessionPropertiesBuffer, sharedSecret);\n                            }))() : { protocol_version: 'legacy' };\n                        state = { __type: 'connected', sharedSecret, sessionProperties };\n                        const wallet = createMobileWalletProxy(sessionProperties.protocol_version, (method, params) => __awaiter(this, void 0, void 0, function* () {\n                            const id = nextJsonRpcMessageId++;\n                            socket.send(yield encryptJsonRpcMessage({\n                                id,\n                                jsonrpc: '2.0',\n                                method,\n                                params: params !== null && params !== void 0 ? params : {},\n                            }, sharedSecret));\n                            return new Promise((resolve, reject) => {\n                                jsonRpcResponsePromises[id] = {\n                                    resolve(result) {\n                                        switch (method) {\n                                            case 'authorize':\n                                            case 'reauthorize': {\n                                                const { wallet_uri_base } = result;\n                                                if (wallet_uri_base != null) {\n                                                    try {\n                                                        assertSecureEndpointSpecificURI(wallet_uri_base);\n                                                    }\n                                                    catch (e) {\n                                                        reject(e);\n                                                        return;\n                                                    }\n                                                }\n                                                break;\n                                            }\n                                        }\n                                        resolve(result);\n                                    },\n                                    reject,\n                                };\n                            });\n                        }));\n                        try {\n                            resolve(yield callback(wallet));\n                        }\n                        catch (e) {\n                            reject(e);\n                        }\n                        finally {\n                            disposeSocket();\n                            socket.close();\n                        }\n                        break;\n                    }\n                }\n            });\n            let disposeSocket;\n            let retryWaitTimeoutId;\n            const attemptSocketConnection = () => {\n                if (disposeSocket) {\n                    disposeSocket();\n                }\n                state = { __type: 'connecting', associationKeypair };\n                if (connectionStartTime === undefined) {\n                    connectionStartTime = Date.now();\n                }\n                socket = new WebSocket(websocketURL, [WEBSOCKET_PROTOCOL_BINARY]);\n                socket.addEventListener('open', handleOpen);\n                socket.addEventListener('close', handleClose);\n                socket.addEventListener('error', handleError);\n                socket.addEventListener('message', handleMessage);\n                disposeSocket = () => {\n                    window.clearTimeout(retryWaitTimeoutId);\n                    socket.removeEventListener('open', handleOpen);\n                    socket.removeEventListener('close', handleClose);\n                    socket.removeEventListener('error', handleError);\n                    socket.removeEventListener('message', handleMessage);\n                };\n            };\n            attemptSocketConnection();\n        });\n    });\n}\nfunction startRemoteScenario(config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        assertSecureContext();\n        const associationKeypair = yield generateAssociationKeypair();\n        const websocketURL = `wss://${config === null || config === void 0 ? void 0 : config.remoteHostAuthority}/reflect`;\n        let connectionStartTime;\n        const getNextRetryDelayMs = (() => {\n            const schedule = [...WEBSOCKET_CONNECTION_CONFIG.retryDelayScheduleMs];\n            return () => (schedule.length > 1 ? schedule.shift() : schedule[0]);\n        })();\n        let nextJsonRpcMessageId = 1;\n        let lastKnownInboundSequenceNumber = 0;\n        let encoding;\n        let state = { __type: 'disconnected' };\n        let socket;\n        let disposeSocket;\n        let decodeBytes = (evt) => __awaiter(this, void 0, void 0, function* () {\n            if (encoding == 'base64') { // base64 encoding\n                const message = yield evt.data;\n                return toUint8Array(message).buffer;\n            }\n            else {\n                return yield evt.data.arrayBuffer();\n            }\n        });\n        // Reflector Connection Phase\n        // here we connect to the reflector and wait for the REFLECTOR_ID message \n        // so we build the association URL and return that back to the caller\n        const associationUrl = yield new Promise((resolve, reject) => {\n            const handleOpen = () => __awaiter(this, void 0, void 0, function* () {\n                if (state.__type !== 'connecting') {\n                    console.warn('Expected adapter state to be `connecting` at the moment the websocket opens. ' +\n                        `Got \\`${state.__type}\\`.`);\n                    return;\n                }\n                if (socket.protocol.includes(WEBSOCKET_PROTOCOL_BASE64)) {\n                    encoding = 'base64';\n                }\n                else {\n                    encoding = 'binary';\n                }\n                socket.removeEventListener('open', handleOpen);\n            });\n            const handleClose = (evt) => {\n                if (evt.wasClean) {\n                    state = { __type: 'disconnected' };\n                }\n                else {\n                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session dropped unexpectedly (${evt.code}: ${evt.reason}).`, { closeEvent: evt }));\n                }\n                disposeSocket();\n            };\n            const handleError = (_evt) => __awaiter(this, void 0, void 0, function* () {\n                disposeSocket();\n                if (Date.now() - connectionStartTime >= WEBSOCKET_CONNECTION_CONFIG.timeoutMs) {\n                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_TIMEOUT, `Failed to connect to the wallet websocket at ${websocketURL}.`));\n                }\n                else {\n                    yield new Promise((resolve) => {\n                        const retryDelayMs = getNextRetryDelayMs();\n                        retryWaitTimeoutId = window.setTimeout(resolve, retryDelayMs);\n                    });\n                    attemptSocketConnection();\n                }\n            });\n            const handleReflectorIdMessage = (evt) => __awaiter(this, void 0, void 0, function* () {\n                const responseBuffer = yield decodeBytes(evt);\n                if (state.__type === 'connecting') {\n                    if (responseBuffer.byteLength == 0) {\n                        throw new Error('Encountered unexpected message while connecting');\n                    }\n                    const reflectorId = getReflectorIdFromByteArray(responseBuffer);\n                    state = {\n                        __type: 'reflector_id_received',\n                        reflectorId: reflectorId\n                    };\n                    const associationUrl = yield getRemoteAssociateAndroidIntentURL(associationKeypair.publicKey, config.remoteHostAuthority, reflectorId, config === null || config === void 0 ? void 0 : config.baseUri);\n                    socket.removeEventListener('message', handleReflectorIdMessage);\n                    resolve(associationUrl);\n                }\n            });\n            let retryWaitTimeoutId;\n            const attemptSocketConnection = () => {\n                if (disposeSocket) {\n                    disposeSocket();\n                }\n                state = { __type: 'connecting', associationKeypair };\n                if (connectionStartTime === undefined) {\n                    connectionStartTime = Date.now();\n                }\n                socket = new WebSocket(websocketURL, [WEBSOCKET_PROTOCOL_BINARY, WEBSOCKET_PROTOCOL_BASE64]);\n                socket.addEventListener('open', handleOpen);\n                socket.addEventListener('close', handleClose);\n                socket.addEventListener('error', handleError);\n                socket.addEventListener('message', handleReflectorIdMessage);\n                disposeSocket = () => {\n                    window.clearTimeout(retryWaitTimeoutId);\n                    socket.removeEventListener('open', handleOpen);\n                    socket.removeEventListener('close', handleClose);\n                    socket.removeEventListener('error', handleError);\n                    socket.removeEventListener('message', handleReflectorIdMessage);\n                };\n            };\n            attemptSocketConnection();\n        });\n        // Wallet Connection Phase\n        // here we return the association URL (containing the reflector ID) to the caller + \n        // a promise that will resolve the MobileWallet object once the wallet connects.\n        let sessionEstablished = false;\n        let handleClose;\n        return { associationUrl, close: () => {\n                socket.close();\n                handleClose();\n            }, wallet: new Promise((resolve, reject) => {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                const jsonRpcResponsePromises = {};\n                const handleMessage = (evt) => __awaiter(this, void 0, void 0, function* () {\n                    const responseBuffer = yield decodeBytes(evt);\n                    switch (state.__type) {\n                        case 'reflector_id_received':\n                            if (responseBuffer.byteLength !== 0) {\n                                throw new Error('Encountered unexpected message while awaiting reflection');\n                            }\n                            const ecdhKeypair = yield generateECDHKeypair();\n                            const binaryMsg = yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey);\n                            if (encoding == 'base64') {\n                                socket.send(fromUint8Array(binaryMsg));\n                            }\n                            else {\n                                socket.send(binaryMsg);\n                            }\n                            state = {\n                                __type: 'hello_req_sent',\n                                associationPublicKey: associationKeypair.publicKey,\n                                ecdhPrivateKey: ecdhKeypair.privateKey,\n                            };\n                            break;\n                        case 'connected':\n                            try {\n                                const sequenceNumberVector = responseBuffer.slice(0, SEQUENCE_NUMBER_BYTES);\n                                const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);\n                                if (sequenceNumber !== (lastKnownInboundSequenceNumber + 1)) {\n                                    throw new Error('Encrypted message has invalid sequence number');\n                                }\n                                lastKnownInboundSequenceNumber = sequenceNumber;\n                                const jsonRpcMessage = yield decryptJsonRpcMessage(responseBuffer, state.sharedSecret);\n                                const responsePromise = jsonRpcResponsePromises[jsonRpcMessage.id];\n                                delete jsonRpcResponsePromises[jsonRpcMessage.id];\n                                responsePromise.resolve(jsonRpcMessage.result);\n                            }\n                            catch (e) {\n                                if (e instanceof SolanaMobileWalletAdapterProtocolError) {\n                                    const responsePromise = jsonRpcResponsePromises[e.jsonRpcMessageId];\n                                    delete jsonRpcResponsePromises[e.jsonRpcMessageId];\n                                    responsePromise.reject(e);\n                                }\n                                else {\n                                    throw e;\n                                }\n                            }\n                            break;\n                        case 'hello_req_sent': {\n                            const sharedSecret = yield parseHelloRsp(responseBuffer, state.associationPublicKey, state.ecdhPrivateKey);\n                            const sessionPropertiesBuffer = responseBuffer.slice(ENCODED_PUBLIC_KEY_LENGTH_BYTES);\n                            const sessionProperties = sessionPropertiesBuffer.byteLength !== 0\n                                ? yield (() => __awaiter(this, void 0, void 0, function* () {\n                                    const sequenceNumberVector = sessionPropertiesBuffer.slice(0, SEQUENCE_NUMBER_BYTES);\n                                    const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);\n                                    if (sequenceNumber !== (lastKnownInboundSequenceNumber + 1)) {\n                                        throw new Error('Encrypted message has invalid sequence number');\n                                    }\n                                    lastKnownInboundSequenceNumber = sequenceNumber;\n                                    return parseSessionProps(sessionPropertiesBuffer, sharedSecret);\n                                }))() : { protocol_version: 'legacy' };\n                            state = { __type: 'connected', sharedSecret, sessionProperties };\n                            const wallet = createMobileWalletProxy(sessionProperties.protocol_version, (method, params) => __awaiter(this, void 0, void 0, function* () {\n                                const id = nextJsonRpcMessageId++;\n                                const binaryMsg = yield encryptJsonRpcMessage({\n                                    id,\n                                    jsonrpc: '2.0',\n                                    method,\n                                    params: params !== null && params !== void 0 ? params : {},\n                                }, sharedSecret);\n                                if (encoding == 'base64') {\n                                    socket.send(fromUint8Array(binaryMsg));\n                                }\n                                else {\n                                    socket.send(binaryMsg);\n                                }\n                                return new Promise((resolve, reject) => {\n                                    jsonRpcResponsePromises[id] = {\n                                        resolve(result) {\n                                            switch (method) {\n                                                case 'authorize':\n                                                case 'reauthorize': {\n                                                    const { wallet_uri_base } = result;\n                                                    if (wallet_uri_base != null) {\n                                                        try {\n                                                            assertSecureEndpointSpecificURI(wallet_uri_base);\n                                                        }\n                                                        catch (e) {\n                                                            reject(e);\n                                                            return;\n                                                        }\n                                                    }\n                                                    break;\n                                                }\n                                            }\n                                            resolve(result);\n                                        },\n                                        reject,\n                                    };\n                                });\n                            }));\n                            sessionEstablished = true;\n                            try {\n                                resolve(wallet);\n                            }\n                            catch (e) {\n                                reject(e);\n                            }\n                            break;\n                        }\n                    }\n                });\n                socket.addEventListener('message', handleMessage);\n                handleClose = () => {\n                    socket.removeEventListener('message', handleMessage);\n                    disposeSocket();\n                    if (!sessionEstablished) {\n                        reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session was closed before connection.`, { closeEvent: new CloseEvent('socket was closed before connection') }));\n                    }\n                };\n            }) };\n    });\n}\n\nexport { SolanaCloneAuthorization, SolanaMobileWalletAdapterError, SolanaMobileWalletAdapterErrorCode, SolanaMobileWalletAdapterProtocolError, SolanaMobileWalletAdapterProtocolErrorCode, SolanaSignInWithSolana, SolanaSignTransactions, startRemoteScenario, transact };\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAEA,+EAA+E;AAC/E,MAAM,qCAAqC;IACvC,qCAAqC;IACrC,iCAAiC;IACjC,iCAAiC;IACjC,+BAA+B;IAC/B,sBAAsB;IACtB,uBAAuB;IACvB,wBAAwB;IACxB,gCAAgC;IAChC,6BAA6B;AACjC;AACA,MAAM,uCAAuC;IACzC,YAAY,GAAG,IAAI,CAAE;QACjB,MAAM,CAAC,MAAM,SAAS,KAAK,GAAG;QAC9B,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,+EAA+E;AAC/E,MAAM,6CAA6C;IAC/C,8EAA8E;IAC9E,4BAA4B,CAAC;IAC7B,wBAAwB,CAAC;IACzB,kBAAkB,CAAC;IACnB,qBAAqB,CAAC;IACtB,yBAAyB,CAAC;IAC1B,6BAA6B,CAAC;AAClC;AACA,MAAM,+CAA+C;IACjD,YAAY,GAAG,IAAI,CAAE;QACjB,MAAM,CAAC,kBAAkB,MAAM,SAAS,KAAK,GAAG;QAChD,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AAEA;;;;;;;;;;;;;8EAa8E,GAE9E,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IAChD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AAEA,SAAS,OAAO,KAAK;IACjB,OAAO,OAAO,IAAI,CAAC;AACvB;AACA,SAAS,eAAe,SAAS,EAAE,OAAO;IACtC,MAAM,SAAS,OAAO,IAAI,CAAC,OAAO,YAAY,CAAC,IAAI,CAAC,SAAS;IAC7D,IAAI,SAAS;QACT,OAAO,OACF,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,OAAO;IACxB,OAEI,OAAO;AACf;AACA,SAAS,aAAa,sBAAsB;IACxC,OAAO,IAAI,WAAW,OACjB,IAAI,CAAC,wBACL,KAAK,CAAC,IACN,GAAG,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC;AACjC;AAEA,SAAS,eAAe,aAAa,EAAE,4BAA4B;IAC/D,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,kBAAkB,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO;QAC7D,MAAM,kBAAkB,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YAAE,MAAM;YAAW,MAAM;QAAQ,GAAG,8BAA8B;QACnH,MAAM,WAAW,IAAI,WAAW,gBAAgB,UAAU,GAAG,gBAAgB,UAAU;QACvF,SAAS,GAAG,CAAC,IAAI,WAAW,kBAAkB;QAC9C,SAAS,GAAG,CAAC,IAAI,WAAW,kBAAkB,gBAAgB,UAAU;QACxE,OAAO;IACX;AACJ;AAEA,SAAS,kBAAkB,OAAO;IAC9B,OAAO,CAAA,GAAA,iLAAA,CAAA,0BAAuB,AAAD,EAAE;AACnC;AACA,SAAS,wBAAwB,OAAO;IACpC,OAAO,OAAO,kBAAkB;AACpC;AAEA,oBAAoB;AACpB,MAAM,yBAAyB;AAC/B,MAAM,2BAA2B;AACjC,MAAM,yBAAyB;AAE/B;;;;;;CAMC,GACD,SAAS,wBAAwB,eAAe,EAAE,sBAAsB;IACpE,OAAO,IAAI,MAAM,CAAC,GAAG;QACjB,KAAI,MAAM,EAAE,CAAC;YACT,gGAAgG;YAChG,wGAAwG;YACxG,4CAA4C;YAC5C,YAAY;YACZ,IAAI,MAAM,QAAQ;gBACd,OAAO;YACX;YACA,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM;gBACnB,MAAM,CAAC,EAAE,GAAG,SAAU,WAAW;oBAC7B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;wBACnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,0BAA0B,GAAG,aAAa;wBACrE,MAAM,SAAS,MAAM,uBAAuB,QAAQ;wBACpD,8GAA8G;wBAC9G,IAAI,WAAW,eAAe,OAAO,eAAe,IAAI,CAAC,OAAO,cAAc,EAAE;4BAC5E,MAAM,CAAC,iBAAiB,GAAG,MAAM,eAAe,OAAO,eAAe,EAAE,QAAQ;wBACpF;wBACA,OAAO,2BAA2B,GAAG,QAAQ;oBACjD;gBACJ;YACJ;YACA,OAAO,MAAM,CAAC,EAAE;QACpB;QACA;YACI,OAAO;QACX;QACA;YACI,OAAO;QACX;IACJ;AACJ;AACA;;;;;;;;CAQC,GACD,SAAS,0BAA0B,UAAU,EAAE,YAAY,EAAE,eAAe;IACxE,IAAI,SAAS;IACb,IAAI,SAAS,WACR,QAAQ,GACR,OAAO,CAAC,UAAU,CAAC,SAAW,CAAC,CAAC,EAAE,OAAO,WAAW,IAAI,EACxD,WAAW;IAChB,OAAQ;QACJ,KAAK;YAAa;gBACd,IAAI,EAAE,KAAK,EAAE,GAAG;gBAChB,IAAI,oBAAoB,UAAU;oBAC9B,OAAQ;wBACJ,KAAK;4BAAkB;gCACnB,QAAQ;gCACR;4BACJ;wBACA,KAAK;4BAAiB;gCAClB,QAAQ;gCACR;4BACJ;wBACA,KAAK;4BAAkB;gCACnB,QAAQ;gCACR;4BACJ;wBACA;4BAAS;gCACL,QAAQ,OAAO,OAAO;4BAC1B;oBACJ;oBACA,OAAO,OAAO,GAAG;gBACrB,OACK;oBACD,OAAQ;wBACJ,KAAK;wBACL,KAAK;4BAAU;gCACX,QAAQ,CAAC,OAAO,EAAE,OAAO;gCACzB;4BACJ;wBACA,KAAK;4BAAgB;gCACjB,QAAQ;gCACR;4BACJ;oBACJ;oBACA,OAAO,KAAK,GAAG;gBACnB;YACJ;QACA,KAAK;YAAe;gBAChB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;gBACjC,IAAI,YAAY;oBACZ,OAAQ;wBACJ,KAAK;4BAAU;gCACX,SAAS;gCACT,SAAS;oCAAE,YAAY;oCAAY,UAAU;gCAAS;gCACtD;4BACJ;wBACA;4BAAS;gCACL,SAAS;gCACT;4BACJ;oBACJ;gBACJ;gBACA;YACJ;IACJ;IACA,OAAO;QAAE;QAAQ;IAAO;AAC5B;AACA;;;;;;;CAOC,GACD,SAAS,2BAA2B,MAAM,EAAE,QAAQ,EAAE,eAAe;IACjE,OAAQ;QACJ,KAAK;YAAmB;gBACpB,MAAM,eAAe;gBACrB,OAAQ;oBACJ,KAAK;wBAAU;4BACX,MAAM,WAAW;gCAAC;6BAAuB;4BACzC,IAAI,aAAa,4BAA4B,KAAK,MAAM;gCACpD,SAAS,IAAI,CAAC;4BAClB;4BACA,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,eAAe;gCAAE,UAAU;4BAAS;wBAC/E;oBACA,KAAK;wBAAM;4BACP,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,eAAe;gCAAE,qCAAqC;gCAAM,8BAA8B,aAAa,QAAQ,CAAC,QAAQ,CAAC;4BAA0B;wBAC9L;gBACJ;YACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,eAAe,aAAa,EAAE,mBAAmB,EAAE,sBAAsB;IAC9E,IAAI;IACJ,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,SAAS,CAAC,KAAK,cAAc,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,QAAQ,CAAC,IAAI;QAChG,MAAM,UAAU,oBAAoB,QAAQ,CAAC,EAAE,CAAC,OAAO;QACvD,MAAM,cAAc,wBAAwB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,gBAAgB;YAAE;YAAQ;QAAQ;QAC9G,MAAM,oBAAoB,MAAM,uBAAuB,iBAAiB;YACpE,WAAW;gBAAC;aAAQ;YACpB,UAAU;gBAAC;aAAY;QAC3B;QACA,MAAM,eAAe;YACjB,SAAS;YACT,gBAAgB;YAChB,WAAW,kBAAkB,eAAe,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,MAAM;QAC5E;QACA,OAAO;IACX;AACJ;AAEA,MAAM,wBAAwB;AAC9B,SAAS,2BAA2B,cAAc;IAC9C,IAAI,kBAAkB,YAAY;QAC9B,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,YAAY,IAAI,YAAY;IAClC,MAAM,OAAO,IAAI,SAAS;IAC1B,KAAK,SAAS,CAAC,GAAG,gBAAgB,gBAAgB,GAAG;IACrD,OAAO,IAAI,WAAW;AAC1B;AAEA,MAAM,8BAA8B;AACpC,MAAM,kCAAkC;AACxC,SAAS,eAAe,SAAS,EAAE,cAAc,EAAE,YAAY;IAC3D,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,uBAAuB,2BAA2B;QACxD,MAAM,uBAAuB,IAAI,WAAW;QAC5C,OAAO,eAAe,CAAC;QACvB,MAAM,aAAa,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC,mBAAmB,sBAAsB,uBAAuB,cAAc,IAAI,cAAc,MAAM,CAAC;QACtJ,MAAM,WAAW,IAAI,WAAW,qBAAqB,UAAU,GAAG,qBAAqB,UAAU,GAAG,WAAW,UAAU;QACzH,SAAS,GAAG,CAAC,IAAI,WAAW,uBAAuB;QACnD,SAAS,GAAG,CAAC,IAAI,WAAW,uBAAuB,qBAAqB,UAAU;QAClF,SAAS,GAAG,CAAC,IAAI,WAAW,aAAa,qBAAqB,UAAU,GAAG,qBAAqB,UAAU;QAC1G,OAAO;IACX;AACJ;AACA,SAAS,eAAe,OAAO,EAAE,YAAY;IACzC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,uBAAuB,QAAQ,KAAK,CAAC,GAAG;QAC9C,MAAM,uBAAuB,QAAQ,KAAK,CAAC,uBAAuB,wBAAwB;QAC1F,MAAM,aAAa,QAAQ,KAAK,CAAC,wBAAwB;QACzD,MAAM,kBAAkB,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC,mBAAmB,sBAAsB,uBAAuB,cAAc;QAClI,MAAM,YAAY,iBAAiB,MAAM,CAAC;QAC1C,OAAO;IACX;AACJ;AACA,SAAS,mBAAmB,cAAc,EAAE,oBAAoB;IAC5D,OAAO;QACH,gBAAgB;QAChB,IAAI;QACJ,MAAM;QACN,WAAW;IACf;AACJ;AACA,IAAI;AACJ,SAAS;IACL,IAAI,iBAAiB,WAAW;QAC5B,eAAe,IAAI,YAAY;IACnC;IACA,OAAO;AACX;AAEA,SAAS;IACL,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,OAAO,MAAM,OAAO,MAAM,CAAC,WAAW,CAAC;YACnC,MAAM;YACN,YAAY;QAChB,GAAG,OAAyB;YAAC;SAAO;IACxC;AACJ;AAEA,SAAS;IACL,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,OAAO,MAAM,OAAO,MAAM,CAAC,WAAW,CAAC;YACnC,MAAM;YACN,YAAY;QAChB,GAAG,OAAyB;YAAC;YAAa;SAAa;IAC3D;AACJ;AAEA,6CAA6C;AAC7C,SAAS,0BAA0B,MAAM;IACrC,IAAI,SAAS;IACb,MAAM,QAAQ,IAAI,WAAW;IAC7B,MAAM,MAAM,MAAM,UAAU;IAC5B,IAAK,IAAI,KAAK,GAAG,KAAK,KAAK,KAAM;QAC7B,UAAU,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG;IAC3C;IACA,OAAO,OAAO,IAAI,CAAC;AACvB;AAEA,SAAS;IACL,OAAO,sBAAsB,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,QAAQ,QAAQ,CAAC;AACtF;AACA,SAAS,sBAAsB,IAAI;IAC/B,IAAI,OAAO,SAAS,OAAO,OAAO;QAC9B,MAAM,IAAI,+BAA+B,mCAAmC,mCAAmC,EAAE,CAAC,yDAAyD,EAAE,KAAK,OAAO,CAAC,EAAE;YAAE;QAAK;IACvM;IACA,OAAO;AACX;AAEA,SAAS,yCAAyC,yBAAyB;IACvE,OAAO,0BAA0B,OAAO,CAAC,UAAU,CAAC,IAAO,CAAA;YACvD,KAAK;YACL,KAAK;YACL,KAAK;QACT,CAAA,CAAC,CAAC,EAAE;AACR;AAEA,MAAM,cAAc;AACpB,SAAS,aAAa,UAAU;IAC5B,OAAQ,UACJ,qCAAqC;KACpC,OAAO,CAAC,gBAAgB,GACzB,iCAAiC;KAChC,KAAK,CAAC;AACf;AACA,SAAS,aAAa,cAAc,EAAE,aAAa;IAC/C,IAAI,UAAU;IACd,IAAI,eAAe;QACf,IAAI;YACA,UAAU,IAAI,IAAI;QACtB,EACA,OAAO,IAAI,CAAE,EAAE,+BAA+B;QAC9C,IAAI,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,QAAQ,MAAM,UAAU;YACnF,MAAM,IAAI,+BAA+B,mCAAmC,+BAA+B,EAAE;QACjH;IACJ;IACA,WAAW,CAAC,UAAU,IAAI,IAAI,GAAG,YAAY,EAAE,CAAC,CAAC;IACjD,MAAM,WAAW,eAAe,UAAU,CAAC,OAEnC,iBAEA;WAAI,aAAa,QAAQ,QAAQ;WAAM,aAAa;KAAgB,CAAC,IAAI,CAAC;IAClF,OAAO,IAAI,IAAI,UAAU;AAC7B;AACA,SAAS,6BAA6B,oBAAoB,EAAE,YAAY,EAAE,kBAAkB,EAAE,mBAAmB;IAAC;CAAK;IACnH,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,kBAAkB,sBAAsB;QAC9C,MAAM,cAAc,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO;QACzD,MAAM,aAAa,0BAA0B;QAC7C,MAAM,MAAM,aAAa,sBAAsB;QAC/C,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe,yCAAyC;QAC7E,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ,GAAG,iBAAiB;QACjD,iBAAiB,OAAO,CAAC,CAAC;YACtB,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK;QAC9B;QACA,OAAO;IACX;AACJ;AACA,SAAS,mCAAmC,oBAAoB,EAAE,aAAa,EAAE,WAAW,EAAE,kBAAkB,EAAE,mBAAmB;IAAC;CAAK;IACvI,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,cAAc,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO;QACzD,MAAM,aAAa,0BAA0B;QAC7C,MAAM,MAAM,aAAa,uBAAuB;QAChD,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe,yCAAyC;QAC7E,IAAI,YAAY,CAAC,GAAG,CAAC,aAAa,GAAG,eAAe;QACpD,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,GAAG,eAAe,aAAa,OAAO;QACjE,iBAAiB,OAAO,CAAC,CAAC;YACtB,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK;QAC9B;QACA,OAAO;IACX;AACJ;AAEA,SAAS,sBAAsB,cAAc,EAAE,YAAY;IACvD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,YAAY,KAAK,SAAS,CAAC;QACjC,MAAM,iBAAiB,eAAe,EAAE;QACxC,OAAO,eAAe,WAAW,gBAAgB;IACrD;AACJ;AACA,SAAS,sBAAsB,OAAO,EAAE,YAAY;IAChD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,YAAY,MAAM,eAAe,SAAS;QAChD,MAAM,iBAAiB,KAAK,KAAK,CAAC;QAClC,IAAI,OAAO,cAAc,CAAC,IAAI,CAAC,gBAAgB,UAAU;YACrD,MAAM,IAAI,uCAAuC,eAAe,EAAE,EAAE,eAAe,KAAK,CAAC,IAAI,EAAE,eAAe,KAAK,CAAC,OAAO;QAC/H;QACA,OAAO;IACX;AACJ;AAEA,SAAS,cAAc,aAAa,EACpC,oBAAoB,EAAE,cAAc;IAChC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,CAAC,4BAA4B,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpE,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO;YAC/B,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,cAAc,KAAK,CAAC,GAAG,kCAAkC;gBAAE,MAAM;gBAAQ,YAAY;YAAQ,GAAG,OAAyB,EAAE;SAC7J;QACD,MAAM,eAAe,MAAM,OAAO,MAAM,CAAC,UAAU,CAAC;YAAE,MAAM;YAAQ,QAAQ;QAAgB,GAAG,gBAAgB;QAC/G,MAAM,gBAAgB,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,cAAc,QAAQ,OAAyB;YAAC;SAAY;QACvH,MAAM,oBAAoB,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC;YACpD,MAAM;YACN,MAAM;YACN,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI;QACd,GAAG,eAAe;YAAE,MAAM;YAAW,QAAQ;QAAI,GAAG,OAAyB;YAAC;YAAW;SAAU;QACnG,OAAO;IACX;AACJ;AAEA,SAAS,kBAAkB,OAAO,EAAE,YAAY;IAC5C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,YAAY,MAAM,eAAe,SAAS;QAChD,MAAM,iBAAiB,KAAK,KAAK,CAAC;QAClC,IAAI,kBAAkB;QACtB,IAAI,OAAO,cAAc,CAAC,IAAI,CAAC,gBAAgB,MAAM;YACjD,OAAQ,eAAe,CAAC;gBACpB,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD,kBAAkB;oBAClB;gBACJ,KAAK;oBACD,kBAAkB;oBAClB;gBACJ;oBACI,MAAM,IAAI,+BAA+B,mCAAmC,8BAA8B,EAAE,CAAC,sCAAsC,EAAE,eAAe,CAAC,EAAE;YAC/K;QACJ;QACA,OAAQ;YACJ,kBAAkB;QACtB;IACJ;AACJ;AAEA,+EAA+E;AAC/E,MAAM,UAAU;IACZ,SAAS;IACT,OAAO;AACX;AACA,SAAS,kBAAkB,CAAC;IACxB,OAAO;AACX;AACA,SAAS;IACL,OAAO,UAAU,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,QAAQ,OAAO,GAAG,QAAQ,KAAK;AAC3F;AACA,SAAS;IACL,yEAAyE;IACzE,qEAAqE;IACrE,8DAA8D;IAC9D,OAAO,IAAI,QAAQ,CAAC,SAAS;QACzB,SAAS;YACL,aAAa;YACb,OAAO,mBAAmB,CAAC,QAAQ;QACvC;QACA,SAAS;YACL;YACA;QACJ;QACA,OAAO,gBAAgB,CAAC,QAAQ;QAChC,MAAM,YAAY,WAAW;YACzB;YACA;QACJ,GAAG;IACP;AACJ;AACA,IAAI,SAAS;AACb,SAAS,4BAA4B,GAAG;IACpC,IAAI,UAAU,MAAM;QAChB,SAAS,SAAS,aAAa,CAAC;QAChC,OAAO,KAAK,CAAC,OAAO,GAAG;QACvB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC9B;IACA,oEAAoE;IACpE,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,QAAQ;AACrD;AACA,SAAS,kBAAkB,cAAc;IACrC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,eAAe,QAAQ,KAAK,UAAU;YACtC,wEAAwE;YACxE,sEAAsE;YACtE,yDAAyD;YACzD,OAAO,QAAQ,CAAC,MAAM,CAAC;QAC3B,OACK;YACD,mEAAmE;YACnE,IAAI;gBACA,MAAM,UAAU;gBAChB,OAAQ;oBACJ,KAAK,QAAQ,OAAO;wBAChB,+DAA+D;wBAC/D,4BAA4B;wBAE5B;oBACJ,KAAK,QAAQ,KAAK;wBAAE;4BAChB,MAAM,mBAAmB;4BACzB,OAAO,QAAQ,CAAC,MAAM,CAAC;4BACvB,MAAM;4BACN;wBACJ;oBACA;wBACI,kBAAkB;gBAC1B;YACJ,EACA,OAAO,GAAG;gBACN,MAAM,IAAI,+BAA+B,mCAAmC,sBAAsB,EAAE;YACxG;QACJ;IACJ;AACJ;AACA,SAAS,aAAa,oBAAoB,EAAE,kBAAkB;IAC1D,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,wBAAwB;QAC9B,MAAM,iBAAiB,MAAM,6BAA6B,sBAAsB,uBAAuB;QACvG,MAAM,kBAAkB;QACxB,OAAO;IACX;AACJ;AAEA,MAAM,8BAA8B;IAChC;;;;;;;;;KASC,GACD,sBAAsB;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAK;IAC/D,WAAW;AACf;AACA,MAAM,4BAA4B;AAClC,MAAM,4BAA4B;AAClC,SAAS;IACL,IAAI,OAAO,WAAW,eAAe,OAAO,eAAe,KAAK,MAAM;QAClE,MAAM,IAAI,+BAA+B,mCAAmC,6BAA6B,EAAE;IAC/G;AACJ;AACA,SAAS,gCAAgC,aAAa;IAClD,IAAI;IACJ,IAAI;QACA,MAAM,IAAI,IAAI;IAClB,EACA,OAAO,IAAI;QACP,MAAM,IAAI,+BAA+B,mCAAmC,+BAA+B,EAAE;IACjH;IACA,IAAI,IAAI,QAAQ,KAAK,UAAU;QAC3B,MAAM,IAAI,+BAA+B,mCAAmC,+BAA+B,EAAE;IACjH;AACJ;AACA,SAAS,+BAA+B,SAAS;IAC7C,MAAM,OAAO,IAAI,SAAS;IAC1B,OAAO,KAAK,SAAS,CAAC,GAAG,gBAAgB,GAAG;AAChD;AACA,SAAS,cAAc,SAAS;IAC5B,IAAI,QAAQ,IAAI,WAAW,YAAY,IAAI,UAAU,UAAU,EAAE,QAAQ,IAAI,QAAQ,GAAG,SAAS,GAAG;IACpG,GAAG;QACC,IAAI,UAAU,KAAK,SAAS,OACxB,MAAM,IAAI,WAAW;QACzB,IAAI,KAAK,CAAC,SAAS;QACnB,SAAS,CAAC,IAAI,IAAI,KAAM,IAAI;IAChC,QAAS,KAAK,KAAM;IACpB,OAAO;QAAE;QAAO;IAAO;AAC3B;AACA,SAAS,4BAA4B,SAAS;IAC1C,IAAI,EAAE,OAAO,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc;IAC9C,OAAO,IAAI,WAAW,UAAU,KAAK,CAAC,QAAQ,SAAS;AAC3D;AACA,SAAS,SAAS,QAAQ,EAAE,MAAM;IAC9B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC;QACA,MAAM,qBAAqB,MAAM;QACjC,MAAM,cAAc,MAAM,aAAa,mBAAmB,SAAS,EAAE,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,OAAO;QACnI,MAAM,eAAe,CAAC,eAAe,EAAE,YAAY,cAAc,CAAC;QAClE,IAAI;QACJ,MAAM,sBAAsB,CAAC;YACzB,MAAM,WAAW;mBAAI,4BAA4B,oBAAoB;aAAC;YACtE,OAAO,IAAO,SAAS,MAAM,GAAG,IAAI,SAAS,KAAK,KAAK,QAAQ,CAAC,EAAE;QACtE,CAAC;QACD,IAAI,uBAAuB;QAC3B,IAAI,iCAAiC;QACrC,IAAI,QAAQ;YAAE,QAAQ;QAAe;QACrC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI;YACJ,8DAA8D;YAC9D,MAAM,0BAA0B,CAAC;YACjC,MAAM,aAAa,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACrD,IAAI,MAAM,MAAM,KAAK,cAAc;wBAC/B,QAAQ,IAAI,CAAC,kFACT,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,GAAG,CAAC;wBAC9B;oBACJ;oBACA,OAAO,mBAAmB,CAAC,QAAQ;oBACnC,2FAA2F;oBAC3F,sFAAsF;oBACtF,8FAA8F;oBAC9F,sFAAsF;oBACtF,+FAA+F;oBAC/F,2FAA2F;oBAC3F,MAAM,EAAE,kBAAkB,EAAE,GAAG;oBAC/B,MAAM,cAAc,MAAM;oBAC1B,OAAO,IAAI,CAAC,CAAA,MAAM,eAAe,YAAY,SAAS,EAAE,mBAAmB,UAAU,CAAA;oBACrF,QAAQ;wBACJ,QAAQ;wBACR,sBAAsB,mBAAmB,SAAS;wBAClD,gBAAgB,YAAY,UAAU;oBAC1C;gBACJ;YACA,MAAM,cAAc,CAAC;gBACjB,IAAI,IAAI,QAAQ,EAAE;oBACd,QAAQ;wBAAE,QAAQ;oBAAe;gBACrC,OACK;oBACD,OAAO,IAAI,+BAA+B,mCAAmC,oBAAoB,EAAE,CAAC,yCAAyC,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE;wBAAE,YAAY;oBAAI;gBAClM;gBACA;YACJ;YACA,MAAM,cAAc,CAAC,OAAS,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBAC1D;oBACA,IAAI,KAAK,GAAG,KAAK,uBAAuB,4BAA4B,SAAS,EAAE;wBAC3E,OAAO,IAAI,+BAA+B,mCAAmC,qBAAqB,EAAE,CAAC,6CAA6C,EAAE,aAAa,CAAC,CAAC;oBACvK,OACK;wBACD,MAAM,IAAI,QAAQ,CAAC;4BACf,MAAM,eAAe;4BACrB,qBAAqB,OAAO,UAAU,CAAC,SAAS;wBACpD;wBACA;oBACJ;gBACJ;YACA,MAAM,gBAAgB,CAAC,MAAQ,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBAC3D,MAAM,iBAAiB,MAAM,IAAI,IAAI,CAAC,WAAW;oBACjD,OAAQ,MAAM,MAAM;wBAChB,KAAK;4BACD,IAAI,eAAe,UAAU,KAAK,GAAG;gCACjC,MAAM,IAAI,MAAM;4BACpB;4BACA,MAAM,cAAc,MAAM;4BAC1B,OAAO,IAAI,CAAC,CAAA,MAAM,eAAe,YAAY,SAAS,EAAE,mBAAmB,UAAU,CAAA;4BACrF,QAAQ;gCACJ,QAAQ;gCACR,sBAAsB,mBAAmB,SAAS;gCAClD,gBAAgB,YAAY,UAAU;4BAC1C;4BACA;wBACJ,KAAK;4BACD,IAAI;gCACA,MAAM,uBAAuB,eAAe,KAAK,CAAC,GAAG;gCACrD,MAAM,iBAAiB,+BAA+B;gCACtD,IAAI,mBAAoB,iCAAiC,GAAI;oCACzD,MAAM,IAAI,MAAM;gCACpB;gCACA,iCAAiC;gCACjC,MAAM,iBAAiB,MAAM,sBAAsB,gBAAgB,MAAM,YAAY;gCACrF,MAAM,kBAAkB,uBAAuB,CAAC,eAAe,EAAE,CAAC;gCAClE,OAAO,uBAAuB,CAAC,eAAe,EAAE,CAAC;gCACjD,gBAAgB,OAAO,CAAC,eAAe,MAAM;4BACjD,EACA,OAAO,GAAG;gCACN,IAAI,aAAa,wCAAwC;oCACrD,MAAM,kBAAkB,uBAAuB,CAAC,EAAE,gBAAgB,CAAC;oCACnE,OAAO,uBAAuB,CAAC,EAAE,gBAAgB,CAAC;oCAClD,gBAAgB,MAAM,CAAC;gCAC3B,OACK;oCACD,MAAM;gCACV;4BACJ;4BACA;wBACJ,KAAK;4BAAkB;gCACnB,sFAAsF;gCACtF,IAAI,eAAe,UAAU,KAAK,GAAG;oCACjC,MAAM,cAAc,MAAM;oCAC1B,OAAO,IAAI,CAAC,CAAA,MAAM,eAAe,YAAY,SAAS,EAAE,mBAAmB,UAAU,CAAA;oCACrF,QAAQ;wCACJ,QAAQ;wCACR,sBAAsB,mBAAmB,SAAS;wCAClD,gBAAgB,YAAY,UAAU;oCAC1C;oCACA;gCACJ;gCACA,MAAM,eAAe,MAAM,cAAc,gBAAgB,MAAM,oBAAoB,EAAE,MAAM,cAAc;gCACzG,MAAM,0BAA0B,eAAe,KAAK,CAAC;gCACrD,MAAM,oBAAoB,wBAAwB,UAAU,KAAK,IAC3D,MAAM,CAAC,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;wCAC3C,MAAM,uBAAuB,wBAAwB,KAAK,CAAC,GAAG;wCAC9D,MAAM,iBAAiB,+BAA+B;wCACtD,IAAI,mBAAoB,iCAAiC,GAAI;4CACzD,MAAM,IAAI,MAAM;wCACpB;wCACA,iCAAiC;wCACjC,OAAO,kBAAkB,yBAAyB;oCACtD,EAAE,MAAM;oCAAE,kBAAkB;gCAAS;gCACzC,QAAQ;oCAAE,QAAQ;oCAAa;oCAAc;gCAAkB;gCAC/D,MAAM,SAAS,wBAAwB,kBAAkB,gBAAgB,EAAE,CAAC,QAAQ,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;wCAC3H,MAAM,KAAK;wCACX,OAAO,IAAI,CAAC,CAAA,MAAM,sBAAsB;4CACpC;4CACA,SAAS;4CACT;4CACA,QAAQ,WAAW,QAAQ,WAAW,KAAK,IAAI,SAAS,CAAC;wCAC7D,GAAG,aAAY;wCACf,OAAO,IAAI,QAAQ,CAAC,SAAS;4CACzB,uBAAuB,CAAC,GAAG,GAAG;gDAC1B,SAAQ,MAAM;oDACV,OAAQ;wDACJ,KAAK;wDACL,KAAK;4DAAe;gEAChB,MAAM,EAAE,eAAe,EAAE,GAAG;gEAC5B,IAAI,mBAAmB,MAAM;oEACzB,IAAI;wEACA,gCAAgC;oEACpC,EACA,OAAO,GAAG;wEACN,OAAO;wEACP;oEACJ;gEACJ;gEACA;4DACJ;oDACJ;oDACA,QAAQ;gDACZ;gDACA;4CACJ;wCACJ;oCACJ;gCACA,IAAI;oCACA,QAAQ,CAAA,MAAM,SAAS,OAAM;gCACjC,EACA,OAAO,GAAG;oCACN,OAAO;gCACX,SACQ;oCACJ;oCACA,OAAO,KAAK;gCAChB;gCACA;4BACJ;oBACJ;gBACJ;YACA,IAAI;YACJ,IAAI;YACJ,MAAM,0BAA0B;gBAC5B,IAAI,eAAe;oBACf;gBACJ;gBACA,QAAQ;oBAAE,QAAQ;oBAAc;gBAAmB;gBACnD,IAAI,wBAAwB,WAAW;oBACnC,sBAAsB,KAAK,GAAG;gBAClC;gBACA,SAAS,IAAI,UAAU,cAAc;oBAAC;iBAA0B;gBAChE,OAAO,gBAAgB,CAAC,QAAQ;gBAChC,OAAO,gBAAgB,CAAC,SAAS;gBACjC,OAAO,gBAAgB,CAAC,SAAS;gBACjC,OAAO,gBAAgB,CAAC,WAAW;gBACnC,gBAAgB;oBACZ,OAAO,YAAY,CAAC;oBACpB,OAAO,mBAAmB,CAAC,QAAQ;oBACnC,OAAO,mBAAmB,CAAC,SAAS;oBACpC,OAAO,mBAAmB,CAAC,SAAS;oBACpC,OAAO,mBAAmB,CAAC,WAAW;gBAC1C;YACJ;YACA;QACJ;IACJ;AACJ;AACA,SAAS,oBAAoB,MAAM;IAC/B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC;QACA,MAAM,qBAAqB,MAAM;QACjC,MAAM,eAAe,CAAC,MAAM,EAAE,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,CAAC,QAAQ,CAAC;QAClH,IAAI;QACJ,MAAM,sBAAsB,CAAC;YACzB,MAAM,WAAW;mBAAI,4BAA4B,oBAAoB;aAAC;YACtE,OAAO,IAAO,SAAS,MAAM,GAAG,IAAI,SAAS,KAAK,KAAK,QAAQ,CAAC,EAAE;QACtE,CAAC;QACD,IAAI,uBAAuB;QAC3B,IAAI,iCAAiC;QACrC,IAAI;QACJ,IAAI,QAAQ;YAAE,QAAQ;QAAe;QACrC,IAAI;QACJ,IAAI;QACJ,IAAI,cAAc,CAAC,MAAQ,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACvD,IAAI,YAAY,UAAU;oBACtB,MAAM,UAAU,MAAM,IAAI,IAAI;oBAC9B,OAAO,aAAa,SAAS,MAAM;gBACvC,OACK;oBACD,OAAO,MAAM,IAAI,IAAI,CAAC,WAAW;gBACrC;YACJ;QACA,6BAA6B;QAC7B,0EAA0E;QAC1E,qEAAqE;QACrE,MAAM,iBAAiB,MAAM,IAAI,QAAQ,CAAC,SAAS;YAC/C,MAAM,aAAa,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACrD,IAAI,MAAM,MAAM,KAAK,cAAc;wBAC/B,QAAQ,IAAI,CAAC,kFACT,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,GAAG,CAAC;wBAC9B;oBACJ;oBACA,IAAI,OAAO,QAAQ,CAAC,QAAQ,CAAC,4BAA4B;wBACrD,WAAW;oBACf,OACK;wBACD,WAAW;oBACf;oBACA,OAAO,mBAAmB,CAAC,QAAQ;gBACvC;YACA,MAAM,cAAc,CAAC;gBACjB,IAAI,IAAI,QAAQ,EAAE;oBACd,QAAQ;wBAAE,QAAQ;oBAAe;gBACrC,OACK;oBACD,OAAO,IAAI,+BAA+B,mCAAmC,oBAAoB,EAAE,CAAC,yCAAyC,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE;wBAAE,YAAY;oBAAI;gBAClM;gBACA;YACJ;YACA,MAAM,cAAc,CAAC,OAAS,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBAC1D;oBACA,IAAI,KAAK,GAAG,KAAK,uBAAuB,4BAA4B,SAAS,EAAE;wBAC3E,OAAO,IAAI,+BAA+B,mCAAmC,qBAAqB,EAAE,CAAC,6CAA6C,EAAE,aAAa,CAAC,CAAC;oBACvK,OACK;wBACD,MAAM,IAAI,QAAQ,CAAC;4BACf,MAAM,eAAe;4BACrB,qBAAqB,OAAO,UAAU,CAAC,SAAS;wBACpD;wBACA;oBACJ;gBACJ;YACA,MAAM,2BAA2B,CAAC,MAAQ,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACtE,MAAM,iBAAiB,MAAM,YAAY;oBACzC,IAAI,MAAM,MAAM,KAAK,cAAc;wBAC/B,IAAI,eAAe,UAAU,IAAI,GAAG;4BAChC,MAAM,IAAI,MAAM;wBACpB;wBACA,MAAM,cAAc,4BAA4B;wBAChD,QAAQ;4BACJ,QAAQ;4BACR,aAAa;wBACjB;wBACA,MAAM,iBAAiB,MAAM,mCAAmC,mBAAmB,SAAS,EAAE,OAAO,mBAAmB,EAAE,aAAa,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,OAAO;wBACrM,OAAO,mBAAmB,CAAC,WAAW;wBACtC,QAAQ;oBACZ;gBACJ;YACA,IAAI;YACJ,MAAM,0BAA0B;gBAC5B,IAAI,eAAe;oBACf;gBACJ;gBACA,QAAQ;oBAAE,QAAQ;oBAAc;gBAAmB;gBACnD,IAAI,wBAAwB,WAAW;oBACnC,sBAAsB,KAAK,GAAG;gBAClC;gBACA,SAAS,IAAI,UAAU,cAAc;oBAAC;oBAA2B;iBAA0B;gBAC3F,OAAO,gBAAgB,CAAC,QAAQ;gBAChC,OAAO,gBAAgB,CAAC,SAAS;gBACjC,OAAO,gBAAgB,CAAC,SAAS;gBACjC,OAAO,gBAAgB,CAAC,WAAW;gBACnC,gBAAgB;oBACZ,OAAO,YAAY,CAAC;oBACpB,OAAO,mBAAmB,CAAC,QAAQ;oBACnC,OAAO,mBAAmB,CAAC,SAAS;oBACpC,OAAO,mBAAmB,CAAC,SAAS;oBACpC,OAAO,mBAAmB,CAAC,WAAW;gBAC1C;YACJ;YACA;QACJ;QACA,0BAA0B;QAC1B,oFAAoF;QACpF,gFAAgF;QAChF,IAAI,qBAAqB;QACzB,IAAI;QACJ,OAAO;YAAE;YAAgB,OAAO;gBACxB,OAAO,KAAK;gBACZ;YACJ;YAAG,QAAQ,IAAI,QAAQ,CAAC,SAAS;gBAC7B,8DAA8D;gBAC9D,MAAM,0BAA0B,CAAC;gBACjC,MAAM,gBAAgB,CAAC,MAAQ,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;wBAC3D,MAAM,iBAAiB,MAAM,YAAY;wBACzC,OAAQ,MAAM,MAAM;4BAChB,KAAK;gCACD,IAAI,eAAe,UAAU,KAAK,GAAG;oCACjC,MAAM,IAAI,MAAM;gCACpB;gCACA,MAAM,cAAc,MAAM;gCAC1B,MAAM,YAAY,MAAM,eAAe,YAAY,SAAS,EAAE,mBAAmB,UAAU;gCAC3F,IAAI,YAAY,UAAU;oCACtB,OAAO,IAAI,CAAC,eAAe;gCAC/B,OACK;oCACD,OAAO,IAAI,CAAC;gCAChB;gCACA,QAAQ;oCACJ,QAAQ;oCACR,sBAAsB,mBAAmB,SAAS;oCAClD,gBAAgB,YAAY,UAAU;gCAC1C;gCACA;4BACJ,KAAK;gCACD,IAAI;oCACA,MAAM,uBAAuB,eAAe,KAAK,CAAC,GAAG;oCACrD,MAAM,iBAAiB,+BAA+B;oCACtD,IAAI,mBAAoB,iCAAiC,GAAI;wCACzD,MAAM,IAAI,MAAM;oCACpB;oCACA,iCAAiC;oCACjC,MAAM,iBAAiB,MAAM,sBAAsB,gBAAgB,MAAM,YAAY;oCACrF,MAAM,kBAAkB,uBAAuB,CAAC,eAAe,EAAE,CAAC;oCAClE,OAAO,uBAAuB,CAAC,eAAe,EAAE,CAAC;oCACjD,gBAAgB,OAAO,CAAC,eAAe,MAAM;gCACjD,EACA,OAAO,GAAG;oCACN,IAAI,aAAa,wCAAwC;wCACrD,MAAM,kBAAkB,uBAAuB,CAAC,EAAE,gBAAgB,CAAC;wCACnE,OAAO,uBAAuB,CAAC,EAAE,gBAAgB,CAAC;wCAClD,gBAAgB,MAAM,CAAC;oCAC3B,OACK;wCACD,MAAM;oCACV;gCACJ;gCACA;4BACJ,KAAK;gCAAkB;oCACnB,MAAM,eAAe,MAAM,cAAc,gBAAgB,MAAM,oBAAoB,EAAE,MAAM,cAAc;oCACzG,MAAM,0BAA0B,eAAe,KAAK,CAAC;oCACrD,MAAM,oBAAoB,wBAAwB,UAAU,KAAK,IAC3D,MAAM,CAAC,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4CAC3C,MAAM,uBAAuB,wBAAwB,KAAK,CAAC,GAAG;4CAC9D,MAAM,iBAAiB,+BAA+B;4CACtD,IAAI,mBAAoB,iCAAiC,GAAI;gDACzD,MAAM,IAAI,MAAM;4CACpB;4CACA,iCAAiC;4CACjC,OAAO,kBAAkB,yBAAyB;wCACtD,EAAE,MAAM;wCAAE,kBAAkB;oCAAS;oCACzC,QAAQ;wCAAE,QAAQ;wCAAa;wCAAc;oCAAkB;oCAC/D,MAAM,SAAS,wBAAwB,kBAAkB,gBAAgB,EAAE,CAAC,QAAQ,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4CAC3H,MAAM,KAAK;4CACX,MAAM,YAAY,MAAM,sBAAsB;gDAC1C;gDACA,SAAS;gDACT;gDACA,QAAQ,WAAW,QAAQ,WAAW,KAAK,IAAI,SAAS,CAAC;4CAC7D,GAAG;4CACH,IAAI,YAAY,UAAU;gDACtB,OAAO,IAAI,CAAC,eAAe;4CAC/B,OACK;gDACD,OAAO,IAAI,CAAC;4CAChB;4CACA,OAAO,IAAI,QAAQ,CAAC,SAAS;gDACzB,uBAAuB,CAAC,GAAG,GAAG;oDAC1B,SAAQ,MAAM;wDACV,OAAQ;4DACJ,KAAK;4DACL,KAAK;gEAAe;oEAChB,MAAM,EAAE,eAAe,EAAE,GAAG;oEAC5B,IAAI,mBAAmB,MAAM;wEACzB,IAAI;4EACA,gCAAgC;wEACpC,EACA,OAAO,GAAG;4EACN,OAAO;4EACP;wEACJ;oEACJ;oEACA;gEACJ;wDACJ;wDACA,QAAQ;oDACZ;oDACA;gDACJ;4CACJ;wCACJ;oCACA,qBAAqB;oCACrB,IAAI;wCACA,QAAQ;oCACZ,EACA,OAAO,GAAG;wCACN,OAAO;oCACX;oCACA;gCACJ;wBACJ;oBACJ;gBACA,OAAO,gBAAgB,CAAC,WAAW;gBACnC,cAAc;oBACV,OAAO,mBAAmB,CAAC,WAAW;oBACtC;oBACA,IAAI,CAAC,oBAAoB;wBACrB,OAAO,IAAI,+BAA+B,mCAAmC,oBAAoB,EAAE,CAAC,gDAAgD,CAAC,EAAE;4BAAE,YAAY,IAAI,WAAW;wBAAuC;oBAC/N;gBACJ;YACJ;QAAG;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solana-mobile/mobile-wallet-adapter-protocol-web3js/lib/esm/index.js"], "sourcesContent": ["import { VersionedMessage, Transaction, VersionedTransaction, SIGNATURE_LENGTH_IN_BYTES } from '@solana/web3.js';\nimport { transact as transact$1, startRemoteScenario as startRemoteScenario$1 } from '@solana-mobile/mobile-wallet-adapter-protocol';\nimport bs58 from 'bs58';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\n\nfunction fromUint8Array(byteArray) {\n    return window.btoa(String.fromCharCode.call(null, ...byteArray));\n}\nfunction toUint8Array(base64EncodedByteArray) {\n    return new Uint8Array(window\n        .atob(base64EncodedByteArray)\n        .split('')\n        .map((c) => c.charCodeAt(0)));\n}\n\nfunction getPayloadFromTransaction(transaction) {\n    const serializedTransaction = 'version' in transaction\n        ? transaction.serialize()\n        : transaction.serialize({\n            requireAllSignatures: false,\n            verifySignatures: false,\n        });\n    const payload = fromUint8Array(serializedTransaction);\n    return payload;\n}\nfunction getTransactionFromWireMessage(byteArray) {\n    const numSignatures = byteArray[0];\n    const messageOffset = numSignatures * SIGNATURE_LENGTH_IN_BYTES + 1;\n    const version = VersionedMessage.deserializeMessageVersion(byteArray.slice(messageOffset, byteArray.length));\n    if (version === 'legacy') {\n        return Transaction.from(byteArray);\n    }\n    else {\n        return VersionedTransaction.deserialize(byteArray);\n    }\n}\nfunction transact(callback, config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const augmentedCallback = (wallet) => {\n            return callback(augmentWalletAPI(wallet));\n        };\n        return yield transact$1(augmentedCallback, config);\n    });\n}\nfunction startRemoteScenario(config) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const { wallet, close, associationUrl } = yield startRemoteScenario$1(config);\n        const augmentedPromise = wallet.then((wallet) => {\n            return augmentWalletAPI(wallet);\n        });\n        return { wallet: augmentedPromise, close, associationUrl };\n    });\n}\nfunction augmentWalletAPI(wallet) {\n    return new Proxy({}, {\n        get(target, p) {\n            if (target[p] == null) {\n                switch (p) {\n                    case 'signAndSendTransactions':\n                        target[p] = function (_a) {\n                            var { minContextSlot, commitment, skipPreflight, maxRetries, waitForCommitmentToSendNextTransaction, transactions } = _a, rest = __rest(_a, [\"minContextSlot\", \"commitment\", \"skipPreflight\", \"maxRetries\", \"waitForCommitmentToSendNextTransaction\", \"transactions\"]);\n                            return __awaiter(this, void 0, void 0, function* () {\n                                const payloads = transactions.map(getPayloadFromTransaction);\n                                const options = {\n                                    min_context_slot: minContextSlot,\n                                    commitment: commitment,\n                                    skip_preflight: skipPreflight,\n                                    max_retries: maxRetries,\n                                    wait_for_commitment_to_send_next_transaction: waitForCommitmentToSendNextTransaction\n                                };\n                                const { signatures: base64EncodedSignatures } = yield wallet.signAndSendTransactions(Object.assign(Object.assign(Object.assign({}, rest), (Object.values(options).some(element => element != null)\n                                    ? { options: options }\n                                    : null)), { payloads }));\n                                const signatures = base64EncodedSignatures.map(toUint8Array).map(bs58.encode);\n                                return signatures;\n                            });\n                        };\n                        break;\n                    case 'signMessages':\n                        target[p] = function (_a) {\n                            var { payloads } = _a, rest = __rest(_a, [\"payloads\"]);\n                            return __awaiter(this, void 0, void 0, function* () {\n                                const base64EncodedPayloads = payloads.map(fromUint8Array);\n                                const { signed_payloads: base64EncodedSignedMessages } = yield wallet.signMessages(Object.assign(Object.assign({}, rest), { payloads: base64EncodedPayloads }));\n                                const signedMessages = base64EncodedSignedMessages.map(toUint8Array);\n                                return signedMessages;\n                            });\n                        };\n                        break;\n                    case 'signTransactions':\n                        target[p] = function (_a) {\n                            var { transactions } = _a, rest = __rest(_a, [\"transactions\"]);\n                            return __awaiter(this, void 0, void 0, function* () {\n                                const payloads = transactions.map(getPayloadFromTransaction);\n                                const { signed_payloads: base64EncodedCompiledTransactions } = yield wallet.signTransactions(Object.assign(Object.assign({}, rest), { payloads }));\n                                const compiledTransactions = base64EncodedCompiledTransactions.map(toUint8Array);\n                                const signedTransactions = compiledTransactions.map(getTransactionFromWireMessage);\n                                return signedTransactions;\n                            });\n                        };\n                        break;\n                    default: {\n                        target[p] = wallet[p];\n                        break;\n                    }\n                }\n            }\n            return target[p];\n        },\n        defineProperty() {\n            return false;\n        },\n        deleteProperty() {\n            return false;\n        },\n    });\n}\n\nexport { startRemoteScenario, transact };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA;;;;;;;;;;;;;8EAa8E,GAE9E,SAAS,OAAO,CAAC,EAAE,CAAC;IAChB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACX;AAEA,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IAChD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AAEA,SAAS,eAAe,SAAS;IAC7B,OAAO,OAAO,IAAI,CAAC,OAAO,YAAY,CAAC,IAAI,CAAC,SAAS;AACzD;AACA,SAAS,aAAa,sBAAsB;IACxC,OAAO,IAAI,WAAW,OACjB,IAAI,CAAC,wBACL,KAAK,CAAC,IACN,GAAG,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC;AACjC;AAEA,SAAS,0BAA0B,WAAW;IAC1C,MAAM,wBAAwB,aAAa,cACrC,YAAY,SAAS,KACrB,YAAY,SAAS,CAAC;QACpB,sBAAsB;QACtB,kBAAkB;IACtB;IACJ,MAAM,UAAU,eAAe;IAC/B,OAAO;AACX;AACA,SAAS,8BAA8B,SAAS;IAC5C,MAAM,gBAAgB,SAAS,CAAC,EAAE;IAClC,MAAM,gBAAgB,gBAAgB,2KAAA,CAAA,4BAAyB,GAAG;IAClE,MAAM,UAAU,2KAAA,CAAA,mBAAgB,CAAC,yBAAyB,CAAC,UAAU,KAAK,CAAC,eAAe,UAAU,MAAM;IAC1G,IAAI,YAAY,UAAU;QACtB,OAAO,2KAAA,CAAA,cAAW,CAAC,IAAI,CAAC;IAC5B,OACK;QACD,OAAO,2KAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC;IAC5C;AACJ;AACA,SAAS,SAAS,QAAQ,EAAE,MAAM;IAC9B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,oBAAoB,CAAC;YACvB,OAAO,SAAS,iBAAiB;QACrC;QACA,OAAO,MAAM,CAAA,GAAA,uMAAA,CAAA,WAAU,AAAD,EAAE,mBAAmB;IAC/C;AACJ;AACA,SAAS,oBAAoB,MAAM;IAC/B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,MAAM,CAAA,GAAA,uMAAA,CAAA,sBAAqB,AAAD,EAAE;QACtE,MAAM,mBAAmB,OAAO,IAAI,CAAC,CAAC;YAClC,OAAO,iBAAiB;QAC5B;QACA,OAAO;YAAE,QAAQ;YAAkB;YAAO;QAAe;IAC7D;AACJ;AACA,SAAS,iBAAiB,MAAM;IAC5B,OAAO,IAAI,MAAM,CAAC,GAAG;QACjB,KAAI,MAAM,EAAE,CAAC;YACT,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM;gBACnB,OAAQ;oBACJ,KAAK;wBACD,MAAM,CAAC,EAAE,GAAG,SAAU,EAAE;4BACpB,IAAI,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,sCAAsC,EAAE,YAAY,EAAE,GAAG,IAAI,OAAO,OAAO,IAAI;gCAAC;gCAAkB;gCAAc;gCAAiB;gCAAc;gCAA0C;6BAAe;4BACrQ,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gCACnC,MAAM,WAAW,aAAa,GAAG,CAAC;gCAClC,MAAM,UAAU;oCACZ,kBAAkB;oCAClB,YAAY;oCACZ,gBAAgB;oCAChB,aAAa;oCACb,8CAA8C;gCAClD;gCACA,MAAM,EAAE,YAAY,uBAAuB,EAAE,GAAG,MAAM,OAAO,uBAAuB,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,OAAQ,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,UAAW,WAAW,QACvL;oCAAE,SAAS;gCAAQ,IACnB,OAAQ;oCAAE;gCAAS;gCACzB,MAAM,aAAa,wBAAwB,GAAG,CAAC,cAAc,GAAG,CAAC,gIAAA,CAAA,UAAI,CAAC,MAAM;gCAC5E,OAAO;4BACX;wBACJ;wBACA;oBACJ,KAAK;wBACD,MAAM,CAAC,EAAE,GAAG,SAAU,EAAE;4BACpB,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO,OAAO,IAAI;gCAAC;6BAAW;4BACrD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gCACnC,MAAM,wBAAwB,SAAS,GAAG,CAAC;gCAC3C,MAAM,EAAE,iBAAiB,2BAA2B,EAAE,GAAG,MAAM,OAAO,YAAY,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;oCAAE,UAAU;gCAAsB;gCAC5J,MAAM,iBAAiB,4BAA4B,GAAG,CAAC;gCACvD,OAAO;4BACX;wBACJ;wBACA;oBACJ,KAAK;wBACD,MAAM,CAAC,EAAE,GAAG,SAAU,EAAE;4BACpB,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,OAAO,OAAO,IAAI;gCAAC;6BAAe;4BAC7D,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gCACnC,MAAM,WAAW,aAAa,GAAG,CAAC;gCAClC,MAAM,EAAE,iBAAiB,iCAAiC,EAAE,GAAG,MAAM,OAAO,gBAAgB,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;oCAAE;gCAAS;gCAC/I,MAAM,uBAAuB,kCAAkC,GAAG,CAAC;gCACnE,MAAM,qBAAqB,qBAAqB,GAAG,CAAC;gCACpD,OAAO;4BACX;wBACJ;wBACA;oBACJ;wBAAS;4BACL,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;4BACrB;wBACJ;gBACJ;YACJ;YACA,OAAO,MAAM,CAAC,EAAE;QACpB;QACA;YACI,OAAO;QACX;QACA;YACI,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solana-mobile/wallet-standard-mobile/lib/esm/index.js"], "sourcesContent": ["import { SolanaSignAndSendTransaction, SolanaSignTransaction, SolanaSignMessage, SolanaSignIn } from '@solana/wallet-standard-features';\nimport { VersionedTransaction, PublicKey } from '@solana/web3.js';\nimport QRCode from 'qrcode';\nimport { StandardConnect, StandardDisconnect, StandardEvents } from '@wallet-standard/features';\nimport { SOLANA_MAINNET_CHAIN } from '@solana/wallet-standard-chains';\nimport { transact, startRemoteScenario } from '@solana-mobile/mobile-wallet-adapter-protocol-web3js';\nimport base58 from 'bs58';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __classPrivateFieldGet$1(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nfunction __classPrivateFieldSet$1(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\n\nvar _EmbeddedModal_instances, _EmbeddedModal_root, _EmbeddedModal_eventListeners, _EmbeddedModal_listenersAttached, _EmbeddedModal_injectHTML, _EmbeddedModal_attachEventListeners, _EmbeddedModal_removeEventListeners, _EmbeddedModal_handleKeyDown;\nconst modalHtml = `\n<div class=\"mobile-wallet-adapter-embedded-modal-container\" role=\"dialog\" aria-modal=\"true\" aria-labelledby=\"modal-title\">\n    <div data-modal-close style=\"position: absolute; width: 100%; height: 100%;\"></div>\n\t<div class=\"mobile-wallet-adapter-embedded-modal-card\">\n\t\t<div>\n\t\t\t<button data-modal-close class=\"mobile-wallet-adapter-embedded-modal-close\">\n\t\t\t\t<svg width=\"14\" height=\"14\">\n\t\t\t\t\t<path d=\"M 6.7125,8.3036995 1.9082,13.108199 c -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 C 0.1056,12.896899 0,12.631699 0,12.312499 c 0,-0.3192 0.1056,-0.5844 0.3167,-0.7958 L 5.1212,6.7124995 0.3167,1.9082 C 0.1056,1.6969 0,1.4317 0,1.1125 0,0.7933 0.1056,0.5281 0.3167,0.3167 0.5281,0.1056 0.7933,0 1.1125,0 1.4317,0 1.6969,0.1056 1.9082,0.3167 L 6.7125,5.1212 11.5167,0.3167 C 11.7281,0.1056 11.9933,0 12.3125,0 c 0.3192,0 0.5844,0.1056 0.7957,0.3167 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 L 8.3037001,6.7124995 13.1082,11.516699 c 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 z\" />\n\t\t\t\t</svg>\n\t\t\t</button>\n\t\t</div>\n\t\t<div class=\"mobile-wallet-adapter-embedded-modal-content\"></div>\n\t</div>\n</div>\n`;\nconst css$2 = `\n.mobile-wallet-adapter-embedded-modal-container {\n    display: flex; /* Use flexbox to center content */\n    justify-content: center; /* Center horizontally */\n    align-items: center; /* Center vertically */\n    position: fixed; /* Stay in place */\n    z-index: 1; /* Sit on top */\n    left: 0;\n    top: 0;\n    width: 100%; /* Full width */\n    height: 100%; /* Full height */\n    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */\n    overflow-y: auto; /* enable scrolling */\n}\n\n.mobile-wallet-adapter-embedded-modal-card {\n    display: flex;\n    flex-direction: column;\n    margin: auto 20px;\n    max-width: 780px;\n    padding: 20px;\n    border-radius: 24px;\n    background: #ffffff;\n    font-family: \"Inter Tight\", \"PT Sans\", Calibri, sans-serif;\n    transform: translateY(-200%);\n    animation: slide-in 0.5s forwards;\n}\n\n@keyframes slide-in {\n    100% { transform: translateY(0%); }\n}\n\n.mobile-wallet-adapter-embedded-modal-close {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 32px;\n    height: 32px;\n    cursor: pointer;\n    background: #e4e9e9;\n    border: none;\n    border-radius: 50%;\n}\n\n.mobile-wallet-adapter-embedded-modal-close:focus-visible {\n    outline-color: red;\n}\n\n.mobile-wallet-adapter-embedded-modal-close svg {\n    fill: #546266;\n    transition: fill 200ms ease 0s;\n}\n\n.mobile-wallet-adapter-embedded-modal-close:hover svg {\n    fill: #fff;\n}\n`;\nconst fonts = `\n<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n<link href=\"https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap\" rel=\"stylesheet\">\n`;\nclass EmbeddedModal {\n    constructor() {\n        _EmbeddedModal_instances.add(this);\n        _EmbeddedModal_root.set(this, null);\n        _EmbeddedModal_eventListeners.set(this, {});\n        _EmbeddedModal_listenersAttached.set(this, false);\n        this.dom = null;\n        this.open = () => {\n            console.debug('Modal open');\n            __classPrivateFieldGet$1(this, _EmbeddedModal_instances, \"m\", _EmbeddedModal_attachEventListeners).call(this);\n            if (__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\")) {\n                __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").style.display = 'flex';\n            }\n        };\n        this.close = (event = undefined) => {\n            var _a;\n            console.debug('Modal close');\n            __classPrivateFieldGet$1(this, _EmbeddedModal_instances, \"m\", _EmbeddedModal_removeEventListeners).call(this);\n            if (__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\")) {\n                __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").style.display = 'none';\n            }\n            (_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")['close']) === null || _a === void 0 ? void 0 : _a.forEach((listener) => listener(event));\n        };\n        _EmbeddedModal_handleKeyDown.set(this, (event) => {\n            if (event.key === 'Escape')\n                this.close(event);\n        });\n        // Bind methods to ensure `this` context is correct\n        this.init = this.init.bind(this);\n        __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.getElementById('mobile-wallet-adapter-embedded-root-ui'), \"f\");\n    }\n    init() {\n        return __awaiter(this, void 0, void 0, function* () {\n            console.log('Injecting modal');\n            __classPrivateFieldGet$1(this, _EmbeddedModal_instances, \"m\", _EmbeddedModal_injectHTML).call(this);\n        });\n    }\n    addEventListener(event, listener) {\n        var _a;\n        ((_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")[event] = [listener]);\n        return () => this.removeEventListener(event, listener);\n    }\n    removeEventListener(event, listener) {\n        var _a;\n        __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")[event] = (_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener) => listener !== existingListener);\n    }\n}\n_EmbeddedModal_root = new WeakMap(), _EmbeddedModal_eventListeners = new WeakMap(), _EmbeddedModal_listenersAttached = new WeakMap(), _EmbeddedModal_handleKeyDown = new WeakMap(), _EmbeddedModal_instances = new WeakSet(), _EmbeddedModal_injectHTML = function _EmbeddedModal_injectHTML() {\n    // Check if the HTML has already been injected\n    if (document.getElementById('mobile-wallet-adapter-embedded-root-ui')) {\n        if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\"))\n            __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.getElementById('mobile-wallet-adapter-embedded-root-ui'), \"f\");\n        return;\n    }\n    // Create a container for the modal\n    __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.createElement('div'), \"f\");\n    __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").id = 'mobile-wallet-adapter-embedded-root-ui';\n    __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").innerHTML = modalHtml;\n    __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").style.display = 'none';\n    // Add modal content\n    const content = __classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").querySelector('.mobile-wallet-adapter-embedded-modal-content');\n    if (content)\n        content.innerHTML = this.contentHtml;\n    // Apply styles\n    const styles = document.createElement('style');\n    styles.id = 'mobile-wallet-adapter-embedded-modal-styles';\n    styles.textContent = css$2 + this.contentStyles;\n    // Create a shadow DOM to encapsulate the modal\n    const host = document.createElement('div');\n    host.innerHTML = fonts;\n    this.dom = host.attachShadow({ mode: 'closed' });\n    this.dom.appendChild(styles);\n    this.dom.appendChild(__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\"));\n    // Append the shadow DOM host to the body\n    document.body.appendChild(host);\n}, _EmbeddedModal_attachEventListeners = function _EmbeddedModal_attachEventListeners() {\n    if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\") || __classPrivateFieldGet$1(this, _EmbeddedModal_listenersAttached, \"f\"))\n        return;\n    const closers = [...__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").querySelectorAll('[data-modal-close]')];\n    closers.forEach(closer => closer === null || closer === void 0 ? void 0 : closer.addEventListener('click', this.close));\n    window.addEventListener('load', this.close);\n    document.addEventListener('keydown', __classPrivateFieldGet$1(this, _EmbeddedModal_handleKeyDown, \"f\"));\n    __classPrivateFieldSet$1(this, _EmbeddedModal_listenersAttached, true, \"f\");\n}, _EmbeddedModal_removeEventListeners = function _EmbeddedModal_removeEventListeners() {\n    if (!__classPrivateFieldGet$1(this, _EmbeddedModal_listenersAttached, \"f\"))\n        return;\n    window.removeEventListener('load', this.close);\n    document.removeEventListener('keydown', __classPrivateFieldGet$1(this, _EmbeddedModal_handleKeyDown, \"f\"));\n    if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\"))\n        return;\n    const closers = [...__classPrivateFieldGet$1(this, _EmbeddedModal_root, \"f\").querySelectorAll('[data-modal-close]')];\n    closers.forEach(closer => closer === null || closer === void 0 ? void 0 : closer.removeEventListener('click', this.close));\n    __classPrivateFieldSet$1(this, _EmbeddedModal_listenersAttached, false, \"f\");\n};\n\nclass RemoteConnectionModal extends EmbeddedModal {\n    constructor() {\n        super(...arguments);\n        this.contentStyles = css$1;\n        this.contentHtml = QRCodeHtml;\n    }\n    initWithQR(qrCode) {\n        const _super = Object.create(null, {\n            init: { get: () => super.init }\n        });\n        return __awaiter(this, void 0, void 0, function* () {\n            _super.init.call(this);\n            this.populateQRCode(qrCode);\n        });\n    }\n    populateQRCode(qrUrl) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            const qrcodeContainer = (_a = this.dom) === null || _a === void 0 ? void 0 : _a.getElementById('mobile-wallet-adapter-embedded-modal-qr-code-container');\n            if (qrcodeContainer) {\n                const qrCodeElement = yield QRCode.toCanvas(qrUrl, { width: 200, margin: 0 });\n                if (qrcodeContainer.firstElementChild !== null) {\n                    qrcodeContainer.replaceChild(qrCodeElement, qrcodeContainer.firstElementChild);\n                }\n                else\n                    qrcodeContainer.appendChild(qrCodeElement);\n            }\n            else {\n                console.error('QRCode Container not found');\n            }\n        });\n    }\n}\nconst QRCodeHtml = `\n<div class=\"mobile-wallet-adapter-embedded-modal-qr-content\">\n    <div>\n        <svg class=\"mobile-wallet-adapter-embedded-modal-icon\" width=\"100%\" height=\"100%\">\n            <circle r=\"52\" cx=\"53\" cy=\"53\" fill=\"#99b3be\" stroke=\"#000000\" stroke-width=\"2\"/>\n            <path d=\"m 53,82.7305 c -3.3116,0 -6.1361,-1.169 -8.4735,-3.507 -2.338,-2.338 -3.507,-5.1625 -3.507,-8.4735 0,-3.3116 1.169,-6.1364 3.507,-8.4744 2.3374,-2.338 5.1619,-3.507 8.4735,-3.507 3.3116,0 6.1361,1.169 8.4735,3.507 2.338,2.338 3.507,5.1628 3.507,8.4744 0,3.311 -1.169,6.1355 -3.507,8.4735 -2.3374,2.338 -5.1619,3.507 -8.4735,3.507 z m 0.007,-5.25 c 1.8532,0 3.437,-0.6598 4.7512,-1.9793 1.3149,-1.3195 1.9723,-2.9058 1.9723,-4.7591 0,-1.8526 -0.6598,-3.4364 -1.9793,-4.7512 -1.3195,-1.3149 -2.9055,-1.9723 -4.7582,-1.9723 -1.8533,0 -3.437,0.6598 -4.7513,1.9793 -1.3148,1.3195 -1.9722,2.9058 -1.9722,4.7591 0,1.8527 0.6597,3.4364 1.9792,4.7512 1.3195,1.3149 2.9056,1.9723 4.7583,1.9723 z m -28,-33.5729 -3.85,-3.6347 c 4.1195,-4.025 8.8792,-7.1984 14.2791,-9.52 5.4005,-2.3223 11.2551,-3.4834 17.5639,-3.4834 6.3087,0 12.1634,1.1611 17.5639,3.4834 5.3999,2.3216 10.1596,5.495 14.2791,9.52 l -3.85,3.6347 C 77.2999,40.358 73.0684,37.5726 68.2985,35.5514 63.5292,33.5301 58.4296,32.5195 53,32.5195 c -5.4297,0 -10.5292,1.0106 -15.2985,3.0319 -4.7699,2.0212 -9.0014,4.8066 -12.6945,8.3562 z m 44.625,10.8771 c -2.2709,-2.1046 -4.7962,-3.7167 -7.5758,-4.8361 -2.7795,-1.12 -5.7983,-1.68 -9.0562,-1.68 -3.2579,0 -6.2621,0.56 -9.0125,1.68 -2.7504,1.1194 -5.2903,2.7315 -7.6195,4.8361 L 32.5189,51.15 c 2.8355,-2.6028 5.9777,-4.6086 9.4263,-6.0174 3.4481,-1.4087 7.133,-2.1131 11.0548,-2.1131 3.9217,0 7.5979,0.7044 11.0285,2.1131 3.43,1.4088 6.5631,3.4146 9.3992,6.0174 z\"/>\n        </svg>\n        <div class=\"mobile-wallet-adapter-embedded-modal-title\">Remote Mobile Wallet Adapter</div>\n    </div>\n    <div>\n        <div>\n            <h4 class=\"mobile-wallet-adapter-embedded-modal-qr-label\">\n                Open your wallet and scan this code\n            </h4>\n        </div>\n        <div id=\"mobile-wallet-adapter-embedded-modal-qr-code-container\" class=\"mobile-wallet-adapter-embedded-modal-qr-code-container\"></div>\n    </div>\n</div>\n<div class=\"mobile-wallet-adapter-embedded-modal-divider\"><hr></div>\n<div class=\"mobile-wallet-adapter-embedded-modal-footer\">\n    <div class=\"mobile-wallet-adapter-embedded-modal-subtitle\">\n        Follow the instructions on your device. When you're finished, this screen will update.\n    </div>\n    <div class=\"mobile-wallet-adapter-embedded-modal-progress-badge\">\n        <div>\n            <div class=\"spinner\">\n                <div class=\"leftWrapper\">\n                    <div class=\"left\">\n                        <div class=\"circle\"></div>\n                    </div>\n                </div>\n                <div class=\"rightWrapper\">\n                    <div class=\"right\">\n                        <div class=\"circle\"></div>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div>Waiting for scan</div>\n    </div>\n</div>\n`;\nconst css$1 = `\n.mobile-wallet-adapter-embedded-modal-qr-content {\n    display: flex; \n    margin-top: 10px;\n    padding: 10px;\n}\n\n.mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {\n    display: flex;\n    flex-direction: column;\n    flex: 2;\n    margin-top: auto;\n    margin-right: 30px;\n}\n\n.mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n    margin-left: auto;\n}\n\n.mobile-wallet-adapter-embedded-modal-footer {\n    display: flex;\n    padding: 10px;\n}\n\n.mobile-wallet-adapter-embedded-modal-icon {}\n\n.mobile-wallet-adapter-embedded-modal-title {\n    color: #000000;\n    font-size: 2.5em;\n    font-weight: 600;\n}\n\n.mobile-wallet-adapter-embedded-modal-qr-label {\n    text-align: right;\n    color: #000000;\n}\n\n.mobile-wallet-adapter-embedded-modal-qr-code-container {\n    margin-left: auto;\n}\n\n.mobile-wallet-adapter-embedded-modal-divider {\n    margin-top: 20px;\n    padding-left: 10px;\n    padding-right: 10px;\n}\n\n.mobile-wallet-adapter-embedded-modal-divider hr {\n    border-top: 1px solid #D9DEDE;\n}\n\n.mobile-wallet-adapter-embedded-modal-subtitle {\n    margin: auto;\n    margin-right: 60px;\n    padding: 20px;\n    color: #6E8286;\n}\n\n.mobile-wallet-adapter-embedded-modal-progress-badge {\n    display: flex;\n    background: #F7F8F8;\n    height: 56px;\n    min-width: 200px;\n    margin: auto;\n    padding-left: 20px;\n    padding-right: 20px;\n    border-radius: 18px;\n    color: #A8B6B8;\n    align-items: center;\n}\n\n.mobile-wallet-adapter-embedded-modal-progress-badge > div:first-child {\n    margin-left: auto;\n    margin-right: 20px;\n}\n\n.mobile-wallet-adapter-embedded-modal-progress-badge > div:nth-child(2) {\n    margin-right: auto;\n}\n\n/* Smaller screens */\n@media all and (max-width: 600px) {\n    .mobile-wallet-adapter-embedded-modal-card {\n        text-align: center;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-content {\n        flex-direction: column;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {\n        margin: auto;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {\n        margin: auto;\n        flex: 2 auto;\n    }\n    .mobile-wallet-adapter-embedded-modal-footer {\n        flex-direction: column;\n    }\n    .mobile-wallet-adapter-embedded-modal-icon {\n        display: none;\n    }\n    .mobile-wallet-adapter-embedded-modal-title {\n        font-size: 1.5em;\n    }\n    .mobile-wallet-adapter-embedded-modal-subtitle {\n        margin-right: unset;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-label {\n        text-align: center;\n    }\n    .mobile-wallet-adapter-embedded-modal-qr-code-container {\n        margin: auto;\n    }\n}\n\n/* Spinner */\n@keyframes spinLeft {\n    0% {\n        transform: rotate(20deg);\n    }\n    50% {\n        transform: rotate(160deg);\n    }\n    100% {\n        transform: rotate(20deg);\n    }\n}\n@keyframes spinRight {\n    0% {\n        transform: rotate(160deg);\n    }\n    50% {\n        transform: rotate(20deg);\n    }\n    100% {\n        transform: rotate(160deg);\n    }\n}\n@keyframes spin {\n    0% {\n        transform: rotate(0deg);\n    }\n    100% {\n        transform: rotate(2520deg);\n    }\n}\n\n.spinner {\n    position: relative;\n    width: 1.5em;\n    height: 1.5em;\n    margin: auto;\n    animation: spin 10s linear infinite;\n}\n.spinner::before {\n    content: \"\";\n    position: absolute;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    right: 0;\n}\n.right, .rightWrapper, .left, .leftWrapper {\n    position: absolute;\n    top: 0;\n    overflow: hidden;\n    width: .75em;\n    height: 1.5em;\n}\n.left, .leftWrapper {\n    left: 0;\n}\n.right {\n    left: -12px;\n}\n.rightWrapper {\n    right: 0;\n}\n.circle {\n    border: .125em solid #A8B6B8;\n    width: 1.25em; /* 1.5em - 2*0.125em border */\n    height: 1.25em; /* 1.5em - 2*0.125em border */\n    border-radius: 0.75em; /* 0.5*1.5em spinner size 8 */\n}\n.left {\n    transform-origin: 100% 50%;\n    animation: spinLeft 2.5s cubic-bezier(.2,0,.8,1) infinite;\n}\n.right {\n    transform-origin: 100% 50%;\n    animation: spinRight 2.5s cubic-bezier(.2,0,.8,1) infinite;\n}\n`;\n\nconst icon = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03IDIuNUgxN0MxNy44Mjg0IDIuNSAxOC41IDMuMTcxNTcgMTguNSA0VjIwQzE4LjUgMjAuODI4NCAxNy44Mjg0IDIxLjUgMTcgMjEuNUg3QzYuMTcxNTcgMjEuNSA1LjUgMjAuODI4NCA1LjUgMjBWNEM1LjUgMy4xNzE1NyA2LjE3MTU3IDIuNSA3IDIuNVpNMyA0QzMgMS43OTA4NiA0Ljc5MDg2IDAgNyAwSDE3QzE5LjIwOTEgMCAyMSAxLjc5MDg2IDIxIDRWMjBDMjEgMjIuMjA5MSAxOS4yMDkxIDI0IDE3IDI0SDdDNC43OTA4NiAyNCAzIDIyLjIwOTEgMyAyMFY0Wk0xMSA0LjYxNTM4QzEwLjQ0NzcgNC42MTUzOCAxMCA1LjA2MzEgMTAgNS42MTUzOFY2LjM4NDYyQzEwIDYuOTM2OSAxMC40NDc3IDcuMzg0NjIgMTEgNy4zODQ2MkgxM0MxMy41NTIzIDcuMzg0NjIgMTQgNi45MzY5IDE0IDYuMzg0NjJWNS42MTUzOEMxNCA1LjA2MzEgMTMuNTUyMyA0LjYxNTM4IDEzIDQuNjE1MzhIMTFaIiBmaWxsPSIjRENCOEZGIi8+Cjwvc3ZnPgo=';\n\nfunction isVersionedTransaction(transaction) {\n    return 'version' in transaction;\n}\n\nfunction fromUint8Array(byteArray) {\n    return window.btoa(String.fromCharCode.call(null, ...byteArray));\n}\nfunction toUint8Array(base64EncodedByteArray) {\n    return new Uint8Array(window\n        .atob(base64EncodedByteArray)\n        .split('')\n        .map((c) => c.charCodeAt(0)));\n}\n\nvar _LocalSolanaMobileWalletAdapterWallet_instances, _LocalSolanaMobileWalletAdapterWallet_listeners, _LocalSolanaMobileWalletAdapterWallet_version, _LocalSolanaMobileWalletAdapterWallet_name, _LocalSolanaMobileWalletAdapterWallet_url, _LocalSolanaMobileWalletAdapterWallet_icon, _LocalSolanaMobileWalletAdapterWallet_appIdentity, _LocalSolanaMobileWalletAdapterWallet_authorization, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, _LocalSolanaMobileWalletAdapterWallet_connecting, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, _LocalSolanaMobileWalletAdapterWallet_chains, _LocalSolanaMobileWalletAdapterWallet_chainSelector, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, _LocalSolanaMobileWalletAdapterWallet_on, _LocalSolanaMobileWalletAdapterWallet_emit, _LocalSolanaMobileWalletAdapterWallet_off, _LocalSolanaMobileWalletAdapterWallet_connect, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, _LocalSolanaMobileWalletAdapterWallet_disconnect, _LocalSolanaMobileWalletAdapterWallet_transact, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, _LocalSolanaMobileWalletAdapterWallet_performSignTransactions, _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, _LocalSolanaMobileWalletAdapterWallet_signTransaction, _LocalSolanaMobileWalletAdapterWallet_signMessage, _LocalSolanaMobileWalletAdapterWallet_signIn, _LocalSolanaMobileWalletAdapterWallet_performSignIn, _RemoteSolanaMobileWalletAdapterWallet_instances, _RemoteSolanaMobileWalletAdapterWallet_listeners, _RemoteSolanaMobileWalletAdapterWallet_version, _RemoteSolanaMobileWalletAdapterWallet_name, _RemoteSolanaMobileWalletAdapterWallet_url, _RemoteSolanaMobileWalletAdapterWallet_icon, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, _RemoteSolanaMobileWalletAdapterWallet_authorization, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, _RemoteSolanaMobileWalletAdapterWallet_connecting, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, _RemoteSolanaMobileWalletAdapterWallet_chains, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, _RemoteSolanaMobileWalletAdapterWallet_session, _RemoteSolanaMobileWalletAdapterWallet_on, _RemoteSolanaMobileWalletAdapterWallet_emit, _RemoteSolanaMobileWalletAdapterWallet_off, _RemoteSolanaMobileWalletAdapterWallet_connect, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, _RemoteSolanaMobileWalletAdapterWallet_disconnect, _RemoteSolanaMobileWalletAdapterWallet_transact, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions, _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, _RemoteSolanaMobileWalletAdapterWallet_signTransaction, _RemoteSolanaMobileWalletAdapterWallet_signMessage, _RemoteSolanaMobileWalletAdapterWallet_signIn, _RemoteSolanaMobileWalletAdapterWallet_performSignIn;\nconst SolanaMobileWalletAdapterWalletName = 'Mobile Wallet Adapter';\nconst SIGNATURE_LENGTH_IN_BYTES = 64;\nconst DEFAULT_FEATURES = [SolanaSignAndSendTransaction, SolanaSignTransaction, SolanaSignMessage, SolanaSignIn];\nclass LocalSolanaMobileWalletAdapterWallet {\n    constructor(config) {\n        _LocalSolanaMobileWalletAdapterWallet_instances.add(this);\n        _LocalSolanaMobileWalletAdapterWallet_listeners.set(this, {});\n        _LocalSolanaMobileWalletAdapterWallet_version.set(this, '1.0.0'); // wallet-standard version\n        _LocalSolanaMobileWalletAdapterWallet_name.set(this, SolanaMobileWalletAdapterWalletName);\n        _LocalSolanaMobileWalletAdapterWallet_url.set(this, 'https://solanamobile.com/wallets');\n        _LocalSolanaMobileWalletAdapterWallet_icon.set(this, icon);\n        _LocalSolanaMobileWalletAdapterWallet_appIdentity.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_authorization.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_authorizationCache.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_connecting.set(this, false);\n        /**\n         * Every time the connection is recycled in some way (eg. `disconnect()` is called)\n         * increment this and use it to make sure that `transact` calls from the previous\n         * 'generation' don't continue to do work and throw exceptions.\n         */\n        _LocalSolanaMobileWalletAdapterWallet_connectionGeneration.set(this, 0);\n        _LocalSolanaMobileWalletAdapterWallet_chains.set(this, []);\n        _LocalSolanaMobileWalletAdapterWallet_chainSelector.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_optionalFeatures.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound.set(this, void 0);\n        _LocalSolanaMobileWalletAdapterWallet_on.set(this, (event, listener) => {\n            var _a;\n            ((_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event] = [listener]);\n            return () => __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, \"m\", _LocalSolanaMobileWalletAdapterWallet_off).call(this, event, listener);\n        });\n        _LocalSolanaMobileWalletAdapterWallet_connect.set(this, ({ silent } = {}) => __awaiter(this, void 0, void 0, function* () {\n            if (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, \"f\") || this.connected) {\n                return { accounts: this.accounts };\n            }\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, true, \"f\");\n            try {\n                if (silent) {\n                    const cachedAuthorization = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n                    if (cachedAuthorization) {\n                        yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, cachedAuthorization);\n                    }\n                    else {\n                        return { accounts: this.accounts };\n                    }\n                }\n                else {\n                    yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, \"f\").call(this);\n                }\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n            finally {\n                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            }\n            return { accounts: this.accounts };\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_performAuthorization.set(this, (signInPayload) => __awaiter(this, void 0, void 0, function* () {\n            try {\n                const cachedAuthorizationResult = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n                if (cachedAuthorizationResult) {\n                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, cachedAuthorizationResult);\n                    return cachedAuthorizationResult;\n                }\n                const selectedChain = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chainSelector, \"f\").select(__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, \"f\"));\n                return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    const [capabilities, mwaAuthorizationResult] = yield Promise.all([\n                        wallet.getCapabilities(),\n                        wallet.authorize({\n                            chain: selectedChain,\n                            identity: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, \"f\"),\n                            sign_in_payload: signInPayload,\n                        })\n                    ]);\n                    const accounts = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, \"f\").call(this, mwaAuthorizationResult.accounts);\n                    const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts, chain: selectedChain });\n                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                    Promise.all([\n                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, \"f\").call(this, capabilities),\n                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").set(authorization),\n                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, authorization),\n                    ]);\n                    return authorization;\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult.set(this, (authorization) => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const didPublicKeysChange = \n            // Case 1: We started from having no authorization.\n            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\") == null ||\n                // Case 2: The number of authorized accounts changed.\n                ((_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _a === void 0 ? void 0 : _a.accounts.length) !== authorization.accounts.length ||\n                // Case 3: The new list of addresses isn't exactly the same as the old list, in the same order.\n                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\").accounts.some((account, ii) => account.address !== authorization.accounts[ii].address);\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, authorization, \"f\");\n            if (didPublicKeysChange) {\n                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, \"m\", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { accounts: this.accounts });\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult.set(this, (capabilities) => __awaiter(this, void 0, void 0, function* () {\n            // TODO: investigate why using SolanaSignTransactions constant breaks treeshaking\n            const supportsSignTransaction = capabilities.features.includes('solana:signTransactions'); //SolanaSignTransactions);\n            const supportsSignAndSendTransaction = capabilities.supports_sign_and_send_transactions;\n            const didCapabilitiesChange = SolanaSignAndSendTransaction in this.features !== supportsSignAndSendTransaction ||\n                SolanaSignTransaction in this.features !== supportsSignTransaction;\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, Object.assign(Object.assign({}, ((supportsSignAndSendTransaction || (!supportsSignAndSendTransaction && !supportsSignTransaction)) && {\n                [SolanaSignAndSendTransaction]: {\n                    version: '1.0.0',\n                    supportedTransactionVersions: ['legacy', 0],\n                    signAndSendTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, \"f\"),\n                },\n            })), (supportsSignTransaction && {\n                [SolanaSignTransaction]: {\n                    version: '1.0.0',\n                    supportedTransactionVersions: ['legacy', 0],\n                    signTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signTransaction, \"f\"),\n                },\n            })), \"f\");\n            if (didCapabilitiesChange) {\n                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, \"m\", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { features: this.features });\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_performReauthorization.set(this, (wallet, authToken, chain) => __awaiter(this, void 0, void 0, function* () {\n            try {\n                const mwaAuthorizationResult = yield wallet.authorize({\n                    auth_token: authToken,\n                    identity: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, \"f\"),\n                    chain: chain\n                });\n                const accounts = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, \"f\").call(this, mwaAuthorizationResult.accounts);\n                const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts: accounts, chain: chain });\n                // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                Promise.all([\n                    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").set(authorization),\n                    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, authorization),\n                ]);\n            }\n            catch (e) {\n                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_disconnect, \"f\").call(this);\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_disconnect.set(this, () => __awaiter(this, void 0, void 0, function* () {\n            var _b;\n            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").clear(); // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, (_b = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\"), _b++, _b), \"f\");\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, undefined, \"f\");\n            __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, \"m\", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { accounts: this.accounts });\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_transact.set(this, (callback) => __awaiter(this, void 0, void 0, function* () {\n            var _c;\n            const walletUriBase = (_c = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _c === void 0 ? void 0 : _c.wallet_uri_base;\n            const config = walletUriBase ? { baseUri: walletUriBase } : undefined;\n            const currentConnectionGeneration = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\");\n            try {\n                return yield transact(callback, config);\n            }\n            catch (e) {\n                if (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\") !== currentConnectionGeneration) {\n                    yield new Promise(() => { }); // Never resolve.\n                }\n                if (e instanceof Error &&\n                    e.name === 'SolanaMobileWalletAdapterError' &&\n                    e.code === 'ERROR_WALLET_NOT_FOUND') {\n                    yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, \"f\").call(this, this);\n                }\n                throw e;\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized.set(this, () => {\n            if (!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\"))\n                throw new Error('Wallet not connected');\n            return { authToken: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\").auth_token, chain: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\").chain };\n        });\n        _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts.set(this, (accounts) => {\n            return accounts.map((account) => {\n                var _a, _b;\n                const publicKey = toUint8Array(account.address);\n                return {\n                    address: base58.encode(publicKey),\n                    publicKey,\n                    label: account.label,\n                    icon: account.icon,\n                    chains: (_a = account.chains) !== null && _a !== void 0 ? _a : __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, \"f\"),\n                    // TODO: get supported features from getCapabilities API \n                    features: (_b = account.features) !== null && _b !== void 0 ? _b : DEFAULT_FEATURES\n                };\n            });\n        });\n        _LocalSolanaMobileWalletAdapterWallet_performSignTransactions.set(this, (transactions) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            try {\n                return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain);\n                    const signedTransactions = yield wallet.signTransactions({\n                        transactions,\n                    });\n                    return signedTransactions;\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction.set(this, (transaction, options) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            try {\n                return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    const [capabilities, _1] = yield Promise.all([\n                        wallet.getCapabilities(),\n                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain)\n                    ]);\n                    if (capabilities.supports_sign_and_send_transactions) {\n                        const signatures = yield wallet.signAndSendTransactions(Object.assign(Object.assign({}, options), { transactions: [transaction] }));\n                        return signatures[0];\n                    }\n                    else {\n                        throw new Error('connected wallet does not support signAndSendTransaction');\n                    }\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const outputs = [];\n            for (const input of inputs) {\n                const transaction = VersionedTransaction.deserialize(input.transaction);\n                const signature = (yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, \"f\").call(this, transaction, input.options));\n                outputs.push({ signature: base58.decode(signature) });\n            }\n            return outputs;\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_signTransaction.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const transactions = inputs.map(({ transaction }) => VersionedTransaction.deserialize(transaction));\n            const signedTransactions = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignTransactions, \"f\").call(this, transactions);\n            return signedTransactions.map((signedTransaction) => {\n                const serializedTransaction = isVersionedTransaction(signedTransaction)\n                    ? signedTransaction.serialize()\n                    : new Uint8Array(signedTransaction.serialize({\n                        requireAllSignatures: false,\n                        verifySignatures: false,\n                    }));\n                return { signedTransaction: serializedTransaction };\n            });\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_signMessage.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            const addresses = inputs.map(({ account }) => fromUint8Array(account.publicKey));\n            const messages = inputs.map(({ message }) => message);\n            try {\n                return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain);\n                    const signedMessages = yield wallet.signMessages({\n                        addresses: addresses,\n                        payloads: messages,\n                    });\n                    return signedMessages.map((signedMessage) => {\n                        return { signedMessage: signedMessage, signature: signedMessage.slice(-SIGNATURE_LENGTH_IN_BYTES) };\n                    });\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_signIn.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const outputs = [];\n            if (inputs.length > 1) {\n                for (const input of inputs) {\n                    outputs.push(yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignIn, \"f\").call(this, input));\n                }\n            }\n            else {\n                return [yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignIn, \"f\").call(this, inputs[0])];\n            }\n            return outputs;\n        }));\n        _LocalSolanaMobileWalletAdapterWallet_performSignIn.set(this, (input) => __awaiter(this, void 0, void 0, function* () {\n            var _d, _e;\n            __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, true, \"f\");\n            try {\n                const authorizationResult = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, \"f\").call(this, Object.assign(Object.assign({}, input), { domain: (_d = input === null || input === void 0 ? void 0 : input.domain) !== null && _d !== void 0 ? _d : window.location.host }));\n                if (!authorizationResult.sign_in_result) {\n                    throw new Error(\"Sign in failed, no sign in result returned by wallet\");\n                }\n                const signedInAddress = authorizationResult.sign_in_result.address;\n                const signedInAccount = Object.assign(Object.assign({}, (_e = authorizationResult.accounts.find(acc => acc.address == signedInAddress)) !== null && _e !== void 0 ? _e : {\n                    address: signedInAddress\n                }), { publicKey: toUint8Array(signedInAddress) });\n                return {\n                    account: signedInAccount,\n                    signedMessage: toUint8Array(authorizationResult.sign_in_result.signed_message),\n                    signature: toUint8Array(authorizationResult.sign_in_result.signature)\n                };\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n            finally {\n                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            }\n        }));\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, config.authorizationCache, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, config.appIdentity, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, config.chains, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_chainSelector, config.chainSelector, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, config.onWalletNotFound, \"f\");\n        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, {\n            // We are forced to provide either SolanaSignAndSendTransaction or SolanaSignTransaction\n            // because the wallet-adapter compatible wallet-standard wallet requires at least one of them.\n            // MWA 2.0+ wallets must implement signAndSend and pre 2.0 wallets have always provided it so \n            // this is a safe assumption. We later update the features after we get the wallets capabilities. \n            [SolanaSignAndSendTransaction]: {\n                version: '1.0.0',\n                supportedTransactionVersions: ['legacy', 0],\n                signAndSendTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, \"f\"),\n            },\n        }, \"f\");\n    }\n    get version() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_version, \"f\");\n    }\n    get name() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_name, \"f\");\n    }\n    get url() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_url, \"f\");\n    }\n    get icon() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_icon, \"f\");\n    }\n    get chains() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, \"f\");\n    }\n    get features() {\n        return Object.assign({ [StandardConnect]: {\n                version: '1.0.0',\n                connect: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connect, \"f\"),\n            }, [StandardDisconnect]: {\n                version: '1.0.0',\n                disconnect: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_disconnect, \"f\"),\n            }, [StandardEvents]: {\n                version: '1.0.0',\n                on: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_on, \"f\"),\n            }, [SolanaSignMessage]: {\n                version: '1.0.0',\n                signMessage: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signMessage, \"f\"),\n            }, [SolanaSignIn]: {\n                version: '1.0.0',\n                signIn: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signIn, \"f\"),\n            } }, __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, \"f\"));\n    }\n    get accounts() {\n        var _a, _b;\n        return (_b = (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _a === void 0 ? void 0 : _a.accounts) !== null && _b !== void 0 ? _b : [];\n    }\n    get connected() {\n        return !!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get isAuthorized() {\n        return !!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get currentAuthorization() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get cachedAuthorizationResult() {\n        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n    }\n}\n_LocalSolanaMobileWalletAdapterWallet_listeners = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_version = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_name = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_url = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_icon = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_appIdentity = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_authorization = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_authorizationCache = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connecting = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connectionGeneration = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_chains = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_chainSelector = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_optionalFeatures = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_on = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connect = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performAuthorization = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performReauthorization = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_disconnect = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_transact = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignTransactions = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signTransaction = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signMessage = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signIn = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignIn = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_instances = new WeakSet(), _LocalSolanaMobileWalletAdapterWallet_emit = function _LocalSolanaMobileWalletAdapterWallet_emit(event, ...args) {\n    var _a;\n    // eslint-disable-next-line prefer-spread\n    (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.forEach((listener) => listener.apply(null, args));\n}, _LocalSolanaMobileWalletAdapterWallet_off = function _LocalSolanaMobileWalletAdapterWallet_off(event, listener) {\n    var _a;\n    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event] = (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener) => listener !== existingListener);\n};\nclass RemoteSolanaMobileWalletAdapterWallet {\n    constructor(config) {\n        _RemoteSolanaMobileWalletAdapterWallet_instances.add(this);\n        _RemoteSolanaMobileWalletAdapterWallet_listeners.set(this, {});\n        _RemoteSolanaMobileWalletAdapterWallet_version.set(this, '1.0.0'); // wallet-standard version\n        _RemoteSolanaMobileWalletAdapterWallet_name.set(this, SolanaMobileWalletAdapterWalletName);\n        _RemoteSolanaMobileWalletAdapterWallet_url.set(this, 'https://solanamobile.com/wallets');\n        _RemoteSolanaMobileWalletAdapterWallet_icon.set(this, icon);\n        _RemoteSolanaMobileWalletAdapterWallet_appIdentity.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_authorization.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_authorizationCache.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_connecting.set(this, false);\n        /**\n         * Every time the connection is recycled in some way (eg. `disconnect()` is called)\n         * increment this and use it to make sure that `transact` calls from the previous\n         * 'generation' don't continue to do work and throw exceptions.\n         */\n        _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration.set(this, 0);\n        _RemoteSolanaMobileWalletAdapterWallet_chains.set(this, []);\n        _RemoteSolanaMobileWalletAdapterWallet_chainSelector.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_hostAuthority.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_session.set(this, void 0);\n        _RemoteSolanaMobileWalletAdapterWallet_on.set(this, (event, listener) => {\n            var _a;\n            ((_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event] = [listener]);\n            return () => __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, \"m\", _RemoteSolanaMobileWalletAdapterWallet_off).call(this, event, listener);\n        });\n        _RemoteSolanaMobileWalletAdapterWallet_connect.set(this, ({ silent } = {}) => __awaiter(this, void 0, void 0, function* () {\n            if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, \"f\") || this.connected) {\n                return { accounts: this.accounts };\n            }\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, true, \"f\");\n            try {\n                yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, \"f\").call(this);\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n            finally {\n                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            }\n            return { accounts: this.accounts };\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_performAuthorization.set(this, (signInPayload) => __awaiter(this, void 0, void 0, function* () {\n            try {\n                const cachedAuthorizationResult = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n                if (cachedAuthorizationResult) {\n                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, cachedAuthorizationResult);\n                    return cachedAuthorizationResult;\n                }\n                if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\"))\n                    __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, undefined, \"f\");\n                const selectedChain = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, \"f\").select(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, \"f\"));\n                return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    const [capabilities, mwaAuthorizationResult] = yield Promise.all([\n                        wallet.getCapabilities(),\n                        wallet.authorize({\n                            chain: selectedChain,\n                            identity: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, \"f\"),\n                            sign_in_payload: signInPayload,\n                        })\n                    ]);\n                    const accounts = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, \"f\").call(this, mwaAuthorizationResult.accounts);\n                    const authorizationResult = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts, chain: selectedChain });\n                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                    Promise.all([\n                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, \"f\").call(this, capabilities),\n                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").set(authorizationResult),\n                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, authorizationResult),\n                    ]);\n                    return authorizationResult;\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult.set(this, (authorization) => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const didPublicKeysChange = \n            // Case 1: We started from having no authorization.\n            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\") == null ||\n                // Case 2: The number of authorized accounts changed.\n                ((_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _a === void 0 ? void 0 : _a.accounts.length) !== authorization.accounts.length ||\n                // Case 3: The new list of addresses isn't exactly the same as the old list, in the same order.\n                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\").accounts.some((account, ii) => account.address !== authorization.accounts[ii].address);\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, authorization, \"f\");\n            if (didPublicKeysChange) {\n                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, \"m\", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { accounts: this.accounts });\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult.set(this, (capabilities) => __awaiter(this, void 0, void 0, function* () {\n            // TODO: investigate why using SolanaSignTransactions constant breaks treeshaking\n            const supportsSignTransaction = capabilities.features.includes('solana:signTransactions'); //SolanaSignTransactions);\n            const supportsSignAndSendTransaction = capabilities.supports_sign_and_send_transactions ||\n                capabilities.features.includes('solana:signAndSendTransaction');\n            const didCapabilitiesChange = SolanaSignAndSendTransaction in this.features !== supportsSignAndSendTransaction ||\n                SolanaSignTransaction in this.features !== supportsSignTransaction;\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, Object.assign(Object.assign({}, (supportsSignAndSendTransaction && {\n                [SolanaSignAndSendTransaction]: {\n                    version: '1.0.0',\n                    supportedTransactionVersions: capabilities.supported_transaction_versions,\n                    signAndSendTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, \"f\"),\n                },\n            })), (supportsSignTransaction && {\n                [SolanaSignTransaction]: {\n                    version: '1.0.0',\n                    supportedTransactionVersions: capabilities.supported_transaction_versions,\n                    signTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signTransaction, \"f\"),\n                },\n            })), \"f\");\n            if (didCapabilitiesChange) {\n                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, \"m\", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { features: this.features });\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_performReauthorization.set(this, (wallet, authToken, chain) => __awaiter(this, void 0, void 0, function* () {\n            try {\n                const mwaAuthorizationResult = yield wallet.authorize({\n                    auth_token: authToken,\n                    identity: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, \"f\"),\n                });\n                const accounts = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, \"f\").call(this, mwaAuthorizationResult.accounts);\n                const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), { accounts: accounts, chain: chain });\n                // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n                Promise.all([\n                    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").set(authorization),\n                    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, \"f\").call(this, authorization),\n                ]);\n            }\n            catch (e) {\n                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_disconnect, \"f\").call(this);\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_disconnect.set(this, () => __awaiter(this, void 0, void 0, function* () {\n            var _b;\n            var _c;\n            (_b = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\")) === null || _b === void 0 ? void 0 : _b.close();\n            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").clear(); // TODO: Evaluate whether there's any threat to not `awaiting` this expression\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, (_c = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\"), _c++, _c), \"f\");\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, undefined, \"f\");\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, undefined, \"f\");\n            __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, \"m\", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, 'change', { accounts: this.accounts });\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_transact.set(this, (callback) => __awaiter(this, void 0, void 0, function* () {\n            var _d;\n            const walletUriBase = (_d = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _d === void 0 ? void 0 : _d.wallet_uri_base;\n            const baseConfig = walletUriBase ? { baseUri: walletUriBase } : undefined;\n            const remoteConfig = Object.assign(Object.assign({}, baseConfig), { remoteHostAuthority: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, \"f\") });\n            const currentConnectionGeneration = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\");\n            const modal = new RemoteConnectionModal();\n            if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\")) {\n                return callback(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\").wallet);\n            }\n            try {\n                const { associationUrl, close, wallet } = yield startRemoteScenario(remoteConfig);\n                const removeCloseListener = modal.addEventListener('close', (event) => {\n                    if (event)\n                        close();\n                });\n                modal.initWithQR(associationUrl.toString());\n                modal.open();\n                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, { close, wallet: yield wallet }, \"f\");\n                removeCloseListener();\n                modal.close();\n                return yield callback(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\").wallet);\n            }\n            catch (e) {\n                modal.close();\n                if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, \"f\") !== currentConnectionGeneration) {\n                    yield new Promise(() => { }); // Never resolve.\n                }\n                if (e instanceof Error &&\n                    e.name === 'SolanaMobileWalletAdapterError' &&\n                    e.code === 'ERROR_WALLET_NOT_FOUND') {\n                    yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, \"f\").call(this, this);\n                }\n                throw e;\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized.set(this, () => {\n            if (!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\"))\n                throw new Error('Wallet not connected');\n            return { authToken: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\").auth_token, chain: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\").chain };\n        });\n        _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts.set(this, (accounts) => {\n            return accounts.map((account) => {\n                var _a, _b;\n                const publicKey = toUint8Array(account.address);\n                return {\n                    address: base58.encode(publicKey),\n                    publicKey,\n                    label: account.label,\n                    icon: account.icon,\n                    chains: (_a = account.chains) !== null && _a !== void 0 ? _a : __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, \"f\"),\n                    // TODO: get supported features from getCapabilities API \n                    features: (_b = account.features) !== null && _b !== void 0 ? _b : DEFAULT_FEATURES\n                };\n            });\n        });\n        _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions.set(this, (transactions) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            try {\n                return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain);\n                    const signedTransactions = yield wallet.signTransactions({\n                        transactions,\n                    });\n                    return signedTransactions;\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction.set(this, (transaction, options) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            try {\n                return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    const [capabilities, _1] = yield Promise.all([\n                        wallet.getCapabilities(),\n                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain)\n                    ]);\n                    if (capabilities.supports_sign_and_send_transactions) {\n                        const signatures = yield wallet.signAndSendTransactions(Object.assign(Object.assign({}, options), { transactions: [transaction] }));\n                        return signatures[0];\n                    }\n                    else {\n                        throw new Error('connected wallet does not support signAndSendTransaction');\n                    }\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const outputs = [];\n            for (const input of inputs) {\n                const transaction = VersionedTransaction.deserialize(input.transaction);\n                const signature = (yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, \"f\").call(this, transaction, input.options));\n                outputs.push({ signature: base58.decode(signature) });\n            }\n            return outputs;\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_signTransaction.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const transactions = inputs.map(({ transaction }) => VersionedTransaction.deserialize(transaction));\n            const signedTransactions = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions, \"f\").call(this, transactions);\n            return signedTransactions.map((signedTransaction) => {\n                const serializedTransaction = isVersionedTransaction(signedTransaction)\n                    ? signedTransaction.serialize()\n                    : new Uint8Array(signedTransaction.serialize({\n                        requireAllSignatures: false,\n                        verifySignatures: false,\n                    }));\n                return { signedTransaction: serializedTransaction };\n            });\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_signMessage.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, \"f\").call(this);\n            const addresses = inputs.map(({ account }) => fromUint8Array(account.publicKey));\n            const messages = inputs.map(({ message }) => message);\n            try {\n                return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, \"f\").call(this, (wallet) => __awaiter(this, void 0, void 0, function* () {\n                    yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, \"f\").call(this, wallet, authToken, chain);\n                    const signedMessages = yield wallet.signMessages({\n                        addresses: addresses,\n                        payloads: messages,\n                    });\n                    return signedMessages.map((signedMessage) => {\n                        return { signedMessage: signedMessage, signature: signedMessage.slice(-SIGNATURE_LENGTH_IN_BYTES) };\n                    });\n                }));\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_signIn.set(this, (...inputs) => __awaiter(this, void 0, void 0, function* () {\n            const outputs = [];\n            if (inputs.length > 1) {\n                for (const input of inputs) {\n                    outputs.push(yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignIn, \"f\").call(this, input));\n                }\n            }\n            else {\n                return [yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignIn, \"f\").call(this, inputs[0])];\n            }\n            return outputs;\n        }));\n        _RemoteSolanaMobileWalletAdapterWallet_performSignIn.set(this, (input) => __awaiter(this, void 0, void 0, function* () {\n            var _e, _f;\n            __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, true, \"f\");\n            try {\n                const authorizationResult = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, \"f\").call(this, Object.assign(Object.assign({}, input), { domain: (_e = input === null || input === void 0 ? void 0 : input.domain) !== null && _e !== void 0 ? _e : window.location.host }));\n                if (!authorizationResult.sign_in_result) {\n                    throw new Error(\"Sign in failed, no sign in result returned by wallet\");\n                }\n                const signedInAddress = authorizationResult.sign_in_result.address;\n                const signedInAccount = Object.assign(Object.assign({}, (_f = authorizationResult.accounts.find(acc => acc.address == signedInAddress)) !== null && _f !== void 0 ? _f : {\n                    address: signedInAddress\n                }), { publicKey: toUint8Array(signedInAddress) });\n                return {\n                    account: signedInAccount,\n                    signedMessage: toUint8Array(authorizationResult.sign_in_result.signed_message),\n                    signature: toUint8Array(authorizationResult.sign_in_result.signature)\n                };\n            }\n            catch (e) {\n                throw new Error((e instanceof Error && e.message) || 'Unknown error');\n            }\n            finally {\n                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, \"f\");\n            }\n        }));\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, config.authorizationCache, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, config.appIdentity, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, config.chains, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, config.chainSelector, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, config.remoteHostAuthority, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, config.onWalletNotFound, \"f\");\n        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, {\n            // We are forced to provide either SolanaSignAndSendTransaction or SolanaSignTransaction\n            // because the wallet-adapter compatible wallet-standard wallet requires at least one of them.\n            // MWA 2.0+ wallets must implement signAndSend and pre 2.0 wallets have always provided it so \n            // this is a safe assumption. We later update the features after we get the wallets capabilities. \n            [SolanaSignAndSendTransaction]: {\n                version: '1.0.0',\n                supportedTransactionVersions: ['legacy', 0],\n                signAndSendTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, \"f\"),\n            },\n        }, \"f\");\n    }\n    get version() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_version, \"f\");\n    }\n    get name() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_name, \"f\");\n    }\n    get url() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_url, \"f\");\n    }\n    get icon() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_icon, \"f\");\n    }\n    get chains() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, \"f\");\n    }\n    get features() {\n        return Object.assign({ [StandardConnect]: {\n                version: '1.0.0',\n                connect: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connect, \"f\"),\n            }, [StandardDisconnect]: {\n                version: '1.0.0',\n                disconnect: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_disconnect, \"f\"),\n            }, [StandardEvents]: {\n                version: '1.0.0',\n                on: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_on, \"f\"),\n            }, [SolanaSignMessage]: {\n                version: '1.0.0',\n                signMessage: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signMessage, \"f\"),\n            }, [SolanaSignIn]: {\n                version: '1.0.0',\n                signIn: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signIn, \"f\"),\n            } }, __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, \"f\"));\n    }\n    get accounts() {\n        var _a, _b;\n        return (_b = (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\")) === null || _a === void 0 ? void 0 : _a.accounts) !== null && _b !== void 0 ? _b : [];\n    }\n    get connected() {\n        return !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, \"f\") && !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get isAuthorized() {\n        return !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get currentAuthorization() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, \"f\");\n    }\n    get cachedAuthorizationResult() {\n        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, \"f\").get();\n    }\n}\n_RemoteSolanaMobileWalletAdapterWallet_listeners = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_version = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_name = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_url = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_icon = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_appIdentity = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_authorization = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_authorizationCache = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connecting = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_chains = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_chainSelector = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_hostAuthority = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_session = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_on = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connect = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performAuthorization = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performReauthorization = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_disconnect = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_transact = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signTransaction = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signMessage = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signIn = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignIn = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_instances = new WeakSet(), _RemoteSolanaMobileWalletAdapterWallet_emit = function _RemoteSolanaMobileWalletAdapterWallet_emit(event, ...args) {\n    var _a;\n    // eslint-disable-next-line prefer-spread\n    (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.forEach((listener) => listener.apply(null, args));\n}, _RemoteSolanaMobileWalletAdapterWallet_off = function _RemoteSolanaMobileWalletAdapterWallet_off(event, listener) {\n    var _a;\n    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event] = (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, \"f\")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener) => listener !== existingListener);\n};\n\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _RegisterWalletEvent_detail;\n/**\n * Register a {@link \"@wallet-standard/base\".Wallet} as a Standard Wallet with the app.\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to notify the app that the Wallet is\n * ready to be registered.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowAppReadyEvent} to listen for a notification from\n * the app that the app is ready to register the Wallet.\n *\n * This combination of event dispatch and listener guarantees that the Wallet will be registered synchronously as soon\n * as the app is ready whether the Wallet loads before or after the app.\n *\n * @param wallet Wallet to register.\n *\n * @group Wallet\n */\nfunction registerWallet(wallet) {\n    const callback = ({ register }) => register(wallet);\n    try {\n        window.dispatchEvent(new RegisterWalletEvent(callback));\n    }\n    catch (error) {\n        console.error('wallet-standard:register-wallet event could not be dispatched\\n', error);\n    }\n    try {\n        window.addEventListener('wallet-standard:app-ready', ({ detail: api }) => callback(api));\n    }\n    catch (error) {\n        console.error('wallet-standard:app-ready event listener could not be added\\n', error);\n    }\n}\nclass RegisterWalletEvent extends Event {\n    constructor(callback) {\n        super('wallet-standard:register-wallet', {\n            bubbles: false,\n            cancelable: false,\n            composed: false,\n        });\n        _RegisterWalletEvent_detail.set(this, void 0);\n        __classPrivateFieldSet(this, _RegisterWalletEvent_detail, callback, \"f\");\n    }\n    get detail() {\n        return __classPrivateFieldGet(this, _RegisterWalletEvent_detail, \"f\");\n    }\n    get type() {\n        return 'wallet-standard:register-wallet';\n    }\n    /** @deprecated */\n    preventDefault() {\n        throw new Error('preventDefault cannot be called');\n    }\n    /** @deprecated */\n    stopImmediatePropagation() {\n        throw new Error('stopImmediatePropagation cannot be called');\n    }\n    /** @deprecated */\n    stopPropagation() {\n        throw new Error('stopPropagation cannot be called');\n    }\n}\n_RegisterWalletEvent_detail = new WeakMap();\n\n(undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\n(undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\nfunction getIsLocalAssociationSupported() {\n    return (typeof window !== 'undefined' &&\n        window.isSecureContext &&\n        typeof document !== 'undefined' &&\n        /android/i.test(navigator.userAgent));\n}\nfunction getIsRemoteAssociationSupported() {\n    return (typeof window !== 'undefined' &&\n        window.isSecureContext &&\n        typeof document !== 'undefined' &&\n        !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));\n}\n\nfunction registerMwa(config) {\n    if (getIsLocalAssociationSupported()) {\n        registerWallet(new LocalSolanaMobileWalletAdapterWallet(config));\n    }\n    else if (getIsRemoteAssociationSupported() && config.remoteHostAuthority !== undefined) {\n        registerWallet(new RemoteSolanaMobileWalletAdapterWallet(Object.assign(Object.assign({}, config), { remoteHostAuthority: config.remoteHostAuthority })));\n    }\n    else ;\n}\n\nconst WALLET_NOT_FOUND_ERROR_MESSAGE = 'To use mobile wallet adapter, you must have a compatible mobile wallet application installed on your device.';\nconst BROWSER_NOT_SUPPORTED_ERROR_MESSAGE = 'This browser appears to be incompatible with mobile wallet adapter. Open this page in a compatible mobile browser app and try again.';\nclass ErrorModal extends EmbeddedModal {\n    constructor() {\n        super(...arguments);\n        this.contentStyles = css;\n        this.contentHtml = ErrorDialogHtml;\n    }\n    initWithError(error) {\n        super.init();\n        this.populateError(error);\n    }\n    populateError(error) {\n        var _a, _b;\n        const errorMessageElement = (_a = this.dom) === null || _a === void 0 ? void 0 : _a.getElementById('mobile-wallet-adapter-error-message');\n        const actionBtn = (_b = this.dom) === null || _b === void 0 ? void 0 : _b.getElementById('mobile-wallet-adapter-error-action');\n        if (errorMessageElement) {\n            if (error.name === 'SolanaMobileWalletAdapterError') {\n                switch (error.code) {\n                    case 'ERROR_WALLET_NOT_FOUND':\n                        errorMessageElement.innerHTML = WALLET_NOT_FOUND_ERROR_MESSAGE;\n                        if (actionBtn)\n                            actionBtn.addEventListener('click', () => {\n                                window.location.href = 'https://solanamobile.com/wallets';\n                            });\n                        return;\n                    case 'ERROR_BROWSER_NOT_SUPPORTED':\n                        errorMessageElement.innerHTML = BROWSER_NOT_SUPPORTED_ERROR_MESSAGE;\n                        if (actionBtn)\n                            actionBtn.style.display = 'none';\n                        return;\n                }\n            }\n            errorMessageElement.innerHTML = `An unexpected error occurred: ${error.message}`;\n        }\n        else {\n            console.log('Failed to locate error dialog element');\n        }\n    }\n}\nconst ErrorDialogHtml = `\n<svg class=\"mobile-wallet-adapter-embedded-modal-error-icon\" xmlns=\"http://www.w3.org/2000/svg\" height=\"50px\" viewBox=\"0 -960 960 960\" width=\"50px\" fill=\"#000000\"><path d=\"M 280,-80 Q 197,-80 138.5,-138.5 80,-197 80,-280 80,-363 138.5,-421.5 197,-480 280,-480 q 83,0 141.5,58.5 58.5,58.5 58.5,141.5 0,83 -58.5,141.5 Q 363,-80 280,-80 Z M 824,-120 568,-376 Q 556,-389 542.5,-402.5 529,-416 516,-428 q 38,-24 61,-64 23,-40 23,-88 0,-75 -52.5,-127.5 Q 495,-760 420,-760 345,-760 292.5,-707.5 240,-655 240,-580 q 0,6 0.5,11.5 0.5,5.5 1.5,11.5 -18,2 -39.5,8 -21.5,6 -38.5,14 -2,-11 -3,-22 -1,-11 -1,-23 0,-109 75.5,-184.5 Q 311,-840 420,-840 q 109,0 184.5,75.5 75.5,75.5 75.5,184.5 0,43 -13.5,81.5 Q 653,-460 629,-428 l 251,252 z m -615,-61 71,-71 70,71 29,-28 -71,-71 71,-71 -28,-28 -71,71 -71,-71 -28,28 71,71 -71,71 z\"/></svg>\n<div class=\"mobile-wallet-adapter-embedded-modal-title\">We can't find a wallet.</div>\n<div id=\"mobile-wallet-adapter-error-message\" class=\"mobile-wallet-adapter-embedded-modal-subtitle\"></div>\n<div>\n    <button data-error-action id=\"mobile-wallet-adapter-error-action\" class=\"mobile-wallet-adapter-embedded-modal-error-action\">\n        Find a wallet\n    </button>\n</div>\n`;\nconst css = `\n.mobile-wallet-adapter-embedded-modal-content {\n    text-align: center;\n}\n\n.mobile-wallet-adapter-embedded-modal-error-icon {\n    margin-top: 24px;\n}\n\n.mobile-wallet-adapter-embedded-modal-title {\n    margin: 18px 100px auto 100px;\n    color: #000000;\n    font-size: 2.75em;\n    font-weight: 600;\n}\n\n.mobile-wallet-adapter-embedded-modal-subtitle {\n    margin: 30px 60px 40px 60px;\n    color: #000000;\n    font-size: 1.25em;\n    font-weight: 400;\n}\n\n.mobile-wallet-adapter-embedded-modal-error-action {\n    display: block;\n    width: 100%;\n    height: 56px;\n    /*margin-top: 40px;*/\n    font-size: 1.25em;\n    /*line-height: 24px;*/\n    /*letter-spacing: -1%;*/\n    background: #000000;\n    color: #FFFFFF;\n    border-radius: 18px;\n}\n\n/* Smaller screens */\n@media all and (max-width: 600px) {\n    .mobile-wallet-adapter-embedded-modal-title {\n        font-size: 1.5em;\n        margin-right: 12px;\n        margin-left: 12px;\n    }\n    .mobile-wallet-adapter-embedded-modal-subtitle {\n        margin-right: 12px;\n        margin-left: 12px;\n    }\n}\n`;\n\nfunction defaultErrorModalWalletNotFoundHandler() {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (typeof window !== 'undefined') {\n            const userAgent = window.navigator.userAgent.toLowerCase();\n            const errorDialog = new ErrorModal();\n            if (userAgent.includes('wv')) { // Android WebView\n                // MWA is not supported in this browser so we inform the user\n                // errorDialog.initWithError(\n                //     new SolanaMobileWalletAdapterError(\n                //         SolanaMobileWalletAdapterErrorCode.ERROR_BROWSER_NOT_SUPPORTED, \n                //         ''\n                //     )\n                // );\n                // TODO: investigate why instantiating a new SolanaMobileWalletAdapterError here breaks treeshaking \n                errorDialog.initWithError({\n                    name: 'SolanaMobileWalletAdapterError',\n                    code: 'ERROR_BROWSER_NOT_SUPPORTED',\n                    message: ''\n                });\n            }\n            else { // Browser, user does not have a wallet installed.\n                // errorDialog.initWithError(\n                //     new SolanaMobileWalletAdapterError(\n                //         SolanaMobileWalletAdapterErrorCode.ERROR_WALLET_NOT_FOUND, \n                //         ''\n                //     )\n                // );\n                // TODO: investigate why instantiating a new SolanaMobileWalletAdapterError here breaks treeshaking \n                errorDialog.initWithError({\n                    name: 'SolanaMobileWalletAdapterError',\n                    code: 'ERROR_WALLET_NOT_FOUND',\n                    message: ''\n                });\n            }\n            errorDialog.open();\n        }\n    });\n}\nfunction createDefaultWalletNotFoundHandler() {\n    return () => __awaiter(this, void 0, void 0, function* () { defaultErrorModalWalletNotFoundHandler(); });\n}\n\nconst CACHE_KEY = 'SolanaMobileWalletAdapterDefaultAuthorizationCache';\nfunction createDefaultAuthorizationCache() {\n    let storage;\n    try {\n        storage = window.localStorage;\n        // eslint-disable-next-line no-empty\n    }\n    catch (_a) { }\n    return {\n        clear() {\n            return __awaiter(this, void 0, void 0, function* () {\n                if (!storage) {\n                    return;\n                }\n                try {\n                    storage.removeItem(CACHE_KEY);\n                    // eslint-disable-next-line no-empty\n                }\n                catch (_a) { }\n            });\n        },\n        get() {\n            return __awaiter(this, void 0, void 0, function* () {\n                if (!storage) {\n                    return;\n                }\n                try {\n                    const parsed = JSON.parse(storage.getItem(CACHE_KEY));\n                    if (parsed && parsed.accounts) {\n                        const parsedAccounts = parsed.accounts.map((account) => {\n                            return Object.assign(Object.assign({}, account), { publicKey: 'publicKey' in account\n                                    ? new Uint8Array(Object.values(account.publicKey)) // Rebuild publicKey for WalletAccount\n                                    : new PublicKey(account.address).toBytes() });\n                        });\n                        return Object.assign(Object.assign({}, parsed), { accounts: parsedAccounts });\n                    }\n                    else\n                        return parsed || undefined;\n                    // eslint-disable-next-line no-empty\n                }\n                catch (_a) { }\n            });\n        },\n        set(authorizationResult) {\n            return __awaiter(this, void 0, void 0, function* () {\n                if (!storage) {\n                    return;\n                }\n                try {\n                    storage.setItem(CACHE_KEY, JSON.stringify(authorizationResult));\n                    // eslint-disable-next-line no-empty\n                }\n                catch (_a) { }\n            });\n        },\n    };\n}\n\nfunction createDefaultChainSelector() {\n    return {\n        select(chains) {\n            return __awaiter(this, void 0, void 0, function* () {\n                if (chains.length === 1) {\n                    return chains[0];\n                }\n                else if (chains.includes(SOLANA_MAINNET_CHAIN)) {\n                    return SOLANA_MAINNET_CHAIN;\n                }\n                else\n                    return chains[0];\n            });\n        },\n    };\n}\n\nexport { LocalSolanaMobileWalletAdapterWallet, RemoteSolanaMobileWalletAdapterWallet, SolanaMobileWalletAdapterWalletName, createDefaultAuthorizationCache, createDefaultChainSelector, createDefaultWalletNotFoundHandler, defaultErrorModalWalletNotFoundHandler, registerMwa };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;AAEA;;;;;;;;;;;;;8EAa8E,GAE9E,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IAChD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AAEA,SAAS,yBAAyB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACtD,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AAEA,SAAS,yBAAyB,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC7D,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AAEA,IAAI,0BAA0B,qBAAqB,+BAA+B,kCAAkC,2BAA2B,qCAAqC,qCAAqC;AACzN,MAAM,YAAY,CAAC;;;;;;;;;;;;;;AAcnB,CAAC;AACD,MAAM,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDf,CAAC;AACD,MAAM,QAAQ,CAAC;;;;AAIf,CAAC;AACD,MAAM;IACF,aAAc;QACV,yBAAyB,GAAG,CAAC,IAAI;QACjC,oBAAoB,GAAG,CAAC,IAAI,EAAE;QAC9B,8BAA8B,GAAG,CAAC,IAAI,EAAE,CAAC;QACzC,iCAAiC,GAAG,CAAC,IAAI,EAAE;QAC3C,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG;YACR,QAAQ,KAAK,CAAC;YACd,yBAAyB,IAAI,EAAE,0BAA0B,KAAK,qCAAqC,IAAI,CAAC,IAAI;YAC5G,IAAI,yBAAyB,IAAI,EAAE,qBAAqB,MAAM;gBAC1D,yBAAyB,IAAI,EAAE,qBAAqB,KAAK,KAAK,CAAC,OAAO,GAAG;YAC7E;QACJ;QACA,IAAI,CAAC,KAAK,GAAG,CAAC,QAAQ,SAAS;YAC3B,IAAI;YACJ,QAAQ,KAAK,CAAC;YACd,yBAAyB,IAAI,EAAE,0BAA0B,KAAK,qCAAqC,IAAI,CAAC,IAAI;YAC5G,IAAI,yBAAyB,IAAI,EAAE,qBAAqB,MAAM;gBAC1D,yBAAyB,IAAI,EAAE,qBAAqB,KAAK,KAAK,CAAC,OAAO,GAAG;YAC7E;YACA,CAAC,KAAK,yBAAyB,IAAI,EAAE,+BAA+B,IAAI,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,CAAC,WAAa,SAAS;QAC9J;QACA,6BAA6B,GAAG,CAAC,IAAI,EAAE,CAAC;YACpC,IAAI,MAAM,GAAG,KAAK,UACd,IAAI,CAAC,KAAK,CAAC;QACnB;QACA,mDAAmD;QACnD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;QAC/B,yBAAyB,IAAI,EAAE,qBAAqB,SAAS,cAAc,CAAC,2CAA2C;IAC3H;IACA,OAAO;QACH,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,QAAQ,GAAG,CAAC;YACZ,yBAAyB,IAAI,EAAE,0BAA0B,KAAK,2BAA2B,IAAI,CAAC,IAAI;QACtG;IACJ;IACA,iBAAiB,KAAK,EAAE,QAAQ,EAAE;QAC9B,IAAI;QACJ,CAAC,CAAC,KAAK,yBAAyB,IAAI,EAAE,+BAA+B,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,yBAAyB,IAAI,EAAE,+BAA+B,IAAI,CAAC,MAAM,GAAG;YAAC;SAAS;QACpO,OAAO,IAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO;IACjD;IACA,oBAAoB,KAAK,EAAE,QAAQ,EAAE;QACjC,IAAI;QACJ,yBAAyB,IAAI,EAAE,+BAA+B,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,yBAAyB,IAAI,EAAE,+BAA+B,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,CAAC,mBAAqB,aAAa;IACnP;AACJ;AACA,sBAAsB,IAAI,WAAW,gCAAgC,IAAI,WAAW,mCAAmC,IAAI,WAAW,+BAA+B,IAAI,WAAW,2BAA2B,IAAI,WAAW,4BAA4B,SAAS;IAC/P,8CAA8C;IAC9C,IAAI,SAAS,cAAc,CAAC,2CAA2C;QACnE,IAAI,CAAC,yBAAyB,IAAI,EAAE,qBAAqB,MACrD,yBAAyB,IAAI,EAAE,qBAAqB,SAAS,cAAc,CAAC,2CAA2C;QAC3H;IACJ;IACA,mCAAmC;IACnC,yBAAyB,IAAI,EAAE,qBAAqB,SAAS,aAAa,CAAC,QAAQ;IACnF,yBAAyB,IAAI,EAAE,qBAAqB,KAAK,EAAE,GAAG;IAC9D,yBAAyB,IAAI,EAAE,qBAAqB,KAAK,SAAS,GAAG;IACrE,yBAAyB,IAAI,EAAE,qBAAqB,KAAK,KAAK,CAAC,OAAO,GAAG;IACzE,oBAAoB;IACpB,MAAM,UAAU,yBAAyB,IAAI,EAAE,qBAAqB,KAAK,aAAa,CAAC;IACvF,IAAI,SACA,QAAQ,SAAS,GAAG,IAAI,CAAC,WAAW;IACxC,eAAe;IACf,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,EAAE,GAAG;IACZ,OAAO,WAAW,GAAG,QAAQ,IAAI,CAAC,aAAa;IAC/C,+CAA+C;IAC/C,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,SAAS,GAAG;IACjB,IAAI,CAAC,GAAG,GAAG,KAAK,YAAY,CAAC;QAAE,MAAM;IAAS;IAC9C,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;IACrB,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,yBAAyB,IAAI,EAAE,qBAAqB;IACzE,yCAAyC;IACzC,SAAS,IAAI,CAAC,WAAW,CAAC;AAC9B,GAAG,sCAAsC,SAAS;IAC9C,IAAI,CAAC,yBAAyB,IAAI,EAAE,qBAAqB,QAAQ,yBAAyB,IAAI,EAAE,kCAAkC,MAC9H;IACJ,MAAM,UAAU;WAAI,yBAAyB,IAAI,EAAE,qBAAqB,KAAK,gBAAgB,CAAC;KAAsB;IACpH,QAAQ,OAAO,CAAC,CAAA,SAAU,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,gBAAgB,CAAC,SAAS,IAAI,CAAC,KAAK;IACrH,OAAO,gBAAgB,CAAC,QAAQ,IAAI,CAAC,KAAK;IAC1C,SAAS,gBAAgB,CAAC,WAAW,yBAAyB,IAAI,EAAE,8BAA8B;IAClG,yBAAyB,IAAI,EAAE,kCAAkC,MAAM;AAC3E,GAAG,sCAAsC,SAAS;IAC9C,IAAI,CAAC,yBAAyB,IAAI,EAAE,kCAAkC,MAClE;IACJ,OAAO,mBAAmB,CAAC,QAAQ,IAAI,CAAC,KAAK;IAC7C,SAAS,mBAAmB,CAAC,WAAW,yBAAyB,IAAI,EAAE,8BAA8B;IACrG,IAAI,CAAC,yBAAyB,IAAI,EAAE,qBAAqB,MACrD;IACJ,MAAM,UAAU;WAAI,yBAAyB,IAAI,EAAE,qBAAqB,KAAK,gBAAgB,CAAC;KAAsB;IACpH,QAAQ,OAAO,CAAC,CAAA,SAAU,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,CAAC,SAAS,IAAI,CAAC,KAAK;IACxH,yBAAyB,IAAI,EAAE,kCAAkC,OAAO;AAC5E;AAEA,MAAM,8BAA8B;IAChC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,WAAW,MAAM,EAAE;QACf,MAAM,SAAS,OAAO,MAAM,CAAC,MAAM;YAC/B,MAAM;gBAAE,KAAK,IAAM,KAAK,CAAC;YAAK;QAClC;QACA,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;YACrB,IAAI,CAAC,cAAc,CAAC;QACxB;IACJ;IACA,eAAe,KAAK,EAAE;QAClB,IAAI;QACJ,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,MAAM,kBAAkB,CAAC,KAAK,IAAI,CAAC,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,cAAc,CAAC;YAC/F,IAAI,iBAAiB;gBACjB,MAAM,gBAAgB,MAAM,2IAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,OAAO;oBAAE,OAAO;oBAAK,QAAQ;gBAAE;gBAC3E,IAAI,gBAAgB,iBAAiB,KAAK,MAAM;oBAC5C,gBAAgB,YAAY,CAAC,eAAe,gBAAgB,iBAAiB;gBACjF,OAEI,gBAAgB,WAAW,CAAC;YACpC,OACK;gBACD,QAAQ,KAAK,CAAC;YAClB;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCpB,CAAC;AACD,MAAM,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmMf,CAAC;AAED,MAAM,OAAO;AAEb,SAAS,uBAAuB,WAAW;IACvC,OAAO,aAAa;AACxB;AAEA,SAAS,eAAe,SAAS;IAC7B,OAAO,OAAO,IAAI,CAAC,OAAO,YAAY,CAAC,IAAI,CAAC,SAAS;AACzD;AACA,SAAS,aAAa,sBAAsB;IACxC,OAAO,IAAI,WAAW,OACjB,IAAI,CAAC,wBACL,KAAK,CAAC,IACN,GAAG,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC;AACjC;AAEA,IAAI,iDAAiD,iDAAiD,+CAA+C,4CAA4C,2CAA2C,4CAA4C,mDAAmD,qDAAqD,0DAA0D,kDAAkD,4DAA4D,8CAA8C,qDAAqD,wDAAwD,wDAAwD,0CAA0C,4CAA4C,2CAA2C,+CAA+C,4DAA4D,iEAAiE,sEAAsE,8DAA8D,kDAAkD,gDAAgD,0DAA0D,wEAAwE,+DAA+D,qEAAqE,8DAA8D,uDAAuD,mDAAmD,8CAA8C,qDAAqD,kDAAkD,kDAAkD,gDAAgD,6CAA6C,4CAA4C,6CAA6C,oDAAoD,sDAAsD,2DAA2D,mDAAmD,6DAA6D,+CAA+C,sDAAsD,yDAAyD,yDAAyD,sDAAsD,gDAAgD,2CAA2C,6CAA6C,4CAA4C,gDAAgD,6DAA6D,kEAAkE,uEAAuE,+DAA+D,mDAAmD,iDAAiD,2DAA2D,yEAAyE,gEAAgE,sEAAsE,+DAA+D,wDAAwD,oDAAoD,+CAA+C;AAC5oH,MAAM,sCAAsC;AAC5C,MAAM,4BAA4B;AAClC,MAAM,mBAAmB;IAAC,qMAAA,CAAA,+BAA4B;IAAE,8LAAA,CAAA,wBAAqB;IAAE,0LAAA,CAAA,oBAAiB;IAAE,qLAAA,CAAA,eAAY;CAAC;AAC/G,MAAM;IACF,YAAY,MAAM,CAAE;QAChB,gDAAgD,GAAG,CAAC,IAAI;QACxD,gDAAgD,GAAG,CAAC,IAAI,EAAE,CAAC;QAC3D,8CAA8C,GAAG,CAAC,IAAI,EAAE,UAAU,0BAA0B;QAC5F,2CAA2C,GAAG,CAAC,IAAI,EAAE;QACrD,0CAA0C,GAAG,CAAC,IAAI,EAAE;QACpD,2CAA2C,GAAG,CAAC,IAAI,EAAE;QACrD,kDAAkD,GAAG,CAAC,IAAI,EAAE,KAAK;QACjE,oDAAoD,GAAG,CAAC,IAAI,EAAE,KAAK;QACnE,yDAAyD,GAAG,CAAC,IAAI,EAAE,KAAK;QACxE,iDAAiD,GAAG,CAAC,IAAI,EAAE;QAC3D;;;;SAIC,GACD,2DAA2D,GAAG,CAAC,IAAI,EAAE;QACrE,6CAA6C,GAAG,CAAC,IAAI,EAAE,EAAE;QACzD,oDAAoD,GAAG,CAAC,IAAI,EAAE,KAAK;QACnE,uDAAuD,GAAG,CAAC,IAAI,EAAE,KAAK;QACtE,uDAAuD,GAAG,CAAC,IAAI,EAAE,KAAK;QACtE,yCAAyC,GAAG,CAAC,IAAI,EAAE,CAAC,OAAO;YACvD,IAAI;YACJ,CAAC,CAAC,KAAK,yBAAyB,IAAI,EAAE,iDAAiD,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,yBAAyB,IAAI,EAAE,iDAAiD,IAAI,CAAC,MAAM,GAAG;gBAAC;aAAS;YACxQ,OAAO,IAAM,yBAAyB,IAAI,EAAE,iDAAiD,KAAK,2CAA2C,IAAI,CAAC,IAAI,EAAE,OAAO;QACnK;QACA,8CAA8C,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAK,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACzG,IAAI,yBAAyB,IAAI,EAAE,kDAAkD,QAAQ,IAAI,CAAC,SAAS,EAAE;oBACzG,OAAO;wBAAE,UAAU,IAAI,CAAC,QAAQ;oBAAC;gBACrC;gBACA,yBAAyB,IAAI,EAAE,kDAAkD,MAAM;gBACvF,IAAI;oBACA,IAAI,QAAQ;wBACR,MAAM,sBAAsB,MAAM,yBAAyB,IAAI,EAAE,0DAA0D,KAAK,GAAG;wBACnI,IAAI,qBAAqB;4BACrB,MAAM,yBAAyB,IAAI,EAAE,iEAAiE,KAAK,IAAI,CAAC,IAAI,EAAE;wBAC1H,OACK;4BACD,OAAO;gCAAE,UAAU,IAAI,CAAC,QAAQ;4BAAC;wBACrC;oBACJ,OACK;wBACD,MAAM,yBAAyB,IAAI,EAAE,4DAA4D,KAAK,IAAI,CAAC,IAAI;oBACnH;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD,SACQ;oBACJ,yBAAyB,IAAI,EAAE,kDAAkD,OAAO;gBAC5F;gBACA,OAAO;oBAAE,UAAU,IAAI,CAAC,QAAQ;gBAAC;YACrC;QACA,2DAA2D,GAAG,CAAC,IAAI,EAAE,CAAC,gBAAkB,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACpH,IAAI;oBACA,MAAM,4BAA4B,MAAM,yBAAyB,IAAI,EAAE,0DAA0D,KAAK,GAAG;oBACzI,IAAI,2BAA2B;wBAC3B,8EAA8E;wBAC9E,yBAAyB,IAAI,EAAE,iEAAiE,KAAK,IAAI,CAAC,IAAI,EAAE;wBAChH,OAAO;oBACX;oBACA,MAAM,gBAAgB,MAAM,yBAAyB,IAAI,EAAE,qDAAqD,KAAK,MAAM,CAAC,yBAAyB,IAAI,EAAE,8CAA8C;oBACzM,OAAO,MAAM,yBAAyB,IAAI,EAAE,gDAAgD,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4BACpJ,MAAM,CAAC,cAAc,uBAAuB,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAC7D,OAAO,eAAe;gCACtB,OAAO,SAAS,CAAC;oCACb,OAAO;oCACP,UAAU,yBAAyB,IAAI,EAAE,mDAAmD;oCAC5F,iBAAiB;gCACrB;6BACH;4BACD,MAAM,WAAW,yBAAyB,IAAI,EAAE,wEAAwE,KAAK,IAAI,CAAC,IAAI,EAAE,uBAAuB,QAAQ;4BACvK,MAAM,gBAAgB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,yBAAyB;gCAAE;gCAAU,OAAO;4BAAc;4BAChH,8EAA8E;4BAC9E,QAAQ,GAAG,CAAC;gCACR,yBAAyB,IAAI,EAAE,sEAAsE,KAAK,IAAI,CAAC,IAAI,EAAE;gCACrH,yBAAyB,IAAI,EAAE,0DAA0D,KAAK,GAAG,CAAC;gCAClG,yBAAyB,IAAI,EAAE,iEAAiE,KAAK,IAAI,CAAC,IAAI,EAAE;6BACnH;4BACD,OAAO;wBACX;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,gEAAgE,GAAG,CAAC,IAAI,EAAE,CAAC,gBAAkB,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACzH,IAAI;gBACJ,MAAM,sBACN,mDAAmD;gBACnD,yBAAyB,IAAI,EAAE,qDAAqD,QAAQ,QACxF,qDAAqD;gBACrD,CAAC,CAAC,KAAK,yBAAyB,IAAI,EAAE,qDAAqD,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,MAAM,MAAM,cAAc,QAAQ,CAAC,MAAM,IAC3L,+FAA+F;gBAC/F,yBAAyB,IAAI,EAAE,qDAAqD,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,KAAO,QAAQ,OAAO,KAAK,cAAc,QAAQ,CAAC,GAAG,CAAC,OAAO;gBAClL,yBAAyB,IAAI,EAAE,qDAAqD,eAAe;gBACnG,IAAI,qBAAqB;oBACrB,yBAAyB,IAAI,EAAE,iDAAiD,KAAK,4CAA4C,IAAI,CAAC,IAAI,EAAE,UAAU;wBAAE,UAAU,IAAI,CAAC,QAAQ;oBAAC;gBACpL;YACJ;QACA,qEAAqE,GAAG,CAAC,IAAI,EAAE,CAAC,eAAiB,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAC7H,iFAAiF;gBACjF,MAAM,0BAA0B,aAAa,QAAQ,CAAC,QAAQ,CAAC,4BAA4B,0BAA0B;gBACrH,MAAM,iCAAiC,aAAa,mCAAmC;gBACvF,MAAM,wBAAwB,qMAAA,CAAA,+BAA4B,IAAI,IAAI,CAAC,QAAQ,KAAK,kCAC5E,8LAAA,CAAA,wBAAqB,IAAI,IAAI,CAAC,QAAQ,KAAK;gBAC/C,yBAAyB,IAAI,EAAE,wDAAwD,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAI,CAAC,kCAAmC,CAAC,kCAAkC,CAAC,uBAAwB,KAAK;oBACzN,CAAC,qMAAA,CAAA,+BAA4B,CAAC,EAAE;wBAC5B,SAAS;wBACT,8BAA8B;4BAAC;4BAAU;yBAAE;wBAC3C,wBAAwB,yBAAyB,IAAI,EAAE,8DAA8D;oBACzH;gBACJ,IAAM,2BAA2B;oBAC7B,CAAC,8LAAA,CAAA,wBAAqB,CAAC,EAAE;wBACrB,SAAS;wBACT,8BAA8B;4BAAC;4BAAU;yBAAE;wBAC3C,iBAAiB,yBAAyB,IAAI,EAAE,uDAAuD;oBAC3G;gBACJ,IAAK;gBACL,IAAI,uBAAuB;oBACvB,yBAAyB,IAAI,EAAE,iDAAiD,KAAK,4CAA4C,IAAI,CAAC,IAAI,EAAE,UAAU;wBAAE,UAAU,IAAI,CAAC,QAAQ;oBAAC;gBACpL;YACJ;QACA,6DAA6D,GAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,WAAW,QAAU,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACjI,IAAI;oBACA,MAAM,yBAAyB,MAAM,OAAO,SAAS,CAAC;wBAClD,YAAY;wBACZ,UAAU,yBAAyB,IAAI,EAAE,mDAAmD;wBAC5F,OAAO;oBACX;oBACA,MAAM,WAAW,yBAAyB,IAAI,EAAE,wEAAwE,KAAK,IAAI,CAAC,IAAI,EAAE,uBAAuB,QAAQ;oBACvK,MAAM,gBAAgB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,yBAAyB;wBAAE,UAAU;wBAAU,OAAO;oBAAM;oBAClH,8EAA8E;oBAC9E,QAAQ,GAAG,CAAC;wBACR,yBAAyB,IAAI,EAAE,0DAA0D,KAAK,GAAG,CAAC;wBAClG,yBAAyB,IAAI,EAAE,iEAAiE,KAAK,IAAI,CAAC,IAAI,EAAE;qBACnH;gBACL,EACA,OAAO,GAAG;oBACN,yBAAyB,IAAI,EAAE,kDAAkD,KAAK,IAAI,CAAC,IAAI;oBAC/F,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,iDAAiD,GAAG,CAAC,IAAI,EAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAC7F,IAAI;gBACJ,yBAAyB,IAAI,EAAE,0DAA0D,KAAK,KAAK,IAAI,8EAA8E;gBACrL,yBAAyB,IAAI,EAAE,kDAAkD,OAAO;gBACxF,yBAAyB,IAAI,EAAE,4DAA4D,CAAC,KAAK,yBAAyB,IAAI,EAAE,4DAA4D,MAAM,MAAM,EAAE,GAAG;gBAC7M,yBAAyB,IAAI,EAAE,qDAAqD,WAAW;gBAC/F,yBAAyB,IAAI,EAAE,iDAAiD,KAAK,4CAA4C,IAAI,CAAC,IAAI,EAAE,UAAU;oBAAE,UAAU,IAAI,CAAC,QAAQ;gBAAC;YACpL;QACA,+CAA+C,GAAG,CAAC,IAAI,EAAE,CAAC,WAAa,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACnG,IAAI;gBACJ,MAAM,gBAAgB,CAAC,KAAK,yBAAyB,IAAI,EAAE,qDAAqD,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe;gBAC7K,MAAM,SAAS,gBAAgB;oBAAE,SAAS;gBAAc,IAAI;gBAC5D,MAAM,8BAA8B,yBAAyB,IAAI,EAAE,4DAA4D;gBAC/H,IAAI;oBACA,OAAO,MAAM,CAAA,GAAA,iNAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;gBACpC,EACA,OAAO,GAAG;oBACN,IAAI,yBAAyB,IAAI,EAAE,4DAA4D,SAAS,6BAA6B;wBACjI,MAAM,IAAI,QAAQ,KAAQ,IAAI,iBAAiB;oBACnD;oBACA,IAAI,aAAa,SACb,EAAE,IAAI,KAAK,oCACX,EAAE,IAAI,KAAK,0BAA0B;wBACrC,MAAM,yBAAyB,IAAI,EAAE,wDAAwD,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI;oBACrH;oBACA,MAAM;gBACV;YACJ;QACA,yDAAyD,GAAG,CAAC,IAAI,EAAE;YAC/D,IAAI,CAAC,yBAAyB,IAAI,EAAE,qDAAqD,MACrF,MAAM,IAAI,MAAM;YACpB,OAAO;gBAAE,WAAW,yBAAyB,IAAI,EAAE,qDAAqD,KAAK,UAAU;gBAAE,OAAO,yBAAyB,IAAI,EAAE,qDAAqD,KAAK,KAAK;YAAC;QACnO;QACA,uEAAuE,GAAG,CAAC,IAAI,EAAE,CAAC;YAC9E,OAAO,SAAS,GAAG,CAAC,CAAC;gBACjB,IAAI,IAAI;gBACR,MAAM,YAAY,aAAa,QAAQ,OAAO;gBAC9C,OAAO;oBACH,SAAS,gIAAA,CAAA,UAAM,CAAC,MAAM,CAAC;oBACvB;oBACA,OAAO,QAAQ,KAAK;oBACpB,MAAM,QAAQ,IAAI;oBAClB,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,yBAAyB,IAAI,EAAE,8CAA8C;oBAC5I,yDAAyD;oBACzD,UAAU,CAAC,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;gBACvE;YACJ;QACJ;QACA,8DAA8D,GAAG,CAAC,IAAI,EAAE,CAAC,eAAiB,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACtH,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,yBAAyB,IAAI,EAAE,0DAA0D,KAAK,IAAI,CAAC,IAAI;gBACpI,IAAI;oBACA,OAAO,MAAM,yBAAyB,IAAI,EAAE,gDAAgD,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4BACpJ,MAAM,yBAAyB,IAAI,EAAE,8DAA8D,KAAK,IAAI,CAAC,IAAI,EAAE,QAAQ,WAAW;4BACtI,MAAM,qBAAqB,MAAM,OAAO,gBAAgB,CAAC;gCACrD;4BACJ;4BACA,OAAO;wBACX;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,oEAAoE,GAAG,CAAC,IAAI,EAAE,CAAC,aAAa,UAAY,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACpI,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,yBAAyB,IAAI,EAAE,0DAA0D,KAAK,IAAI,CAAC,IAAI;gBACpI,IAAI;oBACA,OAAO,MAAM,yBAAyB,IAAI,EAAE,gDAAgD,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4BACpJ,MAAM,CAAC,cAAc,GAAG,GAAG,MAAM,QAAQ,GAAG,CAAC;gCACzC,OAAO,eAAe;gCACtB,yBAAyB,IAAI,EAAE,8DAA8D,KAAK,IAAI,CAAC,IAAI,EAAE,QAAQ,WAAW;6BACnI;4BACD,IAAI,aAAa,mCAAmC,EAAE;gCAClD,MAAM,aAAa,MAAM,OAAO,uBAAuB,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;oCAAE,cAAc;wCAAC;qCAAY;gCAAC;gCAChI,OAAO,UAAU,CAAC,EAAE;4BACxB,OACK;gCACD,MAAM,IAAI,MAAM;4BACpB;wBACJ;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,6DAA6D,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAClH,MAAM,UAAU,EAAE;gBAClB,KAAK,MAAM,SAAS,OAAQ;oBACxB,MAAM,cAAc,2KAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,MAAM,WAAW;oBACtE,MAAM,YAAa,MAAM,yBAAyB,IAAI,EAAE,qEAAqE,KAAK,IAAI,CAAC,IAAI,EAAE,aAAa,MAAM,OAAO;oBACvK,QAAQ,IAAI,CAAC;wBAAE,WAAW,gIAAA,CAAA,UAAM,CAAC,MAAM,CAAC;oBAAW;gBACvD;gBACA,OAAO;YACX;QACA,sDAAsD,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAC3G,MAAM,eAAe,OAAO,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,GAAK,2KAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC;gBACtF,MAAM,qBAAqB,MAAM,yBAAyB,IAAI,EAAE,+DAA+D,KAAK,IAAI,CAAC,IAAI,EAAE;gBAC/I,OAAO,mBAAmB,GAAG,CAAC,CAAC;oBAC3B,MAAM,wBAAwB,uBAAuB,qBAC/C,kBAAkB,SAAS,KAC3B,IAAI,WAAW,kBAAkB,SAAS,CAAC;wBACzC,sBAAsB;wBACtB,kBAAkB;oBACtB;oBACJ,OAAO;wBAAE,mBAAmB;oBAAsB;gBACtD;YACJ;QACA,kDAAkD,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACvG,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,yBAAyB,IAAI,EAAE,0DAA0D,KAAK,IAAI,CAAC,IAAI;gBACpI,MAAM,YAAY,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,GAAK,eAAe,QAAQ,SAAS;gBAC9E,MAAM,WAAW,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,GAAK;gBAC7C,IAAI;oBACA,OAAO,MAAM,yBAAyB,IAAI,EAAE,gDAAgD,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4BACpJ,MAAM,yBAAyB,IAAI,EAAE,8DAA8D,KAAK,IAAI,CAAC,IAAI,EAAE,QAAQ,WAAW;4BACtI,MAAM,iBAAiB,MAAM,OAAO,YAAY,CAAC;gCAC7C,WAAW;gCACX,UAAU;4BACd;4BACA,OAAO,eAAe,GAAG,CAAC,CAAC;gCACvB,OAAO;oCAAE,eAAe;oCAAe,WAAW,cAAc,KAAK,CAAC,CAAC;gCAA2B;4BACtG;wBACJ;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,6CAA6C,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAClG,MAAM,UAAU,EAAE;gBAClB,IAAI,OAAO,MAAM,GAAG,GAAG;oBACnB,KAAK,MAAM,SAAS,OAAQ;wBACxB,QAAQ,IAAI,CAAC,CAAA,MAAM,yBAAyB,IAAI,EAAE,qDAAqD,KAAK,IAAI,CAAC,IAAI,EAAE,MAAK;oBAChI;gBACJ,OACK;oBACD,OAAO;wBAAC,CAAA,MAAM,yBAAyB,IAAI,EAAE,qDAAqD,KAAK,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAA;qBAAE;gBACjI;gBACA,OAAO;YACX;QACA,oDAAoD,GAAG,CAAC,IAAI,EAAE,CAAC,QAAU,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACrG,IAAI,IAAI;gBACR,yBAAyB,IAAI,EAAE,kDAAkD,MAAM;gBACvF,IAAI;oBACA,MAAM,sBAAsB,MAAM,yBAAyB,IAAI,EAAE,4DAA4D,KAAK,IAAI,CAAC,IAAI,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ;wBAAE,QAAQ,CAAC,KAAK,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,QAAQ,CAAC,IAAI;oBAAC;oBACvT,IAAI,CAAC,oBAAoB,cAAc,EAAE;wBACrC,MAAM,IAAI,MAAM;oBACpB;oBACA,MAAM,kBAAkB,oBAAoB,cAAc,CAAC,OAAO;oBAClE,MAAM,kBAAkB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,oBAAoB,QAAQ,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,OAAO,IAAI,gBAAgB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBACrK,SAAS;oBACb,IAAI;wBAAE,WAAW,aAAa;oBAAiB;oBAC/C,OAAO;wBACH,SAAS;wBACT,eAAe,aAAa,oBAAoB,cAAc,CAAC,cAAc;wBAC7E,WAAW,aAAa,oBAAoB,cAAc,CAAC,SAAS;oBACxE;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD,SACQ;oBACJ,yBAAyB,IAAI,EAAE,kDAAkD,OAAO;gBAC5F;YACJ;QACA,yBAAyB,IAAI,EAAE,0DAA0D,OAAO,kBAAkB,EAAE;QACpH,yBAAyB,IAAI,EAAE,mDAAmD,OAAO,WAAW,EAAE;QACtG,yBAAyB,IAAI,EAAE,8CAA8C,OAAO,MAAM,EAAE;QAC5F,yBAAyB,IAAI,EAAE,qDAAqD,OAAO,aAAa,EAAE;QAC1G,yBAAyB,IAAI,EAAE,wDAAwD,OAAO,gBAAgB,EAAE;QAChH,yBAAyB,IAAI,EAAE,wDAAwD;YACnF,wFAAwF;YACxF,8FAA8F;YAC9F,8FAA8F;YAC9F,kGAAkG;YAClG,CAAC,qMAAA,CAAA,+BAA4B,CAAC,EAAE;gBAC5B,SAAS;gBACT,8BAA8B;oBAAC;oBAAU;iBAAE;gBAC3C,wBAAwB,yBAAyB,IAAI,EAAE,8DAA8D;YACzH;QACJ,GAAG;IACP;IACA,IAAI,UAAU;QACV,OAAO,yBAAyB,IAAI,EAAE,+CAA+C;IACzF;IACA,IAAI,OAAO;QACP,OAAO,yBAAyB,IAAI,EAAE,4CAA4C;IACtF;IACA,IAAI,MAAM;QACN,OAAO,yBAAyB,IAAI,EAAE,2CAA2C;IACrF;IACA,IAAI,OAAO;QACP,OAAO,yBAAyB,IAAI,EAAE,4CAA4C;IACtF;IACA,IAAI,SAAS;QACT,OAAO,yBAAyB,IAAI,EAAE,8CAA8C;IACxF;IACA,IAAI,WAAW;QACX,OAAO,OAAO,MAAM,CAAC;YAAE,CAAC,4KAAA,CAAA,kBAAe,CAAC,EAAE;gBAClC,SAAS;gBACT,SAAS,yBAAyB,IAAI,EAAE,+CAA+C;YAC3F;YAAG,CAAC,+KAAA,CAAA,qBAAkB,CAAC,EAAE;gBACrB,SAAS;gBACT,YAAY,yBAAyB,IAAI,EAAE,kDAAkD;YACjG;YAAG,CAAC,2KAAA,CAAA,iBAAc,CAAC,EAAE;gBACjB,SAAS;gBACT,IAAI,yBAAyB,IAAI,EAAE,0CAA0C;YACjF;YAAG,CAAC,0LAAA,CAAA,oBAAiB,CAAC,EAAE;gBACpB,SAAS;gBACT,aAAa,yBAAyB,IAAI,EAAE,mDAAmD;YACnG;YAAG,CAAC,qLAAA,CAAA,eAAY,CAAC,EAAE;gBACf,SAAS;gBACT,QAAQ,yBAAyB,IAAI,EAAE,8CAA8C;YACzF;QAAE,GAAG,yBAAyB,IAAI,EAAE,wDAAwD;IACpG;IACA,IAAI,WAAW;QACX,IAAI,IAAI;QACR,OAAO,CAAC,KAAK,CAAC,KAAK,yBAAyB,IAAI,EAAE,qDAAqD,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACtM;IACA,IAAI,YAAY;QACZ,OAAO,CAAC,CAAC,yBAAyB,IAAI,EAAE,qDAAqD;IACjG;IACA,IAAI,eAAe;QACf,OAAO,CAAC,CAAC,yBAAyB,IAAI,EAAE,qDAAqD;IACjG;IACA,IAAI,uBAAuB;QACvB,OAAO,yBAAyB,IAAI,EAAE,qDAAqD;IAC/F;IACA,IAAI,4BAA4B;QAC5B,OAAO,yBAAyB,IAAI,EAAE,0DAA0D,KAAK,GAAG;IAC5G;AACJ;AACA,kDAAkD,IAAI,WAAW,gDAAgD,IAAI,WAAW,6CAA6C,IAAI,WAAW,4CAA4C,IAAI,WAAW,6CAA6C,IAAI,WAAW,oDAAoD,IAAI,WAAW,sDAAsD,IAAI,WAAW,2DAA2D,IAAI,WAAW,mDAAmD,IAAI,WAAW,6DAA6D,IAAI,WAAW,+CAA+C,IAAI,WAAW,sDAAsD,IAAI,WAAW,yDAAyD,IAAI,WAAW,yDAAyD,IAAI,WAAW,2CAA2C,IAAI,WAAW,gDAAgD,IAAI,WAAW,6DAA6D,IAAI,WAAW,kEAAkE,IAAI,WAAW,uEAAuE,IAAI,WAAW,+DAA+D,IAAI,WAAW,mDAAmD,IAAI,WAAW,iDAAiD,IAAI,WAAW,2DAA2D,IAAI,WAAW,yEAAyE,IAAI,WAAW,gEAAgE,IAAI,WAAW,sEAAsE,IAAI,WAAW,+DAA+D,IAAI,WAAW,wDAAwD,IAAI,WAAW,oDAAoD,IAAI,WAAW,+CAA+C,IAAI,WAAW,sDAAsD,IAAI,WAAW,kDAAkD,IAAI,WAAW,6CAA6C,SAAS,2CAA2C,KAAK,EAAE,GAAG,IAAI;IAC/yE,IAAI;IACJ,yCAAyC;IACzC,CAAC,KAAK,yBAAyB,IAAI,EAAE,iDAAiD,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,CAAC,WAAa,SAAS,KAAK,CAAC,MAAM;AAC1L,GAAG,4CAA4C,SAAS,0CAA0C,KAAK,EAAE,QAAQ;IAC7G,IAAI;IACJ,yBAAyB,IAAI,EAAE,iDAAiD,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,yBAAyB,IAAI,EAAE,iDAAiD,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,CAAC,mBAAqB,aAAa;AACvR;AACA,MAAM;IACF,YAAY,MAAM,CAAE;QAChB,iDAAiD,GAAG,CAAC,IAAI;QACzD,iDAAiD,GAAG,CAAC,IAAI,EAAE,CAAC;QAC5D,+CAA+C,GAAG,CAAC,IAAI,EAAE,UAAU,0BAA0B;QAC7F,4CAA4C,GAAG,CAAC,IAAI,EAAE;QACtD,2CAA2C,GAAG,CAAC,IAAI,EAAE;QACrD,4CAA4C,GAAG,CAAC,IAAI,EAAE;QACtD,mDAAmD,GAAG,CAAC,IAAI,EAAE,KAAK;QAClE,qDAAqD,GAAG,CAAC,IAAI,EAAE,KAAK;QACpE,0DAA0D,GAAG,CAAC,IAAI,EAAE,KAAK;QACzE,kDAAkD,GAAG,CAAC,IAAI,EAAE;QAC5D;;;;SAIC,GACD,4DAA4D,GAAG,CAAC,IAAI,EAAE;QACtE,8CAA8C,GAAG,CAAC,IAAI,EAAE,EAAE;QAC1D,qDAAqD,GAAG,CAAC,IAAI,EAAE,KAAK;QACpE,wDAAwD,GAAG,CAAC,IAAI,EAAE,KAAK;QACvE,wDAAwD,GAAG,CAAC,IAAI,EAAE,KAAK;QACvE,qDAAqD,GAAG,CAAC,IAAI,EAAE,KAAK;QACpE,+CAA+C,GAAG,CAAC,IAAI,EAAE,KAAK;QAC9D,0CAA0C,GAAG,CAAC,IAAI,EAAE,CAAC,OAAO;YACxD,IAAI;YACJ,CAAC,CAAC,KAAK,yBAAyB,IAAI,EAAE,kDAAkD,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,yBAAyB,IAAI,EAAE,kDAAkD,IAAI,CAAC,MAAM,GAAG;gBAAC;aAAS;YAC1Q,OAAO,IAAM,yBAAyB,IAAI,EAAE,kDAAkD,KAAK,4CAA4C,IAAI,CAAC,IAAI,EAAE,OAAO;QACrK;QACA,+CAA+C,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAK,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAC1G,IAAI,yBAAyB,IAAI,EAAE,mDAAmD,QAAQ,IAAI,CAAC,SAAS,EAAE;oBAC1G,OAAO;wBAAE,UAAU,IAAI,CAAC,QAAQ;oBAAC;gBACrC;gBACA,yBAAyB,IAAI,EAAE,mDAAmD,MAAM;gBACxF,IAAI;oBACA,MAAM,yBAAyB,IAAI,EAAE,6DAA6D,KAAK,IAAI,CAAC,IAAI;gBACpH,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD,SACQ;oBACJ,yBAAyB,IAAI,EAAE,mDAAmD,OAAO;gBAC7F;gBACA,OAAO;oBAAE,UAAU,IAAI,CAAC,QAAQ;gBAAC;YACrC;QACA,4DAA4D,GAAG,CAAC,IAAI,EAAE,CAAC,gBAAkB,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACrH,IAAI;oBACA,MAAM,4BAA4B,MAAM,yBAAyB,IAAI,EAAE,2DAA2D,KAAK,GAAG;oBAC1I,IAAI,2BAA2B;wBAC3B,8EAA8E;wBAC9E,yBAAyB,IAAI,EAAE,kEAAkE,KAAK,IAAI,CAAC,IAAI,EAAE;wBACjH,OAAO;oBACX;oBACA,IAAI,yBAAyB,IAAI,EAAE,gDAAgD,MAC/E,yBAAyB,IAAI,EAAE,gDAAgD,WAAW;oBAC9F,MAAM,gBAAgB,MAAM,yBAAyB,IAAI,EAAE,sDAAsD,KAAK,MAAM,CAAC,yBAAyB,IAAI,EAAE,+CAA+C;oBAC3M,OAAO,MAAM,yBAAyB,IAAI,EAAE,iDAAiD,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4BACrJ,MAAM,CAAC,cAAc,uBAAuB,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAC7D,OAAO,eAAe;gCACtB,OAAO,SAAS,CAAC;oCACb,OAAO;oCACP,UAAU,yBAAyB,IAAI,EAAE,oDAAoD;oCAC7F,iBAAiB;gCACrB;6BACH;4BACD,MAAM,WAAW,yBAAyB,IAAI,EAAE,yEAAyE,KAAK,IAAI,CAAC,IAAI,EAAE,uBAAuB,QAAQ;4BACxK,MAAM,sBAAsB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,yBAAyB;gCAAE;gCAAU,OAAO;4BAAc;4BACtH,8EAA8E;4BAC9E,QAAQ,GAAG,CAAC;gCACR,yBAAyB,IAAI,EAAE,uEAAuE,KAAK,IAAI,CAAC,IAAI,EAAE;gCACtH,yBAAyB,IAAI,EAAE,2DAA2D,KAAK,GAAG,CAAC;gCACnG,yBAAyB,IAAI,EAAE,kEAAkE,KAAK,IAAI,CAAC,IAAI,EAAE;6BACpH;4BACD,OAAO;wBACX;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,iEAAiE,GAAG,CAAC,IAAI,EAAE,CAAC,gBAAkB,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAC1H,IAAI;gBACJ,MAAM,sBACN,mDAAmD;gBACnD,yBAAyB,IAAI,EAAE,sDAAsD,QAAQ,QACzF,qDAAqD;gBACrD,CAAC,CAAC,KAAK,yBAAyB,IAAI,EAAE,sDAAsD,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,MAAM,MAAM,cAAc,QAAQ,CAAC,MAAM,IAC5L,+FAA+F;gBAC/F,yBAAyB,IAAI,EAAE,sDAAsD,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,SAAS,KAAO,QAAQ,OAAO,KAAK,cAAc,QAAQ,CAAC,GAAG,CAAC,OAAO;gBACnL,yBAAyB,IAAI,EAAE,sDAAsD,eAAe;gBACpG,IAAI,qBAAqB;oBACrB,yBAAyB,IAAI,EAAE,kDAAkD,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,UAAU;wBAAE,UAAU,IAAI,CAAC,QAAQ;oBAAC;gBACtL;YACJ;QACA,sEAAsE,GAAG,CAAC,IAAI,EAAE,CAAC,eAAiB,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAC9H,iFAAiF;gBACjF,MAAM,0BAA0B,aAAa,QAAQ,CAAC,QAAQ,CAAC,4BAA4B,0BAA0B;gBACrH,MAAM,iCAAiC,aAAa,mCAAmC,IACnF,aAAa,QAAQ,CAAC,QAAQ,CAAC;gBACnC,MAAM,wBAAwB,qMAAA,CAAA,+BAA4B,IAAI,IAAI,CAAC,QAAQ,KAAK,kCAC5E,8LAAA,CAAA,wBAAqB,IAAI,IAAI,CAAC,QAAQ,KAAK;gBAC/C,yBAAyB,IAAI,EAAE,yDAAyD,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAI,kCAAkC;oBACvJ,CAAC,qMAAA,CAAA,+BAA4B,CAAC,EAAE;wBAC5B,SAAS;wBACT,8BAA8B,aAAa,8BAA8B;wBACzE,wBAAwB,yBAAyB,IAAI,EAAE,+DAA+D;oBAC1H;gBACJ,IAAM,2BAA2B;oBAC7B,CAAC,8LAAA,CAAA,wBAAqB,CAAC,EAAE;wBACrB,SAAS;wBACT,8BAA8B,aAAa,8BAA8B;wBACzE,iBAAiB,yBAAyB,IAAI,EAAE,wDAAwD;oBAC5G;gBACJ,IAAK;gBACL,IAAI,uBAAuB;oBACvB,yBAAyB,IAAI,EAAE,kDAAkD,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,UAAU;wBAAE,UAAU,IAAI,CAAC,QAAQ;oBAAC;gBACtL;YACJ;QACA,8DAA8D,GAAG,CAAC,IAAI,EAAE,CAAC,QAAQ,WAAW,QAAU,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAClI,IAAI;oBACA,MAAM,yBAAyB,MAAM,OAAO,SAAS,CAAC;wBAClD,YAAY;wBACZ,UAAU,yBAAyB,IAAI,EAAE,oDAAoD;oBACjG;oBACA,MAAM,WAAW,yBAAyB,IAAI,EAAE,yEAAyE,KAAK,IAAI,CAAC,IAAI,EAAE,uBAAuB,QAAQ;oBACxK,MAAM,gBAAgB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,yBAAyB;wBAAE,UAAU;wBAAU,OAAO;oBAAM;oBAClH,8EAA8E;oBAC9E,QAAQ,GAAG,CAAC;wBACR,yBAAyB,IAAI,EAAE,2DAA2D,KAAK,GAAG,CAAC;wBACnG,yBAAyB,IAAI,EAAE,kEAAkE,KAAK,IAAI,CAAC,IAAI,EAAE;qBACpH;gBACL,EACA,OAAO,GAAG;oBACN,yBAAyB,IAAI,EAAE,mDAAmD,KAAK,IAAI,CAAC,IAAI;oBAChG,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,kDAAkD,GAAG,CAAC,IAAI,EAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAC9F,IAAI;gBACJ,IAAI;gBACJ,CAAC,KAAK,yBAAyB,IAAI,EAAE,gDAAgD,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;gBACxI,yBAAyB,IAAI,EAAE,2DAA2D,KAAK,KAAK,IAAI,8EAA8E;gBACtL,yBAAyB,IAAI,EAAE,mDAAmD,OAAO;gBACzF,yBAAyB,IAAI,EAAE,6DAA6D,CAAC,KAAK,yBAAyB,IAAI,EAAE,6DAA6D,MAAM,MAAM,EAAE,GAAG;gBAC/M,yBAAyB,IAAI,EAAE,sDAAsD,WAAW;gBAChG,yBAAyB,IAAI,EAAE,gDAAgD,WAAW;gBAC1F,yBAAyB,IAAI,EAAE,kDAAkD,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,UAAU;oBAAE,UAAU,IAAI,CAAC,QAAQ;gBAAC;YACtL;QACA,gDAAgD,GAAG,CAAC,IAAI,EAAE,CAAC,WAAa,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACpG,IAAI;gBACJ,MAAM,gBAAgB,CAAC,KAAK,yBAAyB,IAAI,EAAE,sDAAsD,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe;gBAC9K,MAAM,aAAa,gBAAgB;oBAAE,SAAS;gBAAc,IAAI;gBAChE,MAAM,eAAe,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,aAAa;oBAAE,qBAAqB,yBAAyB,IAAI,EAAE,sDAAsD;gBAAK;gBACnL,MAAM,8BAA8B,yBAAyB,IAAI,EAAE,6DAA6D;gBAChI,MAAM,QAAQ,IAAI;gBAClB,IAAI,yBAAyB,IAAI,EAAE,gDAAgD,MAAM;oBACrF,OAAO,SAAS,yBAAyB,IAAI,EAAE,gDAAgD,KAAK,MAAM;gBAC9G;gBACA,IAAI;oBACA,MAAM,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,iNAAA,CAAA,sBAAmB,AAAD,EAAE;oBACpE,MAAM,sBAAsB,MAAM,gBAAgB,CAAC,SAAS,CAAC;wBACzD,IAAI,OACA;oBACR;oBACA,MAAM,UAAU,CAAC,eAAe,QAAQ;oBACxC,MAAM,IAAI;oBACV,yBAAyB,IAAI,EAAE,gDAAgD;wBAAE;wBAAO,QAAQ,MAAM;oBAAO,GAAG;oBAChH;oBACA,MAAM,KAAK;oBACX,OAAO,MAAM,SAAS,yBAAyB,IAAI,EAAE,gDAAgD,KAAK,MAAM;gBACpH,EACA,OAAO,GAAG;oBACN,MAAM,KAAK;oBACX,IAAI,yBAAyB,IAAI,EAAE,6DAA6D,SAAS,6BAA6B;wBAClI,MAAM,IAAI,QAAQ,KAAQ,IAAI,iBAAiB;oBACnD;oBACA,IAAI,aAAa,SACb,EAAE,IAAI,KAAK,oCACX,EAAE,IAAI,KAAK,0BAA0B;wBACrC,MAAM,yBAAyB,IAAI,EAAE,yDAAyD,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI;oBACtH;oBACA,MAAM;gBACV;YACJ;QACA,0DAA0D,GAAG,CAAC,IAAI,EAAE;YAChE,IAAI,CAAC,yBAAyB,IAAI,EAAE,sDAAsD,MACtF,MAAM,IAAI,MAAM;YACpB,OAAO;gBAAE,WAAW,yBAAyB,IAAI,EAAE,sDAAsD,KAAK,UAAU;gBAAE,OAAO,yBAAyB,IAAI,EAAE,sDAAsD,KAAK,KAAK;YAAC;QACrO;QACA,wEAAwE,GAAG,CAAC,IAAI,EAAE,CAAC;YAC/E,OAAO,SAAS,GAAG,CAAC,CAAC;gBACjB,IAAI,IAAI;gBACR,MAAM,YAAY,aAAa,QAAQ,OAAO;gBAC9C,OAAO;oBACH,SAAS,gIAAA,CAAA,UAAM,CAAC,MAAM,CAAC;oBACvB;oBACA,OAAO,QAAQ,KAAK;oBACpB,MAAM,QAAQ,IAAI;oBAClB,QAAQ,CAAC,KAAK,QAAQ,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,yBAAyB,IAAI,EAAE,+CAA+C;oBAC7I,yDAAyD;oBACzD,UAAU,CAAC,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;gBACvE;YACJ;QACJ;QACA,+DAA+D,GAAG,CAAC,IAAI,EAAE,CAAC,eAAiB,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACvH,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,yBAAyB,IAAI,EAAE,2DAA2D,KAAK,IAAI,CAAC,IAAI;gBACrI,IAAI;oBACA,OAAO,MAAM,yBAAyB,IAAI,EAAE,iDAAiD,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4BACrJ,MAAM,yBAAyB,IAAI,EAAE,+DAA+D,KAAK,IAAI,CAAC,IAAI,EAAE,QAAQ,WAAW;4BACvI,MAAM,qBAAqB,MAAM,OAAO,gBAAgB,CAAC;gCACrD;4BACJ;4BACA,OAAO;wBACX;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,qEAAqE,GAAG,CAAC,IAAI,EAAE,CAAC,aAAa,UAAY,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACrI,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,yBAAyB,IAAI,EAAE,2DAA2D,KAAK,IAAI,CAAC,IAAI;gBACrI,IAAI;oBACA,OAAO,MAAM,yBAAyB,IAAI,EAAE,iDAAiD,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4BACrJ,MAAM,CAAC,cAAc,GAAG,GAAG,MAAM,QAAQ,GAAG,CAAC;gCACzC,OAAO,eAAe;gCACtB,yBAAyB,IAAI,EAAE,+DAA+D,KAAK,IAAI,CAAC,IAAI,EAAE,QAAQ,WAAW;6BACpI;4BACD,IAAI,aAAa,mCAAmC,EAAE;gCAClD,MAAM,aAAa,MAAM,OAAO,uBAAuB,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;oCAAE,cAAc;wCAAC;qCAAY;gCAAC;gCAChI,OAAO,UAAU,CAAC,EAAE;4BACxB,OACK;gCACD,MAAM,IAAI,MAAM;4BACpB;wBACJ;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,8DAA8D,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACnH,MAAM,UAAU,EAAE;gBAClB,KAAK,MAAM,SAAS,OAAQ;oBACxB,MAAM,cAAc,2KAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC,MAAM,WAAW;oBACtE,MAAM,YAAa,MAAM,yBAAyB,IAAI,EAAE,sEAAsE,KAAK,IAAI,CAAC,IAAI,EAAE,aAAa,MAAM,OAAO;oBACxK,QAAQ,IAAI,CAAC;wBAAE,WAAW,gIAAA,CAAA,UAAM,CAAC,MAAM,CAAC;oBAAW;gBACvD;gBACA,OAAO;YACX;QACA,uDAAuD,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBAC5G,MAAM,eAAe,OAAO,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,GAAK,2KAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC;gBACtF,MAAM,qBAAqB,MAAM,yBAAyB,IAAI,EAAE,gEAAgE,KAAK,IAAI,CAAC,IAAI,EAAE;gBAChJ,OAAO,mBAAmB,GAAG,CAAC,CAAC;oBAC3B,MAAM,wBAAwB,uBAAuB,qBAC/C,kBAAkB,SAAS,KAC3B,IAAI,WAAW,kBAAkB,SAAS,CAAC;wBACzC,sBAAsB;wBACtB,kBAAkB;oBACtB;oBACJ,OAAO;wBAAE,mBAAmB;oBAAsB;gBACtD;YACJ;QACA,mDAAmD,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACxG,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,yBAAyB,IAAI,EAAE,2DAA2D,KAAK,IAAI,CAAC,IAAI;gBACrI,MAAM,YAAY,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,GAAK,eAAe,QAAQ,SAAS;gBAC9E,MAAM,WAAW,OAAO,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,GAAK;gBAC7C,IAAI;oBACA,OAAO,MAAM,yBAAyB,IAAI,EAAE,iDAAiD,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;4BACrJ,MAAM,yBAAyB,IAAI,EAAE,+DAA+D,KAAK,IAAI,CAAC,IAAI,EAAE,QAAQ,WAAW;4BACvI,MAAM,iBAAiB,MAAM,OAAO,YAAY,CAAC;gCAC7C,WAAW;gCACX,UAAU;4BACd;4BACA,OAAO,eAAe,GAAG,CAAC,CAAC;gCACvB,OAAO;oCAAE,eAAe;oCAAe,WAAW,cAAc,KAAK,CAAC,CAAC;gCAA2B;4BACtG;wBACJ;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD;YACJ;QACA,8CAA8C,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAW,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACnG,MAAM,UAAU,EAAE;gBAClB,IAAI,OAAO,MAAM,GAAG,GAAG;oBACnB,KAAK,MAAM,SAAS,OAAQ;wBACxB,QAAQ,IAAI,CAAC,CAAA,MAAM,yBAAyB,IAAI,EAAE,sDAAsD,KAAK,IAAI,CAAC,IAAI,EAAE,MAAK;oBACjI;gBACJ,OACK;oBACD,OAAO;wBAAC,CAAA,MAAM,yBAAyB,IAAI,EAAE,sDAAsD,KAAK,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAA;qBAAE;gBAClI;gBACA,OAAO;YACX;QACA,qDAAqD,GAAG,CAAC,IAAI,EAAE,CAAC,QAAU,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACtG,IAAI,IAAI;gBACR,yBAAyB,IAAI,EAAE,mDAAmD,MAAM;gBACxF,IAAI;oBACA,MAAM,sBAAsB,MAAM,yBAAyB,IAAI,EAAE,6DAA6D,KAAK,IAAI,CAAC,IAAI,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ;wBAAE,QAAQ,CAAC,KAAK,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,QAAQ,CAAC,IAAI;oBAAC;oBACxT,IAAI,CAAC,oBAAoB,cAAc,EAAE;wBACrC,MAAM,IAAI,MAAM;oBACpB;oBACA,MAAM,kBAAkB,oBAAoB,cAAc,CAAC,OAAO;oBAClE,MAAM,kBAAkB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,oBAAoB,QAAQ,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,OAAO,IAAI,gBAAgB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBACrK,SAAS;oBACb,IAAI;wBAAE,WAAW,aAAa;oBAAiB;oBAC/C,OAAO;wBACH,SAAS;wBACT,eAAe,aAAa,oBAAoB,cAAc,CAAC,cAAc;wBAC7E,WAAW,aAAa,oBAAoB,cAAc,CAAC,SAAS;oBACxE;gBACJ,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,MAAM,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK;gBACzD,SACQ;oBACJ,yBAAyB,IAAI,EAAE,mDAAmD,OAAO;gBAC7F;YACJ;QACA,yBAAyB,IAAI,EAAE,2DAA2D,OAAO,kBAAkB,EAAE;QACrH,yBAAyB,IAAI,EAAE,oDAAoD,OAAO,WAAW,EAAE;QACvG,yBAAyB,IAAI,EAAE,+CAA+C,OAAO,MAAM,EAAE;QAC7F,yBAAyB,IAAI,EAAE,sDAAsD,OAAO,aAAa,EAAE;QAC3G,yBAAyB,IAAI,EAAE,sDAAsD,OAAO,mBAAmB,EAAE;QACjH,yBAAyB,IAAI,EAAE,yDAAyD,OAAO,gBAAgB,EAAE;QACjH,yBAAyB,IAAI,EAAE,yDAAyD;YACpF,wFAAwF;YACxF,8FAA8F;YAC9F,8FAA8F;YAC9F,kGAAkG;YAClG,CAAC,qMAAA,CAAA,+BAA4B,CAAC,EAAE;gBAC5B,SAAS;gBACT,8BAA8B;oBAAC;oBAAU;iBAAE;gBAC3C,wBAAwB,yBAAyB,IAAI,EAAE,+DAA+D;YAC1H;QACJ,GAAG;IACP;IACA,IAAI,UAAU;QACV,OAAO,yBAAyB,IAAI,EAAE,gDAAgD;IAC1F;IACA,IAAI,OAAO;QACP,OAAO,yBAAyB,IAAI,EAAE,6CAA6C;IACvF;IACA,IAAI,MAAM;QACN,OAAO,yBAAyB,IAAI,EAAE,4CAA4C;IACtF;IACA,IAAI,OAAO;QACP,OAAO,yBAAyB,IAAI,EAAE,6CAA6C;IACvF;IACA,IAAI,SAAS;QACT,OAAO,yBAAyB,IAAI,EAAE,+CAA+C;IACzF;IACA,IAAI,WAAW;QACX,OAAO,OAAO,MAAM,CAAC;YAAE,CAAC,4KAAA,CAAA,kBAAe,CAAC,EAAE;gBAClC,SAAS;gBACT,SAAS,yBAAyB,IAAI,EAAE,gDAAgD;YAC5F;YAAG,CAAC,+KAAA,CAAA,qBAAkB,CAAC,EAAE;gBACrB,SAAS;gBACT,YAAY,yBAAyB,IAAI,EAAE,mDAAmD;YAClG;YAAG,CAAC,2KAAA,CAAA,iBAAc,CAAC,EAAE;gBACjB,SAAS;gBACT,IAAI,yBAAyB,IAAI,EAAE,2CAA2C;YAClF;YAAG,CAAC,0LAAA,CAAA,oBAAiB,CAAC,EAAE;gBACpB,SAAS;gBACT,aAAa,yBAAyB,IAAI,EAAE,oDAAoD;YACpG;YAAG,CAAC,qLAAA,CAAA,eAAY,CAAC,EAAE;gBACf,SAAS;gBACT,QAAQ,yBAAyB,IAAI,EAAE,+CAA+C;YAC1F;QAAE,GAAG,yBAAyB,IAAI,EAAE,yDAAyD;IACrG;IACA,IAAI,WAAW;QACX,IAAI,IAAI;QACR,OAAO,CAAC,KAAK,CAAC,KAAK,yBAAyB,IAAI,EAAE,sDAAsD,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE;IACvM;IACA,IAAI,YAAY;QACZ,OAAO,CAAC,CAAC,yBAAyB,IAAI,EAAE,gDAAgD,QAAQ,CAAC,CAAC,yBAAyB,IAAI,EAAE,sDAAsD;IAC3L;IACA,IAAI,eAAe;QACf,OAAO,CAAC,CAAC,yBAAyB,IAAI,EAAE,sDAAsD;IAClG;IACA,IAAI,uBAAuB;QACvB,OAAO,yBAAyB,IAAI,EAAE,sDAAsD;IAChG;IACA,IAAI,4BAA4B;QAC5B,OAAO,yBAAyB,IAAI,EAAE,2DAA2D,KAAK,GAAG;IAC7G;AACJ;AACA,mDAAmD,IAAI,WAAW,iDAAiD,IAAI,WAAW,8CAA8C,IAAI,WAAW,6CAA6C,IAAI,WAAW,8CAA8C,IAAI,WAAW,qDAAqD,IAAI,WAAW,uDAAuD,IAAI,WAAW,4DAA4D,IAAI,WAAW,oDAAoD,IAAI,WAAW,8DAA8D,IAAI,WAAW,gDAAgD,IAAI,WAAW,uDAAuD,IAAI,WAAW,0DAA0D,IAAI,WAAW,0DAA0D,IAAI,WAAW,uDAAuD,IAAI,WAAW,iDAAiD,IAAI,WAAW,4CAA4C,IAAI,WAAW,iDAAiD,IAAI,WAAW,8DAA8D,IAAI,WAAW,mEAAmE,IAAI,WAAW,wEAAwE,IAAI,WAAW,gEAAgE,IAAI,WAAW,oDAAoD,IAAI,WAAW,kDAAkD,IAAI,WAAW,4DAA4D,IAAI,WAAW,0EAA0E,IAAI,WAAW,iEAAiE,IAAI,WAAW,uEAAuE,IAAI,WAAW,gEAAgE,IAAI,WAAW,yDAAyD,IAAI,WAAW,qDAAqD,IAAI,WAAW,gDAAgD,IAAI,WAAW,uDAAuD,IAAI,WAAW,mDAAmD,IAAI,WAAW,8CAA8C,SAAS,4CAA4C,KAAK,EAAE,GAAG,IAAI;IACv9E,IAAI;IACJ,yCAAyC;IACzC,CAAC,KAAK,yBAAyB,IAAI,EAAE,kDAAkD,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,CAAC,WAAa,SAAS,KAAK,CAAC,MAAM;AAC3L,GAAG,6CAA6C,SAAS,2CAA2C,KAAK,EAAE,QAAQ;IAC/G,IAAI;IACJ,yBAAyB,IAAI,EAAE,kDAAkD,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,yBAAyB,IAAI,EAAE,kDAAkD,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,CAAC,mBAAqB,aAAa;AACzR;AAEA,IAAI,yBAAyB,AAAC,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACrH,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AACA,IAAI,yBAAyB,AAAC,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC9G,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AACA,IAAI;AACJ;;;;;;;;;;;;;;;CAeC,GACD,SAAS,eAAe,MAAM;IAC1B,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAE,GAAK,SAAS;IAC5C,IAAI;QACA,OAAO,aAAa,CAAC,IAAI,oBAAoB;IACjD,EACA,OAAO,OAAO;QACV,QAAQ,KAAK,CAAC,mEAAmE;IACrF;IACA,IAAI;QACA,OAAO,gBAAgB,CAAC,6BAA6B,CAAC,EAAE,QAAQ,GAAG,EAAE,GAAK,SAAS;IACvF,EACA,OAAO,OAAO;QACV,QAAQ,KAAK,CAAC,iEAAiE;IACnF;AACJ;AACA,MAAM,4BAA4B;IAC9B,YAAY,QAAQ,CAAE;QAClB,KAAK,CAAC,mCAAmC;YACrC,SAAS;YACT,YAAY;YACZ,UAAU;QACd;QACA,4BAA4B,GAAG,CAAC,IAAI,EAAE,KAAK;QAC3C,uBAAuB,IAAI,EAAE,6BAA6B,UAAU;IACxE;IACA,IAAI,SAAS;QACT,OAAO,uBAAuB,IAAI,EAAE,6BAA6B;IACrE;IACA,IAAI,OAAO;QACP,OAAO;IACX;IACA,gBAAgB,GAChB,iBAAiB;QACb,MAAM,IAAI,MAAM;IACpB;IACA,gBAAgB,GAChB,2BAA2B;QACvB,MAAM,IAAI,MAAM;IACpB;IACA,gBAAgB,GAChB,kBAAkB;QACd,MAAM,IAAI,MAAM;IACpB;AACJ;AACA,8BAA8B,IAAI;AAEjC,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACxF,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AACC,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACjF,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AAEA,SAAS;IACL,OAAQ,OAAO,WAAW,eACtB,OAAO,eAAe,IACtB,OAAO,aAAa,eACpB,WAAW,IAAI,CAAC,UAAU,SAAS;AAC3C;AACA,SAAS;IACL,OAAQ,OAAO,WAAW,eACtB,OAAO,eAAe,IACtB,OAAO,aAAa,eACpB,CAAC,iEAAiE,IAAI,CAAC,UAAU,SAAS;AAClG;AAEA,SAAS,YAAY,MAAM;IACvB,IAAI,kCAAkC;QAClC,eAAe,IAAI,qCAAqC;IAC5D,OACK,IAAI,qCAAqC,OAAO,mBAAmB,KAAK,WAAW;QACpF,eAAe,IAAI,sCAAsC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;YAAE,qBAAqB,OAAO,mBAAmB;QAAC;IACxJ;AAEJ;AAEA,MAAM,iCAAiC;AACvC,MAAM,sCAAsC;AAC5C,MAAM,mBAAmB;IACrB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,cAAc,KAAK,EAAE;QACjB,KAAK,CAAC;QACN,IAAI,CAAC,aAAa,CAAC;IACvB;IACA,cAAc,KAAK,EAAE;QACjB,IAAI,IAAI;QACR,MAAM,sBAAsB,CAAC,KAAK,IAAI,CAAC,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,cAAc,CAAC;QACnG,MAAM,YAAY,CAAC,KAAK,IAAI,CAAC,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,cAAc,CAAC;QACzF,IAAI,qBAAqB;YACrB,IAAI,MAAM,IAAI,KAAK,kCAAkC;gBACjD,OAAQ,MAAM,IAAI;oBACd,KAAK;wBACD,oBAAoB,SAAS,GAAG;wBAChC,IAAI,WACA,UAAU,gBAAgB,CAAC,SAAS;4BAChC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBAC3B;wBACJ;oBACJ,KAAK;wBACD,oBAAoB,SAAS,GAAG;wBAChC,IAAI,WACA,UAAU,KAAK,CAAC,OAAO,GAAG;wBAC9B;gBACR;YACJ;YACA,oBAAoB,SAAS,GAAG,CAAC,8BAA8B,EAAE,MAAM,OAAO,EAAE;QACpF,OACK;YACD,QAAQ,GAAG,CAAC;QAChB;IACJ;AACJ;AACA,MAAM,kBAAkB,CAAC;;;;;;;;;AASzB,CAAC;AACD,MAAM,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDb,CAAC;AAED,SAAS;IACL,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,OAAO,WAAW,aAAa;YAC/B,MAAM,YAAY,OAAO,SAAS,CAAC,SAAS,CAAC,WAAW;YACxD,MAAM,cAAc,IAAI;YACxB,IAAI,UAAU,QAAQ,CAAC,OAAO;gBAC1B,6DAA6D;gBAC7D,6BAA6B;gBAC7B,0CAA0C;gBAC1C,2EAA2E;gBAC3E,aAAa;gBACb,QAAQ;gBACR,KAAK;gBACL,oGAAoG;gBACpG,YAAY,aAAa,CAAC;oBACtB,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;YACJ,OACK;gBACD,6BAA6B;gBAC7B,0CAA0C;gBAC1C,sEAAsE;gBACtE,aAAa;gBACb,QAAQ;gBACR,KAAK;gBACL,oGAAoG;gBACpG,YAAY,aAAa,CAAC;oBACtB,MAAM;oBACN,MAAM;oBACN,SAAS;gBACb;YACJ;YACA,YAAY,IAAI;QACpB;IACJ;AACJ;AACA,SAAS;IACL,OAAO,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YAAe;QAA0C;AAC1G;AAEA,MAAM,YAAY;AAClB,SAAS;IACL,IAAI;IACJ,IAAI;QACA,UAAU,OAAO,YAAY;IAC7B,oCAAoC;IACxC,EACA,OAAO,IAAI,CAAE;IACb,OAAO;QACH;YACI,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACnC,IAAI,CAAC,SAAS;oBACV;gBACJ;gBACA,IAAI;oBACA,QAAQ,UAAU,CAAC;gBACnB,oCAAoC;gBACxC,EACA,OAAO,IAAI,CAAE;YACjB;QACJ;QACA;YACI,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACnC,IAAI,CAAC,SAAS;oBACV;gBACJ;gBACA,IAAI;oBACA,MAAM,SAAS,KAAK,KAAK,CAAC,QAAQ,OAAO,CAAC;oBAC1C,IAAI,UAAU,OAAO,QAAQ,EAAE;wBAC3B,MAAM,iBAAiB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;4BACxC,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;gCAAE,WAAW,eAAe,UACnE,IAAI,WAAW,OAAO,MAAM,CAAC,QAAQ,SAAS,GAAG,sCAAsC;mCACvF,IAAI,2KAAA,CAAA,YAAS,CAAC,QAAQ,OAAO,EAAE,OAAO;4BAAG;wBACvD;wBACA,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;4BAAE,UAAU;wBAAe;oBAC/E,OAEI,OAAO,UAAU;gBACrB,oCAAoC;gBACxC,EACA,OAAO,IAAI,CAAE;YACjB;QACJ;QACA,KAAI,mBAAmB;YACnB,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACnC,IAAI,CAAC,SAAS;oBACV;gBACJ;gBACA,IAAI;oBACA,QAAQ,OAAO,CAAC,WAAW,KAAK,SAAS,CAAC;gBAC1C,oCAAoC;gBACxC,EACA,OAAO,IAAI,CAAE;YACjB;QACJ;IACJ;AACJ;AAEA,SAAS;IACL,OAAO;QACH,QAAO,MAAM;YACT,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACnC,IAAI,OAAO,MAAM,KAAK,GAAG;oBACrB,OAAO,MAAM,CAAC,EAAE;gBACpB,OACK,IAAI,OAAO,QAAQ,CAAC,kLAAA,CAAA,uBAAoB,GAAG;oBAC5C,OAAO,kLAAA,CAAA,uBAAoB;gBAC/B,OAEI,OAAO,MAAM,CAAC,EAAE;YACxB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solana-mobile/wallet-adapter-mobile/lib/esm/index.js"], "sourcesContent": ["import { BaseSignInMessageSignerWalletAdapter, WalletReadyState, WalletPublicKeyError, WalletConnectionError, WalletNotReadyError, WalletSignMessageError, WalletSendTransactionError, WalletSignTransactionError, WalletNotConnectedError } from '@solana/wallet-adapter-base';\nimport { PublicKey, VersionedMessage, Transaction, VersionedTransaction } from '@solana/web3.js';\nimport { SolanaSignIn, SolanaSignMessage, SolanaSignAndSendTransaction, SolanaSignTransaction } from '@solana/wallet-standard-features';\nimport { LocalSolanaMobileWalletAdapterWallet, createDefaultChainSelector, RemoteSolanaMobileWalletAdapterWallet, createDefaultAuthorizationCache, defaultErrorModalWalletNotFoundHandler } from '@solana-mobile/wallet-standard-mobile';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\n\n(undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\n(undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\n/** Name of the feature. */\nconst StandardConnect = 'standard:connect';\n\n/** Name of the feature. */\nconst StandardDisconnect = 'standard:disconnect';\n\n/** Name of the feature. */\nconst StandardEvents = 'standard:events';\n\n(undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\n(undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\n(undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\n(undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\n\nfunction fromUint8Array(byteArray) {\n    return window.btoa(String.fromCharCode.call(null, ...byteArray));\n}\n\nfunction getIsSupported() {\n    return (typeof window !== 'undefined' &&\n        window.isSecureContext &&\n        typeof document !== 'undefined' &&\n        /android/i.test(navigator.userAgent));\n}\n\nvar _BaseSolanaMobileWalletAdapter_instances, _BaseSolanaMobileWalletAdapter_wallet, _BaseSolanaMobileWalletAdapter_connecting, _BaseSolanaMobileWalletAdapter_readyState, _BaseSolanaMobileWalletAdapter_accountSelector, _BaseSolanaMobileWalletAdapter_selectedAccount, _BaseSolanaMobileWalletAdapter_publicKey, _BaseSolanaMobileWalletAdapter_handleChangeEvent, _BaseSolanaMobileWalletAdapter_connect, _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled, _BaseSolanaMobileWalletAdapter_assertIsAuthorized, _BaseSolanaMobileWalletAdapter_performSignTransactions, _BaseSolanaMobileWalletAdapter_runWithGuard;\nconst SolanaMobileWalletAdapterWalletName = 'Mobile Wallet Adapter';\nconst SIGNATURE_LENGTH_IN_BYTES = 64;\nfunction isVersionedTransaction(transaction) {\n    return 'version' in transaction;\n}\nfunction chainOrClusterToChainId(chain) {\n    switch (chain) {\n        case 'mainnet-beta':\n            return 'solana:mainnet';\n        case 'testnet':\n            return 'solana:testnet';\n        case 'devnet':\n            return 'solana:devnet';\n        default:\n            return chain;\n    }\n}\nclass BaseSolanaMobileWalletAdapter extends BaseSignInMessageSignerWalletAdapter {\n    constructor(wallet, config) {\n        super();\n        _BaseSolanaMobileWalletAdapter_instances.add(this);\n        this.supportedTransactionVersions = new Set(\n        // FIXME(#244): We can't actually know what versions are supported until we know which wallet we're talking to.\n        ['legacy', 0]);\n        _BaseSolanaMobileWalletAdapter_wallet.set(this, void 0);\n        _BaseSolanaMobileWalletAdapter_connecting.set(this, false);\n        _BaseSolanaMobileWalletAdapter_readyState.set(this, getIsSupported() ? WalletReadyState.Loadable : WalletReadyState.Unsupported);\n        _BaseSolanaMobileWalletAdapter_accountSelector.set(this, void 0);\n        _BaseSolanaMobileWalletAdapter_selectedAccount.set(this, void 0);\n        _BaseSolanaMobileWalletAdapter_publicKey.set(this, void 0);\n        _BaseSolanaMobileWalletAdapter_handleChangeEvent.set(this, (properties) => __awaiter(this, void 0, void 0, function* () {\n            if (properties.accounts && properties.accounts.length > 0) {\n                __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled).call(this);\n                const nextSelectedAccount = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_accountSelector, \"f\").call(this, properties.accounts);\n                if (nextSelectedAccount !== __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\")) {\n                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, nextSelectedAccount, \"f\");\n                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_publicKey, undefined, \"f\");\n                    this.emit('connect', \n                    // Having just set `this.#selectedAccount`, `this.publicKey` is definitely non-null\n                    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                    this.publicKey);\n                }\n            }\n        }));\n        // this.#chain = chainOrClusterToChainId(config.chain);\n        __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_accountSelector, (accounts) => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            const selectedBase64EncodedAddress = yield config.addressSelector.select(accounts.map(({ publicKey }) => fromUint8Array(publicKey)));\n            return (_a = accounts.find(({ publicKey }) => fromUint8Array(publicKey) === selectedBase64EncodedAddress)) !== null && _a !== void 0 ? _a : accounts[0];\n        }), \"f\");\n        __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_wallet, wallet, \"f\");\n        __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardEvents].on('change', __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_handleChangeEvent, \"f\"));\n        this.name = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").name;\n        this.icon = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").icon;\n        this.url = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").url;\n        // TODO: evaluate if this logic should be kept - it seems to create a nasty bug where \n        //  the wallet tries to auto connect on page load and gets blocked by the popup blocker\n        // if (this.#readyState !== WalletReadyState.Unsupported) {\n        //     config.authorizationResultCache.get().then((authorizationResult) => {\n        //         if (authorizationResult) {\n        //             // Having a prior authorization result is, right now, the best\n        //             // indication that a mobile wallet is installed. There is no API\n        //             // we can use to test for whether the association URI is supported.\n        //             this.#declareWalletAsInstalled();\n        //         }\n        //     });\n        // }\n    }\n    get publicKey() {\n        var _a;\n        if (!__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_publicKey, \"f\") && __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\")) {\n            try {\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_publicKey, new PublicKey(__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\").publicKey), \"f\");\n            }\n            catch (e) {\n                throw new WalletPublicKeyError((e instanceof Error && (e === null || e === void 0 ? void 0 : e.message)) || 'Unknown error', e);\n            }\n        }\n        return (_a = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_publicKey, \"f\")) !== null && _a !== void 0 ? _a : null;\n    }\n    get connected() {\n        return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").connected;\n    }\n    get connecting() {\n        return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_connecting, \"f\");\n    }\n    get readyState() {\n        return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\");\n    }\n    /** @deprecated Use `autoConnect()` instead. */\n    autoConnect_DO_NOT_USE_OR_YOU_WILL_BE_FIRED() {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield this.autoConnect();\n        });\n    }\n    autoConnect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_connect).call(this, true);\n        });\n    }\n    connect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_connect).call(this);\n        });\n    }\n    /** @deprecated Use `connect()` or `autoConnect()` instead. */\n    performAuthorization(signInPayload) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const cachedAuthorizationResult = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").cachedAuthorizationResult;\n                if (cachedAuthorizationResult) {\n                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardConnect].connect({ silent: true });\n                    return cachedAuthorizationResult;\n                }\n                if (signInPayload) {\n                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignIn].signIn(signInPayload);\n                }\n                else\n                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardConnect].connect();\n                const authorizationResult = yield yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").cachedAuthorizationResult;\n                return authorizationResult;\n            }\n            catch (e) {\n                throw new WalletConnectionError((e instanceof Error && e.message) || 'Unknown error', e);\n            }\n        });\n    }\n    disconnect() {\n        return __awaiter(this, void 0, void 0, function* () {\n            // return await this.#runWithGuard(this.#wallet.features[StandardDisconnect].disconnect);\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, false, \"f\");\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_publicKey, undefined, \"f\");\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, undefined, \"f\");\n                yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardDisconnect].disconnect();\n                this.emit('disconnect');\n            }));\n        });\n    }\n    signIn(input) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                var _a;\n                if (__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Installed && __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Loadable) {\n                    throw new WalletNotReadyError();\n                }\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, true, \"f\");\n                try {\n                    const outputs = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignIn].signIn(Object.assign(Object.assign({}, input), { domain: (_a = input === null || input === void 0 ? void 0 : input.domain) !== null && _a !== void 0 ? _a : window.location.host }));\n                    if (outputs.length > 0) {\n                        return outputs[0];\n                    }\n                    else {\n                        throw new Error(\"Sign in failed, no sign in result returned by wallet\");\n                    }\n                }\n                catch (e) {\n                    throw new WalletConnectionError((e instanceof Error && e.message) || 'Unknown error', e);\n                }\n                finally {\n                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, false, \"f\");\n                }\n            }));\n        });\n    }\n    signMessage(message) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                const account = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);\n                try {\n                    const outputs = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignMessage].signMessage({\n                        account, message: message\n                    });\n                    return outputs[0].signature;\n                }\n                catch (error) {\n                    throw new WalletSignMessageError(error === null || error === void 0 ? void 0 : error.message, error);\n                }\n            }));\n        });\n    }\n    sendTransaction(transaction, connection, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                const account = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);\n                try {\n                    function getTargetCommitment() {\n                        let targetCommitment;\n                        switch (connection.commitment) {\n                            case 'confirmed':\n                            case 'finalized':\n                            case 'processed':\n                                targetCommitment = connection.commitment;\n                                break;\n                            default:\n                                targetCommitment = 'finalized';\n                        }\n                        let targetPreflightCommitment;\n                        switch (options === null || options === void 0 ? void 0 : options.preflightCommitment) {\n                            case 'confirmed':\n                            case 'finalized':\n                            case 'processed':\n                                targetPreflightCommitment = options.preflightCommitment;\n                                break;\n                            case undefined:\n                                targetPreflightCommitment = targetCommitment;\n                                break;\n                            default:\n                                targetPreflightCommitment = 'finalized';\n                        }\n                        const preflightCommitmentScore = targetPreflightCommitment === 'finalized'\n                            ? 2\n                            : targetPreflightCommitment === 'confirmed'\n                                ? 1\n                                : 0;\n                        const targetCommitmentScore = targetCommitment === 'finalized' ? 2 : targetCommitment === 'confirmed' ? 1 : 0;\n                        return preflightCommitmentScore < targetCommitmentScore\n                            ? targetPreflightCommitment\n                            : targetCommitment;\n                    }\n                    if (SolanaSignAndSendTransaction in __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features) {\n                        const chain = chainOrClusterToChainId(__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").currentAuthorization.chain);\n                        const [signature] = (yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignAndSendTransaction].signAndSendTransaction({\n                            account,\n                            transaction: transaction.serialize(),\n                            chain: chain,\n                            options: options ? {\n                                skipPreflight: options.skipPreflight,\n                                maxRetries: options.maxRetries\n                            } : undefined\n                        })).map(((output) => {\n                            return fromUint8Array(output.signature);\n                        }));\n                        return signature;\n                    }\n                    else {\n                        const [signedTransaction] = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, [transaction]);\n                        if (isVersionedTransaction(signedTransaction)) {\n                            return yield connection.sendTransaction(signedTransaction);\n                        }\n                        else {\n                            const serializedTransaction = signedTransaction.serialize();\n                            return yield connection.sendRawTransaction(serializedTransaction, Object.assign(Object.assign({}, options), { preflightCommitment: getTargetCommitment() }));\n                        }\n                    }\n                }\n                catch (error) {\n                    throw new WalletSendTransactionError(error === null || error === void 0 ? void 0 : error.message, error);\n                }\n            }));\n        });\n    }\n    signTransaction(transaction) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                const [signedTransaction] = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, [transaction]);\n                return signedTransaction;\n            }));\n        });\n    }\n    signAllTransactions(transactions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n                const signedTransactions = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, transactions);\n                return signedTransactions;\n            }));\n        });\n    }\n}\n_BaseSolanaMobileWalletAdapter_wallet = new WeakMap(), _BaseSolanaMobileWalletAdapter_connecting = new WeakMap(), _BaseSolanaMobileWalletAdapter_readyState = new WeakMap(), _BaseSolanaMobileWalletAdapter_accountSelector = new WeakMap(), _BaseSolanaMobileWalletAdapter_selectedAccount = new WeakMap(), _BaseSolanaMobileWalletAdapter_publicKey = new WeakMap(), _BaseSolanaMobileWalletAdapter_handleChangeEvent = new WeakMap(), _BaseSolanaMobileWalletAdapter_instances = new WeakSet(), _BaseSolanaMobileWalletAdapter_connect = function _BaseSolanaMobileWalletAdapter_connect(autoConnect = false) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (this.connecting || this.connected) {\n            return;\n        }\n        return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, () => __awaiter(this, void 0, void 0, function* () {\n            if (__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Installed && __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Loadable) {\n                throw new WalletNotReadyError();\n            }\n            __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, true, \"f\");\n            try {\n                yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[StandardConnect].connect({ silent: autoConnect });\n            }\n            catch (e) {\n                throw new WalletConnectionError((e instanceof Error && e.message) || 'Unknown error', e);\n            }\n            finally {\n                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, false, \"f\");\n            }\n        }));\n    });\n}, _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled = function _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled() {\n    if (__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, \"f\") !== WalletReadyState.Installed) {\n        this.emit('readyStateChange', (__classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_readyState, WalletReadyState.Installed, \"f\")));\n    }\n}, _BaseSolanaMobileWalletAdapter_assertIsAuthorized = function _BaseSolanaMobileWalletAdapter_assertIsAuthorized() {\n    if (!__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").isAuthorized || !__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\"))\n        throw new WalletNotConnectedError();\n    return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, \"f\");\n}, _BaseSolanaMobileWalletAdapter_performSignTransactions = function _BaseSolanaMobileWalletAdapter_performSignTransactions(transactions) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const account = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, \"m\", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);\n        try {\n            if (SolanaSignTransaction in __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features) {\n                return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, \"f\").features[SolanaSignTransaction].signTransaction(...transactions.map((value) => {\n                    return { account, transaction: value.serialize() };\n                })).then((outputs) => {\n                    return outputs.map((output) => {\n                        const byteArray = output.signedTransaction;\n                        const numSignatures = byteArray[0];\n                        const messageOffset = numSignatures * SIGNATURE_LENGTH_IN_BYTES + 1;\n                        const version = VersionedMessage.deserializeMessageVersion(byteArray.slice(messageOffset, byteArray.length));\n                        if (version === 'legacy') {\n                            return Transaction.from(byteArray);\n                        }\n                        else {\n                            return VersionedTransaction.deserialize(byteArray);\n                        }\n                    });\n                });\n            }\n            else {\n                throw new Error('Connected wallet does not support signing transactions');\n            }\n        }\n        catch (error) {\n            throw new WalletSignTransactionError(error === null || error === void 0 ? void 0 : error.message, error);\n        }\n    });\n}, _BaseSolanaMobileWalletAdapter_runWithGuard = function _BaseSolanaMobileWalletAdapter_runWithGuard(callback) {\n    return __awaiter(this, void 0, void 0, function* () {\n        try {\n            return yield callback();\n        }\n        catch (e) {\n            this.emit('error', e);\n            throw e;\n        }\n    });\n};\nclass LocalSolanaMobileWalletAdapter extends BaseSolanaMobileWalletAdapter {\n    constructor(config) {\n        var _a;\n        const chain = chainOrClusterToChainId((_a = config.chain) !== null && _a !== void 0 ? _a : config.cluster);\n        super(new LocalSolanaMobileWalletAdapterWallet({\n            appIdentity: config.appIdentity,\n            authorizationCache: {\n                set: config.authorizationResultCache.set,\n                get: () => __awaiter(this, void 0, void 0, function* () {\n                    const authorizationResult = yield config.authorizationResultCache.get();\n                    if (authorizationResult && 'chain' in authorizationResult) {\n                        return authorizationResult;\n                    }\n                    else if (authorizationResult) {\n                        return Object.assign(Object.assign({}, authorizationResult), { chain: chain });\n                    }\n                    else\n                        return undefined;\n                }),\n                clear: config.authorizationResultCache.clear,\n            },\n            chains: [chain],\n            chainSelector: createDefaultChainSelector(),\n            onWalletNotFound: () => __awaiter(this, void 0, void 0, function* () {\n                config.onWalletNotFound(this);\n            }),\n        }), {\n            addressSelector: config.addressSelector,\n            chain: chain,\n        });\n    }\n}\nclass RemoteSolanaMobileWalletAdapter extends BaseSolanaMobileWalletAdapter {\n    constructor(config) {\n        const chain = chainOrClusterToChainId(config.chain);\n        super(new RemoteSolanaMobileWalletAdapterWallet({\n            appIdentity: config.appIdentity,\n            authorizationCache: {\n                set: config.authorizationResultCache.set,\n                get: () => __awaiter(this, void 0, void 0, function* () {\n                    const authorizationResult = yield config.authorizationResultCache.get();\n                    if (authorizationResult && 'chain' in authorizationResult) {\n                        return authorizationResult;\n                    }\n                    else if (authorizationResult) {\n                        return Object.assign(Object.assign({}, authorizationResult), { chain: chain });\n                    }\n                    else\n                        return undefined;\n                }),\n                clear: config.authorizationResultCache.clear,\n            },\n            chains: [chain],\n            chainSelector: createDefaultChainSelector(),\n            remoteHostAuthority: config.remoteHostAuthority,\n            onWalletNotFound: () => __awaiter(this, void 0, void 0, function* () {\n                config.onWalletNotFound(this);\n            }),\n        }), {\n            addressSelector: config.addressSelector,\n            chain: chain,\n        });\n    }\n}\nclass SolanaMobileWalletAdapter extends LocalSolanaMobileWalletAdapter {\n}\n\nfunction createDefaultAddressSelector() {\n    return {\n        select(addresses) {\n            return __awaiter(this, void 0, void 0, function* () {\n                return addresses[0];\n            });\n        },\n    };\n}\n\nfunction createDefaultAuthorizationResultCache() {\n    return createDefaultAuthorizationCache();\n}\n\nfunction defaultWalletNotFoundHandler(mobileWalletAdapter) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return defaultErrorModalWalletNotFoundHandler();\n    });\n}\nfunction createDefaultWalletNotFoundHandler() {\n    return defaultWalletNotFoundHandler;\n}\n\nexport { LocalSolanaMobileWalletAdapter, RemoteSolanaMobileWalletAdapter, SolanaMobileWalletAdapter, SolanaMobileWalletAdapterWalletName, createDefaultAddressSelector, createDefaultAuthorizationResultCache, createDefaultWalletNotFoundHandler };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;AAEA;;;;;;;;;;;;;8EAa8E,GAE9E,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IAChD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AAEA,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpD,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AAEA,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC3D,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AAEC,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACxF,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AACC,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACjF,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AAEA,yBAAyB,GACzB,MAAM,kBAAkB;AAExB,yBAAyB,GACzB,MAAM,qBAAqB;AAE3B,yBAAyB,GACzB,MAAM,iBAAiB;AAEtB,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACxF,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AACC,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACjF,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AAEC,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACxF,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACxG;AACC,aAAa,UAAU,sBAAsB,IAAK,SAAU,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACjF,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACxF;AAEA,SAAS,eAAe,SAAS;IAC7B,OAAO,OAAO,IAAI,CAAC,OAAO,YAAY,CAAC,IAAI,CAAC,SAAS;AACzD;AAEA,SAAS;IACL,OAAQ,OAAO,WAAW,eACtB,OAAO,eAAe,IACtB,OAAO,aAAa,eACpB,WAAW,IAAI,CAAC,UAAU,SAAS;AAC3C;AAEA,IAAI,0CAA0C,uCAAuC,2CAA2C,2CAA2C,gDAAgD,gDAAgD,0CAA0C,kDAAkD,wCAAwC,yDAAyD,mDAAmD,wDAAwD;AACnjB,MAAM,sCAAsC;AAC5C,MAAM,4BAA4B;AAClC,SAAS,uBAAuB,WAAW;IACvC,OAAO,aAAa;AACxB;AACA,SAAS,wBAAwB,KAAK;IAClC,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,MAAM,sCAAsC,gLAAA,CAAA,uCAAoC;IAC5E,YAAY,MAAM,EAAE,MAAM,CAAE;QACxB,KAAK;QACL,yCAAyC,GAAG,CAAC,IAAI;QACjD,IAAI,CAAC,4BAA4B,GAAG,IAAI,IACxC,+GAA+G;QAC/G;YAAC;YAAU;SAAE;QACb,sCAAsC,GAAG,CAAC,IAAI,EAAE,KAAK;QACrD,0CAA0C,GAAG,CAAC,IAAI,EAAE;QACpD,0CAA0C,GAAG,CAAC,IAAI,EAAE,mBAAmB,iMAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,iMAAA,CAAA,mBAAgB,CAAC,WAAW;QAC/H,+CAA+C,GAAG,CAAC,IAAI,EAAE,KAAK;QAC9D,+CAA+C,GAAG,CAAC,IAAI,EAAE,KAAK;QAC9D,yCAAyC,GAAG,CAAC,IAAI,EAAE,KAAK;QACxD,iDAAiD,GAAG,CAAC,IAAI,EAAE,CAAC,aAAe,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACvG,IAAI,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;oBACvD,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,yDAAyD,IAAI,CAAC,IAAI;oBAC9I,MAAM,sBAAsB,MAAM,uBAAuB,IAAI,EAAE,gDAAgD,KAAK,IAAI,CAAC,IAAI,EAAE,WAAW,QAAQ;oBAClJ,IAAI,wBAAwB,uBAAuB,IAAI,EAAE,gDAAgD,MAAM;wBAC3G,uBAAuB,IAAI,EAAE,gDAAgD,qBAAqB;wBAClG,uBAAuB,IAAI,EAAE,0CAA0C,WAAW;wBAClF,IAAI,CAAC,IAAI,CAAC,WACV,mFAAmF;wBACnF,oEAAoE;wBACpE,IAAI,CAAC,SAAS;oBAClB;gBACJ;YACJ;QACA,uDAAuD;QACvD,uBAAuB,IAAI,EAAE,gDAAgD,CAAC,WAAa,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACvH,IAAI;gBACJ,MAAM,+BAA+B,MAAM,OAAO,eAAe,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,GAAK,eAAe;gBACxH,OAAO,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,SAAS,EAAE,GAAK,eAAe,eAAe,6BAA6B,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,EAAE;YAC3J,IAAI;QACJ,uBAAuB,IAAI,EAAE,uCAAuC,QAAQ;QAC5E,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC,UAAU,uBAAuB,IAAI,EAAE,kDAAkD;QAC9L,IAAI,CAAC,IAAI,GAAG,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,IAAI;QACzF,IAAI,CAAC,IAAI,GAAG,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,IAAI;QACzF,IAAI,CAAC,GAAG,GAAG,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,GAAG;IACvF,sFAAsF;IACtF,uFAAuF;IACvF,2DAA2D;IAC3D,4EAA4E;IAC5E,qCAAqC;IACrC,6EAA6E;IAC7E,+EAA+E;IAC/E,kFAAkF;IAClF,gDAAgD;IAChD,YAAY;IACZ,UAAU;IACV,IAAI;IACR;IACA,IAAI,YAAY;QACZ,IAAI;QACJ,IAAI,CAAC,uBAAuB,IAAI,EAAE,0CAA0C,QAAQ,uBAAuB,IAAI,EAAE,gDAAgD,MAAM;YACnK,IAAI;gBACA,uBAAuB,IAAI,EAAE,0CAA0C,IAAI,2KAAA,CAAA,YAAS,CAAC,uBAAuB,IAAI,EAAE,gDAAgD,KAAK,SAAS,GAAG;YACvL,EACA,OAAO,GAAG;gBACN,MAAM,IAAI,gLAAA,CAAA,uBAAoB,CAAC,AAAC,aAAa,SAAS,CAAC,MAAM,QAAQ,MAAM,KAAK,IAAI,KAAK,IAAI,EAAE,OAAO,KAAM,iBAAiB;YACjI;QACJ;QACA,OAAO,CAAC,KAAK,uBAAuB,IAAI,EAAE,0CAA0C,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAC/H;IACA,IAAI,YAAY;QACZ,OAAO,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,SAAS;IAC7F;IACA,IAAI,aAAa;QACb,OAAO,uBAAuB,IAAI,EAAE,2CAA2C;IACnF;IACA,IAAI,aAAa;QACb,OAAO,uBAAuB,IAAI,EAAE,2CAA2C;IACnF;IACA,6CAA6C,GAC7C,8CAA8C;QAC1C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,MAAM,IAAI,CAAC,WAAW;QACjC;IACJ;IACA,cAAc;QACV,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,wCAAwC,IAAI,CAAC,IAAI,EAAE;QACnI;IACJ;IACA,UAAU;QACN,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,wCAAwC,IAAI,CAAC,IAAI;QACjI;IACJ;IACA,4DAA4D,GAC5D,qBAAqB,aAAa,EAAE;QAChC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;gBACA,MAAM,4BAA4B,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,yBAAyB;gBAC1I,IAAI,2BAA2B;oBAC3B,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC;wBAAE,QAAQ;oBAAK;oBAChI,OAAO;gBACX;gBACA,IAAI,eAAe;oBACf,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,qLAAA,CAAA,eAAY,CAAC,CAAC,MAAM,CAAC;gBACjH,OAEI,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,gBAAgB,CAAC,OAAO;gBACpH,MAAM,sBAAsB,MAAM,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,yBAAyB;gBAC1I,OAAO;YACX,EACA,OAAO,GAAG;gBACN,MAAM,IAAI,gLAAA,CAAA,wBAAqB,CAAC,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK,iBAAiB;YAC1F;QACJ;IACJ;IACA,aAAa;QACT,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,yFAAyF;YACzF,OAAO,MAAM,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACnL,uBAAuB,IAAI,EAAE,2CAA2C,OAAO;oBAC/E,uBAAuB,IAAI,EAAE,0CAA0C,WAAW;oBAClF,uBAAuB,IAAI,EAAE,gDAAgD,WAAW;oBACxF,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,mBAAmB,CAAC,UAAU;oBACtH,IAAI,CAAC,IAAI,CAAC;gBACd;QACJ;IACJ;IACA,OAAO,KAAK,EAAE;QACV,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBAC7K,IAAI;oBACJ,IAAI,uBAAuB,IAAI,EAAE,2CAA2C,SAAS,iMAAA,CAAA,mBAAgB,CAAC,SAAS,IAAI,uBAAuB,IAAI,EAAE,2CAA2C,SAAS,iMAAA,CAAA,mBAAgB,CAAC,QAAQ,EAAE;wBAC3N,MAAM,IAAI,gLAAA,CAAA,sBAAmB;oBACjC;oBACA,uBAAuB,IAAI,EAAE,2CAA2C,MAAM;oBAC9E,IAAI;wBACA,MAAM,UAAU,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,qLAAA,CAAA,eAAY,CAAC,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ;4BAAE,QAAQ,CAAC,KAAK,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,QAAQ,CAAC,IAAI;wBAAC;wBACvS,IAAI,QAAQ,MAAM,GAAG,GAAG;4BACpB,OAAO,OAAO,CAAC,EAAE;wBACrB,OACK;4BACD,MAAM,IAAI,MAAM;wBACpB;oBACJ,EACA,OAAO,GAAG;wBACN,MAAM,IAAI,gLAAA,CAAA,wBAAqB,CAAC,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK,iBAAiB;oBAC1F,SACQ;wBACJ,uBAAuB,IAAI,EAAE,2CAA2C,OAAO;oBACnF;gBACJ;QACJ;IACJ;IACA,YAAY,OAAO,EAAE;QACjB,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,MAAM,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACnL,MAAM,UAAU,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,mDAAmD,IAAI,CAAC,IAAI;oBACxJ,IAAI;wBACA,MAAM,UAAU,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,0LAAA,CAAA,oBAAiB,CAAC,CAAC,WAAW,CAAC;4BACnI;4BAAS,SAAS;wBACtB;wBACA,OAAO,OAAO,CAAC,EAAE,CAAC,SAAS;oBAC/B,EACA,OAAO,OAAO;wBACV,MAAM,IAAI,gLAAA,CAAA,yBAAsB,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,EAAE;oBAClG;gBACJ;QACJ;IACJ;IACA,gBAAgB,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE;QAC9C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,MAAM,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACnL,MAAM,UAAU,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,mDAAmD,IAAI,CAAC,IAAI;oBACxJ,IAAI;wBACA,SAAS;4BACL,IAAI;4BACJ,OAAQ,WAAW,UAAU;gCACzB,KAAK;gCACL,KAAK;gCACL,KAAK;oCACD,mBAAmB,WAAW,UAAU;oCACxC;gCACJ;oCACI,mBAAmB;4BAC3B;4BACA,IAAI;4BACJ,OAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,mBAAmB;gCACjF,KAAK;gCACL,KAAK;gCACL,KAAK;oCACD,4BAA4B,QAAQ,mBAAmB;oCACvD;gCACJ,KAAK;oCACD,4BAA4B;oCAC5B;gCACJ;oCACI,4BAA4B;4BACpC;4BACA,MAAM,2BAA2B,8BAA8B,cACzD,IACA,8BAA8B,cAC1B,IACA;4BACV,MAAM,wBAAwB,qBAAqB,cAAc,IAAI,qBAAqB,cAAc,IAAI;4BAC5G,OAAO,2BAA2B,wBAC5B,4BACA;wBACV;wBACA,IAAI,qMAAA,CAAA,+BAA4B,IAAI,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,EAAE;4BACnH,MAAM,QAAQ,wBAAwB,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,oBAAoB,CAAC,KAAK;4BACzI,MAAM,CAAC,UAAU,GAAG,CAAC,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,qMAAA,CAAA,+BAA4B,CAAC,CAAC,sBAAsB,CAAC;gCAC9J;gCACA,aAAa,YAAY,SAAS;gCAClC,OAAO;gCACP,SAAS,UAAU;oCACf,eAAe,QAAQ,aAAa;oCACpC,YAAY,QAAQ,UAAU;gCAClC,IAAI;4BACR,EAAE,EAAE,GAAG,CAAE,CAAC;gCACN,OAAO,eAAe,OAAO,SAAS;4BAC1C;4BACA,OAAO;wBACX,OACK;4BACD,MAAM,CAAC,kBAAkB,GAAG,MAAM,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,wDAAwD,IAAI,CAAC,IAAI,EAAE;gCAAC;6BAAY;4BAC9L,IAAI,uBAAuB,oBAAoB;gCAC3C,OAAO,MAAM,WAAW,eAAe,CAAC;4BAC5C,OACK;gCACD,MAAM,wBAAwB,kBAAkB,SAAS;gCACzD,OAAO,MAAM,WAAW,kBAAkB,CAAC,uBAAuB,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;oCAAE,qBAAqB;gCAAsB;4BAC7J;wBACJ;oBACJ,EACA,OAAO,OAAO;wBACV,MAAM,IAAI,gLAAA,CAAA,6BAA0B,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,EAAE;oBACtG;gBACJ;QACJ;IACJ;IACA,gBAAgB,WAAW,EAAE;QACzB,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,MAAM,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACnL,MAAM,CAAC,kBAAkB,GAAG,MAAM,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,wDAAwD,IAAI,CAAC,IAAI,EAAE;wBAAC;qBAAY;oBAC9L,OAAO;gBACX;QACJ;IACJ;IACA,oBAAoB,YAAY,EAAE;QAC9B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,MAAM,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACnL,MAAM,qBAAqB,MAAM,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,wDAAwD,IAAI,CAAC,IAAI,EAAE;oBAChL,OAAO;gBACX;QACJ;IACJ;AACJ;AACA,wCAAwC,IAAI,WAAW,4CAA4C,IAAI,WAAW,4CAA4C,IAAI,WAAW,iDAAiD,IAAI,WAAW,iDAAiD,IAAI,WAAW,2CAA2C,IAAI,WAAW,mDAAmD,IAAI,WAAW,2CAA2C,IAAI,WAAW,yCAAyC,SAAS,uCAAuC,cAAc,KAAK;IAC3kB,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC;QACJ;QACA,OAAO,MAAM,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,6CAA6C,IAAI,CAAC,IAAI,EAAE,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACnL,IAAI,uBAAuB,IAAI,EAAE,2CAA2C,SAAS,iMAAA,CAAA,mBAAgB,CAAC,SAAS,IAAI,uBAAuB,IAAI,EAAE,2CAA2C,SAAS,iMAAA,CAAA,mBAAgB,CAAC,QAAQ,EAAE;oBAC3N,MAAM,IAAI,gLAAA,CAAA,sBAAmB;gBACjC;gBACA,uBAAuB,IAAI,EAAE,2CAA2C,MAAM;gBAC9E,IAAI;oBACA,MAAM,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC;wBAAE,QAAQ;oBAAY;gBAC3I,EACA,OAAO,GAAG;oBACN,MAAM,IAAI,gLAAA,CAAA,wBAAqB,CAAC,AAAC,aAAa,SAAS,EAAE,OAAO,IAAK,iBAAiB;gBAC1F,SACQ;oBACJ,uBAAuB,IAAI,EAAE,2CAA2C,OAAO;gBACnF;YACJ;IACJ;AACJ,GAAG,0DAA0D,SAAS;IAClE,IAAI,uBAAuB,IAAI,EAAE,2CAA2C,SAAS,iMAAA,CAAA,mBAAgB,CAAC,SAAS,EAAE;QAC7G,IAAI,CAAC,IAAI,CAAC,oBAAqB,uBAAuB,IAAI,EAAE,2CAA2C,iMAAA,CAAA,mBAAgB,CAAC,SAAS,EAAE;IACvI;AACJ,GAAG,oDAAoD,SAAS;IAC5D,IAAI,CAAC,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,YAAY,IAAI,CAAC,uBAAuB,IAAI,EAAE,gDAAgD,MACxK,MAAM,IAAI,gLAAA,CAAA,0BAAuB;IACrC,OAAO,uBAAuB,IAAI,EAAE,gDAAgD;AACxF,GAAG,yDAAyD,SAAS,uDAAuD,YAAY;IACpI,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,MAAM,UAAU,uBAAuB,IAAI,EAAE,0CAA0C,KAAK,mDAAmD,IAAI,CAAC,IAAI;QACxJ,IAAI;YACA,IAAI,8LAAA,CAAA,wBAAqB,IAAI,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,EAAE;gBAC5G,OAAO,uBAAuB,IAAI,EAAE,uCAAuC,KAAK,QAAQ,CAAC,8LAAA,CAAA,wBAAqB,CAAC,CAAC,eAAe,IAAI,aAAa,GAAG,CAAC,CAAC;oBACjJ,OAAO;wBAAE;wBAAS,aAAa,MAAM,SAAS;oBAAG;gBACrD,IAAI,IAAI,CAAC,CAAC;oBACN,OAAO,QAAQ,GAAG,CAAC,CAAC;wBAChB,MAAM,YAAY,OAAO,iBAAiB;wBAC1C,MAAM,gBAAgB,SAAS,CAAC,EAAE;wBAClC,MAAM,gBAAgB,gBAAgB,4BAA4B;wBAClE,MAAM,UAAU,2KAAA,CAAA,mBAAgB,CAAC,yBAAyB,CAAC,UAAU,KAAK,CAAC,eAAe,UAAU,MAAM;wBAC1G,IAAI,YAAY,UAAU;4BACtB,OAAO,2KAAA,CAAA,cAAW,CAAC,IAAI,CAAC;wBAC5B,OACK;4BACD,OAAO,2KAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC;wBAC5C;oBACJ;gBACJ;YACJ,OACK;gBACD,MAAM,IAAI,MAAM;YACpB;QACJ,EACA,OAAO,OAAO;YACV,MAAM,IAAI,gLAAA,CAAA,6BAA0B,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,EAAE;QACtG;IACJ;AACJ,GAAG,8CAA8C,SAAS,4CAA4C,QAAQ;IAC1G,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,IAAI;YACA,OAAO,MAAM;QACjB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,IAAI,CAAC,SAAS;YACnB,MAAM;QACV;IACJ;AACJ;AACA,MAAM,uCAAuC;IACzC,YAAY,MAAM,CAAE;QAChB,IAAI;QACJ,MAAM,QAAQ,wBAAwB,CAAC,KAAK,OAAO,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,OAAO;QACzG,KAAK,CAAC,IAAI,4LAAA,CAAA,uCAAoC,CAAC;YAC3C,aAAa,OAAO,WAAW;YAC/B,oBAAoB;gBAChB,KAAK,OAAO,wBAAwB,CAAC,GAAG;gBACxC,KAAK,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;wBACvC,MAAM,sBAAsB,MAAM,OAAO,wBAAwB,CAAC,GAAG;wBACrE,IAAI,uBAAuB,WAAW,qBAAqB;4BACvD,OAAO;wBACX,OACK,IAAI,qBAAqB;4BAC1B,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,sBAAsB;gCAAE,OAAO;4BAAM;wBAChF,OAEI,OAAO;oBACf;gBACA,OAAO,OAAO,wBAAwB,CAAC,KAAK;YAChD;YACA,QAAQ;gBAAC;aAAM;YACf,eAAe,CAAA,GAAA,4LAAA,CAAA,6BAA0B,AAAD;YACxC,kBAAkB,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACpD,OAAO,gBAAgB,CAAC,IAAI;gBAChC;QACJ,IAAI;YACA,iBAAiB,OAAO,eAAe;YACvC,OAAO;QACX;IACJ;AACJ;AACA,MAAM,wCAAwC;IAC1C,YAAY,MAAM,CAAE;QAChB,MAAM,QAAQ,wBAAwB,OAAO,KAAK;QAClD,KAAK,CAAC,IAAI,4LAAA,CAAA,wCAAqC,CAAC;YAC5C,aAAa,OAAO,WAAW;YAC/B,oBAAoB;gBAChB,KAAK,OAAO,wBAAwB,CAAC,GAAG;gBACxC,KAAK,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;wBACvC,MAAM,sBAAsB,MAAM,OAAO,wBAAwB,CAAC,GAAG;wBACrE,IAAI,uBAAuB,WAAW,qBAAqB;4BACvD,OAAO;wBACX,OACK,IAAI,qBAAqB;4BAC1B,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,sBAAsB;gCAAE,OAAO;4BAAM;wBAChF,OAEI,OAAO;oBACf;gBACA,OAAO,OAAO,wBAAwB,CAAC,KAAK;YAChD;YACA,QAAQ;gBAAC;aAAM;YACf,eAAe,CAAA,GAAA,4LAAA,CAAA,6BAA0B,AAAD;YACxC,qBAAqB,OAAO,mBAAmB;YAC/C,kBAAkB,IAAM,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;oBACpD,OAAO,gBAAgB,CAAC,IAAI;gBAChC;QACJ,IAAI;YACA,iBAAiB,OAAO,eAAe;YACvC,OAAO;QACX;IACJ;AACJ;AACA,MAAM,kCAAkC;AACxC;AAEA,SAAS;IACL,OAAO;QACH,QAAO,SAAS;YACZ,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACnC,OAAO,SAAS,CAAC,EAAE;YACvB;QACJ;IACJ;AACJ;AAEA,SAAS;IACL,OAAO,CAAA,GAAA,4LAAA,CAAA,kCAA+B,AAAD;AACzC;AAEA,SAAS,6BAA6B,mBAAmB;IACrD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;QACnC,OAAO,CAAA,GAAA,4LAAA,CAAA,yCAAsC,AAAD;IAChD;AACJ;AACA,SAAS;IACL,OAAO;AACX", "ignoreList": [0], "debugId": null}}]}