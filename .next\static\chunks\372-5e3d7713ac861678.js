(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[372],{228:(t,e,r)=>{var i=r(4134),n=i.Buffer;function o(t,e){for(var r in t)e[r]=t[r]}function s(t,e,r){return n(t,e,r)}n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow?t.exports=i:(o(i,e),e.Buffer=s),s.prototype=Object.create(n.prototype),o(n,s),s.from=function(t,e,r){if("number"==typeof t)throw TypeError("Argument must not be a number");return n(t,e,r)},s.alloc=function(t,e,r){if("number"!=typeof t)throw TypeError("Argument must be a number");var i=n(t);return void 0!==e?"string"==typeof r?i.fill(e,r):i.fill(e):i.fill(0),i},s.allocUnsafe=function(t){if("number"!=typeof t)throw TypeError("Argument must be a number");return n(t)},s.allocUnsafeSlow=function(t){if("number"!=typeof t)throw TypeError("Argument must be a number");return i.SlowBuffer(t)}},456:(t,e,r)=>{"use strict";r.d(e,{B8:()=>p,D0:()=>function t(e,r,i=!1,d={}){let g;if(e<=o)throw Error("invalid field: expected ORDER > 0, got "+e);let{nBitLength:w,nByteLength:v}=E(e,r);if(v>2048)throw Error("invalid field: expected ORDER of <= 2048 bytes");let M=Object.freeze({ORDER:e,isLE:i,BITS:w,BYTES:v,MASK:(0,n.OG)(w),ZERO:o,ONE:s,create:t=>c(t,e),isValid:t=>{if("bigint"!=typeof t)throw Error("invalid field element: expected bigint, got "+typeof t);return o<=t&&t<e},is0:t=>t===o,isOdd:t=>(t&s)===s,neg:t=>c(-t,e),eql:(t,e)=>t===e,sqr:t=>c(t*t,e),add:(t,r)=>c(t+r,e),sub:(t,r)=>c(t-r,e),mul:(t,r)=>c(t*r,e),pow:(t,e)=>(function(t,e,r){if(r<o)throw Error("invalid exponent, negatives unsupported");if(r===o)return t.ONE;if(r===s)return e;let i=t.ONE,n=e;for(;r>o;)r&s&&(i=t.mul(i,n)),n=t.sqr(n),r>>=s;return i})(M,t,e),div:(t,r)=>c(t*p(r,e),e),sqrN:t=>t*t,addN:(t,e)=>t+e,subN:(t,e)=>t-e,mulN:(t,e)=>t*e,inv:t=>p(t,e),sqrt:d.sqrt||(r=>(g||(g=e%h===a?m:e%l===u?y:function(e){if(e<BigInt(3))throw Error("sqrt is not defined for small field");let r=e-s,i=0;for(;r%f===o;)r/=f,i++;let n=f,a=t(e);for(;1===x(a,n);)if(n++>1e3)throw Error("Cannot find square root: probably non-prime P");if(1===i)return m;let h=a.pow(n,r),u=(r+s)/f;return function(t,e){if(t.is0(e))return e;if(1!==x(t,e))throw Error("Cannot find square root");let n=i,o=t.mul(t.ONE,h),f=t.pow(e,r),a=t.pow(e,u);for(;!t.eql(f,t.ONE);){if(t.is0(f))return t.ZERO;let e=1,r=t.sqr(f);for(;!t.eql(r,t.ONE);)if(e++,r=t.sqr(r),e===n)throw Error("Cannot find square root");let i=s<<BigInt(n-e-1),h=t.pow(o,i);n=e,o=t.sqr(h),f=t.mul(f,o),a=t.mul(a,h)}return a}}(e)),g(M,r))),toBytes:t=>i?(0,n.z)(t,v):(0,n.lq)(t,v),fromBytes:t=>{if(t.length!==v)throw Error("Field.fromBytes: expected "+v+" bytes, got "+t.length);return i?(0,n.lX)(t):(0,n.Ph)(t)},invertBatch:t=>b(M,t),cmov:(t,e,r)=>r?e:t});return Object.freeze(M)},LH:()=>E,Tp:()=>B,dQ:()=>g,jr:()=>v,pS:()=>b,qy:()=>A,zH:()=>d,zi:()=>c});var i=r(9989),n=r(4611);let o=BigInt(0),s=BigInt(1),f=BigInt(2),a=BigInt(3),h=BigInt(4),u=BigInt(5),l=BigInt(8);function c(t,e){let r=t%e;return r>=o?r:e+r}function d(t,e,r){let i=t;for(;e-- >o;)i*=i,i%=r;return i}function p(t,e){if(t===o)throw Error("invert: expected non-zero number");if(e<=o)throw Error("invert: expected positive modulus, got "+e);let r=c(t,e),i=e,n=o,f=s,a=s,h=o;for(;r!==o;){let t=i/r,e=i%r,o=n-a*t,s=f-h*t;i=r,r=e,n=a,f=h,a=o,h=s}if(i!==s)throw Error("invert: does not exist");return c(n,e)}function m(t,e){let r=(t.ORDER+s)/h,i=t.pow(e,r);if(!t.eql(t.sqr(i),e))throw Error("Cannot find square root");return i}function y(t,e){let r=(t.ORDER-u)/l,i=t.mul(e,f),n=t.pow(i,r),o=t.mul(e,n),s=t.mul(t.mul(o,f),n),a=t.mul(o,t.sub(s,t.ONE));if(!t.eql(t.sqr(a),e))throw Error("Cannot find square root");return a}let g=(t,e)=>(c(t,e)&s)===s,w=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function v(t){let e=w.reduce((t,e)=>(t[e]="function",t),{ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"});return(0,n.Q5)(t,e)}function b(t,e,r=!1){let i=Array(e.length).fill(r?t.ZERO:void 0),n=e.reduce((e,r,n)=>t.is0(r)?e:(i[n]=e,t.mul(e,r)),t.ONE),o=t.inv(n);return e.reduceRight((e,r,n)=>t.is0(r)?e:(i[n]=t.mul(e,i[n]),t.mul(e,r)),o),i}function x(t,e){let r=(t.ORDER-s)/f,i=t.pow(e,r),n=t.eql(i,t.ONE),o=t.eql(i,t.ZERO),a=t.eql(i,t.neg(t.ONE));if(!n&&!o&&!a)throw Error("invalid Legendre symbol result");return n?1:o?0:-1}function E(t,e){void 0!==e&&(0,i.Fe)(e);let r=void 0!==e?e:t.toString(2).length,n=Math.ceil(r/8);return{nBitLength:r,nByteLength:n}}function M(t){if("bigint"!=typeof t)throw Error("field order must be bigint");return Math.ceil(t.toString(2).length/8)}function B(t){let e=M(t);return e+Math.ceil(e/2)}function A(t,e,r=!1){let i=t.length,o=M(e),f=B(e);if(i<16||i<f||i>1024)throw Error("expected "+f+"-1024 bytes of input, got "+i);let a=c(r?(0,n.lX)(t):(0,n.Ph)(t),e-s)+s;return r?(0,n.z)(a,o):(0,n.lq)(a,o)}},840:(t,e,r)=>{"use strict";r.d(e,{o:()=>f,w:()=>s});var i=r(2115);let n={setVisible(t){console.error(o("call","setVisible"))},visible:!1};function o(t,e){return`You have tried to  ${t} "${e}" on a WalletModalContext without providing one. Make sure to render a WalletModalProvider as an ancestor of the component that uses WalletModalContext`}Object.defineProperty(n,"visible",{get:()=>(console.error(o("read","visible")),!1)});let s=(0,i.createContext)(n);function f(){return(0,i.useContext)(s)}},1361:(t,e,r)=>{"use strict";r.d(e,{ev:()=>b});var i=r(8693),n=r(9989),o=r(4341),s=r(456),f=r(4611);let a=BigInt(0),h=BigInt(1),u=BigInt(2),l=BigInt(8),c={zip215:!0},d=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),p=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752");BigInt(0);let m=BigInt(1),y=BigInt(2);BigInt(3);let g=BigInt(5),w=BigInt(8),v=(0,s.D0)(d,void 0,!0),b=function(t){let e=function(t){let e=(0,o.hp)(t);return(0,f.Q5)(t,{hash:"function",a:"bigint",d:"bigint",randomBytes:"function"},{adjustScalarBytes:"function",domain:"function",uvRatio:"function",mapToCurve:"function"}),Object.freeze({...e})}(t),{Fp:r,n:i,prehash:n,hash:d,randomBytes:p,nByteLength:m,h:y}=e,g=u<<BigInt(8*m)-h,w=r.create,v=(0,s.D0)(e.n,e.nBitLength);if(!function(t,i){let n=r.sqr(t),o=r.sqr(i),s=r.add(r.mul(e.a,n),o),f=r.add(r.ONE,r.mul(e.d,r.mul(n,o)));return r.eql(s,f)}(e.Gx,e.Gy))throw Error("bad curve params: generator point");let b=e.uvRatio||((t,e)=>{try{return{isValid:!0,value:r.sqrt(t*r.inv(e))}}catch(t){return{isValid:!1,value:a}}}),x=e.adjustScalarBytes||(t=>t),E=e.domain||((t,e,r)=>{if((0,f.e8)("phflag",r),e.length||r)throw Error("Contexts/pre-hash are not supported");return t});function M(t,e,r=!1){let i=r?h:a;(0,f.aK)("coordinate "+t,e,i,g)}function B(t){if(!(t instanceof O))throw Error("ExtendedPoint expected")}let A=(0,f.x)((t,e)=>{let{ex:i,ey:n,ez:o}=t,s=t.is0();null==e&&(e=s?l:r.inv(o));let f=w(i*e),u=w(n*e),c=w(o*e);if(s)return{x:a,y:h};if(c!==h)throw Error("invZ was invalid");return{x:f,y:u}}),_=(0,f.x)(t=>{let{a:r,d:i}=e;if(t.is0())throw Error("bad point: ZERO");let{ex:n,ey:o,ez:s,et:f}=t,a=w(n*n),h=w(o*o),u=w(s*s),l=w(u*u),c=w(a*r);if(w(u*w(c+h))!==w(l+w(i*w(a*h))))throw Error("bad point: equation left != right (1)");if(w(n*o)!==w(s*f))throw Error("bad point: equation left != right (2)");return!0});class O{constructor(t,e,r,i){M("x",t),M("y",e),M("z",r,!0),M("t",i),this.ex=t,this.ey=e,this.ez=r,this.et=i,Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(t){if(t instanceof O)throw Error("extended point not allowed");let{x:e,y:r}=t||{};return M("x",e),M("y",r),new O(e,r,h,w(e*r))}static normalizeZ(t){let e=(0,s.pS)(r,t.map(t=>t.ez));return t.map((t,r)=>t.toAffine(e[r])).map(O.fromAffine)}static msm(t,e){return(0,o.Xf)(O,v,t,e)}_setWindowSize(t){U.setWindowSize(this,t)}assertValidity(){_(this)}equals(t){B(t);let{ex:e,ey:r,ez:i}=this,{ex:n,ey:o,ez:s}=t,f=w(e*s),a=w(n*i),h=w(r*s),u=w(o*i);return f===a&&h===u}is0(){return this.equals(O.ZERO)}negate(){return new O(w(-this.ex),this.ey,this.ez,w(-this.et))}double(){let{a:t}=e,{ex:r,ey:i,ez:n}=this,o=w(r*r),s=w(i*i),f=w(u*w(n*n)),a=w(t*o),h=r+i,l=w(w(h*h)-o-s),c=a+s,d=c-f,p=a-s,m=w(l*d),y=w(c*p),g=w(l*p);return new O(m,y,w(d*c),g)}add(t){B(t);let{a:r,d:i}=e,{ex:n,ey:o,ez:s,et:f}=this,{ex:a,ey:h,ez:u,et:l}=t,c=w(n*a),d=w(o*h),p=w(f*i*l),m=w(s*u),y=w((n+o)*(a+h)-c-d),g=m-p,v=m+p,b=w(d-r*c),x=w(y*g),E=w(v*b),M=w(y*b);return new O(x,E,w(g*v),M)}subtract(t){return this.add(t.negate())}wNAF(t){return U.wNAFCached(this,t,O.normalizeZ)}multiply(t){(0,f.aK)("scalar",t,h,i);let{p:e,f:r}=this.wNAF(t);return O.normalizeZ([e,r])[0]}multiplyUnsafe(t,e=O.ZERO){return((0,f.aK)("scalar",t,a,i),t===a)?S:this.is0()||t===h?this:U.wNAFCachedUnsafe(this,t,O.normalizeZ,e)}isSmallOrder(){return this.multiplyUnsafe(y).is0()}isTorsionFree(){return U.unsafeLadder(this,i).is0()}toAffine(t){return A(this,t)}clearCofactor(){let{h:t}=e;return t===h?this:this.multiplyUnsafe(t)}static fromHex(t,i=!1){let{d:n,a:o}=e,s=r.BYTES;t=(0,f.qj)("pointHex",t,s),(0,f.e8)("zip215",i);let u=t.slice(),l=t[s-1];u[s-1]=-129&l;let c=(0,f.lX)(u),d=i?g:r.ORDER;(0,f.aK)("pointHex.y",c,a,d);let p=w(c*c),{isValid:m,value:y}=b(w(p-h),w(n*p-o));if(!m)throw Error("Point.fromHex: invalid y coordinate");let v=(y&h)===h,x=(128&l)!=0;if(!i&&y===a&&x)throw Error("Point.fromHex: x=0 and x_0=1");return x!==v&&(y=w(-y)),O.fromAffine({x:y,y:c})}static fromPrivateKey(t){let{scalar:e}=R(t);return I.multiply(e)}toRawBytes(){let{x:t,y:e}=this.toAffine(),i=(0,f.z)(e,r.BYTES);return i[i.length-1]|=t&h?128:0,i}toHex(){return(0,f.My)(this.toRawBytes())}}O.BASE=new O(e.Gx,e.Gy,h,w(e.Gx*e.Gy)),O.ZERO=new O(a,h,h,a);let{BASE:I,ZERO:S}=O,U=(0,o.hT)(O,8*m);function k(t){var e;return e=(0,f.lX)(t),(0,s.zi)(e,i)}function R(t){let e=r.BYTES;t=(0,f.qj)("private key",t,e);let i=(0,f.qj)("hashed private key",d(t),2*e),n=x(i.slice(0,e)),o=i.slice(e,2*e),s=k(n);return{head:n,prefix:o,scalar:s}}function z(t){let{head:e,prefix:r,scalar:i}=R(t),n=I.multiply(i),o=n.toRawBytes();return{head:e,prefix:r,scalar:i,point:n,pointBytes:o}}function T(t=Uint8Array.of(),...e){return k(d(E((0,f.Id)(...e),(0,f.qj)("context",t),!!n)))}return I._setWindowSize(8),{CURVE:e,getPublicKey:function(t){return z(t).pointBytes},sign:function(t,e,o={}){var h;t=(0,f.qj)("message",t),n&&(t=n(t));let{prefix:u,scalar:l,pointBytes:c}=z(e),d=T(o.context,u,t),p=I.multiply(d).toRawBytes(),m=(h=d+T(o.context,p,c,t)*l,(0,s.zi)(h,i));(0,f.aK)("signature.s",m,a,i);let y=(0,f.Id)(p,(0,f.z)(m,r.BYTES));return(0,f.qj)("result",y,2*r.BYTES)},verify:function(t,e,i,o=c){let s,a,h,{context:u,zip215:l}=o,d=r.BYTES;t=(0,f.qj)("signature",t,2*d),e=(0,f.qj)("message",e),i=(0,f.qj)("publicKey",i,d),void 0!==l&&(0,f.e8)("zip215",l),n&&(e=n(e));let p=(0,f.lX)(t.slice(d,2*d));try{s=O.fromHex(i,l),a=O.fromHex(t.slice(0,d),l),h=I.multiplyUnsafe(p)}catch(t){return!1}if(!l&&s.isSmallOrder())return!1;let m=T(u,a.toRawBytes(),s.toRawBytes(),e);return a.add(s.multiplyUnsafe(m)).subtract(h).clearCofactor().equals(O.ZERO)},ExtendedPoint:O,utils:{getExtendedPublicKey:z,randomPrivateKey:()=>p(r.BYTES),precompute:(t=8,e=O.BASE)=>(e._setWindowSize(t),e.multiply(BigInt(3)),e)}}}({a:v.create(BigInt(-1)),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),Fp:v,n:BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),h:w,Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960"),hash:i.Zf,randomBytes:n.po,adjustScalarBytes:function(t){return t[0]&=248,t[31]&=127,t[31]|=64,t},uvRatio:function(t,e){let r=(0,s.zi)(e*e*e,d),i=function(t){let e=BigInt(10),r=BigInt(20),i=BigInt(40),n=BigInt(80),o=t*t%d*t%d,f=(0,s.zH)(o,y,d)*o%d,a=(0,s.zH)(f,m,d)*t%d,h=(0,s.zH)(a,g,d)*a%d,u=(0,s.zH)(h,e,d)*h%d,l=(0,s.zH)(u,r,d)*u%d,c=(0,s.zH)(l,i,d)*l%d,p=(0,s.zH)(c,n,d)*c%d,w=(0,s.zH)(p,n,d)*c%d,v=(0,s.zH)(w,e,d)*h%d;return{pow_p_5_8:(0,s.zH)(v,y,d)*t%d,b2:o}}(t*(0,s.zi)(r*r*e,d)).pow_p_5_8,n=(0,s.zi)(t*r*i,d),o=(0,s.zi)(e*n*n,d),f=n,a=(0,s.zi)(n*p,d),h=o===t,u=o===(0,s.zi)(-t,d),l=o===(0,s.zi)(-t*p,d);return h&&(n=f),(u||l)&&(n=a),(0,s.dQ)(n,d)&&(n=(0,s.zi)(-n,d)),{isValid:h||u,value:n}}})},1392:(t,e,r)=>{"use strict";r.d(e,{b:()=>f,v:()=>a});var i=r(2115);let n=[],o={autoConnect:!1,connecting:!1,connected:!1,disconnecting:!1,select(){s("call","select")},connect:()=>Promise.reject(s("call","connect")),disconnect:()=>Promise.reject(s("call","disconnect")),sendTransaction:()=>Promise.reject(s("call","sendTransaction")),signTransaction:()=>Promise.reject(s("call","signTransaction")),signAllTransactions:()=>Promise.reject(s("call","signAllTransactions")),signMessage:()=>Promise.reject(s("call","signMessage")),signIn:()=>Promise.reject(s("call","signIn"))};function s(t,e){let r=Error(`You have tried to ${t} "${e}" on a WalletContext without providing one. Make sure to render a WalletProvider as an ancestor of the component that uses WalletContext.`);return console.error(r),r}Object.defineProperty(o,"wallets",{get:()=>(s("read","wallets"),n)}),Object.defineProperty(o,"wallet",{get:()=>(s("read","wallet"),null)}),Object.defineProperty(o,"publicKey",{get:()=>(s("read","publicKey"),null)});let f=(0,i.createContext)(o);function a(){return(0,i.useContext)(f)}},2021:(t,e,r)=>{"use strict";r.d(e,{v4:()=>h});var i,n=new Uint8Array(16);let o=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var s=[],f=0;f<256;++f)s.push((f+256).toString(16).substr(1));let a=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=(s[t[e+0]]+s[t[e+1]]+s[t[e+2]]+s[t[e+3]]+"-"+s[t[e+4]]+s[t[e+5]]+"-"+s[t[e+6]]+s[t[e+7]]+"-"+s[t[e+8]]+s[t[e+9]]+"-"+s[t[e+10]]+s[t[e+11]]+s[t[e+12]]+s[t[e+13]]+s[t[e+14]]+s[t[e+15]]).toLowerCase();if(!("string"==typeof r&&o.test(r)))throw TypeError("Stringified UUID is invalid");return r},h=function(t,e,r){var o=(t=t||{}).random||(t.rng||function(){if(!i&&!(i="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return i(n)})();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,e){r=r||0;for(var s=0;s<16;++s)e[r+s]=o[s];return e}return a(o)}},2288:(t,e,r)=>{"use strict";r.d(e,{sc:()=>i});let i=r(8693).sc},2437:function(t,e,r){!function(t,e){"use strict";function i(t,e){if(!t)throw Error(e||"Assertion failed")}function n(t,e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}function o(t,e,r){if(o.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&(("le"===e||"be"===e)&&(r=e,e=10),this._init(t||0,e||10,r||"be"))}"object"==typeof t?t.exports=o:e.BN=o,o.BN=o,o.wordSize=26;try{l="undefined"!=typeof window&&void 0!==window.Buffer?window.Buffer:r(7790).Buffer}catch(t){}function s(t,e){var r=t.charCodeAt(e);return r>=48&&r<=57?r-48:r>=65&&r<=70?r-55:r>=97&&r<=102?r-87:void i(!1,"Invalid character in "+t)}function f(t,e,r){var i=s(t,r);return r-1>=e&&(i|=s(t,r-1)<<4),i}function a(t,e,r,n){for(var o=0,s=0,f=Math.min(t.length,r),a=e;a<f;a++){var h=t.charCodeAt(a)-48;o*=n,s=h>=49?h-49+10:h>=17?h-17+10:h,i(h>=0&&s<n,"Invalid character"),o+=s}return o}function h(t,e){t.words=e.words,t.length=e.length,t.negative=e.negative,t.red=e.red}if(o.isBN=function(t){return t instanceof o||null!==t&&"object"==typeof t&&t.constructor.wordSize===o.wordSize&&Array.isArray(t.words)},o.max=function(t,e){return t.cmp(e)>0?t:e},o.min=function(t,e){return 0>t.cmp(e)?t:e},o.prototype._init=function(t,e,r){if("number"==typeof t)return this._initNumber(t,e,r);if("object"==typeof t)return this._initArray(t,e,r);"hex"===e&&(e=16),i(e===(0|e)&&e>=2&&e<=36);var n=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&(n++,this.negative=1),n<t.length&&(16===e?this._parseHex(t,n,r):(this._parseBase(t,e,n),"le"===r&&this._initArray(this.toArray(),e,r)))},o.prototype._initNumber=function(t,e,r){t<0&&(this.negative=1,t=-t),t<0x4000000?(this.words=[0x3ffffff&t],this.length=1):t<0x10000000000000?(this.words=[0x3ffffff&t,t/0x4000000&0x3ffffff],this.length=2):(i(t<0x20000000000000),this.words=[0x3ffffff&t,t/0x4000000&0x3ffffff,1],this.length=3),"le"===r&&this._initArray(this.toArray(),e,r)},o.prototype._initArray=function(t,e,r){if(i("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=Array(this.length);for(var n,o,s=0;s<this.length;s++)this.words[s]=0;var f=0;if("be"===r)for(s=t.length-1,n=0;s>=0;s-=3)o=t[s]|t[s-1]<<8|t[s-2]<<16,this.words[n]|=o<<f&0x3ffffff,this.words[n+1]=o>>>26-f&0x3ffffff,(f+=24)>=26&&(f-=26,n++);else if("le"===r)for(s=0,n=0;s<t.length;s+=3)o=t[s]|t[s+1]<<8|t[s+2]<<16,this.words[n]|=o<<f&0x3ffffff,this.words[n+1]=o>>>26-f&0x3ffffff,(f+=24)>=26&&(f-=26,n++);return this._strip()},o.prototype._parseHex=function(t,e,r){this.length=Math.ceil((t.length-e)/6),this.words=Array(this.length);for(var i,n=0;n<this.length;n++)this.words[n]=0;var o=0,s=0;if("be"===r)for(n=t.length-1;n>=e;n-=2)i=f(t,e,n)<<o,this.words[s]|=0x3ffffff&i,o>=18?(o-=18,s+=1,this.words[s]|=i>>>26):o+=8;else for(n=(t.length-e)%2==0?e+1:e;n<t.length;n+=2)i=f(t,e,n)<<o,this.words[s]|=0x3ffffff&i,o>=18?(o-=18,s+=1,this.words[s]|=i>>>26):o+=8;this._strip()},o.prototype._parseBase=function(t,e,r){this.words=[0],this.length=1;for(var i=0,n=1;n<=0x3ffffff;n*=e)i++;i--,n=n/e|0;for(var o=t.length-r,s=o%i,f=Math.min(o,o-s)+r,h=0,u=r;u<f;u+=i)h=a(t,u,u+i,e),this.imuln(n),this.words[0]+h<0x4000000?this.words[0]+=h:this._iaddn(h);if(0!==s){var l=1;for(h=a(t,u,t.length,e),u=0;u<s;u++)l*=e;this.imuln(l),this.words[0]+h<0x4000000?this.words[0]+=h:this._iaddn(h)}this._strip()},o.prototype.copy=function(t){t.words=Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},o.prototype._move=function(t){h(t,this)},o.prototype.clone=function(){var t=new o(null);return this.copy(t),t},o.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},o.prototype._strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},o.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},"undefined"!=typeof Symbol&&"function"==typeof Symbol.for)try{o.prototype[Symbol.for("nodejs.util.inspect.custom")]=u}catch(t){o.prototype.inspect=u}else o.prototype.inspect=u;function u(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var l,c=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],d=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],p=[0,0,0x2000000,0x290d741,0x1000000,0x2e90edd,0x39aa400,0x267bf47,0x1000000,0x290d741,1e7,0x12959c3,0x222c000,0x3bd7765,7529536,0xadcea1,0x1000000,0x1704f61,0x206fc40,0x2cddcf9,64e6,4084101,5153632,6436343,7962624,9765625,0xb54ba0,0xdaf26b,0x1069c00,0x138f9ad,243e5,0x1b4d89f,0x2000000,0x25528a1,0x2b54a20,0x3216b93,0x39aa400];function m(t,e,r){r.negative=e.negative^t.negative;var i=t.length+e.length|0;r.length=i,i=i-1|0;var n=0|t.words[0],o=0|e.words[0],s=n*o,f=0x3ffffff&s,a=s/0x4000000|0;r.words[0]=f;for(var h=1;h<i;h++){for(var u=a>>>26,l=0x3ffffff&a,c=Math.min(h,e.length-1),d=Math.max(0,h-t.length+1);d<=c;d++){var p=h-d|0;u+=(s=(n=0|t.words[p])*(o=0|e.words[d])+l)/0x4000000|0,l=0x3ffffff&s}r.words[h]=0|l,a=0|u}return 0!==a?r.words[h]=0|a:r.length--,r._strip()}o.prototype.toString=function(t,e){if(e=0|e||1,16===(t=t||10)||"hex"===t){for(var r="",n=0,o=0,s=0;s<this.length;s++){var f=this.words[s],a=((f<<n|o)&0xffffff).toString(16);o=f>>>24-n&0xffffff,(n+=2)>=26&&(n-=26,s--),r=0!==o||s!==this.length-1?c[6-a.length]+a+r:a+r}for(0!==o&&(r=o.toString(16)+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}if(t===(0|t)&&t>=2&&t<=36){var h=d[t],u=p[t];r="";var l=this.clone();for(l.negative=0;!l.isZero();){var m=l.modrn(u).toString(t);r=(l=l.idivn(u)).isZero()?m+r:c[h-m.length]+m+r}for(this.isZero()&&(r="0"+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}i(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=0x4000000*this.words[1]:3===this.length&&1===this.words[2]?t+=0x10000000000000+0x4000000*this.words[1]:this.length>2&&i(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},o.prototype.toJSON=function(){return this.toString(16,2)},l&&(o.prototype.toBuffer=function(t,e){return this.toArrayLike(l,t,e)}),o.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},o.prototype.toArrayLike=function(t,e,r){this._strip();var n=this.byteLength(),o=r||Math.max(1,n);i(n<=o,"byte array longer than desired length"),i(o>0,"Requested array length <= 0");var s=t.allocUnsafe?t.allocUnsafe(o):new t(o);return this["_toArrayLike"+("le"===e?"LE":"BE")](s,n),s},o.prototype._toArrayLikeLE=function(t,e){for(var r=0,i=0,n=0,o=0;n<this.length;n++){var s=this.words[n]<<o|i;t[r++]=255&s,r<t.length&&(t[r++]=s>>8&255),r<t.length&&(t[r++]=s>>16&255),6===o?(r<t.length&&(t[r++]=s>>24&255),i=0,o=0):(i=s>>>24,o+=2)}if(r<t.length)for(t[r++]=i;r<t.length;)t[r++]=0},o.prototype._toArrayLikeBE=function(t,e){for(var r=t.length-1,i=0,n=0,o=0;n<this.length;n++){var s=this.words[n]<<o|i;t[r--]=255&s,r>=0&&(t[r--]=s>>8&255),r>=0&&(t[r--]=s>>16&255),6===o?(r>=0&&(t[r--]=s>>24&255),i=0,o=0):(i=s>>>24,o+=2)}if(r>=0)for(t[r--]=i;r>=0;)t[r--]=0},Math.clz32?o.prototype._countBits=function(t){return 32-Math.clz32(t)}:o.prototype._countBits=function(t){var e=t,r=0;return e>=4096&&(r+=13,e>>>=13),e>=64&&(r+=7,e>>>=7),e>=8&&(r+=4,e>>>=4),e>=2&&(r+=2,e>>>=2),r+e},o.prototype._zeroBits=function(t){if(0===t)return 26;var e=t,r=0;return(8191&e)==0&&(r+=13,e>>>=13),(127&e)==0&&(r+=7,e>>>=7),(15&e)==0&&(r+=4,e>>>=4),(3&e)==0&&(r+=2,e>>>=2),(1&e)==0&&r++,r},o.prototype.bitLength=function(){var t=this.words[this.length-1],e=this._countBits(t);return(this.length-1)*26+e},o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var r=this._zeroBits(this.words[e]);if(t+=r,26!==r)break}return t},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},o.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return 0!==this.negative},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]=this.words[e]|t.words[e];return this._strip()},o.prototype.ior=function(t){return i((this.negative|t.negative)==0),this.iuor(t)},o.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},o.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},o.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var r=0;r<e.length;r++)this.words[r]=this.words[r]&t.words[r];return this.length=e.length,this._strip()},o.prototype.iand=function(t){return i((this.negative|t.negative)==0),this.iuand(t)},o.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},o.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},o.prototype.iuxor=function(t){this.length>t.length?(e=this,r=t):(e=t,r=this);for(var e,r,i=0;i<r.length;i++)this.words[i]=e.words[i]^r.words[i];if(this!==e)for(;i<e.length;i++)this.words[i]=e.words[i];return this.length=e.length,this._strip()},o.prototype.ixor=function(t){return i((this.negative|t.negative)==0),this.iuxor(t)},o.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},o.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},o.prototype.inotn=function(t){i("number"==typeof t&&t>=0);var e=0|Math.ceil(t/26),r=t%26;this._expand(e),r>0&&e--;for(var n=0;n<e;n++)this.words[n]=0x3ffffff&~this.words[n];return r>0&&(this.words[n]=~this.words[n]&0x3ffffff>>26-r),this._strip()},o.prototype.notn=function(t){return this.clone().inotn(t)},o.prototype.setn=function(t,e){i("number"==typeof t&&t>=0);var r=t/26|0,n=t%26;return this._expand(r+1),e?this.words[r]=this.words[r]|1<<n:this.words[r]=this.words[r]&~(1<<n),this._strip()},o.prototype.iadd=function(t){if(0!==this.negative&&0===t.negative)return this.negative=0,e=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();this.length>t.length?(r=this,i=t):(r=t,i=this);for(var e,r,i,n=0,o=0;o<i.length;o++)e=(0|r.words[o])+(0|i.words[o])+n,this.words[o]=0x3ffffff&e,n=e>>>26;for(;0!==n&&o<r.length;o++)e=(0|r.words[o])+n,this.words[o]=0x3ffffff&e,n=e>>>26;if(this.length=r.length,0!==n)this.words[this.length]=n,this.length++;else if(r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this},o.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},o.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e,r,i=this.iadd(t);return t.negative=1,i._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var n=this.cmp(t);if(0===n)return this.negative=0,this.length=1,this.words[0]=0,this;n>0?(e=this,r=t):(e=t,r=this);for(var o=0,s=0;s<r.length;s++)o=(i=(0|e.words[s])-(0|r.words[s])+o)>>26,this.words[s]=0x3ffffff&i;for(;0!==o&&s<e.length;s++)o=(i=(0|e.words[s])+o)>>26,this.words[s]=0x3ffffff&i;if(0===o&&s<e.length&&e!==this)for(;s<e.length;s++)this.words[s]=e.words[s];return this.length=Math.max(this.length,s),e!==this&&(this.negative=1),this._strip()},o.prototype.sub=function(t){return this.clone().isub(t)};var y=function(t,e,r){var i,n,o,s=t.words,f=e.words,a=r.words,h=0,u=0|s[0],l=8191&u,c=u>>>13,d=0|s[1],p=8191&d,m=d>>>13,y=0|s[2],g=8191&y,w=y>>>13,v=0|s[3],b=8191&v,x=v>>>13,E=0|s[4],M=8191&E,B=E>>>13,A=0|s[5],_=8191&A,O=A>>>13,I=0|s[6],S=8191&I,U=I>>>13,k=0|s[7],R=8191&k,z=k>>>13,T=0|s[8],L=8191&T,N=T>>>13,j=0|s[9],C=8191&j,q=j>>>13,P=0|f[0],H=8191&P,F=P>>>13,D=0|f[1],$=8191&D,Z=D>>>13,V=0|f[2],W=8191&V,G=V>>>13,Y=0|f[3],K=8191&Y,Q=Y>>>13,X=0|f[4],J=8191&X,tt=X>>>13,te=0|f[5],tr=8191&te,ti=te>>>13,tn=0|f[6],to=8191&tn,ts=tn>>>13,tf=0|f[7],ta=8191&tf,th=tf>>>13,tu=0|f[8],tl=8191&tu,tc=tu>>>13,td=0|f[9],tp=8191&td,tm=td>>>13;r.negative=t.negative^e.negative,r.length=19,i=Math.imul(l,H);var ty=(h+i|0)+((8191&(n=(n=Math.imul(l,F))+Math.imul(c,H)|0))<<13)|0;h=((o=Math.imul(c,F))+(n>>>13)|0)+(ty>>>26)|0,ty&=0x3ffffff,i=Math.imul(p,H),n=(n=Math.imul(p,F))+Math.imul(m,H)|0,o=Math.imul(m,F),i=i+Math.imul(l,$)|0;var tg=(h+i|0)+((8191&(n=(n=n+Math.imul(l,Z)|0)+Math.imul(c,$)|0))<<13)|0;h=((o=o+Math.imul(c,Z)|0)+(n>>>13)|0)+(tg>>>26)|0,tg&=0x3ffffff,i=Math.imul(g,H),n=(n=Math.imul(g,F))+Math.imul(w,H)|0,o=Math.imul(w,F),i=i+Math.imul(p,$)|0,n=(n=n+Math.imul(p,Z)|0)+Math.imul(m,$)|0,o=o+Math.imul(m,Z)|0,i=i+Math.imul(l,W)|0;var tw=(h+i|0)+((8191&(n=(n=n+Math.imul(l,G)|0)+Math.imul(c,W)|0))<<13)|0;h=((o=o+Math.imul(c,G)|0)+(n>>>13)|0)+(tw>>>26)|0,tw&=0x3ffffff,i=Math.imul(b,H),n=(n=Math.imul(b,F))+Math.imul(x,H)|0,o=Math.imul(x,F),i=i+Math.imul(g,$)|0,n=(n=n+Math.imul(g,Z)|0)+Math.imul(w,$)|0,o=o+Math.imul(w,Z)|0,i=i+Math.imul(p,W)|0,n=(n=n+Math.imul(p,G)|0)+Math.imul(m,W)|0,o=o+Math.imul(m,G)|0,i=i+Math.imul(l,K)|0;var tv=(h+i|0)+((8191&(n=(n=n+Math.imul(l,Q)|0)+Math.imul(c,K)|0))<<13)|0;h=((o=o+Math.imul(c,Q)|0)+(n>>>13)|0)+(tv>>>26)|0,tv&=0x3ffffff,i=Math.imul(M,H),n=(n=Math.imul(M,F))+Math.imul(B,H)|0,o=Math.imul(B,F),i=i+Math.imul(b,$)|0,n=(n=n+Math.imul(b,Z)|0)+Math.imul(x,$)|0,o=o+Math.imul(x,Z)|0,i=i+Math.imul(g,W)|0,n=(n=n+Math.imul(g,G)|0)+Math.imul(w,W)|0,o=o+Math.imul(w,G)|0,i=i+Math.imul(p,K)|0,n=(n=n+Math.imul(p,Q)|0)+Math.imul(m,K)|0,o=o+Math.imul(m,Q)|0,i=i+Math.imul(l,J)|0;var tb=(h+i|0)+((8191&(n=(n=n+Math.imul(l,tt)|0)+Math.imul(c,J)|0))<<13)|0;h=((o=o+Math.imul(c,tt)|0)+(n>>>13)|0)+(tb>>>26)|0,tb&=0x3ffffff,i=Math.imul(_,H),n=(n=Math.imul(_,F))+Math.imul(O,H)|0,o=Math.imul(O,F),i=i+Math.imul(M,$)|0,n=(n=n+Math.imul(M,Z)|0)+Math.imul(B,$)|0,o=o+Math.imul(B,Z)|0,i=i+Math.imul(b,W)|0,n=(n=n+Math.imul(b,G)|0)+Math.imul(x,W)|0,o=o+Math.imul(x,G)|0,i=i+Math.imul(g,K)|0,n=(n=n+Math.imul(g,Q)|0)+Math.imul(w,K)|0,o=o+Math.imul(w,Q)|0,i=i+Math.imul(p,J)|0,n=(n=n+Math.imul(p,tt)|0)+Math.imul(m,J)|0,o=o+Math.imul(m,tt)|0,i=i+Math.imul(l,tr)|0;var tx=(h+i|0)+((8191&(n=(n=n+Math.imul(l,ti)|0)+Math.imul(c,tr)|0))<<13)|0;h=((o=o+Math.imul(c,ti)|0)+(n>>>13)|0)+(tx>>>26)|0,tx&=0x3ffffff,i=Math.imul(S,H),n=(n=Math.imul(S,F))+Math.imul(U,H)|0,o=Math.imul(U,F),i=i+Math.imul(_,$)|0,n=(n=n+Math.imul(_,Z)|0)+Math.imul(O,$)|0,o=o+Math.imul(O,Z)|0,i=i+Math.imul(M,W)|0,n=(n=n+Math.imul(M,G)|0)+Math.imul(B,W)|0,o=o+Math.imul(B,G)|0,i=i+Math.imul(b,K)|0,n=(n=n+Math.imul(b,Q)|0)+Math.imul(x,K)|0,o=o+Math.imul(x,Q)|0,i=i+Math.imul(g,J)|0,n=(n=n+Math.imul(g,tt)|0)+Math.imul(w,J)|0,o=o+Math.imul(w,tt)|0,i=i+Math.imul(p,tr)|0,n=(n=n+Math.imul(p,ti)|0)+Math.imul(m,tr)|0,o=o+Math.imul(m,ti)|0,i=i+Math.imul(l,to)|0;var tE=(h+i|0)+((8191&(n=(n=n+Math.imul(l,ts)|0)+Math.imul(c,to)|0))<<13)|0;h=((o=o+Math.imul(c,ts)|0)+(n>>>13)|0)+(tE>>>26)|0,tE&=0x3ffffff,i=Math.imul(R,H),n=(n=Math.imul(R,F))+Math.imul(z,H)|0,o=Math.imul(z,F),i=i+Math.imul(S,$)|0,n=(n=n+Math.imul(S,Z)|0)+Math.imul(U,$)|0,o=o+Math.imul(U,Z)|0,i=i+Math.imul(_,W)|0,n=(n=n+Math.imul(_,G)|0)+Math.imul(O,W)|0,o=o+Math.imul(O,G)|0,i=i+Math.imul(M,K)|0,n=(n=n+Math.imul(M,Q)|0)+Math.imul(B,K)|0,o=o+Math.imul(B,Q)|0,i=i+Math.imul(b,J)|0,n=(n=n+Math.imul(b,tt)|0)+Math.imul(x,J)|0,o=o+Math.imul(x,tt)|0,i=i+Math.imul(g,tr)|0,n=(n=n+Math.imul(g,ti)|0)+Math.imul(w,tr)|0,o=o+Math.imul(w,ti)|0,i=i+Math.imul(p,to)|0,n=(n=n+Math.imul(p,ts)|0)+Math.imul(m,to)|0,o=o+Math.imul(m,ts)|0,i=i+Math.imul(l,ta)|0;var tM=(h+i|0)+((8191&(n=(n=n+Math.imul(l,th)|0)+Math.imul(c,ta)|0))<<13)|0;h=((o=o+Math.imul(c,th)|0)+(n>>>13)|0)+(tM>>>26)|0,tM&=0x3ffffff,i=Math.imul(L,H),n=(n=Math.imul(L,F))+Math.imul(N,H)|0,o=Math.imul(N,F),i=i+Math.imul(R,$)|0,n=(n=n+Math.imul(R,Z)|0)+Math.imul(z,$)|0,o=o+Math.imul(z,Z)|0,i=i+Math.imul(S,W)|0,n=(n=n+Math.imul(S,G)|0)+Math.imul(U,W)|0,o=o+Math.imul(U,G)|0,i=i+Math.imul(_,K)|0,n=(n=n+Math.imul(_,Q)|0)+Math.imul(O,K)|0,o=o+Math.imul(O,Q)|0,i=i+Math.imul(M,J)|0,n=(n=n+Math.imul(M,tt)|0)+Math.imul(B,J)|0,o=o+Math.imul(B,tt)|0,i=i+Math.imul(b,tr)|0,n=(n=n+Math.imul(b,ti)|0)+Math.imul(x,tr)|0,o=o+Math.imul(x,ti)|0,i=i+Math.imul(g,to)|0,n=(n=n+Math.imul(g,ts)|0)+Math.imul(w,to)|0,o=o+Math.imul(w,ts)|0,i=i+Math.imul(p,ta)|0,n=(n=n+Math.imul(p,th)|0)+Math.imul(m,ta)|0,o=o+Math.imul(m,th)|0,i=i+Math.imul(l,tl)|0;var tB=(h+i|0)+((8191&(n=(n=n+Math.imul(l,tc)|0)+Math.imul(c,tl)|0))<<13)|0;h=((o=o+Math.imul(c,tc)|0)+(n>>>13)|0)+(tB>>>26)|0,tB&=0x3ffffff,i=Math.imul(C,H),n=(n=Math.imul(C,F))+Math.imul(q,H)|0,o=Math.imul(q,F),i=i+Math.imul(L,$)|0,n=(n=n+Math.imul(L,Z)|0)+Math.imul(N,$)|0,o=o+Math.imul(N,Z)|0,i=i+Math.imul(R,W)|0,n=(n=n+Math.imul(R,G)|0)+Math.imul(z,W)|0,o=o+Math.imul(z,G)|0,i=i+Math.imul(S,K)|0,n=(n=n+Math.imul(S,Q)|0)+Math.imul(U,K)|0,o=o+Math.imul(U,Q)|0,i=i+Math.imul(_,J)|0,n=(n=n+Math.imul(_,tt)|0)+Math.imul(O,J)|0,o=o+Math.imul(O,tt)|0,i=i+Math.imul(M,tr)|0,n=(n=n+Math.imul(M,ti)|0)+Math.imul(B,tr)|0,o=o+Math.imul(B,ti)|0,i=i+Math.imul(b,to)|0,n=(n=n+Math.imul(b,ts)|0)+Math.imul(x,to)|0,o=o+Math.imul(x,ts)|0,i=i+Math.imul(g,ta)|0,n=(n=n+Math.imul(g,th)|0)+Math.imul(w,ta)|0,o=o+Math.imul(w,th)|0,i=i+Math.imul(p,tl)|0,n=(n=n+Math.imul(p,tc)|0)+Math.imul(m,tl)|0,o=o+Math.imul(m,tc)|0,i=i+Math.imul(l,tp)|0;var tA=(h+i|0)+((8191&(n=(n=n+Math.imul(l,tm)|0)+Math.imul(c,tp)|0))<<13)|0;h=((o=o+Math.imul(c,tm)|0)+(n>>>13)|0)+(tA>>>26)|0,tA&=0x3ffffff,i=Math.imul(C,$),n=(n=Math.imul(C,Z))+Math.imul(q,$)|0,o=Math.imul(q,Z),i=i+Math.imul(L,W)|0,n=(n=n+Math.imul(L,G)|0)+Math.imul(N,W)|0,o=o+Math.imul(N,G)|0,i=i+Math.imul(R,K)|0,n=(n=n+Math.imul(R,Q)|0)+Math.imul(z,K)|0,o=o+Math.imul(z,Q)|0,i=i+Math.imul(S,J)|0,n=(n=n+Math.imul(S,tt)|0)+Math.imul(U,J)|0,o=o+Math.imul(U,tt)|0,i=i+Math.imul(_,tr)|0,n=(n=n+Math.imul(_,ti)|0)+Math.imul(O,tr)|0,o=o+Math.imul(O,ti)|0,i=i+Math.imul(M,to)|0,n=(n=n+Math.imul(M,ts)|0)+Math.imul(B,to)|0,o=o+Math.imul(B,ts)|0,i=i+Math.imul(b,ta)|0,n=(n=n+Math.imul(b,th)|0)+Math.imul(x,ta)|0,o=o+Math.imul(x,th)|0,i=i+Math.imul(g,tl)|0,n=(n=n+Math.imul(g,tc)|0)+Math.imul(w,tl)|0,o=o+Math.imul(w,tc)|0,i=i+Math.imul(p,tp)|0;var t_=(h+i|0)+((8191&(n=(n=n+Math.imul(p,tm)|0)+Math.imul(m,tp)|0))<<13)|0;h=((o=o+Math.imul(m,tm)|0)+(n>>>13)|0)+(t_>>>26)|0,t_&=0x3ffffff,i=Math.imul(C,W),n=(n=Math.imul(C,G))+Math.imul(q,W)|0,o=Math.imul(q,G),i=i+Math.imul(L,K)|0,n=(n=n+Math.imul(L,Q)|0)+Math.imul(N,K)|0,o=o+Math.imul(N,Q)|0,i=i+Math.imul(R,J)|0,n=(n=n+Math.imul(R,tt)|0)+Math.imul(z,J)|0,o=o+Math.imul(z,tt)|0,i=i+Math.imul(S,tr)|0,n=(n=n+Math.imul(S,ti)|0)+Math.imul(U,tr)|0,o=o+Math.imul(U,ti)|0,i=i+Math.imul(_,to)|0,n=(n=n+Math.imul(_,ts)|0)+Math.imul(O,to)|0,o=o+Math.imul(O,ts)|0,i=i+Math.imul(M,ta)|0,n=(n=n+Math.imul(M,th)|0)+Math.imul(B,ta)|0,o=o+Math.imul(B,th)|0,i=i+Math.imul(b,tl)|0,n=(n=n+Math.imul(b,tc)|0)+Math.imul(x,tl)|0,o=o+Math.imul(x,tc)|0,i=i+Math.imul(g,tp)|0;var tO=(h+i|0)+((8191&(n=(n=n+Math.imul(g,tm)|0)+Math.imul(w,tp)|0))<<13)|0;h=((o=o+Math.imul(w,tm)|0)+(n>>>13)|0)+(tO>>>26)|0,tO&=0x3ffffff,i=Math.imul(C,K),n=(n=Math.imul(C,Q))+Math.imul(q,K)|0,o=Math.imul(q,Q),i=i+Math.imul(L,J)|0,n=(n=n+Math.imul(L,tt)|0)+Math.imul(N,J)|0,o=o+Math.imul(N,tt)|0,i=i+Math.imul(R,tr)|0,n=(n=n+Math.imul(R,ti)|0)+Math.imul(z,tr)|0,o=o+Math.imul(z,ti)|0,i=i+Math.imul(S,to)|0,n=(n=n+Math.imul(S,ts)|0)+Math.imul(U,to)|0,o=o+Math.imul(U,ts)|0,i=i+Math.imul(_,ta)|0,n=(n=n+Math.imul(_,th)|0)+Math.imul(O,ta)|0,o=o+Math.imul(O,th)|0,i=i+Math.imul(M,tl)|0,n=(n=n+Math.imul(M,tc)|0)+Math.imul(B,tl)|0,o=o+Math.imul(B,tc)|0,i=i+Math.imul(b,tp)|0;var tI=(h+i|0)+((8191&(n=(n=n+Math.imul(b,tm)|0)+Math.imul(x,tp)|0))<<13)|0;h=((o=o+Math.imul(x,tm)|0)+(n>>>13)|0)+(tI>>>26)|0,tI&=0x3ffffff,i=Math.imul(C,J),n=(n=Math.imul(C,tt))+Math.imul(q,J)|0,o=Math.imul(q,tt),i=i+Math.imul(L,tr)|0,n=(n=n+Math.imul(L,ti)|0)+Math.imul(N,tr)|0,o=o+Math.imul(N,ti)|0,i=i+Math.imul(R,to)|0,n=(n=n+Math.imul(R,ts)|0)+Math.imul(z,to)|0,o=o+Math.imul(z,ts)|0,i=i+Math.imul(S,ta)|0,n=(n=n+Math.imul(S,th)|0)+Math.imul(U,ta)|0,o=o+Math.imul(U,th)|0,i=i+Math.imul(_,tl)|0,n=(n=n+Math.imul(_,tc)|0)+Math.imul(O,tl)|0,o=o+Math.imul(O,tc)|0,i=i+Math.imul(M,tp)|0;var tS=(h+i|0)+((8191&(n=(n=n+Math.imul(M,tm)|0)+Math.imul(B,tp)|0))<<13)|0;h=((o=o+Math.imul(B,tm)|0)+(n>>>13)|0)+(tS>>>26)|0,tS&=0x3ffffff,i=Math.imul(C,tr),n=(n=Math.imul(C,ti))+Math.imul(q,tr)|0,o=Math.imul(q,ti),i=i+Math.imul(L,to)|0,n=(n=n+Math.imul(L,ts)|0)+Math.imul(N,to)|0,o=o+Math.imul(N,ts)|0,i=i+Math.imul(R,ta)|0,n=(n=n+Math.imul(R,th)|0)+Math.imul(z,ta)|0,o=o+Math.imul(z,th)|0,i=i+Math.imul(S,tl)|0,n=(n=n+Math.imul(S,tc)|0)+Math.imul(U,tl)|0,o=o+Math.imul(U,tc)|0,i=i+Math.imul(_,tp)|0;var tU=(h+i|0)+((8191&(n=(n=n+Math.imul(_,tm)|0)+Math.imul(O,tp)|0))<<13)|0;h=((o=o+Math.imul(O,tm)|0)+(n>>>13)|0)+(tU>>>26)|0,tU&=0x3ffffff,i=Math.imul(C,to),n=(n=Math.imul(C,ts))+Math.imul(q,to)|0,o=Math.imul(q,ts),i=i+Math.imul(L,ta)|0,n=(n=n+Math.imul(L,th)|0)+Math.imul(N,ta)|0,o=o+Math.imul(N,th)|0,i=i+Math.imul(R,tl)|0,n=(n=n+Math.imul(R,tc)|0)+Math.imul(z,tl)|0,o=o+Math.imul(z,tc)|0,i=i+Math.imul(S,tp)|0;var tk=(h+i|0)+((8191&(n=(n=n+Math.imul(S,tm)|0)+Math.imul(U,tp)|0))<<13)|0;h=((o=o+Math.imul(U,tm)|0)+(n>>>13)|0)+(tk>>>26)|0,tk&=0x3ffffff,i=Math.imul(C,ta),n=(n=Math.imul(C,th))+Math.imul(q,ta)|0,o=Math.imul(q,th),i=i+Math.imul(L,tl)|0,n=(n=n+Math.imul(L,tc)|0)+Math.imul(N,tl)|0,o=o+Math.imul(N,tc)|0,i=i+Math.imul(R,tp)|0;var tR=(h+i|0)+((8191&(n=(n=n+Math.imul(R,tm)|0)+Math.imul(z,tp)|0))<<13)|0;h=((o=o+Math.imul(z,tm)|0)+(n>>>13)|0)+(tR>>>26)|0,tR&=0x3ffffff,i=Math.imul(C,tl),n=(n=Math.imul(C,tc))+Math.imul(q,tl)|0,o=Math.imul(q,tc),i=i+Math.imul(L,tp)|0;var tz=(h+i|0)+((8191&(n=(n=n+Math.imul(L,tm)|0)+Math.imul(N,tp)|0))<<13)|0;h=((o=o+Math.imul(N,tm)|0)+(n>>>13)|0)+(tz>>>26)|0,tz&=0x3ffffff,i=Math.imul(C,tp);var tT=(h+i|0)+((8191&(n=(n=Math.imul(C,tm))+Math.imul(q,tp)|0))<<13)|0;return h=((o=Math.imul(q,tm))+(n>>>13)|0)+(tT>>>26)|0,tT&=0x3ffffff,a[0]=ty,a[1]=tg,a[2]=tw,a[3]=tv,a[4]=tb,a[5]=tx,a[6]=tE,a[7]=tM,a[8]=tB,a[9]=tA,a[10]=t_,a[11]=tO,a[12]=tI,a[13]=tS,a[14]=tU,a[15]=tk,a[16]=tR,a[17]=tz,a[18]=tT,0!==h&&(a[19]=h,r.length++),r};function g(t,e,r){r.negative=e.negative^t.negative,r.length=t.length+e.length;for(var i=0,n=0,o=0;o<r.length-1;o++){var s=n;n=0;for(var f=0x3ffffff&i,a=Math.min(o,e.length-1),h=Math.max(0,o-t.length+1);h<=a;h++){var u=o-h,l=(0|t.words[u])*(0|e.words[h]),c=0x3ffffff&l;s=s+(l/0x4000000|0)|0,f=0x3ffffff&(c=c+f|0),n+=(s=s+(c>>>26)|0)>>>26,s&=0x3ffffff}r.words[o]=f,i=s,s=n}return 0!==i?r.words[o]=i:r.length--,r._strip()}function w(t,e){this.x=t,this.y=e}Math.imul||(y=m),o.prototype.mulTo=function(t,e){var r,i=this.length+t.length;return 10===this.length&&10===t.length?r=y(this,t,e):i<63?r=m(this,t,e):r=g(this,t,e),r},w.prototype.makeRBT=function(t){for(var e=Array(t),r=o.prototype._countBits(t)-1,i=0;i<t;i++)e[i]=this.revBin(i,r,t);return e},w.prototype.revBin=function(t,e,r){if(0===t||t===r-1)return t;for(var i=0,n=0;n<e;n++)i|=(1&t)<<e-n-1,t>>=1;return i},w.prototype.permute=function(t,e,r,i,n,o){for(var s=0;s<o;s++)i[s]=e[t[s]],n[s]=r[t[s]]},w.prototype.transform=function(t,e,r,i,n,o){this.permute(o,t,e,r,i,n);for(var s=1;s<n;s<<=1)for(var f=s<<1,a=Math.cos(2*Math.PI/f),h=Math.sin(2*Math.PI/f),u=0;u<n;u+=f)for(var l=a,c=h,d=0;d<s;d++){var p=r[u+d],m=i[u+d],y=r[u+d+s],g=i[u+d+s],w=l*y-c*g;g=l*g+c*y,y=w,r[u+d]=p+y,i[u+d]=m+g,r[u+d+s]=p-y,i[u+d+s]=m-g,d!==f&&(w=a*l-h*c,c=a*c+h*l,l=w)}},w.prototype.guessLen13b=function(t,e){var r=1|Math.max(e,t),i=1&r,n=0;for(r=r/2|0;r;r>>>=1)n++;return 1<<n+1+i},w.prototype.conjugate=function(t,e,r){if(!(r<=1))for(var i=0;i<r/2;i++){var n=t[i];t[i]=t[r-i-1],t[r-i-1]=n,n=e[i],e[i]=-e[r-i-1],e[r-i-1]=-n}},w.prototype.normalize13b=function(t,e){for(var r=0,i=0;i<e/2;i++){var n=8192*Math.round(t[2*i+1]/e)+Math.round(t[2*i]/e)+r;t[i]=0x3ffffff&n,r=n<0x4000000?0:n/0x4000000|0}return t},w.prototype.convert13b=function(t,e,r,n){for(var o=0,s=0;s<e;s++)o+=0|t[s],r[2*s]=8191&o,o>>>=13,r[2*s+1]=8191&o,o>>>=13;for(s=2*e;s<n;++s)r[s]=0;i(0===o),i((-8192&o)==0)},w.prototype.stub=function(t){for(var e=Array(t),r=0;r<t;r++)e[r]=0;return e},w.prototype.mulp=function(t,e,r){var i=2*this.guessLen13b(t.length,e.length),n=this.makeRBT(i),o=this.stub(i),s=Array(i),f=Array(i),a=Array(i),h=Array(i),u=Array(i),l=Array(i),c=r.words;c.length=i,this.convert13b(t.words,t.length,s,i),this.convert13b(e.words,e.length,h,i),this.transform(s,o,f,a,i,n),this.transform(h,o,u,l,i,n);for(var d=0;d<i;d++){var p=f[d]*u[d]-a[d]*l[d];a[d]=f[d]*l[d]+a[d]*u[d],f[d]=p}return this.conjugate(f,a,i),this.transform(f,a,c,o,i,n),this.conjugate(c,o,i),this.normalize13b(c,i),r.negative=t.negative^e.negative,r.length=t.length+e.length,r._strip()},o.prototype.mul=function(t){var e=new o(null);return e.words=Array(this.length+t.length),this.mulTo(t,e)},o.prototype.mulf=function(t){var e=new o(null);return e.words=Array(this.length+t.length),g(this,t,e)},o.prototype.imul=function(t){return this.clone().mulTo(t,this)},o.prototype.imuln=function(t){var e=t<0;e&&(t=-t),i("number"==typeof t),i(t<0x4000000);for(var r=0,n=0;n<this.length;n++){var o=(0|this.words[n])*t,s=(0x3ffffff&o)+(0x3ffffff&r);r>>=26,r+=(o/0x4000000|0)+(s>>>26),this.words[n]=0x3ffffff&s}return 0!==r&&(this.words[n]=r,this.length++),this.length=0===t?1:this.length,e?this.ineg():this},o.prototype.muln=function(t){return this.clone().imuln(t)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(t){var e=function(t){for(var e=Array(t.bitLength()),r=0;r<e.length;r++){var i=r/26|0,n=r%26;e[r]=t.words[i]>>>n&1}return e}(t);if(0===e.length)return new o(1);for(var r=this,i=0;i<e.length&&0===e[i];i++,r=r.sqr());if(++i<e.length)for(var n=r.sqr();i<e.length;i++,n=n.sqr())0!==e[i]&&(r=r.mul(n));return r},o.prototype.iushln=function(t){i("number"==typeof t&&t>=0);var e,r=t%26,n=(t-r)/26,o=0x3ffffff>>>26-r<<26-r;if(0!==r){var s=0;for(e=0;e<this.length;e++){var f=this.words[e]&o,a=(0|this.words[e])-f<<r;this.words[e]=a|s,s=f>>>26-r}s&&(this.words[e]=s,this.length++)}if(0!==n){for(e=this.length-1;e>=0;e--)this.words[e+n]=this.words[e];for(e=0;e<n;e++)this.words[e]=0;this.length+=n}return this._strip()},o.prototype.ishln=function(t){return i(0===this.negative),this.iushln(t)},o.prototype.iushrn=function(t,e,r){i("number"==typeof t&&t>=0);var n=e?(e-e%26)/26:0,o=t%26,s=Math.min((t-o)/26,this.length),f=0x3ffffff^0x3ffffff>>>o<<o;if(n-=s,n=Math.max(0,n),r){for(var a=0;a<s;a++)r.words[a]=this.words[a];r.length=s}if(0===s);else if(this.length>s)for(this.length-=s,a=0;a<this.length;a++)this.words[a]=this.words[a+s];else this.words[0]=0,this.length=1;var h=0;for(a=this.length-1;a>=0&&(0!==h||a>=n);a--){var u=0|this.words[a];this.words[a]=h<<26-o|u>>>o,h=u&f}return r&&0!==h&&(r.words[r.length++]=h),0===this.length&&(this.words[0]=0,this.length=1),this._strip()},o.prototype.ishrn=function(t,e,r){return i(0===this.negative),this.iushrn(t,e,r)},o.prototype.shln=function(t){return this.clone().ishln(t)},o.prototype.ushln=function(t){return this.clone().iushln(t)},o.prototype.shrn=function(t){return this.clone().ishrn(t)},o.prototype.ushrn=function(t){return this.clone().iushrn(t)},o.prototype.testn=function(t){i("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26;return!(this.length<=r)&&!!(this.words[r]&1<<e)},o.prototype.imaskn=function(t){i("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26;return(i(0===this.negative,"imaskn works only with positive numbers"),this.length<=r)?this:(0!==e&&r++,this.length=Math.min(r,this.length),0!==e&&(this.words[this.length-1]&=0x3ffffff^0x3ffffff>>>e<<e),this._strip())},o.prototype.maskn=function(t){return this.clone().imaskn(t)},o.prototype.iaddn=function(t){return(i("number"==typeof t),i(t<0x4000000),t<0)?this.isubn(-t):0!==this.negative?(1===this.length&&(0|this.words[0])<=t?(this.words[0]=t-(0|this.words[0]),this.negative=0):(this.negative=0,this.isubn(t),this.negative=1),this):this._iaddn(t)},o.prototype._iaddn=function(t){this.words[0]+=t;for(var e=0;e<this.length&&this.words[e]>=0x4000000;e++)this.words[e]-=0x4000000,e===this.length-1?this.words[e+1]=1:this.words[e+1]++;return this.length=Math.max(this.length,e+1),this},o.prototype.isubn=function(t){if(i("number"==typeof t),i(t<0x4000000),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var e=0;e<this.length&&this.words[e]<0;e++)this.words[e]+=0x4000000,this.words[e+1]-=1;return this._strip()},o.prototype.addn=function(t){return this.clone().iaddn(t)},o.prototype.subn=function(t){return this.clone().isubn(t)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(t,e,r){var n,o,s=t.length+r;this._expand(s);var f=0;for(n=0;n<t.length;n++){o=(0|this.words[n+r])+f;var a=(0|t.words[n])*e;o-=0x3ffffff&a,f=(o>>26)-(a/0x4000000|0),this.words[n+r]=0x3ffffff&o}for(;n<this.length-r;n++)f=(o=(0|this.words[n+r])+f)>>26,this.words[n+r]=0x3ffffff&o;if(0===f)return this._strip();for(i(-1===f),f=0,n=0;n<this.length;n++)f=(o=-(0|this.words[n])+f)>>26,this.words[n]=0x3ffffff&o;return this.negative=1,this._strip()},o.prototype._wordDiv=function(t,e){var r,i=this.length-t.length,n=this.clone(),s=t,f=0|s.words[s.length-1];0!=(i=26-this._countBits(f))&&(s=s.ushln(i),n.iushln(i),f=0|s.words[s.length-1]);var a=n.length-s.length;if("mod"!==e){(r=new o(null)).length=a+1,r.words=Array(r.length);for(var h=0;h<r.length;h++)r.words[h]=0}var u=n.clone()._ishlnsubmul(s,1,a);0===u.negative&&(n=u,r&&(r.words[a]=1));for(var l=a-1;l>=0;l--){var c=(0|n.words[s.length+l])*0x4000000+(0|n.words[s.length+l-1]);for(c=Math.min(c/f|0,0x3ffffff),n._ishlnsubmul(s,c,l);0!==n.negative;)c--,n.negative=0,n._ishlnsubmul(s,1,l),n.isZero()||(n.negative^=1);r&&(r.words[l]=c)}return r&&r._strip(),n._strip(),"div"!==e&&0!==i&&n.iushrn(i),{div:r||null,mod:n}},o.prototype.divmod=function(t,e,r){var n,s,f;return(i(!t.isZero()),this.isZero())?{div:new o(0),mod:new o(0)}:0!==this.negative&&0===t.negative?(f=this.neg().divmod(t,e),"mod"!==e&&(n=f.div.neg()),"div"!==e&&(s=f.mod.neg(),r&&0!==s.negative&&s.iadd(t)),{div:n,mod:s}):0===this.negative&&0!==t.negative?(f=this.divmod(t.neg(),e),"mod"!==e&&(n=f.div.neg()),{div:n,mod:f.mod}):(this.negative&t.negative)!=0?(f=this.neg().divmod(t.neg(),e),"div"!==e&&(s=f.mod.neg(),r&&0!==s.negative&&s.isub(t)),{div:f.div,mod:s}):t.length>this.length||0>this.cmp(t)?{div:new o(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new o(this.modrn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new o(this.modrn(t.words[0]))}:this._wordDiv(t,e)},o.prototype.div=function(t){return this.divmod(t,"div",!1).div},o.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},o.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},o.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var r=0!==e.div.negative?e.mod.isub(t):e.mod,i=t.ushrn(1),n=t.andln(1),o=r.cmp(i);return o<0||1===n&&0===o?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},o.prototype.modrn=function(t){var e=t<0;e&&(t=-t),i(t<=0x3ffffff);for(var r=0x4000000%t,n=0,o=this.length-1;o>=0;o--)n=(r*n+(0|this.words[o]))%t;return e?-n:n},o.prototype.modn=function(t){return this.modrn(t)},o.prototype.idivn=function(t){var e=t<0;e&&(t=-t),i(t<=0x3ffffff);for(var r=0,n=this.length-1;n>=0;n--){var o=(0|this.words[n])+0x4000000*r;this.words[n]=o/t|0,r=o%t}return this._strip(),e?this.ineg():this},o.prototype.divn=function(t){return this.clone().idivn(t)},o.prototype.egcd=function(t){i(0===t.negative),i(!t.isZero());var e=this,r=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var n=new o(1),s=new o(0),f=new o(0),a=new o(1),h=0;e.isEven()&&r.isEven();)e.iushrn(1),r.iushrn(1),++h;for(var u=r.clone(),l=e.clone();!e.isZero();){for(var c=0,d=1;(e.words[0]&d)==0&&c<26;++c,d<<=1);if(c>0)for(e.iushrn(c);c-- >0;)(n.isOdd()||s.isOdd())&&(n.iadd(u),s.isub(l)),n.iushrn(1),s.iushrn(1);for(var p=0,m=1;(r.words[0]&m)==0&&p<26;++p,m<<=1);if(p>0)for(r.iushrn(p);p-- >0;)(f.isOdd()||a.isOdd())&&(f.iadd(u),a.isub(l)),f.iushrn(1),a.iushrn(1);e.cmp(r)>=0?(e.isub(r),n.isub(f),s.isub(a)):(r.isub(e),f.isub(n),a.isub(s))}return{a:f,b:a,gcd:r.iushln(h)}},o.prototype._invmp=function(t){i(0===t.negative),i(!t.isZero());var e,r=this,n=t.clone();r=0!==r.negative?r.umod(t):r.clone();for(var s=new o(1),f=new o(0),a=n.clone();r.cmpn(1)>0&&n.cmpn(1)>0;){for(var h=0,u=1;(r.words[0]&u)==0&&h<26;++h,u<<=1);if(h>0)for(r.iushrn(h);h-- >0;)s.isOdd()&&s.iadd(a),s.iushrn(1);for(var l=0,c=1;(n.words[0]&c)==0&&l<26;++l,c<<=1);if(l>0)for(n.iushrn(l);l-- >0;)f.isOdd()&&f.iadd(a),f.iushrn(1);r.cmp(n)>=0?(r.isub(n),s.isub(f)):(n.isub(r),f.isub(s))}return 0>(e=0===r.cmpn(1)?s:f).cmpn(0)&&e.iadd(t),e},o.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone(),r=t.clone();e.negative=0,r.negative=0;for(var i=0;e.isEven()&&r.isEven();i++)e.iushrn(1),r.iushrn(1);for(;;){for(;e.isEven();)e.iushrn(1);for(;r.isEven();)r.iushrn(1);var n=e.cmp(r);if(n<0){var o=e;e=r,r=o}else if(0===n||0===r.cmpn(1))break;e.isub(r)}return r.iushln(i)},o.prototype.invm=function(t){return this.egcd(t).a.umod(t)},o.prototype.isEven=function(){return(1&this.words[0])==0},o.prototype.isOdd=function(){return(1&this.words[0])==1},o.prototype.andln=function(t){return this.words[0]&t},o.prototype.bincn=function(t){i("number"==typeof t);var e=t%26,r=(t-e)/26,n=1<<e;if(this.length<=r)return this._expand(r+1),this.words[r]|=n,this;for(var o=n,s=r;0!==o&&s<this.length;s++){var f=0|this.words[s];f+=o,o=f>>>26,f&=0x3ffffff,this.words[s]=f}return 0!==o&&(this.words[s]=o,this.length++),this},o.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},o.prototype.cmpn=function(t){var e,r=t<0;if(0!==this.negative&&!r)return -1;if(0===this.negative&&r)return 1;if(this._strip(),this.length>1)e=1;else{r&&(t=-t),i(t<=0x3ffffff,"Number is too big");var n=0|this.words[0];e=n===t?0:n<t?-1:1}return 0!==this.negative?0|-e:e},o.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return -1;if(0===this.negative&&0!==t.negative)return 1;var e=this.ucmp(t);return 0!==this.negative?0|-e:e},o.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return -1;for(var e=0,r=this.length-1;r>=0;r--){var i=0|this.words[r],n=0|t.words[r];if(i!==n){i<n?e=-1:i>n&&(e=1);break}}return e},o.prototype.gtn=function(t){return 1===this.cmpn(t)},o.prototype.gt=function(t){return 1===this.cmp(t)},o.prototype.gten=function(t){return this.cmpn(t)>=0},o.prototype.gte=function(t){return this.cmp(t)>=0},o.prototype.ltn=function(t){return -1===this.cmpn(t)},o.prototype.lt=function(t){return -1===this.cmp(t)},o.prototype.lten=function(t){return 0>=this.cmpn(t)},o.prototype.lte=function(t){return 0>=this.cmp(t)},o.prototype.eqn=function(t){return 0===this.cmpn(t)},o.prototype.eq=function(t){return 0===this.cmp(t)},o.red=function(t){return new A(t)},o.prototype.toRed=function(t){return i(!this.red,"Already a number in reduction context"),i(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},o.prototype.fromRed=function(){return i(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(t){return this.red=t,this},o.prototype.forceRed=function(t){return i(!this.red,"Already a number in reduction context"),this._forceRed(t)},o.prototype.redAdd=function(t){return i(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},o.prototype.redIAdd=function(t){return i(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},o.prototype.redSub=function(t){return i(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},o.prototype.redISub=function(t){return i(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},o.prototype.redShl=function(t){return i(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},o.prototype.redMul=function(t){return i(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},o.prototype.redIMul=function(t){return i(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},o.prototype.redSqr=function(){return i(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return i(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return i(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return i(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return i(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(t){return i(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var v={k256:null,p224:null,p192:null,p25519:null};function b(t,e){this.name=t,this.p=new o(e,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function x(){b.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function E(){b.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function M(){b.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function B(){b.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function A(t){if("string"==typeof t){var e=o._prime(t);this.m=e.p,this.prime=e}else i(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function _(t){A.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}b.prototype._tmp=function(){var t=new o(null);return t.words=Array(Math.ceil(this.n/13)),t},b.prototype.ireduce=function(t){var e,r=t;do this.split(r,this.tmp),e=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength();while(e>this.n);var i=e<this.n?-1:r.ucmp(this.p);return 0===i?(r.words[0]=0,r.length=1):i>0?r.isub(this.p):void 0!==r.strip?r.strip():r._strip(),r},b.prototype.split=function(t,e){t.iushrn(this.n,0,e)},b.prototype.imulK=function(t){return t.imul(this.k)},n(x,b),x.prototype.split=function(t,e){for(var r=Math.min(t.length,9),i=0;i<r;i++)e.words[i]=t.words[i];if(e.length=r,t.length<=9){t.words[0]=0,t.length=1;return}var n=t.words[9];for(i=10,e.words[e.length++]=4194303&n;i<t.length;i++){var o=0|t.words[i];t.words[i-10]=(4194303&o)<<4|n>>>22,n=o}n>>>=22,t.words[i-10]=n,0===n&&t.length>10?t.length-=10:t.length-=9},x.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,r=0;r<t.length;r++){var i=0|t.words[r];e+=977*i,t.words[r]=0x3ffffff&e,e=64*i+(e/0x4000000|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},n(E,b),n(M,b),n(B,b),B.prototype.imulK=function(t){for(var e=0,r=0;r<t.length;r++){var i=(0|t.words[r])*19+e,n=0x3ffffff&i;i>>>=26,t.words[r]=n,e=i}return 0!==e&&(t.words[t.length++]=e),t},o._prime=function(t){var e;if(v[t])return v[t];if("k256"===t)e=new x;else if("p224"===t)e=new E;else if("p192"===t)e=new M;else if("p25519"===t)e=new B;else throw Error("Unknown prime "+t);return v[t]=e,e},A.prototype._verify1=function(t){i(0===t.negative,"red works only with positives"),i(t.red,"red works only with red numbers")},A.prototype._verify2=function(t,e){i((t.negative|e.negative)==0,"red works only with positives"),i(t.red&&t.red===e.red,"red works only with red numbers")},A.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):(h(t,t.umod(this.m)._forceRed(this)),t)},A.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},A.prototype.add=function(t,e){this._verify2(t,e);var r=t.add(e);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},A.prototype.iadd=function(t,e){this._verify2(t,e);var r=t.iadd(e);return r.cmp(this.m)>=0&&r.isub(this.m),r},A.prototype.sub=function(t,e){this._verify2(t,e);var r=t.sub(e);return 0>r.cmpn(0)&&r.iadd(this.m),r._forceRed(this)},A.prototype.isub=function(t,e){this._verify2(t,e);var r=t.isub(e);return 0>r.cmpn(0)&&r.iadd(this.m),r},A.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},A.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},A.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},A.prototype.isqr=function(t){return this.imul(t,t.clone())},A.prototype.sqr=function(t){return this.mul(t,t)},A.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(i(e%2==1),3===e){var r=this.m.add(new o(1)).iushrn(2);return this.pow(t,r)}for(var n=this.m.subn(1),s=0;!n.isZero()&&0===n.andln(1);)s++,n.iushrn(1);i(!n.isZero());var f=new o(1).toRed(this),a=f.redNeg(),h=this.m.subn(1).iushrn(1),u=this.m.bitLength();for(u=new o(2*u*u).toRed(this);0!==this.pow(u,h).cmp(a);)u.redIAdd(a);for(var l=this.pow(u,n),c=this.pow(t,n.addn(1).iushrn(1)),d=this.pow(t,n),p=s;0!==d.cmp(f);){for(var m=d,y=0;0!==m.cmp(f);y++)m=m.redSqr();i(y<p);var g=this.pow(l,new o(1).iushln(p-y-1));c=c.redMul(g),l=g.redSqr(),d=d.redMul(l),p=y}return c},A.prototype.invm=function(t){var e=t._invmp(this.m);return 0!==e.negative?(e.negative=0,this.imod(e).redNeg()):this.imod(e)},A.prototype.pow=function(t,e){if(e.isZero())return new o(1).toRed(this);if(0===e.cmpn(1))return t.clone();var r=Array(16);r[0]=new o(1).toRed(this),r[1]=t;for(var i=2;i<r.length;i++)r[i]=this.mul(r[i-1],t);var n=r[0],s=0,f=0,a=e.bitLength()%26;for(0===a&&(a=26),i=e.length-1;i>=0;i--){for(var h=e.words[i],u=a-1;u>=0;u--){var l=h>>u&1;if(n!==r[0]&&(n=this.sqr(n)),0===l&&0===s){f=0;continue}s<<=1,s|=l,(4==++f||0===i&&0===u)&&(n=this.mul(n,r[s]),f=0,s=0)}a=26}return n},A.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},A.prototype.convertFrom=function(t){var e=t.clone();return e.red=null,e},o.mont=function(t){return new _(t)},n(_,A),_.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},_.prototype.convertFrom=function(t){var e=this.imod(t.mul(this.rinv));return e.red=null,e},_.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var r=t.imul(e),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=r.isub(i).iushrn(this.shift),o=n;return n.cmp(this.m)>=0?o=n.isub(this.m):0>n.cmpn(0)&&(o=n.iadd(this.m)),o._forceRed(this)},_.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new o(0)._forceRed(this);var r=t.mul(e),i=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),n=r.isub(i).iushrn(this.shift),s=n;return n.cmp(this.m)>=0?s=n.isub(this.m):0>n.cmpn(0)&&(s=n.iadd(this.m)),s._forceRed(this)},_.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t=r.nmd(t),this)},2512:(t,e)=>{"use strict";function r(t,e,r){return e<=t&&t<=r}function i(t){if(void 0===t)return{};if(t===Object(t))return t;throw TypeError("Could not convert argument to dictionary")}function n(t){this.tokens=[].slice.call(t)}function o(t,e){if(t)throw TypeError("Decoder error");return e||65533}n.prototype={endOfStream:function(){return!this.tokens.length},read:function(){return this.tokens.length?this.tokens.shift():-1},prepend:function(t){if(Array.isArray(t))for(;t.length;)this.tokens.unshift(t.pop());else this.tokens.unshift(t)},push:function(t){if(Array.isArray(t))for(;t.length;)this.tokens.push(t.shift());else this.tokens.push(t)}};var s="utf-8";function f(t,e){if(!(this instanceof f))return new f(t,e);if((t=void 0!==t?String(t).toLowerCase():s)!==s)throw Error("Encoding not supported. Only utf-8 is supported");e=i(e),this._streaming=!1,this._BOMseen=!1,this._decoder=null,this._fatal=!!e.fatal,this._ignoreBOM=!!e.ignoreBOM,Object.defineProperty(this,"encoding",{value:"utf-8"}),Object.defineProperty(this,"fatal",{value:this._fatal}),Object.defineProperty(this,"ignoreBOM",{value:this._ignoreBOM})}function a(t,e){if(!(this instanceof a))return new a(t,e);if((t=void 0!==t?String(t).toLowerCase():s)!==s)throw Error("Encoding not supported. Only utf-8 is supported");e=i(e),this._streaming=!1,this._encoder=null,this._options={fatal:!!e.fatal},Object.defineProperty(this,"encoding",{value:"utf-8"})}function h(t){var e=t.fatal,i=0,n=0,s=0,f=128,a=191;this.handler=function(t,h){if(-1===h&&0!==s)return s=0,o(e);if(-1===h)return -1;if(0===s){if(r(h,0,127))return h;if(r(h,194,223))s=1,i=h-192;else if(r(h,224,239))224===h&&(f=160),237===h&&(a=159),s=2,i=h-224;else{if(!r(h,240,244))return o(e);240===h&&(f=144),244===h&&(a=143),s=3,i=h-240}return i<<=6*s,null}if(!r(h,f,a))return i=s=n=0,f=128,a=191,t.prepend(h),o(e);if(f=128,a=191,n+=1,i+=h-128<<6*(s-n),n!==s)return null;var u=i;return i=s=n=0,u}}function u(t){t.fatal,this.handler=function(t,e){if(-1===e)return -1;if(r(e,0,127))return e;r(e,128,2047)?(i=1,n=192):r(e,2048,65535)?(i=2,n=224):r(e,65536,1114111)&&(i=3,n=240);for(var i,n,o=[(e>>6*i)+n];i>0;){var s=e>>6*(i-1);o.push(128|63&s),i-=1}return o}}f.prototype={decode:function(t,e){r="object"==typeof t&&t instanceof ArrayBuffer?new Uint8Array(t):"object"==typeof t&&"buffer"in t&&t.buffer instanceof ArrayBuffer?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):new Uint8Array(0),e=i(e),this._streaming||(this._decoder=new h({fatal:this._fatal}),this._BOMseen=!1),this._streaming=!!e.stream;for(var r,o,s=new n(r),f=[];!s.endOfStream()&&-1!==(o=this._decoder.handler(s,s.read()));)null!==o&&(Array.isArray(o)?f.push.apply(f,o):f.push(o));if(!this._streaming){do{if(-1===(o=this._decoder.handler(s,s.read())))break;if(null===o)continue;Array.isArray(o)?f.push.apply(f,o):f.push(o)}while(!s.endOfStream());this._decoder=null}!f.length||-1===["utf-8"].indexOf(this.encoding)||this._ignoreBOM||this._BOMseen||(65279===f[0]?(this._BOMseen=!0,f.shift()):this._BOMseen=!0);for(var a="",u=0;u<f.length;++u){var l=f[u];l<=65535?a+=String.fromCharCode(l):(l-=65536,a+=String.fromCharCode((l>>10)+55296,(1023&l)+56320))}return a}},a.prototype={encode:function(t,e){t=t?String(t):"",e=i(e),this._streaming||(this._encoder=new u(this._options)),this._streaming=!!e.stream;for(var r,o=[],s=new n(function(t){for(var e=String(t),r=e.length,i=0,n=[];i<r;){var o=e.charCodeAt(i);if(o<55296||o>57343)n.push(o);else if(56320<=o&&o<=57343)n.push(65533);else if(55296<=o&&o<=56319)if(i===r-1)n.push(65533);else{var s=t.charCodeAt(i+1);if(56320<=s&&s<=57343){var f=1023&o,a=1023&s;n.push(65536+(f<<10)+a),i+=1}else n.push(65533)}i+=1}return n}(t));!s.endOfStream()&&-1!==(r=this._encoder.handler(s,s.read()));)Array.isArray(r)?o.push.apply(o,r):o.push(r);if(!this._streaming){for(;-1!==(r=this._encoder.handler(s,s.read()));)Array.isArray(r)?o.push.apply(o,r):o.push(r);this._encoder=null}return new Uint8Array(o)}},e.TextEncoder=a,e.TextDecoder=f},2661:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function i(){}function n(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,i,o,s){if("function"!=typeof i)throw TypeError("The listener must be a function");var f=new n(i,o||t,s),a=r?r+e:e;return t._events[a]?t._events[a].fn?t._events[a]=[t._events[a],f]:t._events[a].push(f):(t._events[a]=f,t._eventsCount++),t}function s(t,e){0==--t._eventsCount?t._events=new i:delete t._events[e]}function f(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(r=!1)),f.prototype.eventNames=function(){var t,i,n=[];if(0===this._eventsCount)return n;for(i in t=this._events)e.call(t,i)&&n.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(t)):n},f.prototype.listeners=function(t){var e=r?r+t:t,i=this._events[e];if(!i)return[];if(i.fn)return[i.fn];for(var n=0,o=i.length,s=Array(o);n<o;n++)s[n]=i[n].fn;return s},f.prototype.listenerCount=function(t){var e=r?r+t:t,i=this._events[e];return i?i.fn?1:i.length:0},f.prototype.emit=function(t,e,i,n,o,s){var f=r?r+t:t;if(!this._events[f])return!1;var a,h,u=this._events[f],l=arguments.length;if(u.fn){switch(u.once&&this.removeListener(t,u.fn,void 0,!0),l){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,e),!0;case 3:return u.fn.call(u.context,e,i),!0;case 4:return u.fn.call(u.context,e,i,n),!0;case 5:return u.fn.call(u.context,e,i,n,o),!0;case 6:return u.fn.call(u.context,e,i,n,o,s),!0}for(h=1,a=Array(l-1);h<l;h++)a[h-1]=arguments[h];u.fn.apply(u.context,a)}else{var c,d=u.length;for(h=0;h<d;h++)switch(u[h].once&&this.removeListener(t,u[h].fn,void 0,!0),l){case 1:u[h].fn.call(u[h].context);break;case 2:u[h].fn.call(u[h].context,e);break;case 3:u[h].fn.call(u[h].context,e,i);break;case 4:u[h].fn.call(u[h].context,e,i,n);break;default:if(!a)for(c=1,a=Array(l-1);c<l;c++)a[c-1]=arguments[c];u[h].fn.apply(u[h].context,a)}}return!0},f.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},f.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},f.prototype.removeListener=function(t,e,i,n){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return s(this,o),this;var f=this._events[o];if(f.fn)f.fn!==e||n&&!f.once||i&&f.context!==i||s(this,o);else{for(var a=0,h=[],u=f.length;a<u;a++)(f[a].fn!==e||n&&!f[a].once||i&&f[a].context!==i)&&h.push(f[a]);h.length?this._events[o]=1===h.length?h[0]:h:s(this,o)}return this},f.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&s(this,e)):(this._events=new i,this._eventsCount=0),this},f.prototype.off=f.prototype.removeListener,f.prototype.addListener=f.prototype.on,f.prefixed=r,f.EventEmitter=f,t.exports=f},2888:(t,e,r)=>{"use strict";let i=r(2021).v4;t.exports=function(t,e,r,n){if("string"!=typeof t)throw TypeError(t+" must be a string");let o="number"==typeof(n=n||{}).version?n.version:2;if(1!==o&&2!==o)throw TypeError(o+" must be 1 or 2");let s={method:t};if(2===o&&(s.jsonrpc="2.0"),e){if("object"!=typeof e&&!Array.isArray(e))throw TypeError(e+" must be an object, array or omitted");s.params=e}return void 0===r?s.id=("function"==typeof n.generator?n.generator:function(){return i()})(s,n):2===o&&null===r?n.notificationIdNull&&(s.id=null):s.id=r,s}},3099:(t,e,r)=>{"use strict";r.d(e,{bI:()=>A});var i=r(8693),n=r(9989);class o extends n.Vw{constructor(t,e){super(),this.finished=!1,this.destroyed=!1,(0,n.sd)(t);let r=(0,n.ZJ)(e);if(this.iHash=t.create(),"function"!=typeof this.iHash.update)throw Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;let i=this.blockLen,o=new Uint8Array(i);o.set(r.length>i?t.create().update(r).digest():r);for(let t=0;t<o.length;t++)o[t]^=54;this.iHash.update(o),this.oHash=t.create();for(let t=0;t<o.length;t++)o[t]^=106;this.oHash.update(o),(0,n.uH)(o)}update(t){return(0,n.CC)(this),this.iHash.update(t),this}digestInto(t){(0,n.CC)(this),(0,n.DO)(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){let t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));let{oHash:e,iHash:r,finished:i,destroyed:n,blockLen:o,outputLen:s}=this;return t.finished=i,t.destroyed=n,t.blockLen=o,t.outputLen=s,t.oHash=e._cloneInto(t.oHash),t.iHash=r._cloneInto(t.iHash),t}clone(){return this._cloneInto()}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}let s=(t,e,r)=>new o(t,e).update(r).digest();s.create=(t,e)=>new o(t,e);var f=r(4341),a=r(456),h=r(4611);function u(t){void 0!==t.lowS&&(0,h.e8)("lowS",t.lowS),void 0!==t.prehash&&(0,h.e8)("prehash",t.prehash)}class l extends Error{constructor(t=""){super(t)}}let c={Err:l,_tlv:{encode:(t,e)=>{let{Err:r}=c;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(1&e.length)throw new r("tlv.encode: unpadded data");let i=e.length/2,n=(0,h.zW)(i);if(n.length/2&128)throw new r("tlv.encode: long form length too big");let o=i>127?(0,h.zW)(n.length/2|128):"";return(0,h.zW)(t)+o+n+e},decode(t,e){let{Err:r}=c,i=0;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length<2||e[i++]!==t)throw new r("tlv.decode: wrong tlv");let n=e[i++],o=0;if(128&n){let t=127&n;if(!t)throw new r("tlv.decode(long): indefinite length not supported");if(t>4)throw new r("tlv.decode(long): byte length is too big");let s=e.subarray(i,i+t);if(s.length!==t)throw new r("tlv.decode: length bytes not complete");if(0===s[0])throw new r("tlv.decode(long): zero leftmost byte");for(let t of s)o=o<<8|t;if(i+=t,o<128)throw new r("tlv.decode(long): not minimal encoding")}else o=n;let s=e.subarray(i,i+o);if(s.length!==o)throw new r("tlv.decode: wrong value length");return{v:s,l:e.subarray(i+o)}}},_int:{encode(t){let{Err:e}=c;if(t<p)throw new e("integer: negative integers are not allowed");let r=(0,h.zW)(t);if(8&Number.parseInt(r[0],16)&&(r="00"+r),1&r.length)throw new e("unexpected DER parsing assertion: unpadded hex");return r},decode(t){let{Err:e}=c;if(128&t[0])throw new e("invalid signature integer: negative");if(0===t[0]&&!(128&t[1]))throw new e("invalid signature integer: unnecessary leading zero");return(0,h.Ph)(t)}},toSig(t){let{Err:e,_int:r,_tlv:i}=c,n=(0,h.qj)("signature",t),{v:o,l:s}=i.decode(48,n);if(s.length)throw new e("invalid signature: left bytes after parsing");let{v:f,l:a}=i.decode(2,o),{v:u,l:l}=i.decode(2,a);if(l.length)throw new e("invalid signature: left bytes after parsing");return{r:r.decode(f),s:r.decode(u)}},hexFromSig(t){let{_tlv:e,_int:r}=c,i=e.encode(2,r.encode(t.r)),n=e.encode(2,r.encode(t.s));return e.encode(48,i+n)}};function d(t,e){return(0,h.My)((0,h.lq)(t,e))}let p=BigInt(0),m=BigInt(1),y=(BigInt(2),BigInt(3)),g=BigInt(4),w=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),v=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),b=BigInt(0),x=BigInt(1),E=BigInt(2),M=(t,e)=>(t+e/E)/e,B=(0,a.D0)(w,void 0,void 0,{sqrt:function(t){let e=BigInt(3),r=BigInt(6),i=BigInt(11),n=BigInt(22),o=BigInt(23),s=BigInt(44),f=BigInt(88),h=t*t*t%w,u=h*h*t%w,l=(0,a.zH)(u,e,w)*u%w,c=(0,a.zH)(l,e,w)*u%w,d=(0,a.zH)(c,E,w)*h%w,p=(0,a.zH)(d,i,w)*d%w,m=(0,a.zH)(p,n,w)*p%w,y=(0,a.zH)(m,s,w)*m%w,g=(0,a.zH)(y,f,w)*y%w,v=(0,a.zH)(g,s,w)*m%w,b=(0,a.zH)(v,e,w)*u%w,x=(0,a.zH)(b,o,w)*p%w,M=(0,a.zH)(x,r,w)*h%w,A=(0,a.zH)(M,E,w);if(!B.eql(B.sqr(A),t))throw Error("Cannot find square root");return A}}),A=function(t,e){let r=e=>(function(t){let e=function(t){let e=(0,f.hp)(t);return(0,h.Q5)(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}(t),{Fp:r,n:i,nByteLength:n,nBitLength:o}=e,s=r.BYTES+1,l=2*r.BYTES+1;function w(t){return(0,a.zi)(t,i)}function v(t){return(0,a.B8)(t,i)}let{ProjectivePoint:b,normPrivateKeyToScalar:x,weierstrassEquation:E,isWithinCurveOrder:M}=function(t){let e=function(t){let e=(0,f.hp)(t);(0,h.Q5)(e,{a:"field",b:"field"},{allowInfinityPoint:"boolean",allowedPrivateKeyLengths:"array",clearCofactor:"function",fromBytes:"function",isTorsionFree:"function",toBytes:"function",wrapPrivateKey:"boolean"});let{endo:r,Fp:i,a:n}=e;if(r){if(!i.eql(n,i.ZERO))throw Error("invalid endo: CURVE.a must be 0");if("object"!=typeof r||"bigint"!=typeof r.beta||"function"!=typeof r.splitScalar)throw Error('invalid endo: expected "beta": bigint and "splitScalar": function')}return Object.freeze({...e})}(t),{Fp:r}=e,i=(0,a.D0)(e.n,e.nBitLength),n=e.toBytes||((t,e,i)=>{let n=e.toAffine();return(0,h.Id)(Uint8Array.from([4]),r.toBytes(n.x),r.toBytes(n.y))}),o=e.fromBytes||(t=>{let e=t.subarray(1);return{x:r.fromBytes(e.subarray(0,r.BYTES)),y:r.fromBytes(e.subarray(r.BYTES,2*r.BYTES))}});function s(t){let{a:i,b:n}=e,o=r.sqr(t),s=r.mul(o,t);return r.add(r.add(s,r.mul(t,i)),n)}function u(t,e){let i=r.sqr(e),n=s(t);return r.eql(i,n)}if(!u(e.Gx,e.Gy))throw Error("bad curve params: generator point");let l=r.mul(r.pow(e.a,y),g),c=r.mul(r.sqr(e.b),BigInt(27));if(r.is0(r.add(l,c)))throw Error("bad curve params: a or b");function d(t){let r,{allowedPrivateKeyLengths:i,nByteLength:n,wrapPrivateKey:o,n:s}=e;if(i&&"bigint"!=typeof t){if((0,h.aY)(t)&&(t=(0,h.My)(t)),"string"!=typeof t||!i.includes(t.length))throw Error("invalid private key");t=t.padStart(2*n,"0")}try{r="bigint"==typeof t?t:(0,h.Ph)((0,h.qj)("private key",t,n))}catch(e){throw Error("invalid private key, expected hex or "+n+" bytes, got "+typeof t)}return o&&(r=(0,a.zi)(r,s)),(0,h.aK)("private key",r,m,s),r}function w(t){if(!(t instanceof x))throw Error("ProjectivePoint expected")}let v=(0,h.x)((t,e)=>{let{px:i,py:n,pz:o}=t;if(r.eql(o,r.ONE))return{x:i,y:n};let s=t.is0();null==e&&(e=s?r.ONE:r.inv(o));let f=r.mul(i,e),a=r.mul(n,e),h=r.mul(o,e);if(s)return{x:r.ZERO,y:r.ZERO};if(!r.eql(h,r.ONE))throw Error("invZ was invalid");return{x:f,y:a}}),b=(0,h.x)(t=>{if(t.is0()){if(e.allowInfinityPoint&&!r.is0(t.py))return;throw Error("bad point: ZERO")}let{x:i,y:n}=t.toAffine();if(!r.isValid(i)||!r.isValid(n))throw Error("bad point: x or y not FE");if(!u(i,n))throw Error("bad point: equation left != right");if(!t.isTorsionFree())throw Error("bad point: not in prime-order subgroup");return!0});class x{constructor(t,e,i){if(null==t||!r.isValid(t))throw Error("x required");if(null==e||!r.isValid(e)||r.is0(e))throw Error("y required");if(null==i||!r.isValid(i))throw Error("z required");this.px=t,this.py=e,this.pz=i,Object.freeze(this)}static fromAffine(t){let{x:e,y:i}=t||{};if(!t||!r.isValid(e)||!r.isValid(i))throw Error("invalid affine point");if(t instanceof x)throw Error("projective point not allowed");let n=t=>r.eql(t,r.ZERO);return n(e)&&n(i)?x.ZERO:new x(e,i,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(t){let e=(0,a.pS)(r,t.map(t=>t.pz));return t.map((t,r)=>t.toAffine(e[r])).map(x.fromAffine)}static fromHex(t){let e=x.fromAffine(o((0,h.qj)("pointHex",t)));return e.assertValidity(),e}static fromPrivateKey(t){return x.BASE.multiply(d(t))}static msm(t,e){return(0,f.Xf)(x,i,t,e)}_setWindowSize(t){B.setWindowSize(this,t)}assertValidity(){b(this)}hasEvenY(){let{y:t}=this.toAffine();if(r.isOdd)return!r.isOdd(t);throw Error("Field doesn't support isOdd")}equals(t){w(t);let{px:e,py:i,pz:n}=this,{px:o,py:s,pz:f}=t,a=r.eql(r.mul(e,f),r.mul(o,n)),h=r.eql(r.mul(i,f),r.mul(s,n));return a&&h}negate(){return new x(this.px,r.neg(this.py),this.pz)}double(){let{a:t,b:i}=e,n=r.mul(i,y),{px:o,py:s,pz:f}=this,a=r.ZERO,h=r.ZERO,u=r.ZERO,l=r.mul(o,o),c=r.mul(s,s),d=r.mul(f,f),p=r.mul(o,s);return p=r.add(p,p),u=r.mul(o,f),u=r.add(u,u),a=r.mul(t,u),h=r.mul(n,d),h=r.add(a,h),a=r.sub(c,h),h=r.add(c,h),h=r.mul(a,h),a=r.mul(p,a),u=r.mul(n,u),d=r.mul(t,d),p=r.sub(l,d),p=r.mul(t,p),p=r.add(p,u),u=r.add(l,l),l=r.add(u,l),l=r.add(l,d),l=r.mul(l,p),h=r.add(h,l),d=r.mul(s,f),d=r.add(d,d),l=r.mul(d,p),a=r.sub(a,l),u=r.mul(d,c),u=r.add(u,u),new x(a,h,u=r.add(u,u))}add(t){w(t);let{px:i,py:n,pz:o}=this,{px:s,py:f,pz:a}=t,h=r.ZERO,u=r.ZERO,l=r.ZERO,c=e.a,d=r.mul(e.b,y),p=r.mul(i,s),m=r.mul(n,f),g=r.mul(o,a),v=r.add(i,n),b=r.add(s,f);v=r.mul(v,b),b=r.add(p,m),v=r.sub(v,b),b=r.add(i,o);let E=r.add(s,a);return b=r.mul(b,E),E=r.add(p,g),b=r.sub(b,E),E=r.add(n,o),h=r.add(f,a),E=r.mul(E,h),h=r.add(m,g),E=r.sub(E,h),l=r.mul(c,b),h=r.mul(d,g),l=r.add(h,l),h=r.sub(m,l),l=r.add(m,l),u=r.mul(h,l),m=r.add(p,p),m=r.add(m,p),g=r.mul(c,g),b=r.mul(d,b),m=r.add(m,g),g=r.sub(p,g),g=r.mul(c,g),b=r.add(b,g),p=r.mul(m,b),u=r.add(u,p),p=r.mul(E,b),h=r.mul(v,h),h=r.sub(h,p),p=r.mul(v,m),l=r.mul(E,l),new x(h,u,l=r.add(l,p))}subtract(t){return this.add(t.negate())}is0(){return this.equals(x.ZERO)}wNAF(t){return B.wNAFCached(this,t,x.normalizeZ)}multiplyUnsafe(t){let{endo:i,n:n}=e;(0,h.aK)("scalar",t,p,n);let o=x.ZERO;if(t===p)return o;if(this.is0()||t===m)return this;if(!i||B.hasPrecomputes(this))return B.wNAFCachedUnsafe(this,t,x.normalizeZ);let{k1neg:s,k1:f,k2neg:a,k2:u}=i.splitScalar(t),l=o,c=o,d=this;for(;f>p||u>p;)f&m&&(l=l.add(d)),u&m&&(c=c.add(d)),d=d.double(),f>>=m,u>>=m;return s&&(l=l.negate()),a&&(c=c.negate()),c=new x(r.mul(c.px,i.beta),c.py,c.pz),l.add(c)}multiply(t){let i,n,{endo:o,n:s}=e;if((0,h.aK)("scalar",t,m,s),o){let{k1neg:e,k1:s,k2neg:f,k2:a}=o.splitScalar(t),{p:h,f:u}=this.wNAF(s),{p:l,f:c}=this.wNAF(a);h=B.constTimeNegate(e,h),l=B.constTimeNegate(f,l),l=new x(r.mul(l.px,o.beta),l.py,l.pz),i=h.add(l),n=u.add(c)}else{let{p:e,f:r}=this.wNAF(t);i=e,n=r}return x.normalizeZ([i,n])[0]}multiplyAndAddUnsafe(t,e,r){let i=x.BASE,n=(t,e)=>e!==p&&e!==m&&t.equals(i)?t.multiply(e):t.multiplyUnsafe(e),o=n(this,e).add(n(t,r));return o.is0()?void 0:o}toAffine(t){return v(this,t)}isTorsionFree(){let{h:t,isTorsionFree:r}=e;if(t===m)return!0;if(r)return r(x,this);throw Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){let{h:t,clearCofactor:r}=e;return t===m?this:r?r(x,this):this.multiplyUnsafe(e.h)}toRawBytes(t=!0){return(0,h.e8)("isCompressed",t),this.assertValidity(),n(x,this,t)}toHex(t=!0){return(0,h.e8)("isCompressed",t),(0,h.My)(this.toRawBytes(t))}}x.BASE=new x(e.Gx,e.Gy,r.ONE),x.ZERO=new x(r.ZERO,r.ONE,r.ZERO);let{endo:E,nBitLength:M}=e,B=(0,f.hT)(x,E?Math.ceil(M/2):M);return{CURVE:e,ProjectivePoint:x,normPrivateKeyToScalar:d,weierstrassEquation:s,isWithinCurveOrder:function(t){return(0,h.r4)(t,m,e.n)}}}({...e,toBytes(t,e,i){let n=e.toAffine(),o=r.toBytes(n.x),s=h.Id;return((0,h.e8)("isCompressed",i),i)?s(Uint8Array.from([e.hasEvenY()?2:3]),o):s(Uint8Array.from([4]),o,r.toBytes(n.y))},fromBytes(t){let e=t.length,i=t[0],n=t.subarray(1);if(e===s&&(2===i||3===i)){let t,e=(0,h.Ph)(n);if(!(0,h.r4)(e,m,r.ORDER))throw Error("Point is not on curve");let o=E(e);try{t=r.sqrt(o)}catch(t){throw Error("Point is not on curve"+(t instanceof Error?": "+t.message:""))}return(1&i)==1!=((t&m)===m)&&(t=r.neg(t)),{x:e,y:t}}if(e===l&&4===i)return{x:r.fromBytes(n.subarray(0,r.BYTES)),y:r.fromBytes(n.subarray(r.BYTES,2*r.BYTES))};throw Error("invalid Point, expected length of "+s+", or uncompressed "+l+", got "+e)}}),B=(t,e,r)=>(0,h.Ph)(t.slice(e,r));class A{constructor(t,e,r){(0,h.aK)("r",t,m,i),(0,h.aK)("s",e,m,i),this.r=t,this.s=e,null!=r&&(this.recovery=r),Object.freeze(this)}static fromCompact(t){return new A(B(t=(0,h.qj)("compactSignature",t,2*n),0,n),B(t,n,2*n))}static fromDER(t){let{r:e,s:r}=c.toSig((0,h.qj)("DER",t));return new A(e,r)}assertValidity(){}addRecoveryBit(t){return new A(this.r,this.s,t)}recoverPublicKey(t){let{r:i,s:n,recovery:o}=this,s=I((0,h.qj)("msgHash",t));if(null==o||![0,1,2,3].includes(o))throw Error("recovery id invalid");let f=2===o||3===o?i+e.n:i;if(f>=r.ORDER)throw Error("recovery id 2 or 3 invalid");let a=(1&o)==0?"02":"03",u=b.fromHex(a+d(f,r.BYTES)),l=v(f),c=w(-s*l),p=w(n*l),m=b.BASE.multiplyAndAddUnsafe(u,c,p);if(!m)throw Error("point at infinify");return m.assertValidity(),m}hasHighS(){return this.s>i>>m}normalizeS(){return this.hasHighS()?new A(this.r,w(-this.s),this.recovery):this}toDERRawBytes(){return(0,h.aT)(this.toDERHex())}toDERHex(){return c.hexFromSig(this)}toCompactRawBytes(){return(0,h.aT)(this.toCompactHex())}toCompactHex(){return d(this.r,n)+d(this.s,n)}}function _(t){if("bigint"==typeof t)return!1;if(t instanceof b)return!0;let i=(0,h.qj)("key",t).length,o=r.BYTES,s=o+1;if(!e.allowedPrivateKeyLengths&&n!==s)return i===s||i===2*o+1}let O=e.bits2int||function(t){if(t.length>8192)throw Error("input is too large");let e=(0,h.Ph)(t),r=8*t.length-o;return r>0?e>>BigInt(r):e},I=e.bits2int_modN||function(t){return w(O(t))},S=(0,h.OG)(o);function U(t){return(0,h.aK)("num < 2^"+o,t,p,S),(0,h.lq)(t,n)}let k={lowS:e.lowS,prehash:!1},R={lowS:e.lowS,prehash:!1};return b.BASE._setWindowSize(8),{CURVE:e,getPublicKey:function(t,e=!0){return b.fromPrivateKey(t).toRawBytes(e)},getSharedSecret:function(t,e,r=!0){if(!0===_(t))throw Error("first arg must be private key");if(!1===_(e))throw Error("second arg must be public key");return b.fromHex(e).multiply(x(t)).toRawBytes(r)},sign:function(t,n,o=k){let{seed:s,k2sig:f}=function(t,n,o=k){if(["recovered","canonical"].some(t=>t in o))throw Error("sign() legacy options not supported");let{hash:s,randomBytes:f}=e,{lowS:a,prehash:l,extraEntropy:c}=o;null==a&&(a=!0),t=(0,h.qj)("msgHash",t),u(o),l&&(t=(0,h.qj)("prehashed msgHash",s(t)));let d=I(t),y=x(n),g=[U(y),U(d)];if(null!=c&&!1!==c){let t=!0===c?f(r.BYTES):c;g.push((0,h.qj)("extraEntropy",t))}return{seed:(0,h.Id)(...g),k2sig:function(t){var e;let r=O(t);if(!M(r))return;let n=v(r),o=b.BASE.multiply(r).toAffine(),s=w(o.x);if(s===p)return;let f=w(n*w(d+s*y));if(f===p)return;let h=2*(o.x!==s)|Number(o.y&m),u=f;return a&&f>i>>m&&(u=(e=f)>i>>m?w(-e):e,h^=1),new A(s,u,h)}}}(t,n,o);return(0,h.fg)(e.hash.outputLen,e.nByteLength,e.hmac)(s,f)},verify:function(t,r,i,n=R){let o,s;r=(0,h.qj)("msgHash",r),i=(0,h.qj)("publicKey",i);let{lowS:f,prehash:a,format:l}=n;if(u(n),"strict"in n)throw Error("options.strict was renamed to lowS");if(void 0!==l&&"compact"!==l&&"der"!==l)throw Error("format must be compact or der");let d="string"==typeof t||(0,h.aY)(t),p=!d&&!l&&"object"==typeof t&&null!==t&&"bigint"==typeof t.r&&"bigint"==typeof t.s;if(!d&&!p)throw Error("invalid signature, expected Uint8Array, hex string or Signature instance");try{if(p&&(s=new A(t.r,t.s)),d){try{"compact"!==l&&(s=A.fromDER(t))}catch(t){if(!(t instanceof c.Err))throw t}s||"der"===l||(s=A.fromCompact(t))}o=b.fromHex(i)}catch(t){return!1}if(!s||f&&s.hasHighS())return!1;a&&(r=e.hash(r));let{r:m,s:y}=s,g=I(r),x=v(y),E=w(g*x),M=w(m*x),B=b.BASE.multiplyAndAddUnsafe(o,E,M)?.toAffine();return!!B&&w(B.x)===m},ProjectivePoint:b,Signature:A,utils:{isValidPrivateKey(t){try{return x(t),!0}catch(t){return!1}},normPrivateKeyToScalar:x,randomPrivateKey:()=>{let t=(0,a.Tp)(e.n);return(0,a.qy)(e.randomBytes(t),e.n)},precompute:(t=8,e=b.BASE)=>(e._setWindowSize(t),e.multiply(BigInt(3)),e)}}})({...t,...{hash:e,hmac:(t,...r)=>s(e,t,(0,n.Id)(...r)),randomBytes:n.po}});return{...r(e),create:r}}({a:b,b:BigInt(7),Fp:B,n:v,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:t=>{let e=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-x*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),n=BigInt("0x100000000000000000000000000000000"),o=M(e*t,v),s=M(-r*t,v),f=(0,a.zi)(t-o*e-s*i,v),h=(0,a.zi)(-o*r-s*e,v),u=f>n,l=h>n;if(u&&(f=v-f),l&&(h=v-h),f>n||h>n)throw Error("splitScalar: Endomorphism failed, k="+t);return{k1neg:u,k1:f,k2neg:l,k2:h}}}},i.sc),_={},O=t=>t.toRawBytes(!0).slice(1),I=t=>mod(t,v)},3347:(t,e,r)=>{"use strict";r.d(e,{l:()=>n});var i=r(2115);let n=({wallet:t,...e})=>t&&i.createElement("img",{src:t.adapter.icon,alt:`${t.adapter.name} icon`,...e})},3711:(t,e,r)=>{"use strict";var i=r(228).Buffer;t.exports=function(t){if(t.length>=255)throw TypeError("Alphabet too long");for(var e=new Uint8Array(256),r=0;r<e.length;r++)e[r]=255;for(var n=0;n<t.length;n++){var o=t.charAt(n),s=o.charCodeAt(0);if(255!==e[s])throw TypeError(o+" is ambiguous");e[s]=n}var f=t.length,a=t.charAt(0),h=Math.log(f)/Math.log(256),u=Math.log(256)/Math.log(f);function l(t){if("string"!=typeof t)throw TypeError("Expected String");if(0===t.length)return i.alloc(0);for(var r=0,n=0,o=0;t[r]===a;)n++,r++;for(var s=(t.length-r)*h+1>>>0,u=new Uint8Array(s);r<t.length;){var l=t.charCodeAt(r);if(l>255)return;var c=e[l];if(255===c)return;for(var d=0,p=s-1;(0!==c||d<o)&&-1!==p;p--,d++)c+=f*u[p]>>>0,u[p]=c%256>>>0,c=c/256>>>0;if(0!==c)throw Error("Non-zero carry");o=d,r++}for(var m=s-o;m!==s&&0===u[m];)m++;var y=i.allocUnsafe(n+(s-m));y.fill(0,0,n);for(var g=n;m!==s;)y[g++]=u[m++];return y}return{encode:function(e){if((Array.isArray(e)||e instanceof Uint8Array)&&(e=i.from(e)),!i.isBuffer(e))throw TypeError("Expected Buffer");if(0===e.length)return"";for(var r=0,n=0,o=0,s=e.length;o!==s&&0===e[o];)o++,r++;for(var h=(s-o)*u+1>>>0,l=new Uint8Array(h);o!==s;){for(var c=e[o],d=0,p=h-1;(0!==c||d<n)&&-1!==p;p--,d++)c+=256*l[p]>>>0,l[p]=c%f>>>0,c=c/f>>>0;if(0!==c)throw Error("Non-zero carry");n=d,o++}for(var m=h-n;m!==h&&0===l[m];)m++;for(var y=a.repeat(r);m<h;++m)y+=t.charAt(l[m]);return y},decodeUnsafe:l,decode:function(t){var e=l(t);if(e)return e;throw Error("Non-base"+f+" character")}}}},4134:(t,e,r)=>{"use strict";let i=r(7719),n=r(7610),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');let e=new Uint8Array(t);return Object.setPrototypeOf(e,f.prototype),e}function f(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return u(t)}return a(t,e,r)}function a(t,e,r){if("string"==typeof t){var i=t,n=e;if(("string"!=typeof n||""===n)&&(n="utf8"),!f.isEncoding(n))throw TypeError("Unknown encoding: "+n);let r=0|p(i,n),o=s(r),a=o.write(i,n);return a!==r&&(o=o.slice(0,a)),o}if(ArrayBuffer.isView(t)){var o=t;if(j(o,Uint8Array)){let t=new Uint8Array(o);return c(t.buffer,t.byteOffset,t.byteLength)}return l(o)}if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(j(t,ArrayBuffer)||t&&j(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(j(t,SharedArrayBuffer)||t&&j(t.buffer,SharedArrayBuffer)))return c(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');let a=t.valueOf&&t.valueOf();if(null!=a&&a!==t)return f.from(a,e,r);let h=function(t){if(f.isBuffer(t)){let e=0|d(t.length),r=s(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?s(0):l(t):"Buffer"===t.type&&Array.isArray(t.data)?l(t.data):void 0}(t);if(h)return h;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return f.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function h(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function u(t){return h(t),s(t<0?0:0|d(t))}function l(t){let e=t.length<0?0:0|d(t.length),r=s(e);for(let i=0;i<e;i+=1)r[i]=255&t[i];return r}function c(t,e,r){let i;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(i=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),f.prototype),i}function d(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(f.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||j(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);let r=t.length,i=arguments.length>2&&!0===arguments[2];if(!i&&0===r)return 0;let n=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return T(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return L(t).length;default:if(n)return i?-1:T(t).length;e=(""+e).toLowerCase(),n=!0}}function m(t,e,r){let n=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){let i=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>i)&&(r=i);let n="";for(let i=e;i<r;++i)n+=C[t[i]];return n}(this,e,r);case"utf8":case"utf-8":return v(this,e,r);case"ascii":return function(t,e,r){let i="";r=Math.min(t.length,r);for(let n=e;n<r;++n)i+=String.fromCharCode(127&t[n]);return i}(this,e,r);case"latin1":case"binary":return function(t,e,r){let i="";r=Math.min(t.length,r);for(let n=e;n<r;++n)i+=String.fromCharCode(t[n]);return i}(this,e,r);case"base64":var o,s,f;return o=this,s=e,f=r,0===s&&f===o.length?i.fromByteArray(o):i.fromByteArray(o.slice(s,f));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){let i=t.slice(e,r),n="";for(let t=0;t<i.length-1;t+=2)n+=String.fromCharCode(i[t]+256*i[t+1]);return n}(this,e,r);default:if(n)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function y(t,e,r){let i=t[e];t[e]=t[r],t[r]=i}function g(t,e,r,i,n){var o;if(0===t.length)return -1;if("string"==typeof r?(i=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=n?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(n)return -1;else r=t.length-1;else if(r<0)if(!n)return -1;else r=0;if("string"==typeof e&&(e=f.from(e,i)),f.isBuffer(e))return 0===e.length?-1:w(t,e,r,i,n);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(n)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return w(t,[e],r,i,n)}throw TypeError("val must be string, number or Buffer")}function w(t,e,r,i,n){let o,s=1,f=t.length,a=e.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(t.length<2||e.length<2)return -1;s=2,f/=2,a/=2,r/=2}function h(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(n){let i=-1;for(o=r;o<f;o++)if(h(t,o)===h(e,-1===i?0:o-i)){if(-1===i&&(i=o),o-i+1===a)return i*s}else -1!==i&&(o-=o-i),i=-1}else for(r+a>f&&(r=f-a),o=r;o>=0;o--){let r=!0;for(let i=0;i<a;i++)if(h(t,o+i)!==h(e,i)){r=!1;break}if(r)return o}return -1}function v(t,e,r){r=Math.min(t.length,r);let i=[],n=e;for(;n<r;){let e=t[n],o=null,s=e>239?4:e>223?3:e>191?2:1;if(n+s<=r){let r,i,f,a;switch(s){case 1:e<128&&(o=e);break;case 2:(192&(r=t[n+1]))==128&&(a=(31&e)<<6|63&r)>127&&(o=a);break;case 3:r=t[n+1],i=t[n+2],(192&r)==128&&(192&i)==128&&(a=(15&e)<<12|(63&r)<<6|63&i)>2047&&(a<55296||a>57343)&&(o=a);break;case 4:r=t[n+1],i=t[n+2],f=t[n+3],(192&r)==128&&(192&i)==128&&(192&f)==128&&(a=(15&e)<<18|(63&r)<<12|(63&i)<<6|63&f)>65535&&a<1114112&&(o=a)}}null===o?(o=65533,s=1):o>65535&&(o-=65536,i.push(o>>>10&1023|55296),o=56320|1023&o),i.push(o),n+=s}var o=i;let s=o.length;if(s<=4096)return String.fromCharCode.apply(String,o);let f="",a=0;for(;a<s;)f+=String.fromCharCode.apply(String,o.slice(a,a+=4096));return f}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function x(t,e,r,i,n,o){if(!f.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>n||e<o)throw RangeError('"value" argument is out of bounds');if(r+i>t.length)throw RangeError("Index out of range")}function E(t,e,r,i,n){U(e,i,n,t,r,7);let o=Number(e&BigInt(0xffffffff));t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o;let s=Number(e>>BigInt(32)&BigInt(0xffffffff));return t[r++]=s,s>>=8,t[r++]=s,s>>=8,t[r++]=s,s>>=8,t[r++]=s,r}function M(t,e,r,i,n){U(e,i,n,t,r,7);let o=Number(e&BigInt(0xffffffff));t[r+7]=o,o>>=8,t[r+6]=o,o>>=8,t[r+5]=o,o>>=8,t[r+4]=o;let s=Number(e>>BigInt(32)&BigInt(0xffffffff));return t[r+3]=s,s>>=8,t[r+2]=s,s>>=8,t[r+1]=s,s>>=8,t[r]=s,r+8}function B(t,e,r,i,n,o){if(r+i>t.length||r<0)throw RangeError("Index out of range")}function A(t,e,r,i,o){return e*=1,r>>>=0,o||B(t,e,r,4,34028234663852886e22,-34028234663852886e22),n.write(t,e,r,i,23,4),r+4}function _(t,e,r,i,o){return e*=1,r>>>=0,o||B(t,e,r,8,17976931348623157e292,-17976931348623157e292),n.write(t,e,r,i,52,8),r+8}e.Buffer=f,e.SlowBuffer=function(t){return+t!=t&&(t=0),f.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,f.TYPED_ARRAY_SUPPORT=function(){try{let t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),f.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(f.prototype,"parent",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.buffer}}),Object.defineProperty(f.prototype,"offset",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.byteOffset}}),f.poolSize=8192,f.from=function(t,e,r){return a(t,e,r)},Object.setPrototypeOf(f.prototype,Uint8Array.prototype),Object.setPrototypeOf(f,Uint8Array),f.alloc=function(t,e,r){return(h(t),t<=0)?s(t):void 0!==e?"string"==typeof r?s(t).fill(e,r):s(t).fill(e):s(t)},f.allocUnsafe=function(t){return u(t)},f.allocUnsafeSlow=function(t){return u(t)},f.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==f.prototype},f.compare=function(t,e){if(j(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),j(e,Uint8Array)&&(e=f.from(e,e.offset,e.byteLength)),!f.isBuffer(t)||!f.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let r=t.length,i=e.length;for(let n=0,o=Math.min(r,i);n<o;++n)if(t[n]!==e[n]){r=t[n],i=e[n];break}return r<i?-1:+(i<r)},f.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(t,e){let r;if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return f.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;let i=f.allocUnsafe(e),n=0;for(r=0;r<t.length;++r){let e=t[r];if(j(e,Uint8Array))n+e.length>i.length?(f.isBuffer(e)||(e=f.from(e)),e.copy(i,n)):Uint8Array.prototype.set.call(i,e,n);else if(f.isBuffer(e))e.copy(i,n);else throw TypeError('"list" argument must be an Array of Buffers');n+=e.length}return i},f.byteLength=p,f.prototype._isBuffer=!0,f.prototype.swap16=function(){let t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)y(this,e,e+1);return this},f.prototype.swap32=function(){let t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},f.prototype.swap64=function(){let t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},f.prototype.toString=function(){let t=this.length;return 0===t?"":0==arguments.length?v(this,0,t):m.apply(this,arguments)},f.prototype.toLocaleString=f.prototype.toString,f.prototype.equals=function(t){if(!f.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===f.compare(this,t)},f.prototype.inspect=function(){let t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(f.prototype[o]=f.prototype.inspect),f.prototype.compare=function(t,e,r,i,n){if(j(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),!f.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===i&&(i=0),void 0===n&&(n=this.length),e<0||r>t.length||i<0||n>this.length)throw RangeError("out of range index");if(i>=n&&e>=r)return 0;if(i>=n)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,i>>>=0,n>>>=0,this===t)return 0;let o=n-i,s=r-e,a=Math.min(o,s),h=this.slice(i,n),u=t.slice(e,r);for(let t=0;t<a;++t)if(h[t]!==u[t]){o=h[t],s=u[t];break}return o<s?-1:+(s<o)},f.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},f.prototype.indexOf=function(t,e,r){return g(this,t,e,r,!0)},f.prototype.lastIndexOf=function(t,e,r){return g(this,t,e,r,!1)},f.prototype.write=function(t,e,r,i){var n,o,s,f,a,h,u,l;if(void 0===e)i="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)i=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===i&&(i="utf8")):(i=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let c=this.length-e;if((void 0===r||r>c)&&(r=c),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");i||(i="utf8");let d=!1;for(;;)switch(i){case"hex":return function(t,e,r,i){let n;r=Number(r)||0;let o=t.length-r;i?(i=Number(i))>o&&(i=o):i=o;let s=e.length;for(i>s/2&&(i=s/2),n=0;n<i;++n){var f;let i=parseInt(e.substr(2*n,2),16);if((f=i)!=f)break;t[r+n]=i}return n}(this,t,e,r);case"utf8":case"utf-8":return n=e,o=r,N(T(t,this.length-n),this,n,o);case"ascii":case"latin1":case"binary":return s=e,f=r,N(function(t){let e=[];for(let r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(t),this,s,f);case"base64":return a=e,h=r,N(L(t),this,a,h);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return u=e,l=r,N(function(t,e){let r,i,n=[];for(let o=0;o<t.length&&!((e-=2)<0);++o)i=(r=t.charCodeAt(o))>>8,n.push(r%256),n.push(i);return n}(t,this.length-u),this,u,l);default:if(d)throw TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),d=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},f.prototype.slice=function(t,e){let r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);let i=this.subarray(t,e);return Object.setPrototypeOf(i,f.prototype),i},f.prototype.readUintLE=f.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);let i=this[t],n=1,o=0;for(;++o<e&&(n*=256);)i+=this[t+o]*n;return i},f.prototype.readUintBE=f.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);let i=this[t+--e],n=1;for(;e>0&&(n*=256);)i+=this[t+--e]*n;return i},f.prototype.readUint8=f.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},f.prototype.readUint16LE=f.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},f.prototype.readUint16BE=f.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},f.prototype.readUint32LE=f.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},f.prototype.readUint32BE=f.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},f.prototype.readBigUInt64LE=q(function(t){k(t>>>=0,"offset");let e=this[t],r=this[t+7];(void 0===e||void 0===r)&&R(t,this.length-8);let i=e+256*this[++t]+65536*this[++t]+0x1000000*this[++t],n=this[++t]+256*this[++t]+65536*this[++t]+0x1000000*r;return BigInt(i)+(BigInt(n)<<BigInt(32))}),f.prototype.readBigUInt64BE=q(function(t){k(t>>>=0,"offset");let e=this[t],r=this[t+7];(void 0===e||void 0===r)&&R(t,this.length-8);let i=0x1000000*e+65536*this[++t]+256*this[++t]+this[++t],n=0x1000000*this[++t]+65536*this[++t]+256*this[++t]+r;return(BigInt(i)<<BigInt(32))+BigInt(n)}),f.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);let i=this[t],n=1,o=0;for(;++o<e&&(n*=256);)i+=this[t+o]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*e)),i},f.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);let i=e,n=1,o=this[t+--i];for(;i>0&&(n*=256);)o+=this[t+--i]*n;return o>=(n*=128)&&(o-=Math.pow(2,8*e)),o},f.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},f.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);let r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},f.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);let r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},f.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},f.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},f.prototype.readBigInt64LE=q(function(t){k(t>>>=0,"offset");let e=this[t],r=this[t+7];return(void 0===e||void 0===r)&&R(t,this.length-8),(BigInt(this[t+4]+256*this[t+5]+65536*this[t+6]+(r<<24))<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+0x1000000*this[++t])}),f.prototype.readBigInt64BE=q(function(t){k(t>>>=0,"offset");let e=this[t],r=this[t+7];return(void 0===e||void 0===r)&&R(t,this.length-8),(BigInt((e<<24)+65536*this[++t]+256*this[++t]+this[++t])<<BigInt(32))+BigInt(0x1000000*this[++t]+65536*this[++t]+256*this[++t]+r)}),f.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),n.read(this,t,!0,23,4)},f.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),n.read(this,t,!1,23,4)},f.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),n.read(this,t,!0,52,8)},f.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),n.read(this,t,!1,52,8)},f.prototype.writeUintLE=f.prototype.writeUIntLE=function(t,e,r,i){if(t*=1,e>>>=0,r>>>=0,!i){let i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}let n=1,o=0;for(this[e]=255&t;++o<r&&(n*=256);)this[e+o]=t/n&255;return e+r},f.prototype.writeUintBE=f.prototype.writeUIntBE=function(t,e,r,i){if(t*=1,e>>>=0,r>>>=0,!i){let i=Math.pow(2,8*r)-1;x(this,t,e,r,i,0)}let n=r-1,o=1;for(this[e+n]=255&t;--n>=0&&(o*=256);)this[e+n]=t/o&255;return e+r},f.prototype.writeUint8=f.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,1,255,0),this[e]=255&t,e+1},f.prototype.writeUint16LE=f.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},f.prototype.writeUint16BE=f.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},f.prototype.writeUint32LE=f.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},f.prototype.writeUint32BE=f.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},f.prototype.writeBigUInt64LE=q(function(t,e=0){return E(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),f.prototype.writeBigUInt64BE=q(function(t,e=0){return M(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),f.prototype.writeIntLE=function(t,e,r,i){if(t*=1,e>>>=0,!i){let i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}let n=0,o=1,s=0;for(this[e]=255&t;++n<r&&(o*=256);)t<0&&0===s&&0!==this[e+n-1]&&(s=1),this[e+n]=(t/o|0)-s&255;return e+r},f.prototype.writeIntBE=function(t,e,r,i){if(t*=1,e>>>=0,!i){let i=Math.pow(2,8*r-1);x(this,t,e,r,i-1,-i)}let n=r-1,o=1,s=0;for(this[e+n]=255&t;--n>=0&&(o*=256);)t<0&&0===s&&0!==this[e+n+1]&&(s=1),this[e+n]=(t/o|0)-s&255;return e+r},f.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},f.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},f.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},f.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},f.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||x(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},f.prototype.writeBigInt64LE=q(function(t,e=0){return E(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),f.prototype.writeBigInt64BE=q(function(t,e=0){return M(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),f.prototype.writeFloatLE=function(t,e,r){return A(this,t,e,!0,r)},f.prototype.writeFloatBE=function(t,e,r){return A(this,t,e,!1,r)},f.prototype.writeDoubleLE=function(t,e,r){return _(this,t,e,!0,r)},f.prototype.writeDoubleBE=function(t,e,r){return _(this,t,e,!1,r)},f.prototype.copy=function(t,e,r,i){if(!f.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),i||0===i||(i=this.length),e>=t.length&&(e=t.length),e||(e=0),i>0&&i<r&&(i=r),i===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(i<0)throw RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),t.length-e<i-r&&(i=t.length-e+r);let n=i-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,i):Uint8Array.prototype.set.call(t,this.subarray(r,i),e),n},f.prototype.fill=function(t,e,r,i){let n;if("string"==typeof t){if("string"==typeof e?(i=e,e=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),void 0!==i&&"string"!=typeof i)throw TypeError("encoding must be a string");if("string"==typeof i&&!f.isEncoding(i))throw TypeError("Unknown encoding: "+i);if(1===t.length){let e=t.charCodeAt(0);("utf8"===i&&e<128||"latin1"===i)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(n=e;n<r;++n)this[n]=t;else{let o=f.isBuffer(t)?t:f.from(t,i),s=o.length;if(0===s)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(n=0;n<r-e;++n)this[n+e]=o[n%s]}return this};let O={};function I(t,e,r){O[t]=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function S(t){let e="",r=t.length,i=+("-"===t[0]);for(;r>=i+4;r-=3)e=`_${t.slice(r-3,r)}${e}`;return`${t.slice(0,r)}${e}`}function U(t,e,r,i,n,o){if(t>r||t<e){let i,n="bigint"==typeof e?"n":"";throw i=o>3?0===e||e===BigInt(0)?`>= 0${n} and < 2${n} ** ${(o+1)*8}${n}`:`>= -(2${n} ** ${(o+1)*8-1}${n}) and < 2 ** ${(o+1)*8-1}${n}`:`>= ${e}${n} and <= ${r}${n}`,new O.ERR_OUT_OF_RANGE("value",i,t)}k(n,"offset"),(void 0===i[n]||void 0===i[n+o])&&R(n,i.length-(o+1))}function k(t,e){if("number"!=typeof t)throw new O.ERR_INVALID_ARG_TYPE(e,"number",t)}function R(t,e,r){if(Math.floor(t)!==t)throw k(t,r),new O.ERR_OUT_OF_RANGE(r||"offset","an integer",t);if(e<0)throw new O.ERR_BUFFER_OUT_OF_BOUNDS;throw new O.ERR_OUT_OF_RANGE(r||"offset",`>= ${+!!r} and <= ${e}`,t)}I("ERR_BUFFER_OUT_OF_BOUNDS",function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),I("ERR_INVALID_ARG_TYPE",function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`},TypeError),I("ERR_OUT_OF_RANGE",function(t,e,r){let i=`The value of "${t}" is out of range.`,n=r;return Number.isInteger(r)&&Math.abs(r)>0x100000000?n=S(String(r)):"bigint"==typeof r&&(n=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(n=S(n)),n+="n"),i+=` It must be ${e}. Received ${n}`},RangeError);let z=/[^+/0-9A-Za-z-_]/g;function T(t,e){let r;e=e||1/0;let i=t.length,n=null,o=[];for(let s=0;s<i;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!n){if(r>56319||s+1===i){(e-=3)>-1&&o.push(239,191,189);continue}n=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),n=r;continue}r=(n-55296<<10|r-56320)+65536}else n&&(e-=3)>-1&&o.push(239,191,189);if(n=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function L(t){return i.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(z,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function N(t,e,r,i){let n;for(n=0;n<i&&!(n+r>=e.length)&&!(n>=t.length);++n)e[n+r]=t[n];return n}function j(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}let C=function(){let t="0123456789abcdef",e=Array(256);for(let r=0;r<16;++r){let i=16*r;for(let n=0;n<16;++n)e[i+n]=t[r]+t[n]}return e}();function q(t){return"undefined"==typeof BigInt?P:t}function P(){throw Error("BigInt not supported")}},4282:function(t,e,r){"use strict";var i=r(4134).Buffer,n=this&&this.__createBinding||(Object.create?function(t,e,r,i){void 0===i&&(i=r),Object.defineProperty(t,i,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),s=this&&this.__decorate||function(t,e,r,i){var n,o=arguments.length,s=o<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,r):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,i);else for(var f=t.length-1;f>=0;f--)(n=t[f])&&(s=(o<3?n(s):o>3?n(e,r,s):n(e,r))||s);return o>3&&s&&Object.defineProperty(e,r,s),s},f=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.hasOwnProperty.call(t,r)&&n(e,t,r);return o(e,t),e},a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.deserializeUnchecked=e.deserialize=e.serialize=e.BinaryReader=e.BinaryWriter=e.BorshError=e.baseDecode=e.baseEncode=void 0;let h=a(r(2437)),u=a(r(9796)),l=f(r(2512)),c=new("function"!=typeof TextDecoder?l.TextDecoder:TextDecoder)("utf-8",{fatal:!0});e.baseEncode=function(t){return"string"==typeof t&&(t=i.from(t,"utf8")),u.default.encode(i.from(t))},e.baseDecode=function(t){return i.from(u.default.decode(t))};class d extends Error{constructor(t){super(t),this.fieldPath=[],this.originalMessage=t}addToFieldPath(t){this.fieldPath.splice(0,0,t),this.message=this.originalMessage+": "+this.fieldPath.join(".")}}e.BorshError=d;class p{constructor(){this.buf=i.alloc(1024),this.length=0}maybeResize(){this.buf.length<16+this.length&&(this.buf=i.concat([this.buf,i.alloc(1024)]))}writeU8(t){this.maybeResize(),this.buf.writeUInt8(t,this.length),this.length+=1}writeU16(t){this.maybeResize(),this.buf.writeUInt16LE(t,this.length),this.length+=2}writeU32(t){this.maybeResize(),this.buf.writeUInt32LE(t,this.length),this.length+=4}writeU64(t){this.maybeResize(),this.writeBuffer(i.from(new h.default(t).toArray("le",8)))}writeU128(t){this.maybeResize(),this.writeBuffer(i.from(new h.default(t).toArray("le",16)))}writeU256(t){this.maybeResize(),this.writeBuffer(i.from(new h.default(t).toArray("le",32)))}writeU512(t){this.maybeResize(),this.writeBuffer(i.from(new h.default(t).toArray("le",64)))}writeBuffer(t){this.buf=i.concat([i.from(this.buf.subarray(0,this.length)),t,i.alloc(1024)]),this.length+=t.length}writeString(t){this.maybeResize();let e=i.from(t,"utf8");this.writeU32(e.length),this.writeBuffer(e)}writeFixedArray(t){this.writeBuffer(i.from(t))}writeArray(t,e){for(let r of(this.maybeResize(),this.writeU32(t.length),t))this.maybeResize(),e(r)}toArray(){return this.buf.subarray(0,this.length)}}function m(t,e,r){let i=r.value;r.value=function(...t){try{return i.apply(this,t)}catch(t){if(t instanceof RangeError&&["ERR_BUFFER_OUT_OF_BOUNDS","ERR_OUT_OF_RANGE"].indexOf(t.code)>=0)throw new d("Reached the end of buffer when deserializing");throw t}}}e.BinaryWriter=p;class y{constructor(t){this.buf=t,this.offset=0}readU8(){let t=this.buf.readUInt8(this.offset);return this.offset+=1,t}readU16(){let t=this.buf.readUInt16LE(this.offset);return this.offset+=2,t}readU32(){let t=this.buf.readUInt32LE(this.offset);return this.offset+=4,t}readU64(){let t=this.readBuffer(8);return new h.default(t,"le")}readU128(){let t=this.readBuffer(16);return new h.default(t,"le")}readU256(){let t=this.readBuffer(32);return new h.default(t,"le")}readU512(){let t=this.readBuffer(64);return new h.default(t,"le")}readBuffer(t){if(this.offset+t>this.buf.length)throw new d(`Expected buffer length ${t} isn't within bounds`);let e=this.buf.slice(this.offset,this.offset+t);return this.offset+=t,e}readString(){let t=this.readU32(),e=this.readBuffer(t);try{return c.decode(e)}catch(t){throw new d(`Error decoding UTF-8 string: ${t}`)}}readFixedArray(t){return new Uint8Array(this.readBuffer(t))}readArray(t){let e=this.readU32(),r=[];for(let i=0;i<e;++i)r.push(t());return r}}function g(t){return t.charAt(0).toUpperCase()+t.slice(1)}function w(t,e,r,i,n){try{if("string"==typeof i)n[`write${g(i)}`](r);else if(i instanceof Array)if("number"==typeof i[0]){if(r.length!==i[0])throw new d(`Expecting byte array of length ${i[0]}, but got ${r.length} bytes`);n.writeFixedArray(r)}else if(2===i.length&&"number"==typeof i[1]){if(r.length!==i[1])throw new d(`Expecting byte array of length ${i[1]}, but got ${r.length} bytes`);for(let e=0;e<i[1];e++)w(t,null,r[e],i[0],n)}else n.writeArray(r,r=>{w(t,e,r,i[0],n)});else if(void 0!==i.kind)switch(i.kind){case"option":null==r?n.writeU8(0):(n.writeU8(1),w(t,e,r,i.type,n));break;case"map":n.writeU32(r.size),r.forEach((r,o)=>{w(t,e,o,i.key,n),w(t,e,r,i.value,n)});break;default:throw new d(`FieldType ${i} unrecognized`)}else v(t,r,n)}catch(t){throw t instanceof d&&t.addToFieldPath(e),t}}function v(t,e,r){if("function"==typeof e.borshSerialize)return void e.borshSerialize(r);let i=t.get(e.constructor);if(!i)throw new d(`Class ${e.constructor.name} is missing in schema`);if("struct"===i.kind)i.fields.map(([i,n])=>{w(t,i,e[i],n,r)});else if("enum"===i.kind){let n=e[i.field];for(let o=0;o<i.values.length;++o){let[s,f]=i.values[o];if(s===n){r.writeU8(o),w(t,s,e[s],f,r);break}}}else throw new d(`Unexpected schema kind: ${i.kind} for ${e.constructor.name}`)}function b(t,e,r,i){try{if("string"==typeof r)return i[`read${g(r)}`]();if(r instanceof Array)if("number"==typeof r[0])return i.readFixedArray(r[0]);else{if("number"!=typeof r[1])return i.readArray(()=>b(t,e,r[0],i));let n=[];for(let e=0;e<r[1];e++)n.push(b(t,null,r[0],i));return n}if("option"===r.kind){if(i.readU8())return b(t,e,r.type,i);return}if("map"===r.kind){let n=new Map,o=i.readU32();for(let s=0;s<o;s++){let o=b(t,e,r.key,i),s=b(t,e,r.value,i);n.set(o,s)}return n}return x(t,r,i)}catch(t){throw t instanceof d&&t.addToFieldPath(e),t}}function x(t,e,r){if("function"==typeof e.borshDeserialize)return e.borshDeserialize(r);let i=t.get(e);if(!i)throw new d(`Class ${e.name} is missing in schema`);if("struct"===i.kind){let i={};for(let[n,o]of t.get(e).fields)i[n]=b(t,n,o,r);return new e(i)}if("enum"===i.kind){let n=r.readU8();if(n>=i.values.length)throw new d(`Enum index: ${n} is out of range`);let[o,s]=i.values[n],f=b(t,o,s,r);return new e({[o]:f})}throw new d(`Unexpected schema kind: ${i.kind} for ${e.constructor.name}`)}s([m],y.prototype,"readU8",null),s([m],y.prototype,"readU16",null),s([m],y.prototype,"readU32",null),s([m],y.prototype,"readU64",null),s([m],y.prototype,"readU128",null),s([m],y.prototype,"readU256",null),s([m],y.prototype,"readU512",null),s([m],y.prototype,"readString",null),s([m],y.prototype,"readFixedArray",null),s([m],y.prototype,"readArray",null),e.BinaryReader=y,e.serialize=function(t,e,r=p){let i=new r;return v(t,e,i),i.toArray()},e.deserialize=function(t,e,r,i=y){let n=new i(r),o=x(t,e,n);if(n.offset<r.length)throw new d(`Unexpected ${r.length-n.offset} bytes after deserialized data`);return o},e.deserializeUnchecked=function(t,e,r,i=y){return x(t,e,new i(r))}},4341:(t,e,r)=>{"use strict";r.d(e,{Xf:()=>m,hT:()=>p,hp:()=>y});var i=r(456),n=r(4611);let o=BigInt(0),s=BigInt(1);function f(t,e){let r=e.negate();return t?r:e}function a(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw Error("invalid window size, expected [1.."+e+"], got W="+t)}function h(t,e){a(t,e);let r=Math.ceil(e/t)+1,i=2**(t-1),o=2**t;return{windows:r,windowSize:i,mask:(0,n.OG)(t),maxNumber:o,shiftBy:BigInt(t)}}function u(t,e,r){let{windowSize:i,mask:n,maxNumber:o,shiftBy:f}=r,a=Number(t&n),h=t>>f;a>i&&(a-=o,h+=s);let u=e*i,l=u+Math.abs(a)-1,c=0===a;return{nextN:h,offset:l,isZero:c,isNeg:a<0,isNegF:e%2!=0,offsetF:u}}let l=new WeakMap,c=new WeakMap;function d(t){return c.get(t)||1}function p(t,e){return{constTimeNegate:f,hasPrecomputes:t=>1!==d(t),unsafeLadder(e,r,i=t.ZERO){let n=e;for(;r>o;)r&s&&(i=i.add(n)),n=n.double(),r>>=s;return i},precomputeWindow(t,r){let{windows:i,windowSize:n}=h(r,e),o=[],s=t,f=s;for(let t=0;t<i;t++){f=s,o.push(f);for(let t=1;t<n;t++)f=f.add(s),o.push(f);s=f.double()}return o},wNAF(r,i,n){let o=t.ZERO,s=t.BASE,a=h(r,e);for(let t=0;t<a.windows;t++){let{nextN:e,offset:r,isZero:h,isNeg:l,isNegF:c,offsetF:d}=u(n,t,a);n=e,h?s=s.add(f(c,i[d])):o=o.add(f(l,i[r]))}return{p:o,f:s}},wNAFUnsafe(r,i,n,s=t.ZERO){let f=h(r,e);for(let t=0;t<f.windows&&n!==o;t++){let{nextN:e,offset:r,isZero:o,isNeg:a}=u(n,t,f);if(n=e,!o){let t=i[r];s=s.add(a?t.negate():t)}}return s},getPrecomputes(t,e,r){let i=l.get(e);return i||(i=this.precomputeWindow(e,t),1!==t&&l.set(e,r(i))),i},wNAFCached(t,e,r){let i=d(t);return this.wNAF(i,this.getPrecomputes(i,t,r),e)},wNAFCachedUnsafe(t,e,r,i){let n=d(t);return 1===n?this.unsafeLadder(t,e,i):this.wNAFUnsafe(n,this.getPrecomputes(n,t,r),e,i)},setWindowSize(t,r){a(r,e),c.set(t,r),l.delete(t)}}}function m(t,e,r,i){(function(t,e){if(!Array.isArray(t))throw Error("array expected");t.forEach((t,r)=>{if(!(t instanceof e))throw Error("invalid point at index "+r)})})(r,t),function(t,e){if(!Array.isArray(t))throw Error("array of scalars expected");t.forEach((t,r)=>{if(!e.isValid(t))throw Error("invalid scalar at index "+r)})}(i,e);let o=r.length,s=i.length;if(o!==s)throw Error("arrays of points and scalars must have equal length");let f=t.ZERO,a=(0,n.dJ)(BigInt(o)),h=1;a>12?h=a-3:a>4?h=a-2:a>0&&(h=2);let u=(0,n.OG)(h),l=Array(Number(u)+1).fill(f),c=Math.floor((e.BITS-1)/h)*h,d=f;for(let t=c;t>=0;t-=h){l.fill(f);for(let e=0;e<s;e++){let n=Number(i[e]>>BigInt(t)&u);l[n]=l[n].add(r[e])}let e=f;for(let t=l.length-1,r=f;t>0;t--)r=r.add(l[t]),e=e.add(r);if(d=d.add(e),0!==t)for(let t=0;t<h;t++)d=d.double()}return d}function y(t){return(0,i.jr)(t.Fp),(0,n.Q5)(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...(0,i.LH)(t.n,t.nBitLength),...t,...{p:t.Fp.ORDER}})}},4611:(t,e,r)=>{"use strict";r.d(e,{Id:()=>x,My:()=>c,OG:()=>_,Ph:()=>y,Q5:()=>k,aK:()=>B,aT:()=>m,aY:()=>o,dJ:()=>A,e8:()=>f,fg:()=>S,lX:()=>g,lq:()=>w,qj:()=>b,r4:()=>M,x:()=>R,z:()=>v,zW:()=>a});let i=BigInt(0),n=BigInt(1);function o(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&"Uint8Array"===t.constructor.name}function s(t){if(!o(t))throw Error("Uint8Array expected")}function f(t,e){if("boolean"!=typeof e)throw Error(t+" boolean expected, got "+e)}function a(t){let e=t.toString(16);return 1&e.length?"0"+e:e}function h(t){if("string"!=typeof t)throw Error("hex string expected, got "+typeof t);return""===t?i:BigInt("0x"+t)}let u="function"==typeof Uint8Array.from([]).toHex&&"function"==typeof Uint8Array.fromHex,l=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function c(t){if(s(t),u)return t.toHex();let e="";for(let r=0;r<t.length;r++)e+=l[t[r]];return e}let d={_0:48,_9:57,A:65,F:70,a:97,f:102};function p(t){return t>=d._0&&t<=d._9?t-d._0:t>=d.A&&t<=d.F?t-(d.A-10):t>=d.a&&t<=d.f?t-(d.a-10):void 0}function m(t){if("string"!=typeof t)throw Error("hex string expected, got "+typeof t);if(u)return Uint8Array.fromHex(t);let e=t.length,r=e/2;if(e%2)throw Error("hex string expected, got unpadded hex of length "+e);let i=new Uint8Array(r);for(let e=0,n=0;e<r;e++,n+=2){let r=p(t.charCodeAt(n)),o=p(t.charCodeAt(n+1));if(void 0===r||void 0===o)throw Error('hex string expected, got non-hex character "'+(t[n]+t[n+1])+'" at index '+n);i[e]=16*r+o}return i}function y(t){return h(c(t))}function g(t){return s(t),h(c(Uint8Array.from(t).reverse()))}function w(t,e){return m(t.toString(16).padStart(2*e,"0"))}function v(t,e){return w(t,e).reverse()}function b(t,e,r){let i;if("string"==typeof e)try{i=m(e)}catch(e){throw Error(t+" must be hex string or Uint8Array, cause: "+e)}else if(o(e))i=Uint8Array.from(e);else throw Error(t+" must be hex string or Uint8Array");let n=i.length;if("number"==typeof r&&n!==r)throw Error(t+" of length "+r+" expected, got "+n);return i}function x(...t){let e=0;for(let r=0;r<t.length;r++){let i=t[r];s(i),e+=i.length}let r=new Uint8Array(e);for(let e=0,i=0;e<t.length;e++){let n=t[e];r.set(n,i),i+=n.length}return r}let E=t=>"bigint"==typeof t&&i<=t;function M(t,e,r){return E(t)&&E(e)&&E(r)&&e<=t&&t<r}function B(t,e,r,i){if(!M(e,r,i))throw Error("expected valid "+t+": "+r+" <= n < "+i+", got "+e)}function A(t){let e;for(e=0;t>i;t>>=n,e+=1);return e}let _=t=>(n<<BigInt(t))-n,O=t=>new Uint8Array(t),I=t=>Uint8Array.from(t);function S(t,e,r){if("number"!=typeof t||t<2)throw Error("hashLen must be a number");if("number"!=typeof e||e<2)throw Error("qByteLen must be a number");if("function"!=typeof r)throw Error("hmacFn must be a function");let i=O(t),n=O(t),o=0,s=()=>{i.fill(1),n.fill(0),o=0},f=(...t)=>r(n,i,...t),a=(t=O(0))=>{n=f(I([0]),t),i=f(),0!==t.length&&(n=f(I([1]),t),i=f())},h=()=>{if(o++>=1e3)throw Error("drbg: tried 1000 values");let t=0,r=[];for(;t<e;){let e=(i=f()).slice();r.push(e),t+=i.length}return x(...r)};return(t,e)=>{let r;for(s(),a(t);!(r=e(h()));)a();return s(),r}}let U={bigint:t=>"bigint"==typeof t,function:t=>"function"==typeof t,boolean:t=>"boolean"==typeof t,string:t=>"string"==typeof t,stringOrUint8Array:t=>"string"==typeof t||o(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>"function"==typeof t&&Number.isSafeInteger(t.outputLen)};function k(t,e,r={}){let i=(e,r,i)=>{let n=U[r];if("function"!=typeof n)throw Error("invalid validator function");let o=t[e];if((!i||void 0!==o)&&!n(o,t))throw Error("param "+String(e)+" is invalid. Expected "+r+", got "+o)};for(let[t,r]of Object.entries(e))i(t,r,!1);for(let[t,e]of Object.entries(r))i(t,e,!0);return t}function R(t){let e=new WeakMap;return(r,...i)=>{let n=e.get(r);if(void 0!==n)return n;let o=t(r,...i);return e.set(r,o),o}}},4914:(t,e,r)=>{"use strict";function i([t,e]){return`${t}=${function t(e){return Array.isArray(e)?"%5B"+e.map(t).join("%2C%20")+"%5D":"bigint"==typeof e?`${e}n`:encodeURIComponent(String(null!=e&&null===Object.getPrototypeOf(e)?{...e}:e))}(e)}`}r.d(e,{g2:()=>x,eC:()=>v});var n=class extends Error{cause=this.cause;context;constructor(...[t,e]){let r,n;if(e){let{cause:t,...i}=e;t&&(n={cause:t}),Object.keys(i).length>0&&(r=i)}super(function(t,e={}){{let r=`Solana error #${t}; Decode this error by running \`npx @solana/errors decode -- ${t}`;return Object.keys(e).length&&(r+=` '${btoa(Object.entries(e).map(i).join("&"))}'`),`${r}\``}}(t,r),n),this.context={__code:t,...r},this.name="SolanaError"}};function o(...t){"captureStackTrace"in Error&&"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(...t)}function s({errorCodeBaseOffset:t,getErrorContext:e,orderedErrorNames:r,rpcEnumError:i},s){let f,a;"string"==typeof i?f=i:(f=Object.keys(i)[0],a=i[f]);let h=t+r.indexOf(f),u=e(h,f,a),l=new n(h,u);return o(l,s),l}function f(t){var e;let r;if("object"==typeof(e=t)&&null!==e&&"code"in e&&"message"in e&&("number"==typeof e.code||"bigint"==typeof e.code)&&"string"==typeof e.message){let{code:e,data:i,message:o}=t,f=Number(e);if(-32002===f){let{err:t,...e}=i,o=t?{cause:function t(e){return"object"==typeof e&&"InstructionError"in e?function t(e,r){let i=Number(e);return s({errorCodeBaseOffset:4615001,getErrorContext:(t,e,r)=>4615e3===t?{errorName:e,index:i,...void 0!==r?{instructionErrorContext:r}:null}:4615026===t?{code:Number(r),index:i}:4615045===t?{encodedData:r,index:i}:{index:i},orderedErrorNames:null,rpcEnumError:r},t)}(...e.InstructionError):s({errorCodeBaseOffset:7050001,getErrorContext:(t,e,r)=>705e4===t?{errorName:e,...void 0!==r?{transactionErrorContext:r}:null}:7050030===t?{index:Number(r)}:7050031===t||7050035===t?{accountIndex:Number(r.account_index)}:void 0,orderedErrorNames:null,rpcEnumError:e},t)}(t)}:null;r=new n(-32002,{...e,...o})}else{let t;switch(f){case -32603:case -32602:case -32600:case -32601:case -32700:case -32012:case -32001:case -32004:case -32014:case -32010:case -32009:case -32007:case -32006:case -32015:t={__serverMessage:o};break;default:"object"!=typeof i||Array.isArray(i)||(t=i)}r=new n(f,t)}}else{let e="object"==typeof t&&null!==t&&"message"in t&&"string"==typeof t.message?t.message:"Malformed JSON-RPC error with no message attribute";r=new n(10,{error:t,message:e})}return o(r,f),r}var a=(t,e)=>{if(t.length>=e)return t;let r=new Uint8Array(e).fill(0);return r.set(t),r};function h(t){return Object.freeze({...t,encode:e=>{var r,i;let n=new Uint8Array((r=e,"fixedSize"in(i=t)?i.fixedSize:i.getSizeFromValue(r)));return t.write(e,n,0),n}})}function u(t){return Object.freeze({...t,decode:(e,r=0)=>t.read(e,r)[0]})}function l(t){return"fixedSize"in t&&"number"==typeof t.fixedSize}function c(t,e,r){if(e<0||e>r)throw new SolanaError(SOLANA_ERROR__CODECS__OFFSET_OUT_OF_RANGE,{bytesLength:r,codecDescription:t,offset:e})}function d(t,e){return 0===e?0:(t%e+e)%e}function p(t,e,r,i){if(i<e||i>r)throw new n(8078011,{codecDescription:t,max:r,min:e,value:i})}var m=(t=>(t[t.Little=0]="Little",t[t.Big=1]="Big",t))(m||{});function y(t){return t?.endian!==1}function g(t){return h({fixedSize:t.size,write(e,r,i){t.range&&p(t.name,t.range[0],t.range[1],e);let n=new ArrayBuffer(t.size);return t.set(new DataView(n),e,y(t.config)),r.set(new Uint8Array(n),i),i+t.size}})}function w(t){return u({fixedSize:t.size,read(e,r=0){!function(t,e,r=0){if(e.length-r<=0)throw new n(8078e3,{codecDescription:t})}(t.name,e,r),function(t,e,r,i=0){let o=r.length-i;if(o<e)throw new n(8078001,{bytesLength:o,codecDescription:t,expected:e})}(t.name,t.size,e,r);let i=new DataView(function(t,e,r){let i=t.byteOffset+(e??0),n=r??t.byteLength;return t.buffer.slice(i,i+n)}(e,r,t.size));return[t.get(i,y(t.config)),r+t.size]}})}var v=(t={})=>g({config:t,name:"u64",range:[0n,BigInt("0xffffffffffffffff")],set:(t,e,r)=>t.setBigUint64(0,BigInt(e),r),size:8}),b=(t={})=>w({config:t,get:(t,e)=>t.getBigUint64(0,e),name:"u64",size:8}),x=(t={})=>(function(t,e){if(l(t)!==l(e))throw new n(8078004);if(l(t)&&l(e)&&t.fixedSize!==e.fixedSize)throw new n(8078005,{decoderFixedSize:e.fixedSize,encoderFixedSize:t.fixedSize});if(!l(t)&&!l(e)&&t.maxSize!==e.maxSize)throw new n(8078006,{decoderMaxSize:e.maxSize,encoderMaxSize:t.maxSize});return{...e,...t,decode:e.decode,encode:t.encode,read:e.read,write:t.write}})(v(t),b(t))},5474:(t,e,r)=>{"use strict";r.d(e,{KC:()=>_,KJ:()=>y,L5:()=>O,NW:()=>A,PV:()=>B,YO:()=>p,Yj:()=>M,ai:()=>b,au:()=>I,bz:()=>d,eu:()=>g,g1:()=>E,lq:()=>x,me:()=>v,vt:()=>u,zM:()=>m});class i extends TypeError{constructor(t,e){let r,{message:i,explanation:n,...o}=t,{path:s}=t,f=0===s.length?i:`At path: ${s.join(".")} -- ${i}`;super(n??f),null!=n&&(this.cause=f),Object.assign(this,o),this.name=this.constructor.name,this.failures=()=>r??(r=[t,...e()])}}function n(t){return"object"==typeof t&&null!=t}function o(t){return n(t)&&!Array.isArray(t)}function s(t){return"symbol"==typeof t?t.toString():"string"==typeof t?JSON.stringify(t):`${t}`}function*f(t,e,r,i){var o;for(let f of(n(o=t)&&"function"==typeof o[Symbol.iterator]||(t=[t]),t)){let t=function(t,e,r,i){if(!0===t)return;!1===t?t={}:"string"==typeof t&&(t={message:t});let{path:n,branch:o}=e,{type:f}=r,{refinement:a,message:h=`Expected a value of type \`${f}\`${a?` with refinement \`${a}\``:""}, but received: \`${s(i)}\``}=t;return{value:i,type:f,refinement:a,key:n[n.length-1],path:n,branch:o,...t,message:h}}(f,e,r,i);t&&(yield t)}}function*a(t,e,r={}){let{path:i=[],branch:o=[t],coerce:s=!1,mask:f=!1}=r,h={path:i,branch:o,mask:f};s&&(t=e.coercer(t,h));let u="valid";for(let i of e.validator(t,h))i.explanation=r.message,u="not_valid",yield[i,void 0];for(let[l,c,d]of e.entries(t,h))for(let e of a(c,d,{path:void 0===l?i:[...i,l],branch:void 0===l?o:[...o,c],coerce:s,mask:f,message:r.message}))e[0]?(u=null!=e[0].refinement?"not_refined":"not_valid",yield[e[0],void 0]):s&&(c=e[1],void 0===l?t=c:t instanceof Map?t.set(l,c):t instanceof Set?t.add(c):n(t)&&(void 0!==c||l in t)&&(t[l]=c));if("not_valid"!==u)for(let i of e.refiner(t,h))i.explanation=r.message,u="not_refined",yield[i,void 0];"valid"===u&&(yield[void 0,t])}class h{constructor(t){let{type:e,schema:r,validator:i,refiner:n,coercer:o=t=>t,entries:s=function*(){}}=t;this.type=e,this.schema=r,this.entries=s,this.coercer=o,i?this.validator=(t,e)=>f(i(t,e),e,this,t):this.validator=()=>[],n?this.refiner=(t,e)=>f(n(t,e),e,this,t):this.refiner=()=>[]}assert(t,e){var r=t,i=this,n=e;let o=l(r,i,{message:n});if(o[0])throw o[0]}create(t,e){return u(t,this,e)}is(t){var e,r;return e=t,r=this,!l(e,r)[0]}mask(t,e){var r=t,i=this,n=e;let o=l(r,i,{coerce:!0,mask:!0,message:n});if(!o[0])return o[1];throw o[0]}validate(t,e={}){return l(t,this,e)}}function u(t,e,r){let i=l(t,e,{coerce:!0,message:r});if(!i[0])return i[1];throw i[0]}function l(t,e,r={}){let n=a(t,e,r),o=function(t){let{done:e,value:r}=t.next();return e?void 0:r}(n);return o[0]?[new i(o[0],function*(){for(let t of n)t[0]&&(yield t[0])}),void 0]:[void 0,o[1]]}function c(t,e){return new h({type:t,schema:null,validator:e})}function d(){return c("any",()=>!0)}function p(t){return new h({type:"array",schema:t,*entries(e){if(t&&Array.isArray(e))for(let[r,i]of e.entries())yield[r,i,t]},coercer:t=>Array.isArray(t)?t.slice():t,validator:t=>Array.isArray(t)||`Expected an array value, but received: ${s(t)}`})}function m(){return c("boolean",t=>"boolean"==typeof t)}function y(t){return c("instance",e=>e instanceof t||`Expected a \`${t.name}\` instance, but received: ${s(e)}`)}function g(t){let e=s(t),r=typeof t;return new h({type:"literal",schema:"string"===r||"number"===r||"boolean"===r?t:null,validator:r=>r===t||`Expected the literal \`${e}\`, but received: ${s(r)}`})}function w(){return c("never",()=>!1)}function v(t){return new h({...t,validator:(e,r)=>null===e||t.validator(e,r),refiner:(e,r)=>null===e||t.refiner(e,r)})}function b(){return c("number",t=>"number"==typeof t&&!isNaN(t)||`Expected a number, but received: ${s(t)}`)}function x(t){return new h({...t,validator:(e,r)=>void 0===e||t.validator(e,r),refiner:(e,r)=>void 0===e||t.refiner(e,r)})}function E(t,e){return new h({type:"record",schema:null,*entries(r){if(n(r))for(let i in r){let n=r[i];yield[i,i,t],yield[i,n,e]}},validator:t=>o(t)||`Expected an object, but received: ${s(t)}`,coercer:t=>o(t)?{...t}:t})}function M(){return c("string",t=>"string"==typeof t||`Expected a string, but received: ${s(t)}`)}function B(t){let e=w();return new h({type:"tuple",schema:null,*entries(r){if(Array.isArray(r)){let i=Math.max(t.length,r.length);for(let n=0;n<i;n++)yield[n,r[n],t[n]||e]}},validator:t=>Array.isArray(t)||`Expected an array, but received: ${s(t)}`,coercer:t=>Array.isArray(t)?t.slice():t})}function A(t){let e=Object.keys(t);return new h({type:"type",schema:t,*entries(r){if(n(r))for(let i of e)yield[i,r[i],t[i]]},validator:t=>o(t)||`Expected an object, but received: ${s(t)}`,coercer:t=>o(t)?{...t}:t})}function _(t){let e=t.map(t=>t.type).join(" | ");return new h({type:"union",schema:null,coercer(e,r){for(let i of t){let[t,n]=i.validate(e,{coerce:!0,mask:r.mask});if(!t)return n}return e},validator(r,i){let n=[];for(let e of t){let[...t]=a(r,e,i),[o]=t;if(!o[0])return[];for(let[e]of t)e&&n.push(e)}return[`Expected the value to satisfy a union of \`${e}\`, but received: ${s(r)}`,...n]}})}function O(){return c("unknown",()=>!0)}function I(t,e,r){return new h({...t,coercer:(i,n)=>l(i,e)[0]?t.coercer(i,n):t.coercer(r(i,n),n)})}},5484:(t,e,r)=>{"use strict";r.d(e,{A:()=>n,b:()=>i});var i=r(2661);let n=i},5884:(t,e,r)=>{"use strict";var i=r(228).Buffer;t.exports=function(t){if(t.length>=255)throw TypeError("Alphabet too long");for(var e=new Uint8Array(256),r=0;r<e.length;r++)e[r]=255;for(var n=0;n<t.length;n++){var o=t.charAt(n),s=o.charCodeAt(0);if(255!==e[s])throw TypeError(o+" is ambiguous");e[s]=n}var f=t.length,a=t.charAt(0),h=Math.log(f)/Math.log(256),u=Math.log(256)/Math.log(f);function l(t){if("string"!=typeof t)throw TypeError("Expected String");if(0===t.length)return i.alloc(0);for(var r=0,n=0,o=0;t[r]===a;)n++,r++;for(var s=(t.length-r)*h+1>>>0,u=new Uint8Array(s);r<t.length;){var l=t.charCodeAt(r);if(l>255)return;var c=e[l];if(255===c)return;for(var d=0,p=s-1;(0!==c||d<o)&&-1!==p;p--,d++)c+=f*u[p]>>>0,u[p]=c%256>>>0,c=c/256>>>0;if(0!==c)throw Error("Non-zero carry");o=d,r++}for(var m=s-o;m!==s&&0===u[m];)m++;var y=i.allocUnsafe(n+(s-m));y.fill(0,0,n);for(var g=n;m!==s;)y[g++]=u[m++];return y}return{encode:function(e){if((Array.isArray(e)||e instanceof Uint8Array)&&(e=i.from(e)),!i.isBuffer(e))throw TypeError("Expected Buffer");if(0===e.length)return"";for(var r=0,n=0,o=0,s=e.length;o!==s&&0===e[o];)o++,r++;for(var h=(s-o)*u+1>>>0,l=new Uint8Array(h);o!==s;){for(var c=e[o],d=0,p=h-1;(0!==c||d<n)&&-1!==p;p--,d++)c+=256*l[p]>>>0,l[p]=c%f>>>0,c=c/f>>>0;if(0!==c)throw Error("Non-zero carry");n=d,o++}for(var m=h-n;m!==h&&0===l[m];)m++;for(var y=a.repeat(r);m<h;++m)y+=t.charAt(l[m]);return y},decodeUnsafe:l,decode:function(t){var e=l(t);if(e)return e;throw Error("Non-base"+f+" character")}}}},6399:(t,e,r)=>{"use strict";let i=r(2021).v4,n=r(2888),o=function(t,e){if(!(this instanceof o))return new o(t,e);e||(e={}),this.options={reviver:void 0!==e.reviver?e.reviver:null,replacer:void 0!==e.replacer?e.replacer:null,generator:void 0!==e.generator?e.generator:function(){return i()},version:void 0!==e.version?e.version:2,notificationIdNull:"boolean"==typeof e.notificationIdNull&&e.notificationIdNull},this.callServer=t};t.exports=o,o.prototype.request=function(t,e,r,i){let o,s=this,f=null,a=Array.isArray(t)&&"function"==typeof e;if(1===this.options.version&&a)throw TypeError("JSON-RPC 1.0 does not support batching");let h=!a&&t&&"object"==typeof t&&"function"==typeof e;if(a||h)i=e,f=t;else{"function"==typeof r&&(i=r,r=void 0);let o="function"==typeof i;try{f=n(t,e,r,{generator:this.options.generator,version:this.options.version,notificationIdNull:this.options.notificationIdNull})}catch(t){if(o)return i(t);throw t}if(!o)return f}try{o=JSON.stringify(f,this.options.replacer)}catch(t){return i(t)}return this.callServer(o,function(t,e){s._parseResponse(t,e,i)}),f},o.prototype._parseResponse=function(t,e,r){let i;if(t)return void r(t);if(!e)return r();try{i=JSON.parse(e,this.options.reviver)}catch(t){return r(t)}if(3===r.length)if(!Array.isArray(i))return r(null,i.error,i.result);else{let t=function(t){return void 0!==t.error};return r(null,i.filter(t),i.filter(function(e){return!t(e)}))}r(null,i)}},6416:(t,e,r)=>{"use strict";e.I0=e.DH=e.NX=e.u8=e.cY=void 0,e.av=e.O6=e.w3=e.Wg=void 0;let i=r(4134);function n(t){if(!(t instanceof Uint8Array))throw TypeError("b must be a Uint8Array")}function o(t){return n(t),i.Buffer.from(t.buffer,t.byteOffset,t.length)}class s{constructor(t,e){if(!Number.isInteger(t))throw TypeError("span must be an integer");this.span=t,this.property=e}makeDestinationObject(){return{}}getSpan(t,e){if(0>this.span)throw RangeError("indeterminate span");return this.span}replicate(t){let e=Object.create(this.constructor.prototype);return Object.assign(e,this),e.property=t,e}fromArray(t){}}function f(t,e){return e.property?t+"["+e.property+"]":t}class a extends s{isCount(){throw Error("ExternalLayout is abstract")}}class h extends a{constructor(t,e=0,r){if(!(t instanceof s))throw TypeError("layout must be a Layout");if(!Number.isInteger(e))throw TypeError("offset must be integer or undefined");super(t.span,r||t.property),this.layout=t,this.offset=e}isCount(){return this.layout instanceof u||this.layout instanceof l}decode(t,e=0){return this.layout.decode(t,e+this.offset)}encode(t,e,r=0){return this.layout.encode(t,e,r+this.offset)}}class u extends s{constructor(t,e){if(super(t,e),6<this.span)throw RangeError("span must not exceed 6 bytes")}decode(t,e=0){return o(t).readUIntLE(e,this.span)}encode(t,e,r=0){return o(e).writeUIntLE(t,r,this.span),this.span}}class l extends s{constructor(t,e){if(super(t,e),6<this.span)throw RangeError("span must not exceed 6 bytes")}decode(t,e=0){return o(t).readUIntBE(e,this.span)}encode(t,e,r=0){return o(e).writeUIntBE(t,r,this.span),this.span}}function c(t){let e=Math.floor(t/0x100000000);return{hi32:e,lo32:t-0x100000000*e}}function d(t,e){return 0x100000000*t+e}class p extends s{constructor(t){super(8,t)}decode(t,e=0){let r=o(t),i=r.readUInt32LE(e);return d(r.readUInt32LE(e+4),i)}encode(t,e,r=0){let i=c(t),n=o(e);return n.writeUInt32LE(i.lo32,r),n.writeUInt32LE(i.hi32,r+4),8}}class m extends s{constructor(t){super(8,t)}decode(t,e=0){let r=o(t),i=r.readUInt32LE(e);return d(r.readInt32LE(e+4),i)}encode(t,e,r=0){let i=c(t),n=o(e);return n.writeUInt32LE(i.lo32,r),n.writeInt32LE(i.hi32,r+4),8}}class y extends s{constructor(t,e,r){if(!(t instanceof s))throw TypeError("elementLayout must be a Layout");if(!(e instanceof a&&e.isCount()||Number.isInteger(e)&&0<=e))throw TypeError("count must be non-negative integer or an unsigned integer ExternalLayout");let i=-1;e instanceof a||!(0<t.span)||(i=e*t.span),super(i,r),this.elementLayout=t,this.count=e}getSpan(t,e=0){if(0<=this.span)return this.span;let r=0,i=this.count;if(i instanceof a&&(i=i.decode(t,e)),0<this.elementLayout.span)r=i*this.elementLayout.span;else{let n=0;for(;n<i;)r+=this.elementLayout.getSpan(t,e+r),++n}return r}decode(t,e=0){let r=[],i=0,n=this.count;for(n instanceof a&&(n=n.decode(t,e));i<n;)r.push(this.elementLayout.decode(t,e)),e+=this.elementLayout.getSpan(t,e),i+=1;return r}encode(t,e,r=0){let i=this.elementLayout,n=t.reduce((t,n)=>t+i.encode(n,e,r+t),0);return this.count instanceof a&&this.count.encode(t.length,e,r),n}}class g extends s{constructor(t,e,r){if(!(Array.isArray(t)&&t.reduce((t,e)=>t&&e instanceof s,!0)))throw TypeError("fields must be array of Layout instances");for(let i of("boolean"==typeof e&&void 0===r&&(r=e,e=void 0),t))if(0>i.span&&void 0===i.property)throw Error("fields cannot contain unnamed variable-length layout");let i=-1;try{i=t.reduce((t,e)=>t+e.getSpan(),0)}catch(t){}super(i,e),this.fields=t,this.decodePrefixes=!!r}getSpan(t,e=0){if(0<=this.span)return this.span;let r=0;try{r=this.fields.reduce((r,i)=>{let n=i.getSpan(t,e);return e+=n,r+n},0)}catch(t){throw RangeError("indeterminate span")}return r}decode(t,e=0){n(t);let r=this.makeDestinationObject();for(let i of this.fields)if(void 0!==i.property&&(r[i.property]=i.decode(t,e)),e+=i.getSpan(t,e),this.decodePrefixes&&t.length===e)break;return r}encode(t,e,r=0){let i=r,n=0,o=0;for(let i of this.fields){let s=i.span;if(o=0<s?s:0,void 0!==i.property){let n=t[i.property];void 0!==n&&(o=i.encode(n,e,r),0>s&&(s=i.getSpan(e,r)))}n=r,r+=s}return n+o-i}fromArray(t){let e=this.makeDestinationObject();for(let r of this.fields)void 0!==r.property&&0<t.length&&(e[r.property]=t.shift());return e}layoutFor(t){if("string"!=typeof t)throw TypeError("property must be string");for(let e of this.fields)if(e.property===t)return e}offsetOf(t){if("string"!=typeof t)throw TypeError("property must be string");let e=0;for(let r of this.fields){if(r.property===t)return e;0>r.span?e=-1:0<=e&&(e+=r.span)}}}class w{constructor(t){this.property=t}decode(t,e){throw Error("UnionDiscriminator is abstract")}encode(t,e,r){throw Error("UnionDiscriminator is abstract")}}class v extends w{constructor(t,e){if(!(t instanceof a&&t.isCount()))throw TypeError("layout must be an unsigned integer ExternalLayout");super(e||t.property||"variant"),this.layout=t}decode(t,e){return this.layout.decode(t,e)}encode(t,e,r){return this.layout.encode(t,e,r)}}class b extends s{constructor(t,e,r){let i;if(t instanceof u||t instanceof l)i=new v(new h(t));else if(t instanceof a&&t.isCount())i=new v(t);else if(t instanceof w)i=t;else throw TypeError("discr must be a UnionDiscriminator or an unsigned integer layout");if(void 0===e&&(e=null),!(null===e||e instanceof s))throw TypeError("defaultLayout must be null or a Layout");if(null!==e){if(0>e.span)throw Error("defaultLayout must have constant span");void 0===e.property&&(e=e.replicate("content"))}let n=-1;e&&0<=(n=e.span)&&(t instanceof u||t instanceof l)&&(n+=i.layout.span),super(n,r),this.discriminator=i,this.usesPrefixDiscriminator=t instanceof u||t instanceof l,this.defaultLayout=e,this.registry={};let o=this.defaultGetSourceVariant.bind(this);this.getSourceVariant=function(t){return o(t)},this.configGetSourceVariant=function(t){o=t.bind(this)}}getSpan(t,e=0){if(0<=this.span)return this.span;let r=this.getVariant(t,e);if(!r)throw Error("unable to determine span for unrecognized variant");return r.getSpan(t,e)}defaultGetSourceVariant(t){if(Object.prototype.hasOwnProperty.call(t,this.discriminator.property)){if(this.defaultLayout&&this.defaultLayout.property&&Object.prototype.hasOwnProperty.call(t,this.defaultLayout.property))return;let e=this.registry[t[this.discriminator.property]];if(e&&(!e.layout||e.property&&Object.prototype.hasOwnProperty.call(t,e.property)))return e}else for(let e in this.registry){let r=this.registry[e];if(r.property&&Object.prototype.hasOwnProperty.call(t,r.property))return r}throw Error("unable to infer src variant")}decode(t,e=0){let r,i=this.discriminator,n=i.decode(t,e),o=this.registry[n];if(void 0===o){let o=this.defaultLayout,s=0;this.usesPrefixDiscriminator&&(s=i.layout.span),(r=this.makeDestinationObject())[i.property]=n,r[o.property]=o.decode(t,e+s)}else r=o.decode(t,e);return r}encode(t,e,r=0){let i=this.getSourceVariant(t);if(void 0===i){let i=this.discriminator,n=this.defaultLayout,o=0;return this.usesPrefixDiscriminator&&(o=i.layout.span),i.encode(t[i.property],e,r),o+n.encode(t[n.property],e,r+o)}return i.encode(t,e,r)}addVariant(t,e,r){let i=new x(this,t,e,r);return this.registry[t]=i,i}getVariant(t,e=0){let r;return r=t instanceof Uint8Array?this.discriminator.decode(t,e):t,this.registry[r]}}class x extends s{constructor(t,e,r,i){if(!(t instanceof b))throw TypeError("union must be a Union");if(!Number.isInteger(e)||0>e)throw TypeError("variant must be a (non-negative) integer");if("string"==typeof r&&void 0===i&&(i=r,r=null),r){if(!(r instanceof s))throw TypeError("layout must be a Layout");if(null!==t.defaultLayout&&0<=r.span&&r.span>t.defaultLayout.span)throw Error("variant span exceeds span of containing union");if("string"!=typeof i)throw TypeError("variant must have a String property")}let n=t.span;0>t.span&&0<=(n=r?r.span:0)&&t.usesPrefixDiscriminator&&(n+=t.discriminator.layout.span),super(n,i),this.union=t,this.variant=e,this.layout=r||null}getSpan(t,e=0){if(0<=this.span)return this.span;let r=0;this.union.usesPrefixDiscriminator&&(r=this.union.discriminator.layout.span);let i=0;return this.layout&&(i=this.layout.getSpan(t,e+r)),r+i}decode(t,e=0){let r=this.makeDestinationObject();if(this!==this.union.getVariant(t,e))throw Error("variant mismatch");let i=0;return this.union.usesPrefixDiscriminator&&(i=this.union.discriminator.layout.span),this.layout?r[this.property]=this.layout.decode(t,e+i):this.property?r[this.property]=!0:this.union.usesPrefixDiscriminator&&(r[this.union.discriminator.property]=this.variant),r}encode(t,e,r=0){let i=0;if(this.union.usesPrefixDiscriminator&&(i=this.union.discriminator.layout.span),this.layout&&!Object.prototype.hasOwnProperty.call(t,this.property))throw TypeError("variant lacks property "+this.property);this.union.discriminator.encode(this.variant,e,r);let n=i;if(this.layout&&(this.layout.encode(t[this.property],e,r+i),n+=this.layout.getSpan(e,r+i),0<=this.union.span&&n>this.union.span))throw Error("encoded variant overruns containing union");return n}fromArray(t){if(this.layout)return this.layout.fromArray(t)}}function E(t){return 0>t&&(t+=0x100000000),t}class M extends s{constructor(t,e,r){if(!(t instanceof u||t instanceof l))throw TypeError("word must be a UInt or UIntBE layout");if("string"==typeof e&&void 0===r&&(r=e,e=!1),4<t.span)throw RangeError("word cannot exceed 32 bits");super(t.span,r),this.word=t,this.msb=!!e,this.fields=[];let i=0;this._packedSetValue=function(t){return i=E(t),this},this._packedGetValue=function(){return i}}decode(t,e=0){let r=this.makeDestinationObject(),i=this.word.decode(t,e);for(let e of(this._packedSetValue(i),this.fields))void 0!==e.property&&(r[e.property]=e.decode(t));return r}encode(t,e,r=0){let i=this.word.decode(e,r);for(let e of(this._packedSetValue(i),this.fields))if(void 0!==e.property){let r=t[e.property];void 0!==r&&e.encode(r)}return this.word.encode(this._packedGetValue(),e,r)}addField(t,e){let r=new B(this,t,e);return this.fields.push(r),r}addBoolean(t){let e=new A(this,t);return this.fields.push(e),e}fieldFor(t){if("string"!=typeof t)throw TypeError("property must be string");for(let e of this.fields)if(e.property===t)return e}}class B{constructor(t,e,r){if(!(t instanceof M))throw TypeError("container must be a BitStructure");if(!Number.isInteger(e)||0>=e)throw TypeError("bits must be positive integer");let i=8*t.span,n=t.fields.reduce((t,e)=>t+e.bits,0);if(e+n>i)throw Error("bits too long for span remainder ("+(i-n)+" of "+i+" remain)");this.container=t,this.bits=e,this.valueMask=(1<<e)-1,32===e&&(this.valueMask=0xffffffff),this.start=n,this.container.msb&&(this.start=i-n-e),this.wordMask=E(this.valueMask<<this.start),this.property=r}decode(t,e){return E(this.container._packedGetValue()&this.wordMask)>>>this.start}encode(t){if("number"!=typeof t||!Number.isInteger(t)||t!==E(t&this.valueMask))throw TypeError(f("BitField.encode",this)+" value must be integer not exceeding "+this.valueMask);let e=this.container._packedGetValue(),r=E(t<<this.start);this.container._packedSetValue(E(e&~this.wordMask)|r)}}class A extends B{constructor(t,e){super(t,1,e)}decode(t,e){return!!super.decode(t,e)}encode(t){"boolean"==typeof t&&(t*=1),super.encode(t)}}class _ extends s{constructor(t,e){if(!(t instanceof a&&t.isCount()||Number.isInteger(t)&&0<=t))throw TypeError("length must be positive integer or an unsigned integer ExternalLayout");let r=-1;t instanceof a||(r=t),super(r,e),this.length=t}getSpan(t,e){let r=this.span;return 0>r&&(r=this.length.decode(t,e)),r}decode(t,e=0){let r=this.span;return 0>r&&(r=this.length.decode(t,e)),o(t).slice(e,e+r)}encode(t,e,r){let i=this.length;if(this.length instanceof a&&(i=t.length),!(t instanceof Uint8Array&&i===t.length))throw TypeError(f("Blob.encode",this)+" requires (length "+i+") Uint8Array as src");if(r+i>e.length)throw RangeError("encoding overruns Uint8Array");let n=o(t);return o(e).write(n.toString("hex"),r,i,"hex"),this.length instanceof a&&this.length.encode(i,e,r),i}}e.cY=(t,e,r)=>new h(t,e,r),e.u8=t=>new u(1,t),e.NX=t=>new u(2,t),e.DH=t=>new u(4,t),e.I0=t=>new p(t),e.Wg=t=>new m(t),e.w3=(t,e,r)=>new g(t,e,r),e.O6=(t,e,r)=>new y(t,e,r),e.av=(t,e)=>new _(t,e)},7374:(t,e,r)=>{"use strict";r.d(e,{kb:()=>s,vE:()=>a});var i=r(4134),n=r(5484),o=class extends n.b{socket;constructor(t,e,r){super(),this.socket=new window.WebSocket(t,r),this.socket.onopen=()=>this.emit("open"),this.socket.onmessage=t=>this.emit("message",t.data),this.socket.onerror=t=>this.emit("error",t),this.socket.onclose=t=>{this.emit("close",t.code,t.reason)}}send(t,e,r){let i=r||e;try{this.socket.send(t),i()}catch(t){i(t)}}close(t,e){this.socket.close(t,e)}addEventListener(t,e,r){this.socket.addEventListener(t,e,r)}};function s(t,e){return new o(t,e)}var f=class{encode(t){return JSON.stringify(t)}decode(t){return JSON.parse(t)}},a=class extends n.b{address;rpc_id;queue;options;autoconnect;ready;reconnect;reconnect_timer_id;reconnect_interval;max_reconnects;rest_options;current_reconnects;generate_request_id;socket;webSocketFactory;dataPack;constructor(t,e="ws://localhost:8080",{autoconnect:r=!0,reconnect:i=!0,reconnect_interval:n=1e3,max_reconnects:o=5,...s}={},a,h){super(),this.webSocketFactory=t,this.queue={},this.rpc_id=0,this.address=e,this.autoconnect=r,this.ready=!1,this.reconnect=i,this.reconnect_timer_id=void 0,this.reconnect_interval=n,this.max_reconnects=o,this.rest_options=s,this.current_reconnects=0,this.generate_request_id=a||(()=>"number"==typeof this.rpc_id?++this.rpc_id:Number(this.rpc_id)+1),h?this.dataPack=h:this.dataPack=new f,this.autoconnect&&this._connect(this.address,{autoconnect:this.autoconnect,reconnect:this.reconnect,reconnect_interval:this.reconnect_interval,max_reconnects:this.max_reconnects,...this.rest_options})}connect(){this.socket||this._connect(this.address,{autoconnect:this.autoconnect,reconnect:this.reconnect,reconnect_interval:this.reconnect_interval,max_reconnects:this.max_reconnects,...this.rest_options})}call(t,e,r,i){return i||"object"!=typeof r||(i=r,r=null),new Promise((n,o)=>{if(!this.ready)return o(Error("socket not ready"));let s=this.generate_request_id(t,e);this.socket.send(this.dataPack.encode({jsonrpc:"2.0",method:t,params:e||void 0,id:s}),i,t=>{if(t)return o(t);this.queue[s]={promise:[n,o]},r&&(this.queue[s].timeout=setTimeout(()=>{delete this.queue[s],o(Error("reply timeout"))},r))})})}async login(t){let e=await this.call("rpc.login",t);if(!e)throw Error("authentication failed");return e}async listMethods(){return await this.call("__listMethods")}notify(t,e){return new Promise((r,i)=>{if(!this.ready)return i(Error("socket not ready"));this.socket.send(this.dataPack.encode({jsonrpc:"2.0",method:t,params:e}),t=>{if(t)return i(t);r()})})}async subscribe(t){"string"==typeof t&&(t=[t]);let e=await this.call("rpc.on",t);if("string"==typeof t&&"ok"!==e[t])throw Error("Failed subscribing to an event '"+t+"' with: "+e[t]);return e}async unsubscribe(t){"string"==typeof t&&(t=[t]);let e=await this.call("rpc.off",t);if("string"==typeof t&&"ok"!==e[t])throw Error("Failed unsubscribing from an event with: "+e);return e}close(t,e){this.socket.close(t||1e3,e)}setAutoReconnect(t){this.reconnect=t}setReconnectInterval(t){this.reconnect_interval=t}setMaxReconnects(t){this.max_reconnects=t}_connect(t,e){clearTimeout(this.reconnect_timer_id),this.socket=this.webSocketFactory(t,e),this.socket.addEventListener("open",()=>{this.ready=!0,this.emit("open"),this.current_reconnects=0}),this.socket.addEventListener("message",({data:t})=>{t instanceof ArrayBuffer&&(t=i.Buffer.from(t).toString());try{t=this.dataPack.decode(t)}catch(t){return}if(t.notification&&this.listeners(t.notification).length){if(!Object.keys(t.params).length)return this.emit(t.notification);let e=[t.notification];if(t.params.constructor===Object)e.push(t.params);else for(let r=0;r<t.params.length;r++)e.push(t.params[r]);return Promise.resolve().then(()=>{this.emit.apply(this,e)})}if(!this.queue[t.id])return t.method?Promise.resolve().then(()=>{this.emit(t.method,t?.params)}):void 0;"error"in t=="result"in t&&this.queue[t.id].promise[1](Error('Server response malformed. Response must include either "result" or "error", but not both.')),this.queue[t.id].timeout&&clearTimeout(this.queue[t.id].timeout),t.error?this.queue[t.id].promise[1](t.error):this.queue[t.id].promise[0](t.result),delete this.queue[t.id]}),this.socket.addEventListener("error",t=>this.emit("error",t)),this.socket.addEventListener("close",({code:r,reason:i})=>{this.ready&&setTimeout(()=>this.emit("close",r,i),0),this.ready=!1,this.socket=void 0,1e3!==r&&(this.current_reconnects++,this.reconnect&&(this.max_reconnects>this.current_reconnects||0===this.max_reconnects)&&(this.reconnect_timer_id=setTimeout(()=>this._connect(t,e),this.reconnect_interval)))})}}},7610:(t,e)=>{e.read=function(t,e,r,i,n){var o,s,f=8*n-i-1,a=(1<<f)-1,h=a>>1,u=-7,l=r?n-1:0,c=r?-1:1,d=t[e+l];for(l+=c,o=d&(1<<-u)-1,d>>=-u,u+=f;u>0;o=256*o+t[e+l],l+=c,u-=8);for(s=o&(1<<-u)-1,o>>=-u,u+=i;u>0;s=256*s+t[e+l],l+=c,u-=8);if(0===o)o=1-h;else{if(o===a)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,i),o-=h}return(d?-1:1)*s*Math.pow(2,o-i)},e.write=function(t,e,r,i,n,o){var s,f,a,h=8*o-n-1,u=(1<<h)-1,l=u>>1,c=5960464477539062e-23*(23===n),d=i?0:o-1,p=i?1:-1,m=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(f=+!!isNaN(e),s=u):(s=Math.floor(Math.log(e)/Math.LN2),e*(a=Math.pow(2,-s))<1&&(s--,a*=2),s+l>=1?e+=c/a:e+=c*Math.pow(2,1-l),e*a>=2&&(s++,a/=2),s+l>=u?(f=0,s=u):s+l>=1?(f=(e*a-1)*Math.pow(2,n),s+=l):(f=e*Math.pow(2,l-1)*Math.pow(2,n),s=0));n>=8;t[r+d]=255&f,d+=p,f/=256,n-=8);for(s=s<<n|f,h+=n;h>0;t[r+d]=255&s,d+=p,s/=256,h-=8);t[r+d-p]|=128*m}},7615:(t,e,r)=>{"use strict";r.d(e,{$:()=>n});var i=r(2115);let n=t=>i.createElement("button",{className:`wallet-adapter-button ${t.className||""}`,disabled:t.disabled,style:t.style,onClick:t.onClick,tabIndex:t.tabIndex||0,type:"button"},t.startIcon&&i.createElement("i",{className:"wallet-adapter-button-start-icon"},t.startIcon),t.children,t.endIcon&&i.createElement("i",{className:"wallet-adapter-button-end-icon"},t.endIcon))},7719:(t,e)=>{"use strict";e.byteLength=function(t){var e=a(t),r=e[0],i=e[1];return(r+i)*3/4-i},e.toByteArray=function(t){var e,r,o=a(t),s=o[0],f=o[1],h=new n((s+f)*3/4-f),u=0,l=f>0?s-4:s;for(r=0;r<l;r+=4)e=i[t.charCodeAt(r)]<<18|i[t.charCodeAt(r+1)]<<12|i[t.charCodeAt(r+2)]<<6|i[t.charCodeAt(r+3)],h[u++]=e>>16&255,h[u++]=e>>8&255,h[u++]=255&e;return 2===f&&(e=i[t.charCodeAt(r)]<<2|i[t.charCodeAt(r+1)]>>4,h[u++]=255&e),1===f&&(e=i[t.charCodeAt(r)]<<10|i[t.charCodeAt(r+1)]<<4|i[t.charCodeAt(r+2)]>>2,h[u++]=e>>8&255,h[u++]=255&e),h},e.fromByteArray=function(t){for(var e,i=t.length,n=i%3,o=[],s=0,f=i-n;s<f;s+=16383)o.push(function(t,e,i){for(var n,o=[],s=e;s<i;s+=3)n=(t[s]<<16&0xff0000)+(t[s+1]<<8&65280)+(255&t[s+2]),o.push(r[n>>18&63]+r[n>>12&63]+r[n>>6&63]+r[63&n]);return o.join("")}(t,s,s+16383>f?f:s+16383));return 1===n?o.push(r[(e=t[i-1])>>2]+r[e<<4&63]+"=="):2===n&&o.push(r[(e=(t[i-2]<<8)+t[i-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],i=[],n="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,f=o.length;s<f;++s)r[s]=o[s],i[o.charCodeAt(s)]=s;function a(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var i=r===e?0:4-r%4;return[r,i]}i[45]=62,i[95]=63},8512:(t,e,r)=>{"use strict";let i,n,o;r.d(e,{lY:()=>M});var s=r(8701),f=r(9989);let a=BigInt(0),h=BigInt(1),u=BigInt(2),l=BigInt(7),c=BigInt(256),d=BigInt(113),p=[],m=[],y=[];for(let t=0,e=h,r=1,i=0;t<24;t++){[r,i]=[i,(2*r+3*i)%5],p.push(2*(5*i+r)),m.push((t+1)*(t+2)/2%64);let n=a;for(let t=0;t<7;t++)(e=(e<<h^(e>>l)*d)%c)&u&&(n^=h<<(h<<BigInt(t))-h);y.push(n)}let g=(0,s.lD)(y,!0),w=g[0],v=g[1],b=(t,e,r)=>r>32?(0,s.WM)(t,e,r):(0,s.P5)(t,e,r),x=(t,e,r)=>r>32?(0,s.im)(t,e,r):(0,s.B4)(t,e,r);class E extends f.Vw{constructor(t,e,r,i=!1,n=24){if(super(),this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,this.enableXOF=!1,this.blockLen=t,this.suffix=e,this.outputLen=r,this.enableXOF=i,this.rounds=n,(0,f.Fe)(r),!(0<t&&t<200))throw Error("only keccak-f1600 function is supported");this.state=new Uint8Array(200),this.state32=(0,f.DH)(this.state)}clone(){return this._cloneInto()}keccak(){(0,f.fd)(this.state32),function(t,e=24){let r=new Uint32Array(10);for(let i=24-e;i<24;i++){for(let e=0;e<10;e++)r[e]=t[e]^t[e+10]^t[e+20]^t[e+30]^t[e+40];for(let e=0;e<10;e+=2){let i=(e+8)%10,n=(e+2)%10,o=r[n],s=r[n+1],f=b(o,s,1)^r[i],a=x(o,s,1)^r[i+1];for(let r=0;r<50;r+=10)t[e+r]^=f,t[e+r+1]^=a}let e=t[2],n=t[3];for(let r=0;r<24;r++){let i=m[r],o=b(e,n,i),s=x(e,n,i),f=p[r];e=t[f],n=t[f+1],t[f]=o,t[f+1]=s}for(let e=0;e<50;e+=10){for(let i=0;i<10;i++)r[i]=t[e+i];for(let i=0;i<10;i++)t[e+i]^=~r[(i+2)%10]&r[(i+4)%10]}t[0]^=w[i],t[1]^=v[i]}(0,f.uH)(r)}(this.state32,this.rounds),(0,f.fd)(this.state32),this.posOut=0,this.pos=0}update(t){(0,f.CC)(this),t=(0,f.ZJ)(t),(0,f.DO)(t);let{blockLen:e,state:r}=this,i=t.length;for(let n=0;n<i;){let o=Math.min(e-this.pos,i-n);for(let e=0;e<o;e++)r[this.pos++]^=t[n++];this.pos===e&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:e,pos:r,blockLen:i}=this;t[r]^=e,(128&e)!=0&&r===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,f.CC)(this,!1),(0,f.DO)(t),this.finish();let e=this.state,{blockLen:r}=this;for(let i=0,n=t.length;i<n;){this.posOut>=r&&this.keccak();let o=Math.min(r-this.posOut,n-i);t.set(e.subarray(this.posOut,this.posOut+o),i),this.posOut+=o,i+=o}return t}xofInto(t){if(!this.enableXOF)throw Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,f.Fe)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,f.Ht)(t,this),this.finished)throw Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,(0,f.uH)(this.state)}_cloneInto(t){let{blockLen:e,suffix:r,outputLen:i,rounds:n,enableXOF:o}=this;return t||(t=new E(e,r,i,o,n)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=n,t.suffix=r,t.outputLen=i,t.enableXOF=o,t.destroyed=this.destroyed,t}}let M=(i=1,n=136,o=32,(0,f.qj)(()=>new E(n,i,o)))},8693:(t,e,r)=>{"use strict";r.d(e,{sc:()=>g,Zf:()=>w});var i=r(9989);class n extends i.Vw{constructor(t,e,r,n){super(),this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.blockLen=t,this.outputLen=e,this.padOffset=r,this.isLE=n,this.buffer=new Uint8Array(t),this.view=(0,i.O8)(this.buffer)}update(t){(0,i.CC)(this),t=(0,i.ZJ)(t),(0,i.DO)(t);let{view:e,buffer:r,blockLen:n}=this,o=t.length;for(let s=0;s<o;){let f=Math.min(n-this.pos,o-s);if(f===n){let e=(0,i.O8)(t);for(;n<=o-s;s+=n)this.process(e,s);continue}r.set(t.subarray(s,s+f),this.pos),this.pos+=f,s+=f,this.pos===n&&(this.process(e,0),this.pos=0)}return this.length+=t.length,this.roundClean(),this}digestInto(t){(0,i.CC)(this),(0,i.Ht)(t,this),this.finished=!0;let{buffer:e,view:r,blockLen:n,isLE:o}=this,{pos:s}=this;e[s++]=128,(0,i.uH)(this.buffer.subarray(s)),this.padOffset>n-s&&(this.process(r,0),s=0);for(let t=s;t<n;t++)e[t]=0;!function(t,e,r,i){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,r,i);let n=BigInt(32),o=BigInt(0xffffffff),s=Number(r>>n&o),f=Number(r&o),a=4*!!i,h=4*!i;t.setUint32(e+a,s,i),t.setUint32(e+h,f,i)}(r,n-8,BigInt(8*this.length),o),this.process(r,0);let f=(0,i.O8)(t),a=this.outputLen;if(a%4)throw Error("_sha2: outputLen should be aligned to 32bit");let h=a/4,u=this.get();if(h>u.length)throw Error("_sha2: outputLen bigger than state");for(let t=0;t<h;t++)f.setUint32(4*t,u[t],o)}digest(){let{buffer:t,outputLen:e}=this;this.digestInto(t);let r=t.slice(0,e);return this.destroy(),r}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());let{blockLen:e,buffer:r,length:i,finished:n,destroyed:o,pos:s}=this;return t.destroyed=o,t.finished=n,t.length=i,t.pos=s,i%e&&t.buffer.set(r),t}clone(){return this._cloneInto()}}let o=Uint32Array.from([0x6a09e667,0xbb67ae85,0x3c6ef372,0xa54ff53a,0x510e527f,0x9b05688c,0x1f83d9ab,0x5be0cd19]),s=Uint32Array.from([0x6a09e667,0xf3bcc908,0xbb67ae85,0x84caa73b,0x3c6ef372,0xfe94f82b,0xa54ff53a,0x5f1d36f1,0x510e527f,0xade682d1,0x9b05688c,0x2b3e6c1f,0x1f83d9ab,0xfb41bd6b,0x5be0cd19,0x137e2179]);var f=r(8701);let a=Uint32Array.from([0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2]),h=new Uint32Array(64);class u extends n{constructor(t=32){super(64,t,8,!1),this.A=0|o[0],this.B=0|o[1],this.C=0|o[2],this.D=0|o[3],this.E=0|o[4],this.F=0|o[5],this.G=0|o[6],this.H=0|o[7]}get(){let{A:t,B:e,C:r,D:i,E:n,F:o,G:s,H:f}=this;return[t,e,r,i,n,o,s,f]}set(t,e,r,i,n,o,s,f){this.A=0|t,this.B=0|e,this.C=0|r,this.D=0|i,this.E=0|n,this.F=0|o,this.G=0|s,this.H=0|f}process(t,e){for(let r=0;r<16;r++,e+=4)h[r]=t.getUint32(e,!1);for(let t=16;t<64;t++){let e=h[t-15],r=h[t-2],n=(0,i.Ow)(e,7)^(0,i.Ow)(e,18)^e>>>3,o=(0,i.Ow)(r,17)^(0,i.Ow)(r,19)^r>>>10;h[t]=o+h[t-7]+n+h[t-16]|0}let{A:r,B:n,C:o,D:s,E:f,F:u,G:l,H:c}=this;for(let t=0;t<64;t++){var d,p,m,y;let e=c+((0,i.Ow)(f,6)^(0,i.Ow)(f,11)^(0,i.Ow)(f,25))+((d=f)&u^~d&l)+a[t]+h[t]|0,g=((0,i.Ow)(r,2)^(0,i.Ow)(r,13)^(0,i.Ow)(r,22))+((p=r)&(m=n)^p&(y=o)^m&y)|0;c=l,l=u,u=f,f=s+e|0,s=o,o=n,n=r,r=e+g|0}r=r+this.A|0,n=n+this.B|0,o=o+this.C|0,s=s+this.D|0,f=f+this.E|0,u=u+this.F|0,l=l+this.G|0,c=c+this.H|0,this.set(r,n,o,s,f,u,l,c)}roundClean(){(0,i.uH)(h)}destroy(){this.set(0,0,0,0,0,0,0,0),(0,i.uH)(this.buffer)}}let l=f.lD(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(t=>BigInt(t))),c=l[0],d=l[1],p=new Uint32Array(80),m=new Uint32Array(80);class y extends n{constructor(t=64){super(128,t,16,!1),this.Ah=0|s[0],this.Al=0|s[1],this.Bh=0|s[2],this.Bl=0|s[3],this.Ch=0|s[4],this.Cl=0|s[5],this.Dh=0|s[6],this.Dl=0|s[7],this.Eh=0|s[8],this.El=0|s[9],this.Fh=0|s[10],this.Fl=0|s[11],this.Gh=0|s[12],this.Gl=0|s[13],this.Hh=0|s[14],this.Hl=0|s[15]}get(){let{Ah:t,Al:e,Bh:r,Bl:i,Ch:n,Cl:o,Dh:s,Dl:f,Eh:a,El:h,Fh:u,Fl:l,Gh:c,Gl:d,Hh:p,Hl:m}=this;return[t,e,r,i,n,o,s,f,a,h,u,l,c,d,p,m]}set(t,e,r,i,n,o,s,f,a,h,u,l,c,d,p,m){this.Ah=0|t,this.Al=0|e,this.Bh=0|r,this.Bl=0|i,this.Ch=0|n,this.Cl=0|o,this.Dh=0|s,this.Dl=0|f,this.Eh=0|a,this.El=0|h,this.Fh=0|u,this.Fl=0|l,this.Gh=0|c,this.Gl=0|d,this.Hh=0|p,this.Hl=0|m}process(t,e){for(let r=0;r<16;r++,e+=4)p[r]=t.getUint32(e),m[r]=t.getUint32(e+=4);for(let t=16;t<80;t++){let e=0|p[t-15],r=0|m[t-15],i=f.rE(e,r,1)^f.rE(e,r,8)^f.xn(e,r,7),n=f.ry(e,r,1)^f.ry(e,r,8)^f.jm(e,r,7),o=0|p[t-2],s=0|m[t-2],a=f.rE(o,s,19)^f.qh(o,s,61)^f.xn(o,s,6),h=f.ry(o,s,19)^f.Ei(o,s,61)^f.jm(o,s,6),u=f.CW(n,h,m[t-7],m[t-16]),l=f.CQ(u,i,a,p[t-7],p[t-16]);p[t]=0|l,m[t]=0|u}let{Ah:r,Al:i,Bh:n,Bl:o,Ch:s,Cl:a,Dh:h,Dl:u,Eh:l,El:y,Fh:g,Fl:w,Gh:v,Gl:b,Hh:x,Hl:E}=this;for(let t=0;t<80;t++){let e=f.rE(l,y,14)^f.rE(l,y,18)^f.qh(l,y,41),M=f.ry(l,y,14)^f.ry(l,y,18)^f.Ei(l,y,41),B=l&g^~l&v,A=y&w^~y&b,_=f.F8(E,M,A,d[t],m[t]),O=f.TH(_,x,e,B,c[t],p[t]),I=0|_,S=f.rE(r,i,28)^f.qh(r,i,34)^f.qh(r,i,39),U=f.ry(r,i,28)^f.Ei(r,i,34)^f.Ei(r,i,39),k=r&n^r&s^n&s,R=i&o^i&a^o&a;x=0|v,E=0|b,v=0|g,b=0|w,g=0|l,w=0|y,({h:l,l:y}=f.WQ(0|h,0|u,0|O,0|I)),h=0|s,u=0|a,s=0|n,a=0|o,n=0|r,o=0|i;let z=f.Vl(I,U,R);r=f.Vr(z,O,S,k),i=0|z}({h:r,l:i}=f.WQ(0|this.Ah,0|this.Al,0|r,0|i)),({h:n,l:o}=f.WQ(0|this.Bh,0|this.Bl,0|n,0|o)),({h:s,l:a}=f.WQ(0|this.Ch,0|this.Cl,0|s,0|a)),({h:h,l:u}=f.WQ(0|this.Dh,0|this.Dl,0|h,0|u)),({h:l,l:y}=f.WQ(0|this.Eh,0|this.El,0|l,0|y)),({h:g,l:w}=f.WQ(0|this.Fh,0|this.Fl,0|g,0|w)),({h:v,l:b}=f.WQ(0|this.Gh,0|this.Gl,0|v,0|b)),({h:x,l:E}=f.WQ(0|this.Hh,0|this.Hl,0|x,0|E)),this.set(r,i,n,o,s,a,h,u,l,y,g,w,v,b,x,E)}roundClean(){(0,i.uH)(p,m)}destroy(){(0,i.uH)(this.buffer),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}let g=(0,i.qj)(()=>new u),w=(0,i.qj)(()=>new y)},8701:(t,e,r)=>{"use strict";r.d(e,{B4:()=>d,CQ:()=>b,CW:()=>v,Ei:()=>l,F8:()=>x,P5:()=>c,TH:()=>E,Vl:()=>g,Vr:()=>w,WM:()=>p,WQ:()=>y,im:()=>m,jm:()=>f,lD:()=>o,qh:()=>u,rE:()=>a,ry:()=>h,xn:()=>s});let i=BigInt(0x100000000-1),n=BigInt(32);function o(t,e=!1){let r=t.length,s=new Uint32Array(r),f=new Uint32Array(r);for(let o=0;o<r;o++){let{h:r,l:a}=function(t,e=!1){return e?{h:Number(t&i),l:Number(t>>n&i)}:{h:0|Number(t>>n&i),l:0|Number(t&i)}}(t[o],e);[s[o],f[o]]=[r,a]}return[s,f]}let s=(t,e,r)=>t>>>r,f=(t,e,r)=>t<<32-r|e>>>r,a=(t,e,r)=>t>>>r|e<<32-r,h=(t,e,r)=>t<<32-r|e>>>r,u=(t,e,r)=>t<<64-r|e>>>r-32,l=(t,e,r)=>t>>>r-32|e<<64-r,c=(t,e,r)=>t<<r|e>>>32-r,d=(t,e,r)=>e<<r|t>>>32-r,p=(t,e,r)=>e<<r-32|t>>>64-r,m=(t,e,r)=>t<<r-32|e>>>64-r;function y(t,e,r,i){let n=(e>>>0)+(i>>>0);return{h:t+r+(n/0x100000000|0)|0,l:0|n}}let g=(t,e,r)=>(t>>>0)+(e>>>0)+(r>>>0),w=(t,e,r,i)=>e+r+i+(t/0x100000000|0)|0,v=(t,e,r,i)=>(t>>>0)+(e>>>0)+(r>>>0)+(i>>>0),b=(t,e,r,i,n)=>e+r+i+n+(t/0x100000000|0)|0,x=(t,e,r,i,n)=>(t>>>0)+(e>>>0)+(r>>>0)+(i>>>0)+(n>>>0),E=(t,e,r,i,n,o)=>e+r+i+n+o+(t/0x100000000|0)|0},9555:(t,e,r)=>{t.exports=r(5884)("**********************************************************")},9796:(t,e,r)=>{t.exports=r(3711)("**********************************************************")},9989:(t,e,r)=>{"use strict";r.d(e,{Vw:()=>g,DO:()=>o,CC:()=>f,sd:()=>s,Fe:()=>n,Ht:()=>a,uH:()=>u,Id:()=>y,qj:()=>w,O8:()=>l,po:()=>v,Ow:()=>c,fd:()=>d,ZJ:()=>m,DH:()=>h});let i="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0;function n(t){if(!Number.isSafeInteger(t)||t<0)throw Error("positive integer expected, got "+t)}function o(t,...e){if(!(t instanceof Uint8Array||ArrayBuffer.isView(t)&&"Uint8Array"===t.constructor.name))throw Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw Error("Uint8Array expected of length "+e+", got length="+t.length)}function s(t){if("function"!=typeof t||"function"!=typeof t.create)throw Error("Hash should be wrapped by utils.createHasher");n(t.outputLen),n(t.blockLen)}function f(t,e=!0){if(t.destroyed)throw Error("Hash instance has been destroyed");if(e&&t.finished)throw Error("Hash#digest() has already been called")}function a(t,e){o(t);let r=e.outputLen;if(t.length<r)throw Error("digestInto() expects output buffer of length at least "+r)}function h(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function u(...t){for(let e=0;e<t.length;e++)t[e].fill(0)}function l(t){return new DataView(t.buffer,t.byteOffset,t.byteLength)}function c(t,e){return t<<32-e|t>>>e}let d=68===new Uint8Array(new Uint32Array([0x11223344]).buffer)[0]?t=>t:function(t){for(let r=0;r<t.length;r++){var e;t[r]=(e=t[r])<<24&0xff000000|e<<8&0xff0000|e>>>8&65280|e>>>24&255}return t},p=((t,e)=>e.toString(16).padStart(2,"0"),{_0:48,_9:57,A:65,F:70,a:97,f:102});function m(t){return"string"==typeof t&&(t=function(t){if("string"!=typeof t)throw Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}(t)),o(t),t}function y(...t){let e=0;for(let r=0;r<t.length;r++){let i=t[r];o(i),e+=i.length}let r=new Uint8Array(e);for(let e=0,i=0;e<t.length;e++){let n=t[e];r.set(n,i),i+=n.length}return r}class g{}function w(t){let e=e=>t().update(m(e)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function v(t=32){if(i&&"function"==typeof i.getRandomValues)return i.getRandomValues(new Uint8Array(t));if(i&&"function"==typeof i.randomBytes)return Uint8Array.from(i.randomBytes(t));throw Error("crypto.getRandomValues must be defined")}}}]);