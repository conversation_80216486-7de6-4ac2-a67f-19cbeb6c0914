{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { WalletMultiButton } from '@solana/wallet-adapter-react-ui';\nimport { useWallet } from '@solana/wallet-adapter-react';\nimport { SOLANA_NETWORK } from '@/lib/constants';\n// import { ChainSelector, ConnectionStatus } from '@/components/cross-chain/MultiChainWalletProvider';\n\nexport function Header() {\n  const { connected } = useWallet();\n\n  return (\n    <header className=\"bg-black/95 backdrop-blur-sm border-b border-gray-800/50 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-3\">\n              {/* Logo */}\n              <div className=\"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">\n                  TokenLaunch\n                </h1>\n                <p className=\"text-xs text-gray-400\">Cross-Chain Token Creator</p>\n              </div>\n            </div>\n\n            {/* Multi-Chain Status */}\n            <div className=\"hidden lg:flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 bg-gray-800/50 border border-gray-700/30 rounded-xl px-3 py-1\">\n                <span className=\"text-cyan-400 text-sm\">🌐</span>\n                <span className=\"text-sm text-gray-400 font-medium\">Cross-Chain Ready</span>\n              </div>\n              <div className=\"w-px h-6 bg-gray-700\"></div>\n              <div className=\"flex items-center space-x-2\">\n                <div className={`w-2 h-2 rounded-full ${\n                  SOLANA_NETWORK === 'mainnet-beta' ? 'bg-green-400' : 'bg-yellow-400'\n                } animate-pulse`}></div>\n                <span className=\"text-sm text-gray-400 font-medium\">\n                  Solana {SOLANA_NETWORK === 'mainnet-beta' ? 'Mainnet' : 'Devnet'}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a href=\"#features\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Cross-Chain\n            </a>\n            <a href=\"#launch\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Launch\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Docs\n            </a>\n          </nav>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* Chain Indicators */}\n            <div className=\"hidden xl:flex items-center gap-2 p-2 bg-gray-800/50 rounded-xl border border-gray-700/50\">\n              <span className=\"text-gray-400 text-sm font-medium mr-2\">Chains:</span>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg\" title=\"Solana\">\n                <span className=\"text-sm\">◎</span>\n              </div>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 text-gray-400\" title=\"BNB Chain\">\n                <span className=\"text-sm\">🟡</span>\n              </div>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 text-gray-400\" title=\"Avalanche\">\n                <span className=\"text-sm\">🔺</span>\n              </div>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 text-gray-400\" title=\"Polkadot\">\n                <span className=\"text-sm\">⚫</span>\n              </div>\n            </div>\n\n            {/* Solana Connection Status */}\n            {connected && (\n              <div className=\"hidden sm:flex items-center space-x-2 bg-green-900/30 border border-green-500/30 px-3 py-1 rounded-full\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-green-400 font-medium\">Solana Connected</span>\n              </div>\n            )}\n\n            {/* Primary Solana Wallet Button */}\n            <WalletMultiButton className=\"!bg-gradient-to-r !from-purple-500 !to-pink-500 hover:!from-purple-600 hover:!to-pink-600 !text-white !rounded-xl !px-6 !py-2 !text-sm !font-semibold !transition-all !duration-300 !border !border-purple-400/30 hover:!border-purple-300/50 hover:!scale-105\" />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOO,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD;IAE9B,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC5E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAgG;;;;;;0DAG9G,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,0HAAA,CAAA,iBAAc,KAAK,iBAAiB,iBAAiB,gBACtD,cAAc,CAAC;;;;;;0DAChB,6LAAC;gDAAK,WAAU;;oDAAoC;oDAC1C,0HAAA,CAAA,iBAAc,KAAK,iBAAiB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAoE;;;;;;0CAGlG,6LAAC;gCAAE,MAAK;gCAAU,WAAU;0CAAoE;;;;;;0CAGhG,6LAAC;gCAAE,MAAK;gCAAI,WAAU;0CAAoE;;;;;;;;;;;;kCAK5F,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAyC;;;;;;kDACzD,6LAAC;wCAAI,WAAU;wCAAwH,OAAM;kDAC3I,cAAA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;wCAAgF,OAAM;kDACnG,cAAA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;wCAAgF,OAAM;kDACnG,cAAA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;wCAAgF,OAAM;kDACnG,cAAA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;4BAK7B,2BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;;;;;;;0CAKzD,6LAAC,kMAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAvFgB;;QACQ,oLAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/config/chains.ts"], "sourcesContent": ["/**\n * Multi-Chain Configuration\n * Defines supported blockchains and their parameters\n */\n\nexport enum SupportedChain {\n  SOLANA = 'solana',\n  BNB_CHAIN = 'bnb',\n  AVALANCHE = 'avalanche',\n  POLKADOT = 'polkadot'\n}\n\nexport interface ChainConfig {\n  id: string;\n  name: string;\n  displayName: string;\n  nativeCurrency: {\n    name: string;\n    symbol: string;\n    decimals: number;\n  };\n  rpcUrls: {\n    default: string;\n    testnet?: string;\n  };\n  blockExplorers: {\n    default: {\n      name: string;\n      url: string;\n    };\n  };\n  testnet?: boolean;\n  chainType: 'evm' | 'solana' | 'substrate';\n  features: {\n    tokenStandard: string;\n    dexIntegration: string[];\n    bridgeSupport: string[];\n  };\n}\n\nexport const CHAIN_CONFIGS: Record<SupportedChain, ChainConfig> = {\n  [SupportedChain.SOLANA]: {\n    id: 'solana',\n    name: 'solana',\n    displayName: 'Solana',\n    nativeCurrency: {\n      name: '<PERSON><PERSON>',\n      symbol: 'SOL',\n      decimals: 9,\n    },\n    rpcUrls: {\n      default: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',\n      testnet: 'https://api.devnet.solana.com',\n    },\n    blockExplorers: {\n      default: {\n        name: 'Solscan',\n        url: 'https://solscan.io',\n      },\n    },\n    chainType: 'solana',\n    features: {\n      tokenStandard: 'SPL',\n      dexIntegration: ['raydium', 'orca'],\n      bridgeSupport: ['wormhole', 'allbridge'],\n    },\n  },\n  [SupportedChain.BNB_CHAIN]: {\n    id: '56',\n    name: 'bnb-smart-chain',\n    displayName: 'BNB Chain',\n    nativeCurrency: {\n      name: 'BNB',\n      symbol: 'BNB',\n      decimals: 18,\n    },\n    rpcUrls: {\n      default: 'https://bsc-dataseed1.binance.org',\n      testnet: 'https://data-seed-prebsc-1-s1.binance.org:8545',\n    },\n    blockExplorers: {\n      default: {\n        name: 'BscScan',\n        url: 'https://bscscan.com',\n      },\n    },\n    chainType: 'evm',\n    features: {\n      tokenStandard: 'BEP-20',\n      dexIntegration: ['pancakeswap', 'biswap'],\n      bridgeSupport: ['wormhole', 'axelar', 'multichain'],\n    },\n  },\n  [SupportedChain.AVALANCHE]: {\n    id: '43114',\n    name: 'avalanche',\n    displayName: 'Avalanche',\n    nativeCurrency: {\n      name: 'Avalanche',\n      symbol: 'AVAX',\n      decimals: 18,\n    },\n    rpcUrls: {\n      default: 'https://api.avax.network/ext/bc/C/rpc',\n      testnet: 'https://api.avax-test.network/ext/bc/C/rpc',\n    },\n    blockExplorers: {\n      default: {\n        name: 'SnowTrace',\n        url: 'https://snowtrace.io',\n      },\n    },\n    chainType: 'evm',\n    features: {\n      tokenStandard: 'ERC-20',\n      dexIntegration: ['traderjoe', 'pangolin'],\n      bridgeSupport: ['wormhole', 'axelar', 'avalanche-bridge'],\n    },\n  },\n  [SupportedChain.POLKADOT]: {\n    id: 'polkadot',\n    name: 'polkadot',\n    displayName: 'Polkadot',\n    nativeCurrency: {\n      name: 'Polkadot',\n      symbol: 'DOT',\n      decimals: 10,\n    },\n    rpcUrls: {\n      default: 'wss://rpc.polkadot.io',\n      testnet: 'wss://westend-rpc.polkadot.io',\n    },\n    blockExplorers: {\n      default: {\n        name: 'Polkascan',\n        url: 'https://polkascan.io/polkadot',\n      },\n    },\n    chainType: 'substrate',\n    features: {\n      tokenStandard: 'PSP-22',\n      dexIntegration: ['hydradx', 'basilisk'],\n      bridgeSupport: ['xcm', 'wormhole'],\n    },\n  },\n};\n\nexport const DEFAULT_CHAINS = [\n  SupportedChain.SOLANA,\n  SupportedChain.BNB_CHAIN,\n  SupportedChain.AVALANCHE,\n  SupportedChain.POLKADOT,\n];\n\nexport function getChainConfig(chain: SupportedChain): ChainConfig {\n  return CHAIN_CONFIGS[chain];\n}\n\nexport function getAllChainConfigs(): ChainConfig[] {\n  return Object.values(CHAIN_CONFIGS);\n}\n\nexport function isEVMChain(chain: SupportedChain): boolean {\n  return CHAIN_CONFIGS[chain].chainType === 'evm';\n}\n\nexport function isSolanaChain(chain: SupportedChain): boolean {\n  return CHAIN_CONFIGS[chain].chainType === 'solana';\n}\n\nexport function isSubstrateChain(chain: SupportedChain): boolean {\n  return CHAIN_CONFIGS[chain].chainType === 'substrate';\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAgDc;AA9CR,IAAA,AAAK,wCAAA;;;;;WAAA;;AAmCL,MAAM,gBAAqD;IAChE,UAAuB,EAAE;QACvB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;YACP,SAAS,qEAA0C;YACnD,SAAS;QACX;QACA,gBAAgB;YACd,SAAS;gBACP,MAAM;gBACN,KAAK;YACP;QACF;QACA,WAAW;QACX,UAAU;YACR,eAAe;YACf,gBAAgB;gBAAC;gBAAW;aAAO;YACnC,eAAe;gBAAC;gBAAY;aAAY;QAC1C;IACF;IACA,OAA0B,EAAE;QAC1B,IAAI;QACJ,MAAM;QACN,aAAa;QACb,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;YACP,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;YACd,SAAS;gBACP,MAAM;gBACN,KAAK;YACP;QACF;QACA,WAAW;QACX,UAAU;YACR,eAAe;YACf,gBAAgB;gBAAC;gBAAe;aAAS;YACzC,eAAe;gBAAC;gBAAY;gBAAU;aAAa;QACrD;IACF;IACA,aAA0B,EAAE;QAC1B,IAAI;QACJ,MAAM;QACN,aAAa;QACb,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;YACP,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;YACd,SAAS;gBACP,MAAM;gBACN,KAAK;YACP;QACF;QACA,WAAW;QACX,UAAU;YACR,eAAe;YACf,gBAAgB;gBAAC;gBAAa;aAAW;YACzC,eAAe;gBAAC;gBAAY;gBAAU;aAAmB;QAC3D;IACF;IACA,YAAyB,EAAE;QACzB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;YACP,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;YACd,SAAS;gBACP,MAAM;gBACN,KAAK;YACP;QACF;QACA,WAAW;QACX,UAAU;YACR,eAAe;YACf,gBAAgB;gBAAC;gBAAW;aAAW;YACvC,eAAe;gBAAC;gBAAO;aAAW;QACpC;IACF;AACF;AAEO,MAAM,iBAAiB;;;;;CAK7B;AAEM,SAAS,eAAe,KAAqB;IAClD,OAAO,aAAa,CAAC,MAAM;AAC7B;AAEO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB;AAEO,SAAS,WAAW,KAAqB;IAC9C,OAAO,aAAa,CAAC,MAAM,CAAC,SAAS,KAAK;AAC5C;AAEO,SAAS,cAAc,KAAqB;IACjD,OAAO,aAAa,CAAC,MAAM,CAAC,SAAS,KAAK;AAC5C;AAEO,SAAS,iBAAiB,KAAqB;IACpD,OAAO,aAAa,CAAC,MAAM,CAAC,SAAS,KAAK;AAC5C", "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/cross-chain/CrossChainTokenCreator.tsx"], "sourcesContent": ["/**\n * Cross-Chain Token Creator\n * Main interface for creating tokens across multiple blockchains\n */\n\n'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { SupportedChain, getChainConfig } from '@/config/chains';\n// import { useMultiChainWallet } from '@/components/cross-chain/MultiChainWalletProvider';\nimport { TokenDeploymentParams, calculateOptimalDistribution } from '@/config/deployment';\n\ninterface CrossChainTokenCreatorProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function CrossChainTokenCreator({ isOpen, onClose }: CrossChainTokenCreatorProps) {\n  // const { walletState, isChainConnected } = useMultiChainWallet();\n  \n  const [currentStep, setCurrentStep] = useState<'basics' | 'chains' | 'liquidity' | 'review' | 'deploying'>('basics');\n  const [tokenParams, setTokenParams] = useState<Partial<TokenDeploymentParams>>({\n    name: '',\n    symbol: '',\n    totalSupply: '',\n    decimals: 9,\n    description: '',\n    targetChains: [SupportedChain.SOLANA],\n    initialLiquidity: 0.2,\n  });\n\n  const handleParamChange = useCallback((key: keyof TokenDeploymentParams, value: any) => {\n    setTokenParams(prev => ({ ...prev, [key]: value }));\n  }, []);\n\n  const nextStep = useCallback(() => {\n    const steps = ['basics', 'chains', 'liquidity', 'review', 'deploying'] as const;\n    const currentIndex = steps.indexOf(currentStep);\n    if (currentIndex < steps.length - 1) {\n      setCurrentStep(steps[currentIndex + 1]);\n    }\n  }, [currentStep]);\n\n  const prevStep = useCallback(() => {\n    const steps = ['basics', 'chains', 'liquidity', 'review', 'deploying'] as const;\n    const currentIndex = steps.indexOf(currentStep);\n    if (currentIndex > 0) {\n      setCurrentStep(steps[currentIndex - 1]);\n    }\n  }, [currentStep]);\n\n  const getStepTitle = () => {\n    switch (currentStep) {\n      case 'basics': return 'Token Basics';\n      case 'chains': return 'Select Chains';\n      case 'liquidity': return 'Liquidity Distribution';\n      case 'review': return 'Review & Launch';\n      case 'deploying': return 'Deploying Everywhere';\n      default: return 'Create Token';\n    }\n  };\n\n  const getStepSubtitle = () => {\n    switch (currentStep) {\n      case 'basics': return 'Define your token properties';\n      case 'chains': return 'Choose target blockchains';\n      case 'liquidity': return 'Configure cross-chain liquidity';\n      case 'review': return 'Confirm deployment details';\n      case 'deploying': return 'Creating tokens across chains';\n      default: return '';\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"w-full max-w-5xl mx-auto\">\n      <div className=\"bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl w-full shadow-2xl\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 p-6 rounded-t-2xl border-b border-gray-700/50\">\n          <div className=\"flex items-center justify-center\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center shadow-lg\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <div className=\"text-center\">\n                <h2 className=\"text-2xl font-bold text-white\">{getStepTitle()}</h2>\n                <p className=\"text-white/80 text-sm\">{getStepSubtitle()}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"px-6 py-4 bg-gray-800/50\">\n          <div className=\"flex items-center justify-between\">\n            {['basics', 'chains', 'liquidity', 'review'].map((step, index) => {\n              const isActive = currentStep === step;\n              const isCompleted = ['basics', 'chains', 'liquidity', 'review'].indexOf(currentStep) > index;\n              \n              return (\n                <div key={step} className=\"flex items-center\">\n                  <div className={`\n                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold\n                    ${isActive ? 'bg-purple-500 text-white' : \n                      isCompleted ? 'bg-green-500 text-white' : 'bg-gray-600 text-gray-400'}\n                  `}>\n                    {isCompleted ? '✓' : index + 1}\n                  </div>\n                  {index < 3 && (\n                    <div className={`w-16 h-1 mx-2 ${isCompleted ? 'bg-green-500' : 'bg-gray-600'}`}></div>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {/* Step 1: Token Basics */}\n          {currentStep === 'basics' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Token Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={tokenParams.name || ''}\n                    onChange={(e) => handleParamChange('name', e.target.value)}\n                    placeholder=\"My Awesome Token\"\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Token Symbol *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={tokenParams.symbol || ''}\n                    onChange={(e) => handleParamChange('symbol', e.target.value.toUpperCase())}\n                    placeholder=\"MAT\"\n                    maxLength={10}\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Total Supply *\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={tokenParams.totalSupply || ''}\n                    onChange={(e) => handleParamChange('totalSupply', e.target.value)}\n                    placeholder=\"1000000\"\n                    min=\"1\"\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Decimals\n                  </label>\n                  <select\n                    value={tokenParams.decimals || 9}\n                    onChange={(e) => handleParamChange('decimals', parseInt(e.target.value))}\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white transition-all duration-300\"\n                  >\n                    {[6, 8, 9, 18].map(decimal => (\n                      <option key={decimal} value={decimal}>{decimal} decimals</option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Description (Optional)\n                </label>\n                <textarea\n                  value={tokenParams.description || ''}\n                  onChange={(e) => handleParamChange('description', e.target.value)}\n                  placeholder=\"Describe your token and its purpose...\"\n                  rows={3}\n                  className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300 resize-none\"\n                />\n              </div>\n\n              {/* Navigation */}\n              <div className=\"flex justify-end pt-6\">\n                <button\n                  type=\"button\"\n                  onClick={nextStep}\n                  disabled={!tokenParams.name || !tokenParams.symbol || !tokenParams.totalSupply}\n                  className=\"bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30\"\n                >\n                  Continue to Chain Selection\n                  <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Additional steps will be implemented in the next part */}\n          {currentStep !== 'basics' && (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400\">\n                {currentStep === 'chains' && 'Chain selection interface coming next...'}\n                {currentStep === 'liquidity' && 'Liquidity distribution interface coming next...'}\n                {currentStep === 'review' && 'Review interface coming next...'}\n                {currentStep === 'deploying' && 'Deployment interface coming next...'}\n              </div>\n              <button\n                onClick={prevStep}\n                className=\"mt-4 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-2 px-6 rounded-xl transition-all duration-300\"\n              >\n                Back\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;;;AAHA;;;AAYO,SAAS,uBAAuB,EAAE,MAAM,EAAE,OAAO,EAA+B;;IACrF,mEAAmE;IAEnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8D;IAC3G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;QAC7E,MAAM;QACN,QAAQ;QACR,aAAa;QACb,UAAU;QACV,aAAa;QACb,cAAc;YAAC,0HAAA,CAAA,iBAAc,CAAC,MAAM;SAAC;QACrC,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC,KAAkC;YACvE;yEAAe,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,IAAI,EAAE;oBAAM,CAAC;;QACnD;gEAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAC3B,MAAM,QAAQ;gBAAC;gBAAU;gBAAU;gBAAa;gBAAU;aAAY;YACtE,MAAM,eAAe,MAAM,OAAO,CAAC;YACnC,IAAI,eAAe,MAAM,MAAM,GAAG,GAAG;gBACnC,eAAe,KAAK,CAAC,eAAe,EAAE;YACxC;QACF;uDAAG;QAAC;KAAY;IAEhB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAC3B,MAAM,QAAQ;gBAAC;gBAAU;gBAAU;gBAAa;gBAAU;aAAY;YACtE,MAAM,eAAe,MAAM,OAAO,CAAC;YACnC,IAAI,eAAe,GAAG;gBACpB,eAAe,KAAK,CAAC,eAAe,EAAE;YACxC;QACF;uDAAG;QAAC;KAAY;IAEhB,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO9C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAU;4BAAU;4BAAa;yBAAS,CAAC,GAAG,CAAC,CAAC,MAAM;4BACtD,MAAM,WAAW,gBAAgB;4BACjC,MAAM,cAAc;gCAAC;gCAAU;gCAAU;gCAAa;6BAAS,CAAC,OAAO,CAAC,eAAe;4BAEvF,qBACE,6LAAC;gCAAe,WAAU;;kDACxB,6LAAC;wCAAI,WAAW,CAAC;;oBAEf,EAAE,WAAW,6BACX,cAAc,4BAA4B,4BAA4B;kBAC1E,CAAC;kDACE,cAAc,MAAM,QAAQ;;;;;;oCAE9B,QAAQ,mBACP,6LAAC;wCAAI,WAAW,CAAC,cAAc,EAAE,cAAc,iBAAiB,eAAe;;;;;;;+BATzE;;;;;wBAad;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,0BACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,YAAY,IAAI,IAAI;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,aAAY;oDACZ,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,YAAY,MAAM,IAAI;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;oDACvE,aAAY;oDACZ,WAAW;oDACX,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,YAAY,WAAW,IAAI;oDAClC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,KAAI;oDACJ,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO,YAAY,QAAQ,IAAI;oDAC/B,UAAU,CAAC,IAAM,kBAAkB,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;oDACtE,WAAU;8DAET;wDAAC;wDAAG;wDAAG;wDAAG;qDAAG,CAAC,GAAG,CAAC,CAAA,wBACjB,6LAAC;4DAAqB,OAAO;;gEAAU;gEAAQ;;2DAAlC;;;;;;;;;;;;;;;;;;;;;;8CAMrB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,YAAY,WAAW,IAAI;4CAClC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,aAAY;4CACZ,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,YAAY,WAAW;wCAC9E,WAAU;;4CACX;0DAEC,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQ9E,gBAAgB,0BACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,gBAAgB,YAAY;wCAC5B,gBAAgB,eAAe;wCAC/B,gBAAgB,YAAY;wCAC5B,gBAAgB,eAAe;;;;;;;8CAElC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA5NgB;KAAA", "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Head<PERSON> } from \"@/components/Header\";\nimport { CrossChainTokenCreator } from \"@/components/cross-chain/CrossChainTokenCreator\";\n\nexport default function Home() {\n  return (\n    <>\n      <Header />\n\n      {/* Main Content - Token Creation Focus */}\n      <main className=\"relative overflow-hidden bg-black min-h-screen\">\n        {/* Animated background elements */}\n        <div className=\"absolute inset-0\">\n          {/* Grid pattern overlay */}\n          <div className=\"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]\"></div>\n\n          {/* Floating orbs */}\n          <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse\"></div>\n          <div className=\"absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-r from-cyan-500/20 to-green-500/20 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse delay-500\"></div>\n\n          {/* Additional floating elements */}\n          <div className=\"absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-full blur-2xl animate-pulse delay-2000\"></div>\n          <div className=\"absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-full blur-2xl animate-pulse delay-3000\"></div>\n        </div>\n\n        <div className=\"relative container mx-auto px-4 py-8\">\n          {/* Simplified header section */}\n          <div className=\"text-center mb-12\">\n            <div className=\"inline-flex items-center bg-gray-900/50 border border-purple-500/30 rounded-full px-6 py-3 mb-6\">\n              <span className=\"w-2 h-2 bg-cyan-400 rounded-full animate-pulse mr-3\"></span>\n              <span className=\"text-gray-300 text-sm font-medium\">Cross-Chain Platform • 4 Blockchains</span>\n            </div>\n\n            <h1 className=\"text-4xl lg:text-6xl font-black text-white mb-4 leading-tight tracking-tight\">\n              Launch Everywhere\n              <span className=\"block bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent\">\n                Cross-Chain Tokens\n              </span>\n            </h1>\n\n            <p className=\"text-lg lg:text-xl text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed\">\n              Deploy tokens simultaneously across <span className=\"text-purple-400 font-semibold\">Solana, BNB Chain, Avalanche, and Polkadot</span> through a single interface.\n              <span className=\"block mt-2 text-cyan-400 font-semibold\">No bridging required. No capital fragmentation.</span>\n            </p>\n          </div>\n\n          {/* Quick feature highlights */}\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">4 Chains</div>\n              <div className=\"text-gray-400 text-sm\">Simultaneous Deploy</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">No Bridging</div>\n              <div className=\"text-gray-400 text-sm\">Direct Launch</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">Auto Liquidity</div>\n              <div className=\"text-gray-400 text-sm\">Cross-Chain Pools</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">One Interface</div>\n              <div className=\"text-gray-400 text-sm\">Unified Control</div>\n            </div>\n          </div>\n\n          {/* Main Cross-Chain Token Creation Form */}\n          <div className=\"max-w-6xl mx-auto\">\n            <CrossChainTokenCreator\n              isOpen={true}\n              onClose={() => {}}\n            />\n          </div>\n\n          {/* Trust indicators */}\n          <div className=\"mt-16 pt-8 border-t border-gray-800/30 text-center\">\n            <p className=\"text-gray-500 text-sm mb-6\">Powered by industry-leading cross-chain protocols</p>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 max-w-6xl mx-auto\">\n              {/* Blockchains */}\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Solana</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">BNB Chain</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Avalanche</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Polkadot</span>\n              </div>\n\n              {/* Cross-Chain Protocols */}\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Wormhole</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-cyan-500 to-teal-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Axelar</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Singularity</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Raydium</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE;;0BACE,6LAAC,+HAAA,CAAA,SAAM;;;;;0BAGP,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAGtD,6LAAC;wCAAG,WAAU;;4CAA+E;0DAE3F,6LAAC;gDAAK,WAAU;0DAAgG;;;;;;;;;;;;kDAKlH,6LAAC;wCAAE,WAAU;;4CAA0E;0DACjD,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;4CAAiD;0DACrI,6LAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;0CAK7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK3C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iKAAA,CAAA,yBAAsB;oCACrB,QAAQ;oCACR,SAAS,KAAO;;;;;;;;;;;0CAKpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAItD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;KArHwB", "debugId": null}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "file": "useWalletMultiButton.js", "sourceRoot": "", "sources": ["../../src/useWalletMultiButton.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAe,MAAM,8BAA8B,CAAC;AAEtE,OAAO,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;;;AAmB9B,SAAU,oBAAoB,CAAC,EAAE,cAAc,EAAU;IAC3D,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,4LACnG,YAAA,AAAS,EAAE,CAAC;IAChB,IAAI,WAAuC,CAAC;IAC5C,IAAI,UAAU,EAAE,CAAC;QACb,WAAW,GAAG,YAAY,CAAC;IAC/B,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QACnB,WAAW,GAAG,WAAW,CAAC;IAC9B,CAAC,MAAM,IAAI,aAAa,EAAE,CAAC;QACvB,WAAW,GAAG,eAAe,CAAC;IAClC,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC;QAChB,WAAW,GAAG,YAAY,CAAC;IAC/B,CAAC,MAAM,CAAC;QACJ,WAAW,GAAG,WAAW,CAAC;IAC9B,CAAC;IACD,MAAM,aAAa,GAAG,gLAAA,AAAW;2DAAC,GAAG,EAAE;YACnC,OAAO,EAAE,CAAC,KAAK;mEAAC,GAAG,EAAE;gBACjB,gFAAgF;gBACpF,CAAC,CAAC,CAAC;;QACP,CAAC;0DAAE;QAAC,OAAO;KAAC,CAAC,CAAC;IACd,MAAM,gBAAgB,GAAG,gLAAA,AAAW;8DAAC,GAAG,EAAE;YACtC,UAAU,EAAE,CAAC,KAAK;sEAAC,GAAG,EAAE;gBACpB,gFAAgF;gBACpF,CAAC,CAAC,CAAC;;QACP,CAAC;6DAAE;QAAC,UAAU;KAAC,CAAC,CAAC;IACjB,MAAM,kBAAkB,IAAG,+KAAA,AAAW;gEAAC,GAAG,EAAE;YACxC,cAAc,CAAC;gBAAE,cAAc,EAAE,MAAM;gBAAE,OAAO;YAAA,CAAE,CAAC,CAAC;QACxD,CAAC;+DAAE;QAAC,cAAc;QAAE,MAAM;QAAE,OAAO;KAAC,CAAC,CAAC;IACtC,OAAO;QACH,WAAW;QACX,SAAS,EAAE,WAAW,KAAK,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;QACnE,YAAY,EAAE,WAAW,KAAK,eAAe,IAAI,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;QAC3G,cAAc,EAAE,kBAAkB;QAClC,SAAS,EAAE,SAAS,IAAI,SAAS;QACjC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI;QAChC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI;KACnC,CAAC;AACN,CAAC", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "file": "BaseWalletConnectionButton.js", "sourceRoot": "", "sources": ["../../src/BaseWalletConnectionButton.tsx"], "names": [], "mappings": ";;;AACA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;;;;AAOvC,SAAU,0BAA0B,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,KAAK,EAAS;IAClF,OAAO,8JACH,UAAA,CAAA,aAAA,yLAAC,SAAM,EAAA;QAAA,GACC,KAAK;QACT,SAAS,EAAC,+BAA+B;QACzC,SAAS,EACL,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,8JACvB,UAAA,CAAA,aAAA,6LAAC,aAAU,EAAA;YAAC,MAAM,EAAE;gBAAE,OAAO,EAAE;oBAAE,IAAI,EAAE,UAAU;oBAAE,IAAI,EAAE,UAAU;gBAAA,CAAE;YAAA,CAAE;QAAA,EAAI,CAC9E,CAAC,CAAC,AAAC,SAAS;IAAA,EAEnB,CACL,CAAC;AACN,CAAC", "debugId": null}}, {"offset": {"line": 1681, "column": 0}, "map": {"version": 3, "file": "BaseWalletMultiButton.js", "sourceRoot": "", "sources": ["../../src/BaseWalletMultiButton.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,gCAAgC,CAAC;AACtE,OAAO,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACpE,OAAO,EAAE,0BAA0B,EAAE,MAAM,iCAAiC,CAAC;AAE7E,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;;;;;AAc/C,SAAU,qBAAqB,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,KAAK,EAAS;IACvE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,uMAAG,iBAAA,AAAc,EAAE,CAAC;IACzD,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,gOAAA,AAAoB,EAAC;QACrG,cAAc;YACV,eAAe,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;KACJ,CAAC,CAAC;IACH,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,qKAAG,WAAA,AAAQ,EAAC,KAAK,CAAC,CAAC;IAC5C,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,qKAAG,WAAQ,AAAR,EAAS,KAAK,CAAC,CAAC;IAChD,MAAM,GAAG,qKAAG,SAAA,AAAM,EAAmB,IAAI,CAAC,CAAC;QAC3C,0KAAA,AAAS;2CAAC,GAAG,EAAE;YACX,MAAM,QAAQ;4DAAG,CAAC,KAA8B,EAAE,EAAE;oBAChD,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC;oBAEzB,qDAAqD;oBACrD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,EAAE,OAAO;oBAEzD,WAAW,CAAC,KAAK,CAAC,CAAC;gBACvB,CAAC,CAAC;;YAEF,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YACjD,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAElD;mDAAO,GAAG,EAAE;oBACR,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;oBACpD,QAAQ,CAAC,mBAAmB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;gBACzD,CAAC,CAAC;;QACN,CAAC;0CAAE,EAAE,CAAC,CAAC;IACP,MAAM,OAAO,qKAAG,UAAA,AAAO;kDAAC,GAAG,EAAE;YACzB,IAAI,QAAQ,EAAE,CAAC;gBACX,OAAO,QAAQ,CAAC;YACpB,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;gBACnB,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACpC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,MAAM,IAAI,WAAW,KAAK,YAAY,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;gBACtE,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;YAC/B,CAAC,MAAM,CAAC;gBACJ,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;iDAAE;QAAC,WAAW;QAAE,QAAQ;QAAE,MAAM;QAAE,SAAS;KAAC,CAAC,CAAC;IAC/C,OAAO,8JACH,UAAA,CAAA,aAAA,CAAA,OAAA;QAAK,SAAS,EAAC,yBAAyB;IAAA,iKACpC,UAAA,CAAA,aAAA,6MAAC,6BAA0B,EAAA;QAAA,GACnB,KAAK;QAAA,iBACM,QAAQ;QACvB,KAAK,EAAE;YAAE,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YAAE,GAAG,KAAK,CAAC,KAAK;QAAA,CAAE;QACpE,OAAO,EAAE,GAAG,EAAE;YACV,OAAQ,WAAW,EAAE,CAAC;gBAClB,KAAK,WAAW;oBACZ,eAAe,CAAC,IAAI,CAAC,CAAC;oBACtB,MAAM;gBACV,KAAK,YAAY;oBACb,IAAI,SAAS,EAAE,CAAC;wBACZ,SAAS,EAAE,CAAC;oBAChB,CAAC;oBACD,MAAM;gBACV,KAAK,WAAW;oBACZ,WAAW,CAAC,IAAI,CAAC,CAAC;oBAClB,MAAM;YACd,CAAC;QACL,CAAC;QACD,UAAU,EAAE,UAAU;QACtB,UAAU,EAAE,UAAU;IAAA,GAErB,OAAO,CACiB,gKAC7B,UAAA,CAAA,aAAA,CAAA,MAAA;QAAA,cACe,eAAe;QAC1B,SAAS,EAAE,CAAA,6BAAA,EAAgC,QAAQ,IAAI,qCAAqC,EAAE;QAC9F,GAAG,EAAE,GAAG;QACR,IAAI,EAAC,MAAM;IAAA,GAEV,SAAS,CAAC,CAAC,CAAC,8JACT,UAAA,CAAA,aAAA,CAAA,MAAA;QACI,SAAS,EAAC,mCAAmC;QAC7C,OAAO,EAAE,KAAK,IAAI,EAAE;YAChB,MAAM,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1D,SAAS,CAAC,IAAI,CAAC,CAAC;YAChB,UAAU,CAAC,GAAG,CAAG,CAAD,QAAU,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,EAAC,UAAU;IAAA,GAEd,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAClD,CACR,CAAC,CAAC,AAAC,IAAI,gKACR,UAAA,CAAA,aAAA,CAAA,MAAA;QACI,SAAS,EAAC,mCAAmC;QAC7C,OAAO,EAAE,GAAG,EAAE;YACV,eAAe,CAAC,IAAI,CAAC,CAAC;YACtB,WAAW,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;QACD,IAAI,EAAC,UAAU;IAAA,GAEd,MAAM,CAAC,eAAe,CAAC,CACvB,EACJ,YAAY,CAAC,CAAC,CAAC,8JACZ,UAAA,CAAA,aAAA,CAAA,MAAA;QACI,SAAS,EAAC,mCAAmC;QAC7C,OAAO,EAAE,GAAG,EAAE;YACV,YAAY,EAAE,CAAC;YACf,WAAW,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;QACD,IAAI,EAAC,UAAU;IAAA,GAEd,MAAM,CAAC,YAAY,CAAC,CACpB,CACR,CAAC,CAAC,AAAC,IAAI,CACP,CACH,CACT,CAAC;AACN,CAAC", "debugId": null}}, {"offset": {"line": 1802, "column": 0}, "map": {"version": 3, "file": "WalletMultiButton.js", "sourceRoot": "", "sources": ["../../src/WalletMultiButton.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;;;AAGnE,MAAM,MAAM,GAAG;IACX,eAAe,EAAE,eAAe;IAChC,UAAU,EAAE,gBAAgB;IAC5B,cAAc,EAAE,cAAc;IAC9B,MAAM,EAAE,QAAQ;IAChB,UAAU,EAAE,YAAY;IACxB,YAAY,EAAE,SAAS;IACvB,WAAW,EAAE,eAAe;CACtB,CAAC;AAEL,SAAU,iBAAiB,CAAC,KAAkB;IAChD,qKAAO,UAAA,CAAA,aAAA,wMAAC,wBAAqB,EAAA;QAAA,GAAK,KAAK;QAAE,MAAM,EAAE,MAAM;IAAA,EAAI,CAAC;AAChE,CAAC", "debugId": null}}]}