/**
 * Cross-Chain Type Definitions
 * Shared types for multi-chain operations
 */

import { SupportedChain } from '@/config/chains';
import { BridgeProtocol } from '@/config/bridges';

export interface CrossChainToken {
  id: string;
  name: string;
  symbol: string;
  totalSupply: string;
  decimals: number;
  deployments: TokenDeployment[];
  metadata: TokenMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface TokenDeployment {
  chain: SupportedChain;
  contractAddress: string;
  transactionHash: string;
  blockNumber: number;
  status: DeploymentStatus;
  gasUsed: number;
  deployedAt: Date;
}

export enum DeploymentStatus {
  PENDING = 'pending',
  DEPLOYING = 'deploying',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface TokenMetadata {
  description?: string;
  image?: string;
  website?: string;
  twitter?: string;
  telegram?: string;
  discord?: string;
  attributes?: Record<string, any>;
}

export interface CrossChainLaunch {
  id: string;
  creatorAddress: string;
  token: CrossChainToken;
  targetChains: SupportedChain[];
  liquidityDistribution: LiquidityAllocation[];
  totalLiquidity: number;
  status: LaunchStatus;
  deployments: TokenDeployment[];
  bridgeTransactions: BridgeTransaction[];
  costs: LaunchCosts;
  createdAt: Date;
  completedAt?: Date;
}

export enum LaunchStatus {
  DRAFT = 'draft',
  VALIDATING = 'validating',
  DEPLOYING = 'deploying',
  BRIDGING = 'bridging',
  ADDING_LIQUIDITY = 'adding_liquidity',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface LiquidityAllocation {
  chain: SupportedChain;
  amount: number;
  percentage: number;
  nativeTokenAmount: number;
  status: 'pending' | 'adding' | 'added' | 'failed';
  poolAddress?: string;
  transactionHash?: string;
}

export interface BridgeTransaction {
  id: string;
  protocol: BridgeProtocol;
  fromChain: SupportedChain;
  toChain: SupportedChain;
  amount: number;
  tokenAddress: string;
  status: BridgeStatus;
  sourceTransactionHash?: string;
  destinationTransactionHash?: string;
  bridgeTransactionId?: string;
  estimatedTime: number;
  actualTime?: number;
  fees: number;
  createdAt: Date;
  completedAt?: Date;
}

export enum BridgeStatus {
  INITIATED = 'initiated',
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

export interface LaunchCosts {
  deploymentCosts: Record<SupportedChain, number>;
  bridgingCosts: number;
  liquidityCosts: number;
  platformFees: number;
  totalCost: number;
}

export interface ChainWallet {
  chain: SupportedChain;
  address: string;
  connected: boolean;
  balance: number;
  nativeBalance: number;
}

export interface MultiChainWalletState {
  wallets: ChainWallet[];
  activeChain: SupportedChain | null;
  isConnecting: boolean;
  error?: string;
}

export interface CrossChainSwap {
  id: string;
  fromChain: SupportedChain;
  toChain: SupportedChain;
  fromToken: string;
  toToken: string;
  fromAmount: number;
  toAmount: number;
  slippage: number;
  route: SwapRoute[];
  status: SwapStatus;
  transactionHash?: string;
  estimatedTime: number;
  actualTime?: number;
  createdAt: Date;
  completedAt?: Date;
}

export enum SwapStatus {
  PENDING = 'pending',
  EXECUTING = 'executing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  EXPIRED = 'expired',
}

export interface SwapRoute {
  protocol: string;
  fromChain: SupportedChain;
  toChain: SupportedChain;
  estimatedGas: number;
  estimatedTime: number;
  priceImpact: number;
}

export interface CrossChainAnalytics {
  totalLaunches: number;
  totalValueLocked: number;
  chainDistribution: Record<SupportedChain, {
    launches: number;
    tvl: number;
    volume24h: number;
  }>;
  bridgeVolume: Record<BridgeProtocol, {
    volume24h: number;
    transactions24h: number;
    avgTime: number;
  }>;
  topTokens: Array<{
    symbol: string;
    chains: SupportedChain[];
    marketCap: number;
    volume24h: number;
  }>;
}

// Event types for real-time updates
export interface CrossChainEvent {
  type: CrossChainEventType;
  launchId: string;
  chain?: SupportedChain;
  data: any;
  timestamp: Date;
}

export enum CrossChainEventType {
  DEPLOYMENT_STARTED = 'deployment_started',
  DEPLOYMENT_COMPLETED = 'deployment_completed',
  DEPLOYMENT_FAILED = 'deployment_failed',
  BRIDGE_INITIATED = 'bridge_initiated',
  BRIDGE_COMPLETED = 'bridge_completed',
  LIQUIDITY_ADDED = 'liquidity_added',
  LAUNCH_COMPLETED = 'launch_completed',
  LAUNCH_FAILED = 'launch_failed',
}
