/**
 * Cross-Chain Token Creator
 * Main interface for creating tokens across multiple blockchains
 */

'use client';

import React, { useState, useCallback } from 'react';
import '@/styles/slider.css';
import { SupportedChain, getChainConfig } from '@/config/chains';
// import { useMultiChainWallet } from '@/components/cross-chain/MultiChainWalletProvider';
import { TokenDeploymentParams } from '@/config/deployment';
import { ChainSelector } from '@/components/cross-chain/ChainSelector';

interface CrossChainTokenCreatorProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CrossChainTokenCreator({ isOpen, onClose }: CrossChainTokenCreatorProps) {
  // const { walletState, isChainConnected } = useMultiChainWallet();
  
  const [currentStep, setCurrentStep] = useState<'basics' | 'chains' | 'liquidity' | 'review' | 'deploying'>('basics');
  const [tokenParams, setTokenParams] = useState<Partial<TokenDeploymentParams>>({
    name: '',
    symbol: '',
    totalSupply: '',
    decimals: 9,
    description: '',
    targetChains: [SupportedChain.SOLANA],
    initialLiquidity: 0.2,
  });

  const handleParamChange = useCallback((key: keyof TokenDeploymentParams, value: any) => {
    setTokenParams(prev => ({ ...prev, [key]: value }));
  }, []);

  const nextStep = useCallback(() => {
    const steps = ['basics', 'chains', 'liquidity', 'review', 'deploying'] as const;
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  }, [currentStep]);

  const prevStep = useCallback(() => {
    const steps = ['basics', 'chains', 'liquidity', 'review', 'deploying'] as const;
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  }, [currentStep]);

  const getStepTitle = () => {
    switch (currentStep) {
      case 'basics': return 'Token Basics';
      case 'chains': return 'Select Chains';
      case 'liquidity': return 'Liquidity Distribution';
      case 'review': return 'Review & Launch';
      case 'deploying': return 'Deploying Everywhere';
      default: return 'Create Token';
    }
  };

  const getStepSubtitle = () => {
    switch (currentStep) {
      case 'basics': return 'Define your token properties';
      case 'chains': return 'Choose target blockchains';
      case 'liquidity': return 'Configure cross-chain liquidity';
      case 'review': return 'Confirm deployment details';
      case 'deploying': return 'Creating tokens across chains';
      default: return '';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="w-full max-w-5xl mx-auto">
      <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl w-full shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-6 rounded-t-2xl border-b border-gray-700/50">
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center shadow-lg">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white">{getStepTitle()}</h2>
                <p className="text-white/80 text-sm">{getStepSubtitle()}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4 bg-gray-800/50">
          <div className="flex items-center justify-between">
            {['basics', 'chains', 'liquidity', 'review'].map((step, index) => {
              const isActive = currentStep === step;
              const isCompleted = ['basics', 'chains', 'liquidity', 'review'].indexOf(currentStep) > index;
              
              return (
                <div key={step} className="flex items-center">
                  <div className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold
                    ${isActive ? 'bg-purple-500 text-white' : 
                      isCompleted ? 'bg-green-500 text-white' : 'bg-gray-600 text-gray-400'}
                  `}>
                    {isCompleted ? '✓' : index + 1}
                  </div>
                  {index < 3 && (
                    <div className={`w-16 h-1 mx-2 ${isCompleted ? 'bg-green-500' : 'bg-gray-600'}`}></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Step 1: Token Basics */}
          {currentStep === 'basics' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Token Name *
                  </label>
                  <input
                    type="text"
                    value={tokenParams.name || ''}
                    onChange={(e) => handleParamChange('name', e.target.value)}
                    placeholder="My Awesome Token"
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Token Symbol *
                  </label>
                  <input
                    type="text"
                    value={tokenParams.symbol || ''}
                    onChange={(e) => handleParamChange('symbol', e.target.value.toUpperCase())}
                    placeholder="MAT"
                    maxLength={10}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Total Supply *
                  </label>
                  <input
                    type="number"
                    value={tokenParams.totalSupply || ''}
                    onChange={(e) => handleParamChange('totalSupply', e.target.value)}
                    placeholder="1000000"
                    min="1"
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Decimals
                  </label>
                  <select
                    value={tokenParams.decimals || 9}
                    onChange={(e) => handleParamChange('decimals', parseInt(e.target.value))}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white transition-all duration-300"
                    aria-label="Token decimals"
                  >
                    {[6, 8, 9, 18].map(decimal => (
                      <option key={decimal} value={decimal}>{decimal} decimals</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description (Optional)
                </label>
                <textarea
                  value={tokenParams.description || ''}
                  onChange={(e) => handleParamChange('description', e.target.value)}
                  placeholder="Describe your token and its purpose..."
                  rows={3}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300 resize-none"
                />
              </div>

              {/* Navigation */}
              <div className="flex justify-end pt-6">
                <button
                  type="button"
                  onClick={nextStep}
                  disabled={!tokenParams.name || !tokenParams.symbol || !tokenParams.totalSupply}
                  className="bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30"
                >
                  Continue to Chain Selection
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Step 2: Chain Selection */}
          {currentStep === 'chains' && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h3 className="text-xl font-bold text-white mb-2">Select Target Blockchains</h3>
                <p className="text-gray-400">Choose which chains to deploy your token on simultaneously</p>
              </div>

              <ChainSelector
                selectedChains={tokenParams.targetChains || []}
                onSelectionChange={(chains) => handleParamChange('targetChains', chains)}
                minSelections={1}
                showCosts={true}
                showFeatures={true}
                layout="grid"
                size="medium"
              />

              {/* Selection summary */}
              <div className="bg-gray-800/50 border border-gray-700/50 rounded-xl p-6">
                <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
                  <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
                  </svg>
                  Deployment Summary
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{tokenParams.targetChains?.length || 0}</div>
                    <div className="text-gray-400 text-sm">Chains Selected</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">~2-5min</div>
                    <div className="text-gray-400 text-sm">Deploy Time</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">No Bridging</div>
                    <div className="text-gray-400 text-sm">Required</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">Instant</div>
                    <div className="text-gray-400 text-sm">Cross-Chain</div>
                  </div>
                </div>
              </div>

              {/* Navigation */}
              <div className="flex justify-between pt-6">
                <button
                  type="button"
                  onClick={prevStep}
                  className="bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 flex items-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Basics
                </button>
                <button
                  type="button"
                  onClick={nextStep}
                  disabled={!tokenParams.targetChains?.length}
                  className="bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30"
                >
                  Continue to Liquidity
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Liquidity Distribution */}
          {currentStep === 'liquidity' && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h3 className="text-xl font-bold text-white mb-2">Configure Liquidity Distribution</h3>
                <p className="text-gray-400">Set how your initial liquidity will be distributed across selected chains</p>
              </div>

              {/* Total Liquidity Input */}
              <div className="bg-gray-800/50 border border-gray-700/50 rounded-xl p-6">
                <h4 className="text-white font-semibold mb-4 flex items-center gap-2">
                  <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                  Total Initial Liquidity
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Liquidity Amount (SOL equivalent)
                    </label>
                    <input
                      type="number"
                      value={tokenParams.initialLiquidity || ''}
                      onChange={(e) => handleParamChange('initialLiquidity', parseFloat(e.target.value) || 0)}
                      placeholder="1.0"
                      min="0.2"
                      step="0.1"
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                    />
                    <p className="text-gray-500 text-xs mt-1">Minimum 0.2 SOL recommended for Raydium</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Estimated USD Value
                    </label>
                    <div className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-gray-300">
                      ${((tokenParams.initialLiquidity || 0) * 180).toFixed(2)} USD
                    </div>
                    <p className="text-gray-500 text-xs mt-1">Based on current SOL price (~$180)</p>
                  </div>
                </div>
              </div>

              {/* Chain Distribution */}
              <div className="bg-gray-800/50 border border-gray-700/50 rounded-xl p-6">
                <h4 className="text-white font-semibold mb-4 flex items-center gap-2">
                  <svg className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
                  </svg>
                  Distribution Across Chains
                </h4>

                {tokenParams.targetChains && tokenParams.targetChains.length > 0 ? (
                  <div className="space-y-6">
                    {tokenParams.targetChains.map((chain) => {
                      const chainConfig = getChainConfig(chain);
                      const currentDistribution = tokenParams.liquidityDistribution || [];
                      const chainDistribution = currentDistribution.find(d => d.chain === chain);
                      const percentage = chainDistribution?.percentage || (100 / tokenParams.targetChains!.length);
                      const amount = ((tokenParams.initialLiquidity || 0) * percentage) / 100;

                      const chainIcons = {
                        [SupportedChain.SOLANA]: '◎',
                        [SupportedChain.BNB_CHAIN]: '🟡',
                        [SupportedChain.AVALANCHE]: '🔺',
                        [SupportedChain.POLKADOT]: '⚫',
                      };

                      const chainColors = {
                        [SupportedChain.SOLANA]: 'from-purple-500 to-blue-500',
                        [SupportedChain.BNB_CHAIN]: 'from-yellow-500 to-orange-500',
                        [SupportedChain.AVALANCHE]: 'from-red-500 to-pink-500',
                        [SupportedChain.POLKADOT]: 'from-pink-500 to-purple-500',
                      };

                      const updateDistribution = (newPercentage: number) => {
                        const newDistribution = [...(tokenParams.liquidityDistribution || [])];
                        const existingIndex = newDistribution.findIndex(d => d.chain === chain);

                        const distributionItem = {
                          chain,
                          percentage: newPercentage,
                          amount: ((tokenParams.initialLiquidity || 0) * newPercentage) / 100,
                          nativeTokenAmount: 0, // Will be calculated based on chain
                          status: 'pending' as const,
                          minimumAmount: chain === SupportedChain.SOLANA ? 0.2 :
                                        chain === SupportedChain.BNB_CHAIN ? 0.05 :
                                        chain === SupportedChain.AVALANCHE ? 0.5 : 2, // Chain-specific minimums
                          priority: 1, // Default priority
                        };

                        if (existingIndex >= 0) {
                          newDistribution[existingIndex] = distributionItem;
                        } else {
                          newDistribution.push(distributionItem);
                        }

                        handleParamChange('liquidityDistribution', newDistribution);
                      };

                      return (
                        <div key={chain} className="bg-gray-700/30 rounded-xl p-4">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${chainColors[chain]} flex items-center justify-center text-white shadow-lg`}>
                                <span className="text-lg">{chainIcons[chain]}</span>
                              </div>
                              <div>
                                <h5 className="text-white font-semibold">{chainConfig.displayName}</h5>
                                <p className="text-gray-400 text-sm">{chainConfig.features.tokenStandard} Token</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-white font-bold text-lg">{percentage.toFixed(1)}%</div>
                              <div className="text-gray-400 text-sm">{amount.toFixed(3)} SOL equiv</div>
                            </div>
                          </div>

                          {/* Percentage Slider */}
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <label className="text-gray-300 text-sm">Allocation Percentage</label>
                              <span className="text-cyan-400 text-sm font-medium">{percentage.toFixed(1)}%</span>
                            </div>
                            <input
                              type="range"
                              min="5"
                              max="80"
                              step="1"
                              value={percentage}
                              onChange={(e) => updateDistribution(parseFloat(e.target.value))}
                              className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                              aria-label={`Liquidity allocation percentage for ${chainConfig.displayName}`}
                              title={`Adjust liquidity allocation for ${chainConfig.displayName}`}
                            />
                            <div className="flex justify-between text-xs text-gray-500">
                              <span>5%</span>
                              <span>80%</span>
                            </div>
                          </div>

                          {/* Amount Details */}
                          <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                            <div className="bg-gray-800/50 rounded-lg p-3">
                              <div className="text-gray-400">Liquidity Amount</div>
                              <div className="text-white font-semibold">{amount.toFixed(3)} SOL equiv</div>
                            </div>
                            <div className="bg-gray-800/50 rounded-lg p-3">
                              <div className="text-gray-400">USD Value</div>
                              <div className="text-white font-semibold">${(amount * 180).toFixed(2)}</div>
                            </div>
                          </div>
                        </div>
                      );
                    })}

                    {/* Distribution Summary */}
                    <div className="bg-gradient-to-r from-purple-900/30 to-cyan-900/30 border border-purple-500/30 rounded-xl p-4">
                      <h5 className="text-white font-semibold mb-3 flex items-center gap-2">
                        <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        Distribution Summary
                      </h5>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-white">
                            {(tokenParams.liquidityDistribution?.reduce((sum, d) => sum + d.percentage, 0) || 100).toFixed(1)}%
                          </div>
                          <div className="text-gray-400 text-sm">Total Allocated</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-white">{tokenParams.targetChains?.length || 0}</div>
                          <div className="text-gray-400 text-sm">Chains</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-white">{(tokenParams.initialLiquidity || 0).toFixed(2)}</div>
                          <div className="text-gray-400 text-sm">Total SOL</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-white">${((tokenParams.initialLiquidity || 0) * 180).toFixed(0)}</div>
                          <div className="text-gray-400 text-sm">Total USD</div>
                        </div>
                      </div>
                    </div>

                    {/* Quick Distribution Presets */}
                    <div className="space-y-3">
                      <h5 className="text-white font-medium">Quick Presets</h5>
                      <div className="flex flex-wrap gap-2">
                        <button
                          type="button"
                          onClick={() => {
                            const equalPercentage = 100 / tokenParams.targetChains!.length;
                            const newDistribution = tokenParams.targetChains!.map(chain => ({
                              chain,
                              percentage: equalPercentage,
                              amount: ((tokenParams.initialLiquidity || 0) * equalPercentage) / 100,
                              nativeTokenAmount: 0,
                              status: 'pending' as const,
                              minimumAmount: chain === SupportedChain.SOLANA ? 0.2 :
                                            chain === SupportedChain.BNB_CHAIN ? 0.05 :
                                            chain === SupportedChain.AVALANCHE ? 0.5 : 2,
                              priority: 1,
                            }));
                            handleParamChange('liquidityDistribution', newDistribution);
                          }}
                          className="px-3 py-1 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white text-sm rounded-lg transition-all duration-300"
                        >
                          Equal Split
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            // Solana-heavy distribution
                            const otherChains = tokenParams.targetChains!.filter(c => c !== SupportedChain.SOLANA);
                            const otherPercentage = otherChains.length > 0 ? 60 / otherChains.length : 0;

                            const newDistribution = tokenParams.targetChains!.map(chain => ({
                              chain,
                              percentage: chain === SupportedChain.SOLANA ? 40 : otherPercentage,
                              amount: ((tokenParams.initialLiquidity || 0) * (chain === SupportedChain.SOLANA ? 40 : otherPercentage)) / 100,
                              nativeTokenAmount: 0,
                              status: 'pending' as const,
                              minimumAmount: 0.01,
                              priority: chain === SupportedChain.SOLANA ? 1 : 2,
                            }));
                            handleParamChange('liquidityDistribution', newDistribution);
                          }}
                          className="px-3 py-1 bg-purple-800 hover:bg-purple-700 border border-purple-600 hover:border-purple-500 text-purple-200 hover:text-white text-sm rounded-lg transition-all duration-300"
                        >
                          Solana Focus
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            // EVM-heavy distribution (BNB + Avalanche get more)
                            const evmChains = tokenParams.targetChains!.filter(c => c === SupportedChain.BNB_CHAIN || c === SupportedChain.AVALANCHE);
                            const nonEvmChains = tokenParams.targetChains!.filter(c => c !== SupportedChain.BNB_CHAIN && c !== SupportedChain.AVALANCHE);
                            const evmPercentage = evmChains.length > 0 ? 60 / evmChains.length : 0;
                            const nonEvmPercentage = nonEvmChains.length > 0 ? 40 / nonEvmChains.length : 0;

                            const newDistribution = tokenParams.targetChains!.map(chain => ({
                              chain,
                              percentage: (chain === SupportedChain.BNB_CHAIN || chain === SupportedChain.AVALANCHE) ? evmPercentage : nonEvmPercentage,
                              amount: ((tokenParams.initialLiquidity || 0) * ((chain === SupportedChain.BNB_CHAIN || chain === SupportedChain.AVALANCHE) ? evmPercentage : nonEvmPercentage)) / 100,
                              nativeTokenAmount: 0,
                              status: 'pending' as const,
                            }));
                            handleParamChange('liquidityDistribution', newDistribution);
                          }}
                          className="px-3 py-1 bg-yellow-800 hover:bg-yellow-700 border border-yellow-600 hover:border-yellow-500 text-yellow-200 hover:text-white text-sm rounded-lg transition-all duration-300"
                        >
                          EVM Focus
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-400 mb-4">No chains selected</div>
                    <button
                      type="button"
                      onClick={prevStep}
                      className="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300"
                    >
                      Go Back to Select Chains
                    </button>
                  </div>
                )}
              </div>

              {/* Navigation */}
              <div className="flex justify-between pt-6">
                <button
                  type="button"
                  onClick={prevStep}
                  className="bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 flex items-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Chains
                </button>
                <button
                  type="button"
                  onClick={nextStep}
                  disabled={!tokenParams.initialLiquidity || tokenParams.initialLiquidity < 0.2}
                  className="bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30"
                >
                  Continue to Review
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Other steps placeholder */}
          {(currentStep === 'review' || currentStep === 'deploying') && (
            <div className="text-center py-12">
              <div className="text-gray-400">
                {currentStep === 'review' && 'Review interface coming next...'}
                {currentStep === 'deploying' && 'Deployment interface coming next...'}
              </div>
              <button
                type="button"
                onClick={prevStep}
                className="mt-4 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-2 px-6 rounded-xl transition-all duration-300"
              >
                Back
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
