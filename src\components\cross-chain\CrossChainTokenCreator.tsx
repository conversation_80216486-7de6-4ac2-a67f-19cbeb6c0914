/**
 * Cross-Chain Token Creator
 * Main interface for creating tokens across multiple blockchains
 */

'use client';

import React, { useState, useCallback } from 'react';
import { SupportedChain, getChainConfig } from '@/config/chains';
import { useMultiChainWallet } from '@/components/cross-chain/MultiChainWalletProvider';
import { TokenDeploymentParams, calculateOptimalDistribution } from '@/config/deployment';

interface CrossChainTokenCreatorProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CrossChainTokenCreator({ isOpen, onClose }: CrossChainTokenCreatorProps) {
  const { walletState, isChainConnected } = useMultiChainWallet();
  
  const [currentStep, setCurrentStep] = useState<'basics' | 'chains' | 'liquidity' | 'review' | 'deploying'>('basics');
  const [tokenParams, setTokenParams] = useState<Partial<TokenDeploymentParams>>({
    name: '',
    symbol: '',
    totalSupply: '',
    decimals: 9,
    description: '',
    targetChains: [SupportedChain.SOLANA],
    initialLiquidity: 0.2,
  });

  const handleParamChange = useCallback((key: keyof TokenDeploymentParams, value: any) => {
    setTokenParams(prev => ({ ...prev, [key]: value }));
  }, []);

  const nextStep = useCallback(() => {
    const steps = ['basics', 'chains', 'liquidity', 'review', 'deploying'] as const;
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  }, [currentStep]);

  const prevStep = useCallback(() => {
    const steps = ['basics', 'chains', 'liquidity', 'review', 'deploying'] as const;
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  }, [currentStep]);

  const getStepTitle = () => {
    switch (currentStep) {
      case 'basics': return 'Token Basics';
      case 'chains': return 'Select Chains';
      case 'liquidity': return 'Liquidity Distribution';
      case 'review': return 'Review & Launch';
      case 'deploying': return 'Deploying Everywhere';
      default: return 'Create Token';
    }
  };

  const getStepSubtitle = () => {
    switch (currentStep) {
      case 'basics': return 'Define your token properties';
      case 'chains': return 'Choose target blockchains';
      case 'liquidity': return 'Configure cross-chain liquidity';
      case 'review': return 'Confirm deployment details';
      case 'deploying': return 'Creating tokens across chains';
      default: return '';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="w-full max-w-5xl mx-auto">
      <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl w-full shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-6 rounded-t-2xl border-b border-gray-700/50">
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center shadow-lg">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white">{getStepTitle()}</h2>
                <p className="text-white/80 text-sm">{getStepSubtitle()}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4 bg-gray-800/50">
          <div className="flex items-center justify-between">
            {['basics', 'chains', 'liquidity', 'review'].map((step, index) => {
              const isActive = currentStep === step;
              const isCompleted = ['basics', 'chains', 'liquidity', 'review'].indexOf(currentStep) > index;
              
              return (
                <div key={step} className="flex items-center">
                  <div className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold
                    ${isActive ? 'bg-purple-500 text-white' : 
                      isCompleted ? 'bg-green-500 text-white' : 'bg-gray-600 text-gray-400'}
                  `}>
                    {isCompleted ? '✓' : index + 1}
                  </div>
                  {index < 3 && (
                    <div className={`w-16 h-1 mx-2 ${isCompleted ? 'bg-green-500' : 'bg-gray-600'}`}></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Step 1: Token Basics */}
          {currentStep === 'basics' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Token Name *
                  </label>
                  <input
                    type="text"
                    value={tokenParams.name || ''}
                    onChange={(e) => handleParamChange('name', e.target.value)}
                    placeholder="My Awesome Token"
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Token Symbol *
                  </label>
                  <input
                    type="text"
                    value={tokenParams.symbol || ''}
                    onChange={(e) => handleParamChange('symbol', e.target.value.toUpperCase())}
                    placeholder="MAT"
                    maxLength={10}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Total Supply *
                  </label>
                  <input
                    type="number"
                    value={tokenParams.totalSupply || ''}
                    onChange={(e) => handleParamChange('totalSupply', e.target.value)}
                    placeholder="1000000"
                    min="1"
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Decimals
                  </label>
                  <select
                    value={tokenParams.decimals || 9}
                    onChange={(e) => handleParamChange('decimals', parseInt(e.target.value))}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white transition-all duration-300"
                  >
                    {[6, 8, 9, 18].map(decimal => (
                      <option key={decimal} value={decimal}>{decimal} decimals</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description (Optional)
                </label>
                <textarea
                  value={tokenParams.description || ''}
                  onChange={(e) => handleParamChange('description', e.target.value)}
                  placeholder="Describe your token and its purpose..."
                  rows={3}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300 resize-none"
                />
              </div>

              {/* Navigation */}
              <div className="flex justify-end pt-6">
                <button
                  type="button"
                  onClick={nextStep}
                  disabled={!tokenParams.name || !tokenParams.symbol || !tokenParams.totalSupply}
                  className="bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30"
                >
                  Continue to Chain Selection
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Additional steps will be implemented in the next part */}
          {currentStep !== 'basics' && (
            <div className="text-center py-12">
              <div className="text-gray-400">
                {currentStep === 'chains' && 'Chain selection interface coming next...'}
                {currentStep === 'liquidity' && 'Liquidity distribution interface coming next...'}
                {currentStep === 'review' && 'Review interface coming next...'}
                {currentStep === 'deploying' && 'Deployment interface coming next...'}
              </div>
              <button
                onClick={prevStep}
                className="mt-4 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-2 px-6 rounded-xl transition-all duration-300"
              >
                Back
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
