{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/lib/constants.ts"], "sourcesContent": ["import { PublicKey } from '@solana/web3.js';\n\n// Solana Configuration\nexport const SOLANA_NETWORK = process.env.NEXT_PUBLIC_SOLANA_NETWORK || 'devnet';\nexport const SOLANA_RPC_URL = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || 'https://api.devnet.solana.com';\n\n// Token Configuration\nexport const DEFAULT_DECIMALS = 9;\nexport const MIN_LIQUIDITY_PERCENTAGE = 0.1; // 10% minimum liquidity\n\n// Raydium Configuration\nexport const RAYDIUM_API_URL = process.env.NEXT_PUBLIC_RAYDIUM_API_URL || 'https://api.raydium.io/v2/sdk/liquidity';\n\n// Known Token Addresses (Devnet)\nexport const DEVNET_TOKENS = {\n  SOL: 'So11111111111111111111111111111111111111112',\n  USDC: '4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU', // Devnet USDC\n};\n\n// Known Token Addresses (Mainnet)\nexport const MAINNET_TOKENS = {\n  SOL: 'So11111111111111111111111111111111111111112',\n  USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // Mainnet USDC\n};\n\n// Get token addresses based on network\nexport const getTokenAddresses = () => {\n  return SOLANA_NETWORK === 'mainnet-beta' ? MAINNET_TOKENS : DEVNET_TOKENS;\n};\n\n// Raydium Program IDs\nexport const RAYDIUM_PROGRAM_IDS = {\n  LIQUIDITY_POOL_PROGRAM_ID_V4: new PublicKey('675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8'),\n  AMM_PROGRAM_ID: new PublicKey('675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8'),\n  SERUM_PROGRAM_ID: new PublicKey('9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin'),\n};\n\n// Transaction Configuration\nexport const TRANSACTION_TIMEOUT = 30000; // 30 seconds\nexport const MAX_RETRIES = 3;\n\n// UI Configuration\nexport const SUPPORTED_WALLETS = ['Phantom', 'Solflare', 'Backpack'];\n\n// File Upload Configuration\nexport const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB\nexport const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\n\n// Validation Rules\nexport const VALIDATION_RULES = {\n  TOKEN_NAME: {\n    MIN_LENGTH: 1,\n    MAX_LENGTH: 32,\n  },\n  TOKEN_SYMBOL: {\n    MIN_LENGTH: 1,\n    MAX_LENGTH: 10,\n  },\n  DESCRIPTION: {\n    MAX_LENGTH: 500,\n  },\n  WEBSITE_URL: {\n    PATTERN: /^https?:\\/\\/.+/,\n  },\n  TWITTER_HANDLE: {\n    PATTERN: /^@?[A-Za-z0-9_]{1,15}$/,\n  },\n};\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  WALLET_NOT_CONNECTED: 'Please connect your wallet to continue',\n  INSUFFICIENT_BALANCE: 'Insufficient balance for this transaction',\n  INVALID_TOKEN_DATA: 'Please check your token information',\n  TRANSACTION_FAILED: 'Transaction failed. Please try again.',\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  INVALID_FILE_TYPE: 'Please upload a valid image file (JPEG, PNG, GIF, or WebP)',\n  FILE_TOO_LARGE: 'File size must be less than 5MB',\n};\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  TOKEN_CREATED: 'Token created successfully!',\n  METADATA_UPLOADED: 'Metadata uploaded successfully!',\n  POOL_CREATED: 'Liquidity pool created successfully!',\n  LIQUIDITY_ADDED: 'Initial liquidity added successfully!',\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAG8B;AAH9B;;AAGO,MAAM,iBAAiB,8CAA0C;AACjE,MAAM,iBAAiB,qEAA0C;AAGjE,MAAM,mBAAmB;AACzB,MAAM,2BAA2B,KAAK,wBAAwB;AAG9D,MAAM,kBAAkB,+EAA2C;AAGnE,MAAM,gBAAgB;IAC3B,KAAK;IACL,MAAM;AACR;AAGO,MAAM,iBAAiB;IAC5B,KAAK;IACL,MAAM;AACR;AAGO,MAAM,oBAAoB;IAC/B,OAAO,6EAAqD;AAC9D;AAGO,MAAM,sBAAsB;IACjC,8BAA8B,IAAI,2KAAA,CAAA,YAAS,CAAC;IAC5C,gBAAgB,IAAI,2KAAA,CAAA,YAAS,CAAC;IAC9B,kBAAkB,IAAI,2KAAA,CAAA,YAAS,CAAC;AAClC;AAGO,MAAM,sBAAsB,OAAO,aAAa;AAChD,MAAM,cAAc;AAGpB,MAAM,oBAAoB;IAAC;IAAW;IAAY;CAAW;AAG7D,MAAM,gBAAgB,IAAI,OAAO,MAAM,MAAM;AAC7C,MAAM,wBAAwB;IAAC;IAAc;IAAa;IAAa;CAAa;AAGpF,MAAM,mBAAmB;IAC9B,YAAY;QACV,YAAY;QACZ,YAAY;IACd;IACA,cAAc;QACZ,YAAY;QACZ,YAAY;IACd;IACA,aAAa;QACX,YAAY;IACd;IACA,aAAa;QACX,SAAS;IACX;IACA,gBAAgB;QACd,SAAS;IACX;AACF;AAGO,MAAM,iBAAiB;IAC5B,sBAAsB;IACtB,sBAAsB;IACtB,oBAAoB;IACpB,oBAAoB;IACpB,eAAe;IACf,mBAAmB;IACnB,gBAAgB;AAClB;AAGO,MAAM,mBAAmB;IAC9B,eAAe;IACf,mBAAmB;IACnB,cAAc;IACd,iBAAiB;AACnB", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/WalletProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useMemo } from 'react';\nimport { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';\nimport { WalletAdapterNetwork } from '@solana/wallet-adapter-base';\nimport { WalletModalProvider } from '@solana/wallet-adapter-react-ui';\nimport {\n  PhantomWalletAdapter,\n  SolflareWalletAdapter,\n} from '@solana/wallet-adapter-wallets';\nimport { clusterApiUrl } from '@solana/web3.js';\nimport { SOLANA_NETWORK, SOLANA_RPC_URL } from '@/lib/constants';\n\n\n\ninterface WalletContextProviderProps {\n  children: React.ReactNode;\n}\n\nexport function WalletContextProvider({ children }: WalletContextProviderProps) {\n  // Determine network\n  const network = SOLANA_NETWORK === 'mainnet-beta' \n    ? WalletAdapterNetwork.Mainnet \n    : WalletAdapterNetwork.Devnet;\n\n  // Use custom RPC URL or fallback to default\n  const endpoint = useMemo(() => {\n    if (SOLANA_RPC_URL) {\n      return SOLANA_RPC_URL;\n    }\n    return clusterApiUrl(network);\n  }, [network]);\n\n  // Configure supported wallets\n  const wallets = useMemo(\n    () => [\n      new PhantomWalletAdapter(),\n      new SolflareWalletAdapter({ network }),\n    ],\n    [network]\n  );\n\n  return (\n    <ConnectionProvider endpoint={endpoint}>\n      <WalletProvider wallets={wallets} autoConnect>\n        <WalletModalProvider>\n          {children}\n        </WalletModalProvider>\n      </WalletProvider>\n    </ConnectionProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAIA;AACA;;;AAXA;;;;;;;;AAmBO,SAAS,sBAAsB,EAAE,QAAQ,EAA8B;;IAC5E,oBAAoB;IACpB,MAAM,UAAU,0HAAA,CAAA,iBAAc,KAAK,iBAC/B,+KAAA,CAAA,uBAAoB,CAAC,OAAO,GAC5B,+KAAA,CAAA,uBAAoB,CAAC,MAAM;IAE/B,4CAA4C;IAC5C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YACvB,IAAI,0HAAA,CAAA,iBAAc,EAAE;gBAClB,OAAO,0HAAA,CAAA,iBAAc;YACvB;YACA,OAAO,CAAA,GAAA,2KAAA,CAAA,gBAAa,AAAD,EAAE;QACvB;kDAAG;QAAC;KAAQ;IAEZ,8BAA8B;IAC9B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDACpB,IAAM;gBACJ,IAAI,oLAAA,CAAA,uBAAoB;gBACxB,IAAI,qLAAA,CAAA,wBAAqB,CAAC;oBAAE;gBAAQ;aACrC;iDACD;QAAC;KAAQ;IAGX,qBACE,6LAAC,6LAAA,CAAA,qBAAkB;QAAC,UAAU;kBAC5B,cAAA,6LAAC,yLAAA,CAAA,iBAAc;YAAC,SAAS;YAAS,WAAW;sBAC3C,cAAA,6LAAC,oMAAA,CAAA,sBAAmB;0BACjB;;;;;;;;;;;;;;;;AAKX;GAhCgB;KAAA", "debugId": null}}]}