import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "@solana/wallet-adapter-react-ui/styles.css";
import { WalletContextProvider } from "@/components/WalletProvider";
import { Toaster } from "react-hot-toast";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Tweet-Pow - Tweet-Powered Raydium Coin Launcher",
  description: "Discover trending tweets and launch meme coins on Raydium with one click. Turn viral tweets into tradeable tokens instantly.",
  keywords: ["Tweet", "Meme Coin", "Raydium", "Solana", "Token Launch", "Social Trading", "Viral", "Twitter", "Crypto"],
  authors: [{ name: "Tweet-Pow Team" }],
};

export const viewport = "width=device-width, initial-scale=1";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased bg-black min-h-screen`}>
        <WalletContextProvider>
          <div className="min-h-screen flex flex-col">
            {children}
          </div>
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1F2937',
                color: '#fff',
                border: '1px solid #374151',
              },
              success: {
                duration: 3000,
                iconTheme: {
                  primary: '#10B981',
                  secondary: '#fff',
                },
              },
              error: {
                duration: 5000,
                iconTheme: {
                  primary: '#EF4444',
                  secondary: '#fff',
                },
              },
            }}
          />
        </WalletContextProvider>
      </body>
    </html>
  );
}
