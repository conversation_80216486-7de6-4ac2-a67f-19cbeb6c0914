import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "@solana/wallet-adapter-react-ui/styles.css";
import { WalletContextProvider } from "@/components/WalletProvider";
// import { MultiChainWalletProvider } from "@/components/cross-chain/MultiChainWalletProvider";
import { Toaster } from "react-hot-toast";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "TokenLaunch - Cross-Chain Token Launch Platform",
  description: "Launch tokens simultaneously across Solana, BNB Chain, Avalanche, and Polkadot. One interface, multiple blockchains, no bridging required.",
  keywords: ["Cross-Chain", "Multi-Chain", "Token", "Launch", "DeFi", "Solana", "BNB", "Avalanche", "Polkadot", "Wormhole", "Axelar"],
  authors: [{ name: "TokenLaunch Team" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased bg-black min-h-screen`}>
        <WalletContextProvider>
          {/* <MultiChainWalletProvider> */}
            <div className="min-h-screen flex flex-col">
              {children}
            </div>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#1F2937',
                  color: '#fff',
                  border: '1px solid #374151',
                },
                success: {
                  duration: 3000,
                  iconTheme: {
                    primary: '#10B981',
                    secondary: '#fff',
                  },
                },
                error: {
                  duration: 5000,
                  iconTheme: {
                    primary: '#EF4444',
                    secondary: '#fff',
                  },
                },
              }}
            />
          {/* </MultiChainWalletProvider> */}
        </WalletContextProvider>
      </body>
    </html>
  );
}
