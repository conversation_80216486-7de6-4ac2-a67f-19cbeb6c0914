/**
 * Multi-Chain Wallet Hook
 * Manages wallet connections across all supported chains
 */

import { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { useWallet as useSolanaWallet } from '@solana/wallet-adapter-react';
import { SupportedChain } from '@/config/chains';
import { MultiChainWalletState, ChainWallet } from '@/types/cross-chain';

interface MultiChainWalletContextType {
  walletState: MultiChainWalletState;
  connectWallet: (chain: SupportedChain) => Promise<void>;
  disconnectWallet: (chain: SupportedChain) => Promise<void>;
  switchChain: (chain: SupportedChain) => Promise<void>;
  getWalletForChain: (chain: SupportedChain) => ChainWallet | null;
  isChainConnected: (chain: SupportedChain) => boolean;
  getTotalBalance: () => number;
  refreshBalances: () => Promise<void>;
}

const MultiChainWalletContext = createContext<MultiChainWalletContextType | null>(null);

export function useMultiChainWallet(): MultiChainWalletContextType {
  const context = useContext(MultiChainWalletContext);
  if (!context) {
    throw new Error('useMultiChainWallet must be used within a MultiChainWalletProvider');
  }
  return context;
}

export function useMultiChainWalletState() {
  const solanaWallet = useSolanaWallet();
  const [walletState, setWalletState] = useState<MultiChainWalletState>({
    wallets: [],
    activeChain: null,
    isConnecting: false,
    error: undefined,
  });

  // Initialize wallet state for all chains
  useEffect(() => {
    const initializeWallets = () => {
      const initialWallets: ChainWallet[] = [
        {
          chain: SupportedChain.SOLANA,
          address: solanaWallet.publicKey?.toString() || '',
          connected: solanaWallet.connected,
          balance: 0,
          nativeBalance: 0,
        },
        {
          chain: SupportedChain.BNB_CHAIN,
          address: '',
          connected: false,
          balance: 0,
          nativeBalance: 0,
        },
        {
          chain: SupportedChain.AVALANCHE,
          address: '',
          connected: false,
          balance: 0,
          nativeBalance: 0,
        },
        {
          chain: SupportedChain.POLKADOT,
          address: '',
          connected: false,
          balance: 0,
          nativeBalance: 0,
        },
      ];

      setWalletState(prev => ({
        ...prev,
        wallets: initialWallets,
        activeChain: solanaWallet.connected ? SupportedChain.SOLANA : null,
      }));
    };

    initializeWallets();
  }, [solanaWallet.connected, solanaWallet.publicKey]);

  const connectWallet = useCallback(async (chain: SupportedChain) => {
    setWalletState(prev => ({ ...prev, isConnecting: true, error: undefined }));

    try {
      switch (chain) {
        case SupportedChain.SOLANA:
          if (!solanaWallet.connected) {
            await solanaWallet.connect();
          }
          break;

        case SupportedChain.BNB_CHAIN:
        case SupportedChain.AVALANCHE:
          // MetaMask/EVM wallet connection
          if (typeof window !== 'undefined' && window.ethereum) {
            const accounts = await window.ethereum.request({
              method: 'eth_requestAccounts',
            });
            
            if (accounts.length > 0) {
              setWalletState(prev => ({
                ...prev,
                wallets: prev.wallets.map(wallet =>
                  wallet.chain === chain
                    ? { ...wallet, address: accounts[0], connected: true }
                    : wallet
                ),
                activeChain: chain,
              }));
            }
          } else {
            throw new Error('MetaMask not installed');
          }
          break;

        case SupportedChain.POLKADOT:
          // Polkadot.js extension connection
          if (typeof window !== 'undefined') {
            // This would require @polkadot/extension-dapp
            // For now, we'll simulate the connection
            console.log('Polkadot wallet connection would be implemented here');
            setWalletState(prev => ({
              ...prev,
              wallets: prev.wallets.map(wallet =>
                wallet.chain === chain
                  ? { ...wallet, address: 'polkadot-address-placeholder', connected: true }
                  : wallet
              ),
              activeChain: chain,
            }));
          }
          break;

        default:
          throw new Error(`Unsupported chain: ${chain}`);
      }
    } catch (error) {
      setWalletState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to connect wallet',
      }));
    } finally {
      setWalletState(prev => ({ ...prev, isConnecting: false }));
    }
  }, [solanaWallet]);

  const disconnectWallet = useCallback(async (chain: SupportedChain) => {
    try {
      switch (chain) {
        case SupportedChain.SOLANA:
          await solanaWallet.disconnect();
          break;

        case SupportedChain.BNB_CHAIN:
        case SupportedChain.AVALANCHE:
        case SupportedChain.POLKADOT:
          setWalletState(prev => ({
            ...prev,
            wallets: prev.wallets.map(wallet =>
              wallet.chain === chain
                ? { ...wallet, address: '', connected: false, balance: 0, nativeBalance: 0 }
                : wallet
            ),
            activeChain: prev.activeChain === chain ? null : prev.activeChain,
          }));
          break;
      }
    } catch (error) {
      setWalletState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to disconnect wallet',
      }));
    }
  }, [solanaWallet]);

  const switchChain = useCallback(async (chain: SupportedChain) => {
    const wallet = walletState.wallets.find(w => w.chain === chain);
    if (wallet?.connected) {
      setWalletState(prev => ({ ...prev, activeChain: chain }));
    } else {
      await connectWallet(chain);
    }
  }, [walletState.wallets, connectWallet]);

  const getWalletForChain = useCallback((chain: SupportedChain): ChainWallet | null => {
    return walletState.wallets.find(w => w.chain === chain) || null;
  }, [walletState.wallets]);

  const isChainConnected = useCallback((chain: SupportedChain): boolean => {
    const wallet = getWalletForChain(chain);
    return wallet?.connected || false;
  }, [getWalletForChain]);

  const getTotalBalance = useCallback((): number => {
    return walletState.wallets.reduce((total, wallet) => total + wallet.nativeBalance, 0);
  }, [walletState.wallets]);

  const refreshBalances = useCallback(async () => {
    // This would implement balance fetching for each connected chain
    console.log('Refreshing balances for all connected chains...');
    // Implementation would go here
  }, []);

  return {
    walletState,
    connectWallet,
    disconnectWallet,
    switchChain,
    getWalletForChain,
    isChainConnected,
    getTotalBalance,
    refreshBalances,
  };
}

// Extend window type for MetaMask
declare global {
  interface Window {
    ethereum?: {
      request: (args: { method: string; params?: any[] }) => Promise<any>;
      on: (event: string, callback: (...args: any[]) => void) => void;
      removeListener: (event: string, callback: (...args: any[]) => void) => void;
    };
  }
}
