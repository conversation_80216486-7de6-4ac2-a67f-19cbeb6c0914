{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { WalletMultiButton } from '@solana/wallet-adapter-react-ui';\nimport { useWallet } from '@solana/wallet-adapter-react';\nimport { SOLANA_NETWORK } from '@/lib/constants';\n// import { ChainSelector, ConnectionStatus } from '@/components/cross-chain/MultiChainWalletProvider';\n\nexport function Header() {\n  const { connected } = useWallet();\n\n  return (\n    <header className=\"bg-black/95 backdrop-blur-sm border-b border-gray-800/50 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-3\">\n              {/* Logo */}\n              <div className=\"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">\n                  TokenLaunch\n                </h1>\n                <p className=\"text-xs text-gray-400\">Cross-Chain Token Creator</p>\n              </div>\n            </div>\n\n            {/* Multi-Chain Status */}\n            <div className=\"hidden lg:flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 bg-gray-800/50 border border-gray-700/30 rounded-xl px-3 py-1\">\n                <span className=\"text-cyan-400 text-sm\">🌐</span>\n                <span className=\"text-sm text-gray-400 font-medium\">Cross-Chain Ready</span>\n              </div>\n              <div className=\"w-px h-6 bg-gray-700\"></div>\n              <div className=\"flex items-center space-x-2\">\n                <div className={`w-2 h-2 rounded-full ${\n                  SOLANA_NETWORK === 'mainnet-beta' ? 'bg-green-400' : 'bg-yellow-400'\n                } animate-pulse`}></div>\n                <span className=\"text-sm text-gray-400 font-medium\">\n                  Solana {SOLANA_NETWORK === 'mainnet-beta' ? 'Mainnet' : 'Devnet'}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a href=\"#features\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Cross-Chain\n            </a>\n            <a href=\"#launch\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Launch\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Docs\n            </a>\n          </nav>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* Chain Indicators */}\n            <div className=\"hidden xl:flex items-center gap-2 p-2 bg-gray-800/50 rounded-xl border border-gray-700/50\">\n              <span className=\"text-gray-400 text-sm font-medium mr-2\">Chains:</span>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg\" title=\"Solana\">\n                <span className=\"text-sm\">◎</span>\n              </div>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 text-gray-400\" title=\"BNB Chain\">\n                <span className=\"text-sm\">🟡</span>\n              </div>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 text-gray-400\" title=\"Avalanche\">\n                <span className=\"text-sm\">🔺</span>\n              </div>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 text-gray-400\" title=\"Polkadot\">\n                <span className=\"text-sm\">⚫</span>\n              </div>\n            </div>\n\n            {/* Solana Connection Status */}\n            {connected && (\n              <div className=\"hidden sm:flex items-center space-x-2 bg-green-900/30 border border-green-500/30 px-3 py-1 rounded-full\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-green-400 font-medium\">Solana Connected</span>\n              </div>\n            )}\n\n            {/* Primary Solana Wallet Button */}\n            <WalletMultiButton className=\"!bg-gradient-to-r !from-purple-500 !to-pink-500 hover:!from-purple-600 hover:!to-pink-600 !text-white !rounded-xl !px-6 !py-2 !text-sm !font-semibold !transition-all !duration-300 !border !border-purple-400/30 hover:!border-purple-300/50 hover:!scale-105\" />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOO,SAAS;IACd,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD;IAE9B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC5E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgG;;;;;;0DAG9G,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,uHAAA,CAAA,iBAAc,KAAK,iBAAiB,iBAAiB,gBACtD,cAAc,CAAC;;;;;;0DAChB,8OAAC;gDAAK,WAAU;;oDAAoC;oDAC1C,uHAAA,CAAA,iBAAc,KAAK,iBAAiB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAoE;;;;;;0CAGlG,8OAAC;gCAAE,MAAK;gCAAU,WAAU;0CAAoE;;;;;;0CAGhG,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CAAoE;;;;;;;;;;;;kCAK5F,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAyC;;;;;;kDACzD,8OAAC;wCAAI,WAAU;wCAAwH,OAAM;kDAC3I,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;wCAAgF,OAAM;kDACnG,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;wCAAgF,OAAM;kDACnG,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;wCAAgF,OAAM;kDACnG,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;4BAK7B,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAqC;;;;;;;;;;;;0CAKzD,8OAAC,+LAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/config/chains.ts"], "sourcesContent": ["/**\n * Multi-Chain Configuration\n * Defines supported blockchains and their parameters\n */\n\nexport enum SupportedChain {\n  SOLANA = 'solana',\n  BNB_CHAIN = 'bnb',\n  AVALANCHE = 'avalanche',\n  POLKADOT = 'polkadot'\n}\n\nexport interface ChainConfig {\n  id: string;\n  name: string;\n  displayName: string;\n  nativeCurrency: {\n    name: string;\n    symbol: string;\n    decimals: number;\n  };\n  rpcUrls: {\n    default: string;\n    testnet?: string;\n  };\n  blockExplorers: {\n    default: {\n      name: string;\n      url: string;\n    };\n  };\n  testnet?: boolean;\n  chainType: 'evm' | 'solana' | 'substrate';\n  features: {\n    tokenStandard: string;\n    dexIntegration: string[];\n    bridgeSupport: string[];\n  };\n}\n\nexport const CHAIN_CONFIGS: Record<SupportedChain, ChainConfig> = {\n  [SupportedChain.SOLANA]: {\n    id: 'solana',\n    name: 'solana',\n    displayName: 'Solana',\n    nativeCurrency: {\n      name: '<PERSON><PERSON>',\n      symbol: 'SOL',\n      decimals: 9,\n    },\n    rpcUrls: {\n      default: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',\n      testnet: 'https://api.devnet.solana.com',\n    },\n    blockExplorers: {\n      default: {\n        name: 'Solscan',\n        url: 'https://solscan.io',\n      },\n    },\n    chainType: 'solana',\n    features: {\n      tokenStandard: 'SPL',\n      dexIntegration: ['raydium', 'orca'],\n      bridgeSupport: ['wormhole', 'allbridge'],\n    },\n  },\n  [SupportedChain.BNB_CHAIN]: {\n    id: '56',\n    name: 'bnb-smart-chain',\n    displayName: 'BNB Chain',\n    nativeCurrency: {\n      name: 'BNB',\n      symbol: 'BNB',\n      decimals: 18,\n    },\n    rpcUrls: {\n      default: 'https://bsc-dataseed1.binance.org',\n      testnet: 'https://data-seed-prebsc-1-s1.binance.org:8545',\n    },\n    blockExplorers: {\n      default: {\n        name: 'BscScan',\n        url: 'https://bscscan.com',\n      },\n    },\n    chainType: 'evm',\n    features: {\n      tokenStandard: 'BEP-20',\n      dexIntegration: ['pancakeswap', 'biswap'],\n      bridgeSupport: ['wormhole', 'axelar', 'multichain'],\n    },\n  },\n  [SupportedChain.AVALANCHE]: {\n    id: '43114',\n    name: 'avalanche',\n    displayName: 'Avalanche',\n    nativeCurrency: {\n      name: 'Avalanche',\n      symbol: 'AVAX',\n      decimals: 18,\n    },\n    rpcUrls: {\n      default: 'https://api.avax.network/ext/bc/C/rpc',\n      testnet: 'https://api.avax-test.network/ext/bc/C/rpc',\n    },\n    blockExplorers: {\n      default: {\n        name: 'SnowTrace',\n        url: 'https://snowtrace.io',\n      },\n    },\n    chainType: 'evm',\n    features: {\n      tokenStandard: 'ERC-20',\n      dexIntegration: ['traderjoe', 'pangolin'],\n      bridgeSupport: ['wormhole', 'axelar', 'avalanche-bridge'],\n    },\n  },\n  [SupportedChain.POLKADOT]: {\n    id: 'polkadot',\n    name: 'polkadot',\n    displayName: 'Polkadot',\n    nativeCurrency: {\n      name: 'Polkadot',\n      symbol: 'DOT',\n      decimals: 10,\n    },\n    rpcUrls: {\n      default: 'wss://rpc.polkadot.io',\n      testnet: 'wss://westend-rpc.polkadot.io',\n    },\n    blockExplorers: {\n      default: {\n        name: 'Polkascan',\n        url: 'https://polkascan.io/polkadot',\n      },\n    },\n    chainType: 'substrate',\n    features: {\n      tokenStandard: 'PSP-22',\n      dexIntegration: ['hydradx', 'basilisk'],\n      bridgeSupport: ['xcm', 'wormhole'],\n    },\n  },\n};\n\nexport const DEFAULT_CHAINS = [\n  SupportedChain.SOLANA,\n  SupportedChain.BNB_CHAIN,\n  SupportedChain.AVALANCHE,\n  SupportedChain.POLKADOT,\n];\n\nexport function getChainConfig(chain: SupportedChain): ChainConfig {\n  return CHAIN_CONFIGS[chain];\n}\n\nexport function getAllChainConfigs(): ChainConfig[] {\n  return Object.values(CHAIN_CONFIGS);\n}\n\nexport function isEVMChain(chain: SupportedChain): boolean {\n  return CHAIN_CONFIGS[chain].chainType === 'evm';\n}\n\nexport function isSolanaChain(chain: SupportedChain): boolean {\n  return CHAIN_CONFIGS[chain].chainType === 'solana';\n}\n\nexport function isSubstrateChain(chain: SupportedChain): boolean {\n  return CHAIN_CONFIGS[chain].chainType === 'substrate';\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAEM,IAAA,AAAK,wCAAA;;;;;WAAA;;AAmCL,MAAM,gBAAqD;IAChE,UAAuB,EAAE;QACvB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;YACP,SAAS,qEAA0C;YACnD,SAAS;QACX;QACA,gBAAgB;YACd,SAAS;gBACP,MAAM;gBACN,KAAK;YACP;QACF;QACA,WAAW;QACX,UAAU;YACR,eAAe;YACf,gBAAgB;gBAAC;gBAAW;aAAO;YACnC,eAAe;gBAAC;gBAAY;aAAY;QAC1C;IACF;IACA,OAA0B,EAAE;QAC1B,IAAI;QACJ,MAAM;QACN,aAAa;QACb,gBAAgB;Y<PERSON>d,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;YACP,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;YACd,SAAS;gBACP,MAAM;gBACN,KAAK;YACP;QACF;QACA,WAAW;QACX,UAAU;YACR,eAAe;YACf,gBAAgB;gBAAC;gBAAe;aAAS;YACzC,eAAe;gBAAC;gBAAY;gBAAU;aAAa;QACrD;IACF;IACA,aAA0B,EAAE;QAC1B,IAAI;QACJ,MAAM;QACN,aAAa;QACb,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;YACP,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;YACd,SAAS;gBACP,MAAM;gBACN,KAAK;YACP;QACF;QACA,WAAW;QACX,UAAU;YACR,eAAe;YACf,gBAAgB;gBAAC;gBAAa;aAAW;YACzC,eAAe;gBAAC;gBAAY;gBAAU;aAAmB;QAC3D;IACF;IACA,YAAyB,EAAE;QACzB,IAAI;QACJ,MAAM;QACN,aAAa;QACb,gBAAgB;YACd,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;YACP,SAAS;YACT,SAAS;QACX;QACA,gBAAgB;YACd,SAAS;gBACP,MAAM;gBACN,KAAK;YACP;QACF;QACA,WAAW;QACX,UAAU;YACR,eAAe;YACf,gBAAgB;gBAAC;gBAAW;aAAW;YACvC,eAAe;gBAAC;gBAAO;aAAW;QACpC;IACF;AACF;AAEO,MAAM,iBAAiB;;;;;CAK7B;AAEM,SAAS,eAAe,KAAqB;IAClD,OAAO,aAAa,CAAC,MAAM;AAC7B;AAEO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC;AACvB;AAEO,SAAS,WAAW,KAAqB;IAC9C,OAAO,aAAa,CAAC,MAAM,CAAC,SAAS,KAAK;AAC5C;AAEO,SAAS,cAAc,KAAqB;IACjD,OAAO,aAAa,CAAC,MAAM,CAAC,SAAS,KAAK;AAC5C;AAEO,SAAS,iBAAiB,KAAqB;IACpD,OAAO,aAAa,CAAC,MAAM,CAAC,SAAS,KAAK;AAC5C", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/cross-chain/ChainSelector.tsx"], "sourcesContent": ["/**\n * Chain Selector Component\n * Reusable component for selecting multiple blockchains\n */\n\n'use client';\n\nimport React from 'react';\nimport { SupportedChain, getChainConfig } from '@/config/chains';\n\ninterface ChainSelectorProps {\n  selectedChains: SupportedChain[];\n  onSelectionChange: (chains: SupportedChain[]) => void;\n  maxSelections?: number;\n  minSelections?: number;\n  showCosts?: boolean;\n  showFeatures?: boolean;\n  layout?: 'grid' | 'list';\n  size?: 'small' | 'medium' | 'large';\n}\n\nexport function ChainSelector({\n  selectedChains,\n  onSelectionChange,\n  maxSelections,\n  minSelections = 1,\n  showCosts = true,\n  showFeatures = true,\n  layout = 'grid',\n  size = 'medium',\n}: ChainSelectorProps) {\n  const chainIcons = {\n    [SupportedChain.SOLANA]: '◎',\n    [SupportedChain.BNB_CHAIN]: '🟡',\n    [SupportedChain.AVALANCHE]: '🔺',\n    [SupportedChain.POLKADOT]: '⚫',\n  };\n\n  const chainColors = {\n    [SupportedChain.SOLANA]: 'from-purple-500 to-blue-500',\n    [SupportedChain.BNB_CHAIN]: 'from-yellow-500 to-orange-500',\n    [SupportedChain.AVALANCHE]: 'from-red-500 to-pink-500',\n    [SupportedChain.POLKADOT]: 'from-pink-500 to-purple-500',\n  };\n\n  const estimatedCosts = {\n    [SupportedChain.SOLANA]: '~0.15 SOL',\n    [SupportedChain.BNB_CHAIN]: '~0.01 BNB',\n    [SupportedChain.AVALANCHE]: '~0.1 AVAX',\n    [SupportedChain.POLKADOT]: '~1 DOT',\n  };\n\n  const handleChainToggle = (chain: SupportedChain) => {\n    const isSelected = selectedChains.includes(chain);\n    \n    if (isSelected) {\n      // Remove chain (but respect minimum)\n      if (selectedChains.length > minSelections) {\n        onSelectionChange(selectedChains.filter(c => c !== chain));\n      }\n    } else {\n      // Add chain (but respect maximum)\n      if (!maxSelections || selectedChains.length < maxSelections) {\n        onSelectionChange([...selectedChains, chain]);\n      }\n    }\n  };\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'small':\n        return {\n          container: 'p-4',\n          icon: 'text-2xl',\n          title: 'text-lg',\n          subtitle: 'text-xs',\n          feature: 'text-xs',\n        };\n      case 'large':\n        return {\n          container: 'p-8',\n          icon: 'text-5xl',\n          title: 'text-2xl',\n          subtitle: 'text-base',\n          feature: 'text-base',\n        };\n      default: // medium\n        return {\n          container: 'p-6',\n          icon: 'text-4xl',\n          title: 'text-xl',\n          subtitle: 'text-sm',\n          feature: 'text-sm',\n        };\n    }\n  };\n\n  const sizeClasses = getSizeClasses();\n  const gridCols = layout === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-6' : 'space-y-4';\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Selection info */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-white\">\n            Select Blockchains ({selectedChains.length})\n          </h3>\n          <p className=\"text-gray-400 text-sm\">\n            {minSelections > 1 && `Minimum ${minSelections} required. `}\n            {maxSelections && `Maximum ${maxSelections} allowed.`}\n          </p>\n        </div>\n        \n        {selectedChains.length > 0 && (\n          <div className=\"flex items-center gap-2\">\n            {selectedChains.map((chain) => (\n              <div\n                key={chain}\n                className=\"flex items-center gap-1 bg-gray-800 border border-gray-600 rounded-lg px-2 py-1\"\n              >\n                <span className=\"text-sm\">{chainIcons[chain]}</span>\n                <span className=\"text-xs text-gray-300\">{getChainConfig(chain).displayName}</span>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Chain options */}\n      <div className={gridCols}>\n        {Object.values(SupportedChain).map((chain) => {\n          const config = getChainConfig(chain);\n          const isSelected = selectedChains.includes(chain);\n          const isDisabled = !isSelected && maxSelections && selectedChains.length >= maxSelections;\n          const canDeselect = selectedChains.length > minSelections;\n\n          return (\n            <div\n              key={chain}\n              onClick={() => !isDisabled && (isSelected ? canDeselect && handleChainToggle(chain) : handleChainToggle(chain))}\n              className={`\n                relative ${sizeClasses.container} rounded-2xl border-2 transition-all duration-300 \n                ${isDisabled \n                  ? 'cursor-not-allowed opacity-50' \n                  : 'cursor-pointer hover:scale-105'\n                }\n                ${isSelected \n                  ? `bg-gradient-to-r ${chainColors[chain]} border-white/30 shadow-xl` \n                  : 'bg-gray-800/50 border-gray-600/50 hover:border-gray-500/50'\n                }\n              `}\n            >\n              {/* Selection indicator */}\n              <div className=\"absolute top-4 right-4\">\n                <div className={`\n                  w-6 h-6 rounded-full border-2 flex items-center justify-center\n                  ${isSelected ? 'bg-white border-white' : 'border-gray-400'}\n                `}>\n                  {isSelected && (\n                    <svg className=\"w-4 h-4 text-gray-800\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  )}\n                </div>\n              </div>\n\n              {/* Chain info */}\n              <div className=\"flex items-center space-x-4 mb-4\">\n                <div className={sizeClasses.icon}>{chainIcons[chain]}</div>\n                <div>\n                  <h4 className={`${sizeClasses.title} font-bold ${isSelected ? 'text-white' : 'text-white'}`}>\n                    {config.displayName}\n                  </h4>\n                  <p className={`${sizeClasses.subtitle} ${isSelected ? 'text-white/80' : 'text-gray-400'}`}>\n                    {config.features.tokenStandard} Token\n                  </p>\n                </div>\n              </div>\n\n              {/* Features */}\n              {showFeatures && (\n                <div className=\"space-y-2 mb-4\">\n                  <div className={`flex items-center space-x-2 ${sizeClasses.feature} ${isSelected ? 'text-white/90' : 'text-gray-300'}`}>\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                    </svg>\n                    <span>Fast deployment</span>\n                  </div>\n                  <div className={`flex items-center space-x-2 ${sizeClasses.feature} ${isSelected ? 'text-white/90' : 'text-gray-300'}`}>\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                    </svg>\n                    <span>DEX integration</span>\n                  </div>\n                  {showCosts && (\n                    <div className={`flex items-center space-x-2 ${sizeClasses.feature} ${isSelected ? 'text-white/90' : 'text-gray-300'}`}>\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span>{estimatedCosts[chain]}</span>\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* Bridge support */}\n              <div className=\"pt-3 border-t border-white/20\">\n                <p className={`text-xs ${isSelected ? 'text-white/70' : 'text-gray-500'} mb-1`}>\n                  Bridge Support:\n                </p>\n                <div className=\"flex flex-wrap gap-1\">\n                  {config.features.bridgeSupport.slice(0, 2).map((bridge) => (\n                    <span\n                      key={bridge}\n                      className={`px-2 py-1 rounded-md text-xs font-medium ${\n                        isSelected \n                          ? 'bg-white/20 text-white' \n                          : 'bg-gray-700 text-gray-300'\n                      }`}\n                    >\n                      {bridge}\n                    </span>\n                  ))}\n                  {config.features.bridgeSupport.length > 2 && (\n                    <span className={`px-2 py-1 rounded-md text-xs ${isSelected ? 'text-white/70' : 'text-gray-500'}`}>\n                      +{config.features.bridgeSupport.length - 2}\n                    </span>\n                  )}\n                </div>\n              </div>\n\n              {/* Disabled overlay */}\n              {isDisabled && (\n                <div className=\"absolute inset-0 bg-gray-900/50 rounded-2xl flex items-center justify-center\">\n                  <div className=\"bg-gray-800 border border-gray-600 rounded-lg px-3 py-1\">\n                    <span className=\"text-gray-400 text-sm font-medium\">Max reached</span>\n                  </div>\n                </div>\n              )}\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Quick selection buttons */}\n      <div className=\"flex flex-wrap gap-2\">\n        <button\n          type=\"button\"\n          onClick={() => onSelectionChange([SupportedChain.SOLANA])}\n          className=\"px-3 py-1 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white text-sm rounded-lg transition-all duration-300\"\n        >\n          Solana Only\n        </button>\n        <button\n          type=\"button\"\n          onClick={() => onSelectionChange([SupportedChain.SOLANA, SupportedChain.BNB_CHAIN])}\n          className=\"px-3 py-1 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white text-sm rounded-lg transition-all duration-300\"\n        >\n          Solana + BNB\n        </button>\n        <button\n          type=\"button\"\n          onClick={() => onSelectionChange(Object.values(SupportedChain))}\n          className=\"px-3 py-1 bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 text-white text-sm rounded-lg transition-all duration-300\"\n        >\n          All Chains\n        </button>\n        <button\n          type=\"button\"\n          onClick={() => onSelectionChange([])}\n          disabled={minSelections > 0}\n          className=\"px-3 py-1 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white text-sm rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          Clear All\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AAHA;;;AAgBO,SAAS,cAAc,EAC5B,cAAc,EACd,iBAAiB,EACjB,aAAa,EACb,gBAAgB,CAAC,EACjB,YAAY,IAAI,EAChB,eAAe,IAAI,EACnB,SAAS,MAAM,EACf,OAAO,QAAQ,EACI;IACnB,MAAM,aAAa;QACjB,CAAC,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,EAAE;QACzB,CAAC,uHAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,EAAE;QAC5B,CAAC,uHAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,EAAE;QAC5B,CAAC,uHAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,EAAE;IAC7B;IAEA,MAAM,cAAc;QAClB,CAAC,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,EAAE;QACzB,CAAC,uHAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,EAAE;QAC5B,CAAC,uHAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,EAAE;QAC5B,CAAC,uHAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,EAAE;IAC7B;IAEA,MAAM,iBAAiB;QACrB,CAAC,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,EAAE;QACzB,CAAC,uHAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,EAAE;QAC5B,CAAC,uHAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,EAAE;QAC5B,CAAC,uHAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,EAAE;IAC7B;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,eAAe,QAAQ,CAAC;QAE3C,IAAI,YAAY;YACd,qCAAqC;YACrC,IAAI,eAAe,MAAM,GAAG,eAAe;gBACzC,kBAAkB,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;YACrD;QACF,OAAO;YACL,kCAAkC;YAClC,IAAI,CAAC,iBAAiB,eAAe,MAAM,GAAG,eAAe;gBAC3D,kBAAkB;uBAAI;oBAAgB;iBAAM;YAC9C;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,UAAU;oBACV,SAAS;gBACX;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,UAAU;oBACV,SAAS;gBACX;YACF;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,UAAU;oBACV,SAAS;gBACX;QACJ;IACF;IAEA,MAAM,cAAc;IACpB,MAAM,WAAW,WAAW,SAAS,0CAA0C;IAE/E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;oCAAmC;oCAC1B,eAAe,MAAM;oCAAC;;;;;;;0CAE7C,8OAAC;gCAAE,WAAU;;oCACV,gBAAgB,KAAK,CAAC,QAAQ,EAAE,cAAc,WAAW,CAAC;oCAC1D,iBAAiB,CAAC,QAAQ,EAAE,cAAc,SAAS,CAAC;;;;;;;;;;;;;oBAIxD,eAAe,MAAM,GAAG,mBACvB,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAW,UAAU,CAAC,MAAM;;;;;;kDAC5C,8OAAC;wCAAK,WAAU;kDAAyB,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,WAAW;;;;;;;+BAJrE;;;;;;;;;;;;;;;;0BAYf,8OAAC;gBAAI,WAAW;0BACb,OAAO,MAAM,CAAC,uHAAA,CAAA,iBAAc,EAAE,GAAG,CAAC,CAAC;oBAClC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;oBAC9B,MAAM,aAAa,eAAe,QAAQ,CAAC;oBAC3C,MAAM,aAAa,CAAC,cAAc,iBAAiB,eAAe,MAAM,IAAI;oBAC5E,MAAM,cAAc,eAAe,MAAM,GAAG;oBAE5C,qBACE,8OAAC;wBAEC,SAAS,IAAM,CAAC,cAAc,CAAC,aAAa,eAAe,kBAAkB,SAAS,kBAAkB,MAAM;wBAC9G,WAAW,CAAC;yBACD,EAAE,YAAY,SAAS,CAAC;gBACjC,EAAE,aACE,kCACA,iCACH;gBACD,EAAE,aACE,CAAC,iBAAiB,EAAE,WAAW,CAAC,MAAM,CAAC,0BAA0B,CAAC,GAClE,6DACH;cACH,CAAC;;0CAGD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAW,CAAC;;kBAEf,EAAE,aAAa,0BAA0B,kBAAkB;gBAC7D,CAAC;8CACE,4BACC,8OAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAqH,UAAS;;;;;;;;;;;;;;;;;;;;;0CAOjK,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,YAAY,IAAI;kDAAG,UAAU,CAAC,MAAM;;;;;;kDACpD,8OAAC;;0DACC,8OAAC;gDAAG,WAAW,GAAG,YAAY,KAAK,CAAC,WAAW,EAAE,aAAa,eAAe,cAAc;0DACxF,OAAO,WAAW;;;;;;0DAErB,8OAAC;gDAAE,WAAW,GAAG,YAAY,QAAQ,CAAC,CAAC,EAAE,aAAa,kBAAkB,iBAAiB;;oDACtF,OAAO,QAAQ,CAAC,aAAa;oDAAC;;;;;;;;;;;;;;;;;;;4BAMpC,8BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,4BAA4B,EAAE,YAAY,OAAO,CAAC,CAAC,EAAE,aAAa,kBAAkB,iBAAiB;;0DACpH,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAW,CAAC,4BAA4B,EAAE,YAAY,OAAO,CAAC,CAAC,EAAE,aAAa,kBAAkB,iBAAiB;;0DACpH,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;0DAAK;;;;;;;;;;;;oCAEP,2BACC,8OAAC;wCAAI,WAAW,CAAC,4BAA4B,EAAE,YAAY,OAAO,CAAC,CAAC,EAAE,aAAa,kBAAkB,iBAAiB;;0DACpH,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;0DAAM,cAAc,CAAC,MAAM;;;;;;;;;;;;;;;;;;0CAOpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAW,CAAC,QAAQ,EAAE,aAAa,kBAAkB,gBAAgB,KAAK,CAAC;kDAAE;;;;;;kDAGhF,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBAC9C,8OAAC;oDAEC,WAAW,CAAC,yCAAyC,EACnD,aACI,2BACA,6BACJ;8DAED;mDAPI;;;;;4CAUR,OAAO,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,mBACtC,8OAAC;gDAAK,WAAW,CAAC,6BAA6B,EAAE,aAAa,kBAAkB,iBAAiB;;oDAAE;oDAC/F,OAAO,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;4BAOhD,4BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;;;;;;uBAjGrD;;;;;gBAuGX;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,kBAAkB;gCAAC,uHAAA,CAAA,iBAAc,CAAC,MAAM;6BAAC;wBACxD,WAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,kBAAkB;gCAAC,uHAAA,CAAA,iBAAc,CAAC,MAAM;gCAAE,uHAAA,CAAA,iBAAc,CAAC,SAAS;6BAAC;wBAClF,WAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,kBAAkB,OAAO,MAAM,CAAC,uHAAA,CAAA,iBAAc;wBAC7D,WAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,kBAAkB,EAAE;wBACnC,UAAU,gBAAgB;wBAC1B,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/cross-chain/CrossChainTokenCreator.tsx"], "sourcesContent": ["/**\n * Cross-Chain Token Creator\n * Main interface for creating tokens across multiple blockchains\n */\n\n'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { SupportedChain } from '@/config/chains';\n// import { useMultiChainWallet } from '@/components/cross-chain/MultiChainWalletProvider';\nimport { TokenDeploymentParams } from '@/config/deployment';\nimport { ChainSelector } from '@/components/cross-chain/ChainSelector';\n\ninterface CrossChainTokenCreatorProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function CrossChainTokenCreator({ isOpen, onClose }: CrossChainTokenCreatorProps) {\n  // const { walletState, isChainConnected } = useMultiChainWallet();\n  \n  const [currentStep, setCurrentStep] = useState<'basics' | 'chains' | 'liquidity' | 'review' | 'deploying'>('basics');\n  const [tokenParams, setTokenParams] = useState<Partial<TokenDeploymentParams>>({\n    name: '',\n    symbol: '',\n    totalSupply: '',\n    decimals: 9,\n    description: '',\n    targetChains: [SupportedChain.SOLANA],\n    initialLiquidity: 0.2,\n  });\n\n  const handleParamChange = useCallback((key: keyof TokenDeploymentParams, value: any) => {\n    setTokenParams(prev => ({ ...prev, [key]: value }));\n  }, []);\n\n  const nextStep = useCallback(() => {\n    const steps = ['basics', 'chains', 'liquidity', 'review', 'deploying'] as const;\n    const currentIndex = steps.indexOf(currentStep);\n    if (currentIndex < steps.length - 1) {\n      setCurrentStep(steps[currentIndex + 1]);\n    }\n  }, [currentStep]);\n\n  const prevStep = useCallback(() => {\n    const steps = ['basics', 'chains', 'liquidity', 'review', 'deploying'] as const;\n    const currentIndex = steps.indexOf(currentStep);\n    if (currentIndex > 0) {\n      setCurrentStep(steps[currentIndex - 1]);\n    }\n  }, [currentStep]);\n\n  const getStepTitle = () => {\n    switch (currentStep) {\n      case 'basics': return 'Token Basics';\n      case 'chains': return 'Select Chains';\n      case 'liquidity': return 'Liquidity Distribution';\n      case 'review': return 'Review & Launch';\n      case 'deploying': return 'Deploying Everywhere';\n      default: return 'Create Token';\n    }\n  };\n\n  const getStepSubtitle = () => {\n    switch (currentStep) {\n      case 'basics': return 'Define your token properties';\n      case 'chains': return 'Choose target blockchains';\n      case 'liquidity': return 'Configure cross-chain liquidity';\n      case 'review': return 'Confirm deployment details';\n      case 'deploying': return 'Creating tokens across chains';\n      default: return '';\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"w-full max-w-5xl mx-auto\">\n      <div className=\"bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl w-full shadow-2xl\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 p-6 rounded-t-2xl border-b border-gray-700/50\">\n          <div className=\"flex items-center justify-center\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center shadow-lg\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <div className=\"text-center\">\n                <h2 className=\"text-2xl font-bold text-white\">{getStepTitle()}</h2>\n                <p className=\"text-white/80 text-sm\">{getStepSubtitle()}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"px-6 py-4 bg-gray-800/50\">\n          <div className=\"flex items-center justify-between\">\n            {['basics', 'chains', 'liquidity', 'review'].map((step, index) => {\n              const isActive = currentStep === step;\n              const isCompleted = ['basics', 'chains', 'liquidity', 'review'].indexOf(currentStep) > index;\n              \n              return (\n                <div key={step} className=\"flex items-center\">\n                  <div className={`\n                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold\n                    ${isActive ? 'bg-purple-500 text-white' : \n                      isCompleted ? 'bg-green-500 text-white' : 'bg-gray-600 text-gray-400'}\n                  `}>\n                    {isCompleted ? '✓' : index + 1}\n                  </div>\n                  {index < 3 && (\n                    <div className={`w-16 h-1 mx-2 ${isCompleted ? 'bg-green-500' : 'bg-gray-600'}`}></div>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {/* Step 1: Token Basics */}\n          {currentStep === 'basics' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Token Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={tokenParams.name || ''}\n                    onChange={(e) => handleParamChange('name', e.target.value)}\n                    placeholder=\"My Awesome Token\"\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Token Symbol *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={tokenParams.symbol || ''}\n                    onChange={(e) => handleParamChange('symbol', e.target.value.toUpperCase())}\n                    placeholder=\"MAT\"\n                    maxLength={10}\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Total Supply *\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={tokenParams.totalSupply || ''}\n                    onChange={(e) => handleParamChange('totalSupply', e.target.value)}\n                    placeholder=\"1000000\"\n                    min=\"1\"\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Decimals\n                  </label>\n                  <select\n                    value={tokenParams.decimals || 9}\n                    onChange={(e) => handleParamChange('decimals', parseInt(e.target.value))}\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white transition-all duration-300\"\n                    aria-label=\"Token decimals\"\n                  >\n                    {[6, 8, 9, 18].map(decimal => (\n                      <option key={decimal} value={decimal}>{decimal} decimals</option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Description (Optional)\n                </label>\n                <textarea\n                  value={tokenParams.description || ''}\n                  onChange={(e) => handleParamChange('description', e.target.value)}\n                  placeholder=\"Describe your token and its purpose...\"\n                  rows={3}\n                  className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300 resize-none\"\n                />\n              </div>\n\n              {/* Navigation */}\n              <div className=\"flex justify-end pt-6\">\n                <button\n                  type=\"button\"\n                  onClick={nextStep}\n                  disabled={!tokenParams.name || !tokenParams.symbol || !tokenParams.totalSupply}\n                  className=\"bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30\"\n                >\n                  Continue to Chain Selection\n                  <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Step 2: Chain Selection */}\n          {currentStep === 'chains' && (\n            <div className=\"space-y-6\">\n              <div className=\"text-center mb-8\">\n                <h3 className=\"text-xl font-bold text-white mb-2\">Select Target Blockchains</h3>\n                <p className=\"text-gray-400\">Choose which chains to deploy your token on simultaneously</p>\n              </div>\n\n              <ChainSelector\n                selectedChains={tokenParams.targetChains || []}\n                onSelectionChange={(chains) => handleParamChange('targetChains', chains)}\n                minSelections={1}\n                showCosts={true}\n                showFeatures={true}\n                layout=\"grid\"\n                size=\"medium\"\n              />\n\n              {/* Selection summary */}\n              <div className=\"bg-gray-800/50 border border-gray-700/50 rounded-xl p-6\">\n                <h4 className=\"text-white font-semibold mb-3 flex items-center gap-2\">\n                  <svg className=\"w-5 h-5 text-cyan-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z\" />\n                  </svg>\n                  Deployment Summary\n                </h4>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-white\">{tokenParams.targetChains?.length || 0}</div>\n                    <div className=\"text-gray-400 text-sm\">Chains Selected</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-white\">~2-5min</div>\n                    <div className=\"text-gray-400 text-sm\">Deploy Time</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-white\">No Bridging</div>\n                    <div className=\"text-gray-400 text-sm\">Required</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-white\">Instant</div>\n                    <div className=\"text-gray-400 text-sm\">Cross-Chain</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Navigation */}\n              <div className=\"flex justify-between pt-6\">\n                <button\n                  type=\"button\"\n                  onClick={prevStep}\n                  className=\"bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 flex items-center\"\n                >\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                  </svg>\n                  Back to Basics\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={nextStep}\n                  disabled={!tokenParams.targetChains?.length}\n                  className=\"bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30\"\n                >\n                  Continue to Liquidity\n                  <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Other steps placeholder */}\n          {(currentStep === 'liquidity' || currentStep === 'review' || currentStep === 'deploying') && (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400\">\n                {currentStep === 'liquidity' && 'Liquidity distribution interface coming next...'}\n                {currentStep === 'review' && 'Review interface coming next...'}\n                {currentStep === 'deploying' && 'Deployment interface coming next...'}\n              </div>\n              <button\n                type=\"button\"\n                onClick={prevStep}\n                className=\"mt-4 bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-2 px-6 rounded-xl transition-all duration-300\"\n              >\n                Back\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AAGA;AANA;;;;;AAaO,SAAS,uBAAuB,EAAE,MAAM,EAAE,OAAO,EAA+B;IACrF,mEAAmE;IAEnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8D;IAC3G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;QAC7E,MAAM;QACN,QAAQ;QACR,aAAa;QACb,UAAU;QACV,aAAa;QACb,cAAc;YAAC,uHAAA,CAAA,iBAAc,CAAC,MAAM;SAAC;QACrC,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,KAAkC;QACvE,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IACnD,GAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,MAAM,QAAQ;YAAC;YAAU;YAAU;YAAa;YAAU;SAAY;QACtE,MAAM,eAAe,MAAM,OAAO,CAAC;QACnC,IAAI,eAAe,MAAM,MAAM,GAAG,GAAG;YACnC,eAAe,KAAK,CAAC,eAAe,EAAE;QACxC;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,MAAM,QAAQ;YAAC;YAAU;YAAU;YAAa;YAAU;SAAY;QACtE,MAAM,eAAe,MAAM,OAAO,CAAC;QACnC,IAAI,eAAe,GAAG;YACpB,eAAe,KAAK,CAAC,eAAe,EAAE;QACxC;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAU;4BAAU;4BAAa;yBAAS,CAAC,GAAG,CAAC,CAAC,MAAM;4BACtD,MAAM,WAAW,gBAAgB;4BACjC,MAAM,cAAc;gCAAC;gCAAU;gCAAU;gCAAa;6BAAS,CAAC,OAAO,CAAC,eAAe;4BAEvF,qBACE,8OAAC;gCAAe,WAAU;;kDACxB,8OAAC;wCAAI,WAAW,CAAC;;oBAEf,EAAE,WAAW,6BACX,cAAc,4BAA4B,4BAA4B;kBAC1E,CAAC;kDACE,cAAc,MAAM,QAAQ;;;;;;oCAE9B,QAAQ,mBACP,8OAAC;wCAAI,WAAW,CAAC,cAAc,EAAE,cAAc,iBAAiB,eAAe;;;;;;;+BATzE;;;;;wBAad;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,0BACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,YAAY,IAAI,IAAI;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,aAAY;oDACZ,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,YAAY,MAAM,IAAI;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;oDACvE,aAAY;oDACZ,WAAW;oDACX,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO,YAAY,WAAW,IAAI;oDAClC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,KAAI;oDACJ,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO,YAAY,QAAQ,IAAI;oDAC/B,UAAU,CAAC,IAAM,kBAAkB,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;oDACtE,WAAU;oDACV,cAAW;8DAEV;wDAAC;wDAAG;wDAAG;wDAAG;qDAAG,CAAC,GAAG,CAAC,CAAA,wBACjB,8OAAC;4DAAqB,OAAO;;gEAAU;gEAAQ;;2DAAlC;;;;;;;;;;;;;;;;;;;;;;8CAMrB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,YAAY,WAAW,IAAI;4CAClC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,aAAY;4CACZ,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,YAAY,WAAW;wCAC9E,WAAU;;4CACX;0DAEC,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQ9E,gBAAgB,0BACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC,qJAAA,CAAA,gBAAa;oCACZ,gBAAgB,YAAY,YAAY,IAAI,EAAE;oCAC9C,mBAAmB,CAAC,SAAW,kBAAkB,gBAAgB;oCACjE,eAAe;oCACf,WAAW;oCACX,cAAc;oCACd,QAAO;oCACP,MAAK;;;;;;8CAIP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAiC,YAAY,YAAY,EAAE,UAAU;;;;;;sEACpF,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;sEAC/C,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;sEAC/C,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;sEAC/C,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,YAAY,YAAY,EAAE;4CACrC,WAAU;;gDACX;8DAEC,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQ9E,CAAC,gBAAgB,eAAe,gBAAgB,YAAY,gBAAgB,WAAW,mBACtF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,eAAe;wCAC/B,gBAAgB,YAAY;wCAC5B,gBAAgB,eAAe;;;;;;;8CAElC,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Head<PERSON> } from \"@/components/Header\";\nimport { CrossChainTokenCreator } from \"@/components/cross-chain/CrossChainTokenCreator\";\n\nexport default function Home() {\n  return (\n    <>\n      <Header />\n\n      {/* Main Content - Token Creation Focus */}\n      <main className=\"relative overflow-hidden bg-black min-h-screen\">\n        {/* Animated background elements */}\n        <div className=\"absolute inset-0\">\n          {/* Grid pattern overlay */}\n          <div className=\"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]\"></div>\n\n          {/* Floating orbs */}\n          <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse\"></div>\n          <div className=\"absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-r from-cyan-500/20 to-green-500/20 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse delay-500\"></div>\n\n          {/* Additional floating elements */}\n          <div className=\"absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-full blur-2xl animate-pulse delay-2000\"></div>\n          <div className=\"absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-full blur-2xl animate-pulse delay-3000\"></div>\n        </div>\n\n        <div className=\"relative container mx-auto px-4 py-8\">\n          {/* Simplified header section */}\n          <div className=\"text-center mb-12\">\n            <div className=\"inline-flex items-center bg-gray-900/50 border border-purple-500/30 rounded-full px-6 py-3 mb-6\">\n              <span className=\"w-2 h-2 bg-cyan-400 rounded-full animate-pulse mr-3\"></span>\n              <span className=\"text-gray-300 text-sm font-medium\">Cross-Chain Platform • 4 Blockchains</span>\n            </div>\n\n            <h1 className=\"text-4xl lg:text-6xl font-black text-white mb-4 leading-tight tracking-tight\">\n              Launch Everywhere\n              <span className=\"block bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent\">\n                Cross-Chain Tokens\n              </span>\n            </h1>\n\n            <p className=\"text-lg lg:text-xl text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed\">\n              Deploy tokens simultaneously across <span className=\"text-purple-400 font-semibold\">Solana, BNB Chain, Avalanche, and Polkadot</span> through a single interface.\n              <span className=\"block mt-2 text-cyan-400 font-semibold\">No bridging required. No capital fragmentation.</span>\n            </p>\n          </div>\n\n          {/* Quick feature highlights */}\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">4 Chains</div>\n              <div className=\"text-gray-400 text-sm\">Simultaneous Deploy</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">No Bridging</div>\n              <div className=\"text-gray-400 text-sm\">Direct Launch</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">Auto Liquidity</div>\n              <div className=\"text-gray-400 text-sm\">Cross-Chain Pools</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">One Interface</div>\n              <div className=\"text-gray-400 text-sm\">Unified Control</div>\n            </div>\n          </div>\n\n          {/* Main Cross-Chain Token Creation Form */}\n          <div className=\"max-w-6xl mx-auto\">\n            <CrossChainTokenCreator\n              isOpen={true}\n              onClose={() => {}}\n            />\n          </div>\n\n          {/* Trust indicators */}\n          <div className=\"mt-16 pt-8 border-t border-gray-800/30 text-center\">\n            <p className=\"text-gray-500 text-sm mb-6\">Powered by industry-leading cross-chain protocols</p>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 max-w-6xl mx-auto\">\n              {/* Blockchains */}\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Solana</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">BNB Chain</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Avalanche</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Polkadot</span>\n              </div>\n\n              {/* Cross-Chain Protocols */}\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Wormhole</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-cyan-500 to-teal-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Axelar</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Singularity</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2\">\n                <div className=\"w-5 h-5 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium text-sm\">Raydium</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE;;0BACE,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAGP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAGtD,8OAAC;wCAAG,WAAU;;4CAA+E;0DAE3F,8OAAC;gDAAK,WAAU;0DAAgG;;;;;;;;;;;;kDAKlH,8OAAC;wCAAE,WAAU;;4CAA0E;0DACjD,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;4CAAiD;0DACrI,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;0CAK7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8JAAA,CAAA,yBAAsB;oCACrB,QAAQ;oCACR,SAAS,KAAO;;;;;;;;;;;0CAKpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAItD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE", "debugId": null}}]}