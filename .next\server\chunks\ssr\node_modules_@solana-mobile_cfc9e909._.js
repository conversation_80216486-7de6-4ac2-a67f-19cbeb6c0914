module.exports = {

"[project]/node_modules/@solana-mobile/mobile-wallet-adapter-protocol/lib/esm/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SolanaCloneAuthorization": (()=>SolanaCloneAuthorization),
    "SolanaMobileWalletAdapterError": (()=>SolanaMobileWalletAdapterError),
    "SolanaMobileWalletAdapterErrorCode": (()=>SolanaMobileWalletAdapterErrorCode),
    "SolanaMobileWalletAdapterProtocolError": (()=>SolanaMobileWalletAdapterProtocolError),
    "SolanaMobileWalletAdapterProtocolErrorCode": (()=>SolanaMobileWalletAdapterProtocolErrorCode),
    "SolanaSignInWithSolana": (()=>SolanaSignInWithSolana),
    "SolanaSignTransactions": (()=>SolanaSignTransactions),
    "startRemoteScenario": (()=>startRemoteScenario),
    "transact": (()=>transact)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$util$2f$lib$2f$esm$2f$signIn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-util/lib/esm/signIn.js [app-ssr] (ecmascript)");
;
// Typescript `enums` thwart tree-shaking. See https://bargsten.org/jsts/enums/
const SolanaMobileWalletAdapterErrorCode = {
    ERROR_ASSOCIATION_PORT_OUT_OF_RANGE: 'ERROR_ASSOCIATION_PORT_OUT_OF_RANGE',
    ERROR_REFLECTOR_ID_OUT_OF_RANGE: 'ERROR_REFLECTOR_ID_OUT_OF_RANGE',
    ERROR_FORBIDDEN_WALLET_BASE_URL: 'ERROR_FORBIDDEN_WALLET_BASE_URL',
    ERROR_SECURE_CONTEXT_REQUIRED: 'ERROR_SECURE_CONTEXT_REQUIRED',
    ERROR_SESSION_CLOSED: 'ERROR_SESSION_CLOSED',
    ERROR_SESSION_TIMEOUT: 'ERROR_SESSION_TIMEOUT',
    ERROR_WALLET_NOT_FOUND: 'ERROR_WALLET_NOT_FOUND',
    ERROR_INVALID_PROTOCOL_VERSION: 'ERROR_INVALID_PROTOCOL_VERSION',
    ERROR_BROWSER_NOT_SUPPORTED: 'ERROR_BROWSER_NOT_SUPPORTED'
};
class SolanaMobileWalletAdapterError extends Error {
    constructor(...args){
        const [code, message, data] = args;
        super(message);
        this.code = code;
        this.data = data;
        this.name = 'SolanaMobileWalletAdapterError';
    }
}
// Typescript `enums` thwart tree-shaking. See https://bargsten.org/jsts/enums/
const SolanaMobileWalletAdapterProtocolErrorCode = {
    // Keep these in sync with `mobilewalletadapter/common/ProtocolContract.java`.
    ERROR_AUTHORIZATION_FAILED: -1,
    ERROR_INVALID_PAYLOADS: -2,
    ERROR_NOT_SIGNED: -3,
    ERROR_NOT_SUBMITTED: -4,
    ERROR_TOO_MANY_PAYLOADS: -5,
    ERROR_ATTEST_ORIGIN_ANDROID: -100
};
class SolanaMobileWalletAdapterProtocolError extends Error {
    constructor(...args){
        const [jsonRpcMessageId, code, message, data] = args;
        super(message);
        this.code = code;
        this.data = data;
        this.jsonRpcMessageId = jsonRpcMessageId;
        this.name = 'SolanaMobileWalletAdapterProtocolError';
    }
}
/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */ function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}
function encode(input) {
    return window.btoa(input);
}
function fromUint8Array(byteArray, urlsafe) {
    const base64 = window.btoa(String.fromCharCode.call(null, ...byteArray));
    if (urlsafe) {
        return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    } else return base64;
}
function toUint8Array(base64EncodedByteArray) {
    return new Uint8Array(window.atob(base64EncodedByteArray).split('').map((c)=>c.charCodeAt(0)));
}
function createHelloReq(ecdhPublicKey, associationKeypairPrivateKey) {
    return __awaiter(this, void 0, void 0, function*() {
        const publicKeyBuffer = yield crypto.subtle.exportKey('raw', ecdhPublicKey);
        const signatureBuffer = yield crypto.subtle.sign({
            hash: 'SHA-256',
            name: 'ECDSA'
        }, associationKeypairPrivateKey, publicKeyBuffer);
        const response = new Uint8Array(publicKeyBuffer.byteLength + signatureBuffer.byteLength);
        response.set(new Uint8Array(publicKeyBuffer), 0);
        response.set(new Uint8Array(signatureBuffer), publicKeyBuffer.byteLength);
        return response;
    });
}
function createSIWSMessage(payload) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$util$2f$lib$2f$esm$2f$signIn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSignInMessageText"])(payload);
}
function createSIWSMessageBase64(payload) {
    return encode(createSIWSMessage(payload));
}
// optional features
const SolanaSignTransactions = 'solana:signTransactions';
const SolanaCloneAuthorization = 'solana:cloneAuthorization';
const SolanaSignInWithSolana = 'solana:signInWithSolana';
/**
 * Creates a {@link MobileWallet} proxy that handles backwards compatibility and API to RPC conversion.
 *
 * @param protocolVersion the protocol version in use for this session/request
 * @param protocolRequestHandler callback function that handles sending the RPC request to the wallet endpoint.
 * @returns a {@link MobileWallet} proxy
 */ function createMobileWalletProxy(protocolVersion, protocolRequestHandler) {
    return new Proxy({}, {
        get (target, p) {
            // Wrapping a Proxy in a promise results in the Proxy being asked for a 'then' property so must 
            // return null if 'then' is called on this proxy to let the 'resolve()' call know this is not a promise.
            // see: https://stackoverflow.com/a/53890904
            //@ts-ignore
            if (p === 'then') {
                return null;
            }
            if (target[p] == null) {
                target[p] = function(inputParams) {
                    return __awaiter(this, void 0, void 0, function*() {
                        const { method, params } = handleMobileWalletRequest(p, inputParams, protocolVersion);
                        const result = yield protocolRequestHandler(method, params);
                        // if the request tried to sign in but the wallet did not return a sign in result, fallback on message signing
                        if (method === 'authorize' && params.sign_in_payload && !result.sign_in_result) {
                            result['sign_in_result'] = yield signInFallback(params.sign_in_payload, result, protocolRequestHandler);
                        }
                        return handleMobileWalletResponse(p, result, protocolVersion);
                    });
                };
            }
            return target[p];
        },
        defineProperty () {
            return false;
        },
        deleteProperty () {
            return false;
        }
    });
}
/**
 * Handles all {@link MobileWallet} API requests and determines the correct MWA RPC method and params to call.
 * This handles backwards compatibility, based on the provided @protocolVersion.
 *
 * @param methodName the name of {@link MobileWallet} method that was called
 * @param methodParams the parameters that were passed to the method
 * @param protocolVersion the protocol version in use for this session/request
 * @returns the RPC request method and params that should be sent to the wallet endpoint
 */ function handleMobileWalletRequest(methodName, methodParams, protocolVersion) {
    let params = methodParams;
    let method = methodName.toString().replace(/[A-Z]/g, (letter)=>`_${letter.toLowerCase()}`).toLowerCase();
    switch(methodName){
        case 'authorize':
            {
                let { chain } = params;
                if (protocolVersion === 'legacy') {
                    switch(chain){
                        case 'solana:testnet':
                            {
                                chain = 'testnet';
                                break;
                            }
                        case 'solana:devnet':
                            {
                                chain = 'devnet';
                                break;
                            }
                        case 'solana:mainnet':
                            {
                                chain = 'mainnet-beta';
                                break;
                            }
                        default:
                            {
                                chain = params.cluster;
                            }
                    }
                    params.cluster = chain;
                } else {
                    switch(chain){
                        case 'testnet':
                        case 'devnet':
                            {
                                chain = `solana:${chain}`;
                                break;
                            }
                        case 'mainnet-beta':
                            {
                                chain = 'solana:mainnet';
                                break;
                            }
                    }
                    params.chain = chain;
                }
            }
        case 'reauthorize':
            {
                const { auth_token, identity } = params;
                if (auth_token) {
                    switch(protocolVersion){
                        case 'legacy':
                            {
                                method = 'reauthorize';
                                params = {
                                    auth_token: auth_token,
                                    identity: identity
                                };
                                break;
                            }
                        default:
                            {
                                method = 'authorize';
                                break;
                            }
                    }
                }
                break;
            }
    }
    return {
        method,
        params
    };
}
/**
 * Handles all {@link MobileWallet} API responses and modifies the response for backwards compatibility, if needed
 *
 * @param method the {@link MobileWallet} method that was called
 * @param response the original response that was returned by the method call
 * @param protocolVersion the protocol version in use for this session/request
 * @returns the possibly modified response
 */ function handleMobileWalletResponse(method, response, protocolVersion) {
    switch(method){
        case 'getCapabilities':
            {
                const capabilities = response;
                switch(protocolVersion){
                    case 'legacy':
                        {
                            const features = [
                                SolanaSignTransactions
                            ];
                            if (capabilities.supports_clone_authorization === true) {
                                features.push(SolanaCloneAuthorization);
                            }
                            return Object.assign(Object.assign({}, capabilities), {
                                features: features
                            });
                        }
                    case 'v1':
                        {
                            return Object.assign(Object.assign({}, capabilities), {
                                supports_sign_and_send_transactions: true,
                                supports_clone_authorization: capabilities.features.includes(SolanaCloneAuthorization)
                            });
                        }
                }
            }
    }
    return response;
}
function signInFallback(signInPayload, authorizationResult, protocolRequestHandler) {
    var _a;
    return __awaiter(this, void 0, void 0, function*() {
        const domain = (_a = signInPayload.domain) !== null && _a !== void 0 ? _a : window.location.host;
        const address = authorizationResult.accounts[0].address;
        const siwsMessage = createSIWSMessageBase64(Object.assign(Object.assign({}, signInPayload), {
            domain,
            address
        }));
        const signMessageResult = yield protocolRequestHandler('sign_messages', {
            addresses: [
                address
            ],
            payloads: [
                siwsMessage
            ]
        });
        const signInResult = {
            address: address,
            signed_message: siwsMessage,
            signature: signMessageResult.signed_payloads[0].slice(siwsMessage.length)
        };
        return signInResult;
    });
}
const SEQUENCE_NUMBER_BYTES = 4;
function createSequenceNumberVector(sequenceNumber) {
    if (sequenceNumber >= **********) {
        throw new Error('Outbound sequence number overflow. The maximum sequence number is 32-bytes.');
    }
    const byteArray = new ArrayBuffer(SEQUENCE_NUMBER_BYTES);
    const view = new DataView(byteArray);
    view.setUint32(0, sequenceNumber, /* littleEndian */ false);
    return new Uint8Array(byteArray);
}
const INITIALIZATION_VECTOR_BYTES = 12;
const ENCODED_PUBLIC_KEY_LENGTH_BYTES = 65;
function encryptMessage(plaintext, sequenceNumber, sharedSecret) {
    return __awaiter(this, void 0, void 0, function*() {
        const sequenceNumberVector = createSequenceNumberVector(sequenceNumber);
        const initializationVector = new Uint8Array(INITIALIZATION_VECTOR_BYTES);
        crypto.getRandomValues(initializationVector);
        const ciphertext = yield crypto.subtle.encrypt(getAlgorithmParams(sequenceNumberVector, initializationVector), sharedSecret, new TextEncoder().encode(plaintext));
        const response = new Uint8Array(sequenceNumberVector.byteLength + initializationVector.byteLength + ciphertext.byteLength);
        response.set(new Uint8Array(sequenceNumberVector), 0);
        response.set(new Uint8Array(initializationVector), sequenceNumberVector.byteLength);
        response.set(new Uint8Array(ciphertext), sequenceNumberVector.byteLength + initializationVector.byteLength);
        return response;
    });
}
function decryptMessage(message, sharedSecret) {
    return __awaiter(this, void 0, void 0, function*() {
        const sequenceNumberVector = message.slice(0, SEQUENCE_NUMBER_BYTES);
        const initializationVector = message.slice(SEQUENCE_NUMBER_BYTES, SEQUENCE_NUMBER_BYTES + INITIALIZATION_VECTOR_BYTES);
        const ciphertext = message.slice(SEQUENCE_NUMBER_BYTES + INITIALIZATION_VECTOR_BYTES);
        const plaintextBuffer = yield crypto.subtle.decrypt(getAlgorithmParams(sequenceNumberVector, initializationVector), sharedSecret, ciphertext);
        const plaintext = getUtf8Decoder().decode(plaintextBuffer);
        return plaintext;
    });
}
function getAlgorithmParams(sequenceNumber, initializationVector) {
    return {
        additionalData: sequenceNumber,
        iv: initializationVector,
        name: 'AES-GCM',
        tagLength: 128
    };
}
let _utf8Decoder;
function getUtf8Decoder() {
    if (_utf8Decoder === undefined) {
        _utf8Decoder = new TextDecoder('utf-8');
    }
    return _utf8Decoder;
}
function generateAssociationKeypair() {
    return __awaiter(this, void 0, void 0, function*() {
        return yield crypto.subtle.generateKey({
            name: 'ECDSA',
            namedCurve: 'P-256'
        }, false, [
            'sign'
        ]);
    });
}
function generateECDHKeypair() {
    return __awaiter(this, void 0, void 0, function*() {
        return yield crypto.subtle.generateKey({
            name: 'ECDH',
            namedCurve: 'P-256'
        }, false, [
            'deriveKey',
            'deriveBits'
        ]);
    });
}
// https://stackoverflow.com/a/9458996/802047
function arrayBufferToBase64String(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for(let ii = 0; ii < len; ii++){
        binary += String.fromCharCode(bytes[ii]);
    }
    return window.btoa(binary);
}
function getRandomAssociationPort() {
    return assertAssociationPort(49152 + Math.floor(Math.random() * (65535 - 49152 + 1)));
}
function assertAssociationPort(port) {
    if (port < 49152 || port > 65535) {
        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_ASSOCIATION_PORT_OUT_OF_RANGE, `Association port number must be between 49152 and 65535. ${port} given.`, {
            port
        });
    }
    return port;
}
function getStringWithURLUnsafeCharactersReplaced(unsafeBase64EncodedString) {
    return unsafeBase64EncodedString.replace(/[/+=]/g, (m)=>({
            '/': '_',
            '+': '-',
            '=': '.'
        })[m]);
}
const INTENT_NAME = 'solana-wallet';
function getPathParts(pathString) {
    return pathString// Strip leading and trailing slashes
    .replace(/(^\/+|\/+$)/g, '')// Return an array of directories
    .split('/');
}
function getIntentURL(methodPathname, intentUrlBase) {
    let baseUrl = null;
    if (intentUrlBase) {
        try {
            baseUrl = new URL(intentUrlBase);
        } catch (_a) {} // eslint-disable-line no-empty
        if ((baseUrl === null || baseUrl === void 0 ? void 0 : baseUrl.protocol) !== 'https:') {
            throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, 'Base URLs supplied by wallets must be valid `https` URLs');
        }
    }
    baseUrl || (baseUrl = new URL(`${INTENT_NAME}:/`));
    const pathname = methodPathname.startsWith('/') ? methodPathname : [
        ...getPathParts(baseUrl.pathname),
        ...getPathParts(methodPathname)
    ].join('/');
    return new URL(pathname, baseUrl);
}
function getAssociateAndroidIntentURL(associationPublicKey, putativePort, associationURLBase, protocolVersions = [
    'v1'
]) {
    return __awaiter(this, void 0, void 0, function*() {
        const associationPort = assertAssociationPort(putativePort);
        const exportedKey = yield crypto.subtle.exportKey('raw', associationPublicKey);
        const encodedKey = arrayBufferToBase64String(exportedKey);
        const url = getIntentURL('v1/associate/local', associationURLBase);
        url.searchParams.set('association', getStringWithURLUnsafeCharactersReplaced(encodedKey));
        url.searchParams.set('port', `${associationPort}`);
        protocolVersions.forEach((version)=>{
            url.searchParams.set('v', version);
        });
        return url;
    });
}
function getRemoteAssociateAndroidIntentURL(associationPublicKey, hostAuthority, reflectorId, associationURLBase, protocolVersions = [
    'v1'
]) {
    return __awaiter(this, void 0, void 0, function*() {
        const exportedKey = yield crypto.subtle.exportKey('raw', associationPublicKey);
        const encodedKey = arrayBufferToBase64String(exportedKey);
        const url = getIntentURL('v1/associate/remote', associationURLBase);
        url.searchParams.set('association', getStringWithURLUnsafeCharactersReplaced(encodedKey));
        url.searchParams.set('reflector', `${hostAuthority}`);
        url.searchParams.set('id', `${fromUint8Array(reflectorId, true)}`);
        protocolVersions.forEach((version)=>{
            url.searchParams.set('v', version);
        });
        return url;
    });
}
function encryptJsonRpcMessage(jsonRpcMessage, sharedSecret) {
    return __awaiter(this, void 0, void 0, function*() {
        const plaintext = JSON.stringify(jsonRpcMessage);
        const sequenceNumber = jsonRpcMessage.id;
        return encryptMessage(plaintext, sequenceNumber, sharedSecret);
    });
}
function decryptJsonRpcMessage(message, sharedSecret) {
    return __awaiter(this, void 0, void 0, function*() {
        const plaintext = yield decryptMessage(message, sharedSecret);
        const jsonRpcMessage = JSON.parse(plaintext);
        if (Object.hasOwnProperty.call(jsonRpcMessage, 'error')) {
            throw new SolanaMobileWalletAdapterProtocolError(jsonRpcMessage.id, jsonRpcMessage.error.code, jsonRpcMessage.error.message);
        }
        return jsonRpcMessage;
    });
}
function parseHelloRsp(payloadBuffer, associationPublicKey, ecdhPrivateKey) {
    return __awaiter(this, void 0, void 0, function*() {
        const [associationPublicKeyBuffer, walletPublicKey] = yield Promise.all([
            crypto.subtle.exportKey('raw', associationPublicKey),
            crypto.subtle.importKey('raw', payloadBuffer.slice(0, ENCODED_PUBLIC_KEY_LENGTH_BYTES), {
                name: 'ECDH',
                namedCurve: 'P-256'
            }, false, [])
        ]);
        const sharedSecret = yield crypto.subtle.deriveBits({
            name: 'ECDH',
            public: walletPublicKey
        }, ecdhPrivateKey, 256);
        const ecdhSecretKey = yield crypto.subtle.importKey('raw', sharedSecret, 'HKDF', false, [
            'deriveKey'
        ]);
        const aesKeyMaterialVal = yield crypto.subtle.deriveKey({
            name: 'HKDF',
            hash: 'SHA-256',
            salt: new Uint8Array(associationPublicKeyBuffer),
            info: new Uint8Array()
        }, ecdhSecretKey, {
            name: 'AES-GCM',
            length: 128
        }, false, [
            'encrypt',
            'decrypt'
        ]);
        return aesKeyMaterialVal;
    });
}
function parseSessionProps(message, sharedSecret) {
    return __awaiter(this, void 0, void 0, function*() {
        const plaintext = yield decryptMessage(message, sharedSecret);
        const jsonProperties = JSON.parse(plaintext);
        let protocolVersion = 'legacy';
        if (Object.hasOwnProperty.call(jsonProperties, 'v')) {
            switch(jsonProperties.v){
                case 1:
                case '1':
                case 'v1':
                    protocolVersion = 'v1';
                    break;
                case 'legacy':
                    protocolVersion = 'legacy';
                    break;
                default:
                    throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_INVALID_PROTOCOL_VERSION, `Unknown/unsupported protocol version: ${jsonProperties.v}`);
            }
        }
        return {
            protocol_version: protocolVersion
        };
    });
}
// Typescript `enums` thwart tree-shaking. See https://bargsten.org/jsts/enums/
const Browser = {
    Firefox: 0,
    Other: 1
};
function assertUnreachable(x) {
    return x;
}
function getBrowser() {
    return navigator.userAgent.indexOf('Firefox/') !== -1 ? Browser.Firefox : Browser.Other;
}
function getDetectionPromise() {
    // Chrome and others silently fail if a custom protocol is not supported.
    // For these, we wait to see if the browser is navigated away from in
    // a reasonable amount of time (ie. the native wallet opened).
    return new Promise((resolve, reject)=>{
        function cleanup() {
            clearTimeout(timeoutId);
            window.removeEventListener('blur', handleBlur);
        }
        function handleBlur() {
            cleanup();
            resolve();
        }
        window.addEventListener('blur', handleBlur);
        const timeoutId = setTimeout(()=>{
            cleanup();
            reject();
        }, 3000);
    });
}
let _frame = null;
function launchUrlThroughHiddenFrame(url) {
    if (_frame == null) {
        _frame = document.createElement('iframe');
        _frame.style.display = 'none';
        document.body.appendChild(_frame);
    }
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    _frame.contentWindow.location.href = url.toString();
}
function launchAssociation(associationUrl) {
    return __awaiter(this, void 0, void 0, function*() {
        if (associationUrl.protocol === 'https:') {
            // The association URL is an Android 'App Link' or iOS 'Universal Link'.
            // These are regular web URLs that are designed to launch an app if it
            // is installed or load the actual target webpage if not.
            window.location.assign(associationUrl);
        } else {
            // The association URL has a custom protocol (eg. `solana-wallet:`)
            try {
                const browser = getBrowser();
                switch(browser){
                    case Browser.Firefox:
                        // If a custom protocol is not supported in Firefox, it throws.
                        launchUrlThroughHiddenFrame(associationUrl);
                        break;
                    case Browser.Other:
                        {
                            const detectionPromise = getDetectionPromise();
                            window.location.assign(associationUrl);
                            yield detectionPromise;
                            break;
                        }
                    default:
                        assertUnreachable(browser);
                }
            } catch (e) {
                throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_WALLET_NOT_FOUND, 'Found no installed wallet that supports the mobile wallet protocol.');
            }
        }
    });
}
function startSession(associationPublicKey, associationURLBase) {
    return __awaiter(this, void 0, void 0, function*() {
        const randomAssociationPort = getRandomAssociationPort();
        const associationUrl = yield getAssociateAndroidIntentURL(associationPublicKey, randomAssociationPort, associationURLBase);
        yield launchAssociation(associationUrl);
        return randomAssociationPort;
    });
}
const WEBSOCKET_CONNECTION_CONFIG = {
    /**
     * 300 milliseconds is a generally accepted threshold for what someone
     * would consider an acceptable response time for a user interface
     * after having performed a low-attention tapping task. We set the initial
     * interval at which we wait for the wallet to set up the websocket at
     * half this, as per the Nyquist frequency, with a progressive backoff
     * sequence from there. The total wait time is 30s, which allows for the
     * user to be presented with a disambiguation dialog, select a wallet, and
     * for the wallet app to subsequently start.
     */ retryDelayScheduleMs: [
        150,
        150,
        200,
        500,
        500,
        750,
        750,
        1000
    ],
    timeoutMs: 30000
};
const WEBSOCKET_PROTOCOL_BINARY = 'com.solana.mobilewalletadapter.v1';
const WEBSOCKET_PROTOCOL_BASE64 = 'com.solana.mobilewalletadapter.v1.base64';
function assertSecureContext() {
    if (typeof window === 'undefined' || window.isSecureContext !== true) {
        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SECURE_CONTEXT_REQUIRED, 'The mobile wallet adapter protocol must be used in a secure context (`https`).');
    }
}
function assertSecureEndpointSpecificURI(walletUriBase) {
    let url;
    try {
        url = new URL(walletUriBase);
    } catch (_a) {
        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, 'Invalid base URL supplied by wallet');
    }
    if (url.protocol !== 'https:') {
        throw new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_FORBIDDEN_WALLET_BASE_URL, 'Base URLs supplied by wallets must be valid `https` URLs');
    }
}
function getSequenceNumberFromByteArray(byteArray) {
    const view = new DataView(byteArray);
    return view.getUint32(0, /* littleEndian */ false);
}
function decodeVarLong(byteArray) {
    var bytes = new Uint8Array(byteArray), l = byteArray.byteLength, limit = 10, value = 0, offset = 0, b;
    do {
        if (offset >= l || offset > limit) throw new RangeError('Failed to decode varint');
        b = bytes[offset++];
        value |= (b & 0x7F) << 7 * offset;
    }while (b >= 0x80)
    return {
        value,
        offset
    };
}
function getReflectorIdFromByteArray(byteArray) {
    let { value: length, offset } = decodeVarLong(byteArray);
    return new Uint8Array(byteArray.slice(offset, offset + length));
}
function transact(callback, config) {
    return __awaiter(this, void 0, void 0, function*() {
        assertSecureContext();
        const associationKeypair = yield generateAssociationKeypair();
        const sessionPort = yield startSession(associationKeypair.publicKey, config === null || config === void 0 ? void 0 : config.baseUri);
        const websocketURL = `ws://localhost:${sessionPort}/solana-wallet`;
        let connectionStartTime;
        const getNextRetryDelayMs = (()=>{
            const schedule = [
                ...WEBSOCKET_CONNECTION_CONFIG.retryDelayScheduleMs
            ];
            return ()=>schedule.length > 1 ? schedule.shift() : schedule[0];
        })();
        let nextJsonRpcMessageId = 1;
        let lastKnownInboundSequenceNumber = 0;
        let state = {
            __type: 'disconnected'
        };
        return new Promise((resolve, reject)=>{
            let socket;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const jsonRpcResponsePromises = {};
            const handleOpen = ()=>__awaiter(this, void 0, void 0, function*() {
                    if (state.__type !== 'connecting') {
                        console.warn('Expected adapter state to be `connecting` at the moment the websocket opens. ' + `Got \`${state.__type}\`.`);
                        return;
                    }
                    socket.removeEventListener('open', handleOpen);
                    // previous versions of this library and walletlib incorrectly implemented the MWA session 
                    // establishment protocol for local connections. The dapp is supposed to wait for the 
                    // APP_PING message before sending the HELLO_REQ. Instead, the dapp was sending the HELLO_REQ 
                    // immediately upon connection to the websocket server regardless of wether or not an 
                    // APP_PING was sent by the wallet/websocket server. We must continue to support this behavior 
                    // in case the user is using a wallet that has not updated their walletlib implementation. 
                    const { associationKeypair } = state;
                    const ecdhKeypair = yield generateECDHKeypair();
                    socket.send((yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey)));
                    state = {
                        __type: 'hello_req_sent',
                        associationPublicKey: associationKeypair.publicKey,
                        ecdhPrivateKey: ecdhKeypair.privateKey
                    };
                });
            const handleClose = (evt)=>{
                if (evt.wasClean) {
                    state = {
                        __type: 'disconnected'
                    };
                } else {
                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session dropped unexpectedly (${evt.code}: ${evt.reason}).`, {
                        closeEvent: evt
                    }));
                }
                disposeSocket();
            };
            const handleError = (_evt)=>__awaiter(this, void 0, void 0, function*() {
                    disposeSocket();
                    if (Date.now() - connectionStartTime >= WEBSOCKET_CONNECTION_CONFIG.timeoutMs) {
                        reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_TIMEOUT, `Failed to connect to the wallet websocket at ${websocketURL}.`));
                    } else {
                        yield new Promise((resolve)=>{
                            const retryDelayMs = getNextRetryDelayMs();
                            retryWaitTimeoutId = window.setTimeout(resolve, retryDelayMs);
                        });
                        attemptSocketConnection();
                    }
                });
            const handleMessage = (evt)=>__awaiter(this, void 0, void 0, function*() {
                    const responseBuffer = yield evt.data.arrayBuffer();
                    switch(state.__type){
                        case 'connecting':
                            if (responseBuffer.byteLength !== 0) {
                                throw new Error('Encountered unexpected message while connecting');
                            }
                            const ecdhKeypair = yield generateECDHKeypair();
                            socket.send((yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey)));
                            state = {
                                __type: 'hello_req_sent',
                                associationPublicKey: associationKeypair.publicKey,
                                ecdhPrivateKey: ecdhKeypair.privateKey
                            };
                            break;
                        case 'connected':
                            try {
                                const sequenceNumberVector = responseBuffer.slice(0, SEQUENCE_NUMBER_BYTES);
                                const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);
                                if (sequenceNumber !== lastKnownInboundSequenceNumber + 1) {
                                    throw new Error('Encrypted message has invalid sequence number');
                                }
                                lastKnownInboundSequenceNumber = sequenceNumber;
                                const jsonRpcMessage = yield decryptJsonRpcMessage(responseBuffer, state.sharedSecret);
                                const responsePromise = jsonRpcResponsePromises[jsonRpcMessage.id];
                                delete jsonRpcResponsePromises[jsonRpcMessage.id];
                                responsePromise.resolve(jsonRpcMessage.result);
                            } catch (e) {
                                if (e instanceof SolanaMobileWalletAdapterProtocolError) {
                                    const responsePromise = jsonRpcResponsePromises[e.jsonRpcMessageId];
                                    delete jsonRpcResponsePromises[e.jsonRpcMessageId];
                                    responsePromise.reject(e);
                                } else {
                                    throw e;
                                }
                            }
                            break;
                        case 'hello_req_sent':
                            {
                                // if we receive an APP_PING message (empty message), resend the HELLO_REQ (see above)
                                if (responseBuffer.byteLength === 0) {
                                    const ecdhKeypair = yield generateECDHKeypair();
                                    socket.send((yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey)));
                                    state = {
                                        __type: 'hello_req_sent',
                                        associationPublicKey: associationKeypair.publicKey,
                                        ecdhPrivateKey: ecdhKeypair.privateKey
                                    };
                                    break;
                                }
                                const sharedSecret = yield parseHelloRsp(responseBuffer, state.associationPublicKey, state.ecdhPrivateKey);
                                const sessionPropertiesBuffer = responseBuffer.slice(ENCODED_PUBLIC_KEY_LENGTH_BYTES);
                                const sessionProperties = sessionPropertiesBuffer.byteLength !== 0 ? yield (()=>__awaiter(this, void 0, void 0, function*() {
                                        const sequenceNumberVector = sessionPropertiesBuffer.slice(0, SEQUENCE_NUMBER_BYTES);
                                        const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);
                                        if (sequenceNumber !== lastKnownInboundSequenceNumber + 1) {
                                            throw new Error('Encrypted message has invalid sequence number');
                                        }
                                        lastKnownInboundSequenceNumber = sequenceNumber;
                                        return parseSessionProps(sessionPropertiesBuffer, sharedSecret);
                                    }))() : {
                                    protocol_version: 'legacy'
                                };
                                state = {
                                    __type: 'connected',
                                    sharedSecret,
                                    sessionProperties
                                };
                                const wallet = createMobileWalletProxy(sessionProperties.protocol_version, (method, params)=>__awaiter(this, void 0, void 0, function*() {
                                        const id = nextJsonRpcMessageId++;
                                        socket.send((yield encryptJsonRpcMessage({
                                            id,
                                            jsonrpc: '2.0',
                                            method,
                                            params: params !== null && params !== void 0 ? params : {}
                                        }, sharedSecret)));
                                        return new Promise((resolve, reject)=>{
                                            jsonRpcResponsePromises[id] = {
                                                resolve (result) {
                                                    switch(method){
                                                        case 'authorize':
                                                        case 'reauthorize':
                                                            {
                                                                const { wallet_uri_base } = result;
                                                                if (wallet_uri_base != null) {
                                                                    try {
                                                                        assertSecureEndpointSpecificURI(wallet_uri_base);
                                                                    } catch (e) {
                                                                        reject(e);
                                                                        return;
                                                                    }
                                                                }
                                                                break;
                                                            }
                                                    }
                                                    resolve(result);
                                                },
                                                reject
                                            };
                                        });
                                    }));
                                try {
                                    resolve((yield callback(wallet)));
                                } catch (e) {
                                    reject(e);
                                } finally{
                                    disposeSocket();
                                    socket.close();
                                }
                                break;
                            }
                    }
                });
            let disposeSocket;
            let retryWaitTimeoutId;
            const attemptSocketConnection = ()=>{
                if (disposeSocket) {
                    disposeSocket();
                }
                state = {
                    __type: 'connecting',
                    associationKeypair
                };
                if (connectionStartTime === undefined) {
                    connectionStartTime = Date.now();
                }
                socket = new WebSocket(websocketURL, [
                    WEBSOCKET_PROTOCOL_BINARY
                ]);
                socket.addEventListener('open', handleOpen);
                socket.addEventListener('close', handleClose);
                socket.addEventListener('error', handleError);
                socket.addEventListener('message', handleMessage);
                disposeSocket = ()=>{
                    window.clearTimeout(retryWaitTimeoutId);
                    socket.removeEventListener('open', handleOpen);
                    socket.removeEventListener('close', handleClose);
                    socket.removeEventListener('error', handleError);
                    socket.removeEventListener('message', handleMessage);
                };
            };
            attemptSocketConnection();
        });
    });
}
function startRemoteScenario(config) {
    return __awaiter(this, void 0, void 0, function*() {
        assertSecureContext();
        const associationKeypair = yield generateAssociationKeypair();
        const websocketURL = `wss://${config === null || config === void 0 ? void 0 : config.remoteHostAuthority}/reflect`;
        let connectionStartTime;
        const getNextRetryDelayMs = (()=>{
            const schedule = [
                ...WEBSOCKET_CONNECTION_CONFIG.retryDelayScheduleMs
            ];
            return ()=>schedule.length > 1 ? schedule.shift() : schedule[0];
        })();
        let nextJsonRpcMessageId = 1;
        let lastKnownInboundSequenceNumber = 0;
        let encoding;
        let state = {
            __type: 'disconnected'
        };
        let socket;
        let disposeSocket;
        let decodeBytes = (evt)=>__awaiter(this, void 0, void 0, function*() {
                if (encoding == 'base64') {
                    const message = yield evt.data;
                    return toUint8Array(message).buffer;
                } else {
                    return yield evt.data.arrayBuffer();
                }
            });
        // Reflector Connection Phase
        // here we connect to the reflector and wait for the REFLECTOR_ID message 
        // so we build the association URL and return that back to the caller
        const associationUrl = yield new Promise((resolve, reject)=>{
            const handleOpen = ()=>__awaiter(this, void 0, void 0, function*() {
                    if (state.__type !== 'connecting') {
                        console.warn('Expected adapter state to be `connecting` at the moment the websocket opens. ' + `Got \`${state.__type}\`.`);
                        return;
                    }
                    if (socket.protocol.includes(WEBSOCKET_PROTOCOL_BASE64)) {
                        encoding = 'base64';
                    } else {
                        encoding = 'binary';
                    }
                    socket.removeEventListener('open', handleOpen);
                });
            const handleClose = (evt)=>{
                if (evt.wasClean) {
                    state = {
                        __type: 'disconnected'
                    };
                } else {
                    reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session dropped unexpectedly (${evt.code}: ${evt.reason}).`, {
                        closeEvent: evt
                    }));
                }
                disposeSocket();
            };
            const handleError = (_evt)=>__awaiter(this, void 0, void 0, function*() {
                    disposeSocket();
                    if (Date.now() - connectionStartTime >= WEBSOCKET_CONNECTION_CONFIG.timeoutMs) {
                        reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_TIMEOUT, `Failed to connect to the wallet websocket at ${websocketURL}.`));
                    } else {
                        yield new Promise((resolve)=>{
                            const retryDelayMs = getNextRetryDelayMs();
                            retryWaitTimeoutId = window.setTimeout(resolve, retryDelayMs);
                        });
                        attemptSocketConnection();
                    }
                });
            const handleReflectorIdMessage = (evt)=>__awaiter(this, void 0, void 0, function*() {
                    const responseBuffer = yield decodeBytes(evt);
                    if (state.__type === 'connecting') {
                        if (responseBuffer.byteLength == 0) {
                            throw new Error('Encountered unexpected message while connecting');
                        }
                        const reflectorId = getReflectorIdFromByteArray(responseBuffer);
                        state = {
                            __type: 'reflector_id_received',
                            reflectorId: reflectorId
                        };
                        const associationUrl = yield getRemoteAssociateAndroidIntentURL(associationKeypair.publicKey, config.remoteHostAuthority, reflectorId, config === null || config === void 0 ? void 0 : config.baseUri);
                        socket.removeEventListener('message', handleReflectorIdMessage);
                        resolve(associationUrl);
                    }
                });
            let retryWaitTimeoutId;
            const attemptSocketConnection = ()=>{
                if (disposeSocket) {
                    disposeSocket();
                }
                state = {
                    __type: 'connecting',
                    associationKeypair
                };
                if (connectionStartTime === undefined) {
                    connectionStartTime = Date.now();
                }
                socket = new WebSocket(websocketURL, [
                    WEBSOCKET_PROTOCOL_BINARY,
                    WEBSOCKET_PROTOCOL_BASE64
                ]);
                socket.addEventListener('open', handleOpen);
                socket.addEventListener('close', handleClose);
                socket.addEventListener('error', handleError);
                socket.addEventListener('message', handleReflectorIdMessage);
                disposeSocket = ()=>{
                    window.clearTimeout(retryWaitTimeoutId);
                    socket.removeEventListener('open', handleOpen);
                    socket.removeEventListener('close', handleClose);
                    socket.removeEventListener('error', handleError);
                    socket.removeEventListener('message', handleReflectorIdMessage);
                };
            };
            attemptSocketConnection();
        });
        // Wallet Connection Phase
        // here we return the association URL (containing the reflector ID) to the caller + 
        // a promise that will resolve the MobileWallet object once the wallet connects.
        let sessionEstablished = false;
        let handleClose;
        return {
            associationUrl,
            close: ()=>{
                socket.close();
                handleClose();
            },
            wallet: new Promise((resolve, reject)=>{
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const jsonRpcResponsePromises = {};
                const handleMessage = (evt)=>__awaiter(this, void 0, void 0, function*() {
                        const responseBuffer = yield decodeBytes(evt);
                        switch(state.__type){
                            case 'reflector_id_received':
                                if (responseBuffer.byteLength !== 0) {
                                    throw new Error('Encountered unexpected message while awaiting reflection');
                                }
                                const ecdhKeypair = yield generateECDHKeypair();
                                const binaryMsg = yield createHelloReq(ecdhKeypair.publicKey, associationKeypair.privateKey);
                                if (encoding == 'base64') {
                                    socket.send(fromUint8Array(binaryMsg));
                                } else {
                                    socket.send(binaryMsg);
                                }
                                state = {
                                    __type: 'hello_req_sent',
                                    associationPublicKey: associationKeypair.publicKey,
                                    ecdhPrivateKey: ecdhKeypair.privateKey
                                };
                                break;
                            case 'connected':
                                try {
                                    const sequenceNumberVector = responseBuffer.slice(0, SEQUENCE_NUMBER_BYTES);
                                    const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);
                                    if (sequenceNumber !== lastKnownInboundSequenceNumber + 1) {
                                        throw new Error('Encrypted message has invalid sequence number');
                                    }
                                    lastKnownInboundSequenceNumber = sequenceNumber;
                                    const jsonRpcMessage = yield decryptJsonRpcMessage(responseBuffer, state.sharedSecret);
                                    const responsePromise = jsonRpcResponsePromises[jsonRpcMessage.id];
                                    delete jsonRpcResponsePromises[jsonRpcMessage.id];
                                    responsePromise.resolve(jsonRpcMessage.result);
                                } catch (e) {
                                    if (e instanceof SolanaMobileWalletAdapterProtocolError) {
                                        const responsePromise = jsonRpcResponsePromises[e.jsonRpcMessageId];
                                        delete jsonRpcResponsePromises[e.jsonRpcMessageId];
                                        responsePromise.reject(e);
                                    } else {
                                        throw e;
                                    }
                                }
                                break;
                            case 'hello_req_sent':
                                {
                                    const sharedSecret = yield parseHelloRsp(responseBuffer, state.associationPublicKey, state.ecdhPrivateKey);
                                    const sessionPropertiesBuffer = responseBuffer.slice(ENCODED_PUBLIC_KEY_LENGTH_BYTES);
                                    const sessionProperties = sessionPropertiesBuffer.byteLength !== 0 ? yield (()=>__awaiter(this, void 0, void 0, function*() {
                                            const sequenceNumberVector = sessionPropertiesBuffer.slice(0, SEQUENCE_NUMBER_BYTES);
                                            const sequenceNumber = getSequenceNumberFromByteArray(sequenceNumberVector);
                                            if (sequenceNumber !== lastKnownInboundSequenceNumber + 1) {
                                                throw new Error('Encrypted message has invalid sequence number');
                                            }
                                            lastKnownInboundSequenceNumber = sequenceNumber;
                                            return parseSessionProps(sessionPropertiesBuffer, sharedSecret);
                                        }))() : {
                                        protocol_version: 'legacy'
                                    };
                                    state = {
                                        __type: 'connected',
                                        sharedSecret,
                                        sessionProperties
                                    };
                                    const wallet = createMobileWalletProxy(sessionProperties.protocol_version, (method, params)=>__awaiter(this, void 0, void 0, function*() {
                                            const id = nextJsonRpcMessageId++;
                                            const binaryMsg = yield encryptJsonRpcMessage({
                                                id,
                                                jsonrpc: '2.0',
                                                method,
                                                params: params !== null && params !== void 0 ? params : {}
                                            }, sharedSecret);
                                            if (encoding == 'base64') {
                                                socket.send(fromUint8Array(binaryMsg));
                                            } else {
                                                socket.send(binaryMsg);
                                            }
                                            return new Promise((resolve, reject)=>{
                                                jsonRpcResponsePromises[id] = {
                                                    resolve (result) {
                                                        switch(method){
                                                            case 'authorize':
                                                            case 'reauthorize':
                                                                {
                                                                    const { wallet_uri_base } = result;
                                                                    if (wallet_uri_base != null) {
                                                                        try {
                                                                            assertSecureEndpointSpecificURI(wallet_uri_base);
                                                                        } catch (e) {
                                                                            reject(e);
                                                                            return;
                                                                        }
                                                                    }
                                                                    break;
                                                                }
                                                        }
                                                        resolve(result);
                                                    },
                                                    reject
                                                };
                                            });
                                        }));
                                    sessionEstablished = true;
                                    try {
                                        resolve(wallet);
                                    } catch (e) {
                                        reject(e);
                                    }
                                    break;
                                }
                        }
                    });
                socket.addEventListener('message', handleMessage);
                handleClose = ()=>{
                    socket.removeEventListener('message', handleMessage);
                    disposeSocket();
                    if (!sessionEstablished) {
                        reject(new SolanaMobileWalletAdapterError(SolanaMobileWalletAdapterErrorCode.ERROR_SESSION_CLOSED, `The wallet session was closed before connection.`, {
                            closeEvent: new CloseEvent('socket was closed before connection')
                        }));
                    }
                };
            })
        };
    });
}
;
}}),
"[project]/node_modules/@solana-mobile/mobile-wallet-adapter-protocol-web3js/lib/esm/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "startRemoteScenario": (()=>startRemoteScenario),
    "transact": (()=>transact)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/web3.js/lib/index.esm.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$mobile$2d$wallet$2d$adapter$2d$protocol$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana-mobile/mobile-wallet-adapter-protocol/lib/esm/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bs58$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bs58/index.js [app-ssr] (ecmascript)");
;
;
;
/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */ function __rest(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
}
function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}
function fromUint8Array(byteArray) {
    return window.btoa(String.fromCharCode.call(null, ...byteArray));
}
function toUint8Array(base64EncodedByteArray) {
    return new Uint8Array(window.atob(base64EncodedByteArray).split('').map((c)=>c.charCodeAt(0)));
}
function getPayloadFromTransaction(transaction) {
    const serializedTransaction = 'version' in transaction ? transaction.serialize() : transaction.serialize({
        requireAllSignatures: false,
        verifySignatures: false
    });
    const payload = fromUint8Array(serializedTransaction);
    return payload;
}
function getTransactionFromWireMessage(byteArray) {
    const numSignatures = byteArray[0];
    const messageOffset = numSignatures * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SIGNATURE_LENGTH_IN_BYTES"] + 1;
    const version = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VersionedMessage"].deserializeMessageVersion(byteArray.slice(messageOffset, byteArray.length));
    if (version === 'legacy') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Transaction"].from(byteArray);
    } else {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VersionedTransaction"].deserialize(byteArray);
    }
}
function transact(callback, config) {
    return __awaiter(this, void 0, void 0, function*() {
        const augmentedCallback = (wallet)=>{
            return callback(augmentWalletAPI(wallet));
        };
        return yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$mobile$2d$wallet$2d$adapter$2d$protocol$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transact"])(augmentedCallback, config);
    });
}
function startRemoteScenario(config) {
    return __awaiter(this, void 0, void 0, function*() {
        const { wallet, close, associationUrl } = yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$mobile$2d$wallet$2d$adapter$2d$protocol$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["startRemoteScenario"])(config);
        const augmentedPromise = wallet.then((wallet)=>{
            return augmentWalletAPI(wallet);
        });
        return {
            wallet: augmentedPromise,
            close,
            associationUrl
        };
    });
}
function augmentWalletAPI(wallet) {
    return new Proxy({}, {
        get (target, p) {
            if (target[p] == null) {
                switch(p){
                    case 'signAndSendTransactions':
                        target[p] = function(_a) {
                            var { minContextSlot, commitment, skipPreflight, maxRetries, waitForCommitmentToSendNextTransaction, transactions } = _a, rest = __rest(_a, [
                                "minContextSlot",
                                "commitment",
                                "skipPreflight",
                                "maxRetries",
                                "waitForCommitmentToSendNextTransaction",
                                "transactions"
                            ]);
                            return __awaiter(this, void 0, void 0, function*() {
                                const payloads = transactions.map(getPayloadFromTransaction);
                                const options = {
                                    min_context_slot: minContextSlot,
                                    commitment: commitment,
                                    skip_preflight: skipPreflight,
                                    max_retries: maxRetries,
                                    wait_for_commitment_to_send_next_transaction: waitForCommitmentToSendNextTransaction
                                };
                                const { signatures: base64EncodedSignatures } = yield wallet.signAndSendTransactions(Object.assign(Object.assign(Object.assign({}, rest), Object.values(options).some((element)=>element != null) ? {
                                    options: options
                                } : null), {
                                    payloads
                                }));
                                const signatures = base64EncodedSignatures.map(toUint8Array).map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bs58$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].encode);
                                return signatures;
                            });
                        };
                        break;
                    case 'signMessages':
                        target[p] = function(_a) {
                            var { payloads } = _a, rest = __rest(_a, [
                                "payloads"
                            ]);
                            return __awaiter(this, void 0, void 0, function*() {
                                const base64EncodedPayloads = payloads.map(fromUint8Array);
                                const { signed_payloads: base64EncodedSignedMessages } = yield wallet.signMessages(Object.assign(Object.assign({}, rest), {
                                    payloads: base64EncodedPayloads
                                }));
                                const signedMessages = base64EncodedSignedMessages.map(toUint8Array);
                                return signedMessages;
                            });
                        };
                        break;
                    case 'signTransactions':
                        target[p] = function(_a) {
                            var { transactions } = _a, rest = __rest(_a, [
                                "transactions"
                            ]);
                            return __awaiter(this, void 0, void 0, function*() {
                                const payloads = transactions.map(getPayloadFromTransaction);
                                const { signed_payloads: base64EncodedCompiledTransactions } = yield wallet.signTransactions(Object.assign(Object.assign({}, rest), {
                                    payloads
                                }));
                                const compiledTransactions = base64EncodedCompiledTransactions.map(toUint8Array);
                                const signedTransactions = compiledTransactions.map(getTransactionFromWireMessage);
                                return signedTransactions;
                            });
                        };
                        break;
                    default:
                        {
                            target[p] = wallet[p];
                            break;
                        }
                }
            }
            return target[p];
        },
        defineProperty () {
            return false;
        },
        deleteProperty () {
            return false;
        }
    });
}
;
}}),
"[project]/node_modules/@solana-mobile/wallet-standard-mobile/lib/esm/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LocalSolanaMobileWalletAdapterWallet": (()=>LocalSolanaMobileWalletAdapterWallet),
    "RemoteSolanaMobileWalletAdapterWallet": (()=>RemoteSolanaMobileWalletAdapterWallet),
    "SolanaMobileWalletAdapterWalletName": (()=>SolanaMobileWalletAdapterWalletName),
    "createDefaultAuthorizationCache": (()=>createDefaultAuthorizationCache),
    "createDefaultChainSelector": (()=>createDefaultChainSelector),
    "createDefaultWalletNotFoundHandler": (()=>createDefaultWalletNotFoundHandler),
    "defaultErrorModalWalletNotFoundHandler": (()=>defaultErrorModalWalletNotFoundHandler),
    "registerMwa": (()=>registerMwa)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-features/lib/esm/signAndSendTransaction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-features/lib/esm/signTransaction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-features/lib/esm/signMessage.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signIn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-features/lib/esm/signIn.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/web3.js/lib/index.esm.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qrcode$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/qrcode/lib/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wallet$2d$standard$2f$features$2f$lib$2f$esm$2f$connect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wallet-standard/features/lib/esm/connect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wallet$2d$standard$2f$features$2f$lib$2f$esm$2f$disconnect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wallet-standard/features/lib/esm/disconnect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wallet$2d$standard$2f$features$2f$lib$2f$esm$2f$events$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@wallet-standard/features/lib/esm/events.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$chains$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-chains/lib/esm/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$mobile$2d$wallet$2d$adapter$2d$protocol$2d$web3js$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana-mobile/mobile-wallet-adapter-protocol-web3js/lib/esm/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bs58$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bs58/index.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */ function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}
function __classPrivateFieldGet$1(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}
function __classPrivateFieldSet$1(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
}
var _EmbeddedModal_instances, _EmbeddedModal_root, _EmbeddedModal_eventListeners, _EmbeddedModal_listenersAttached, _EmbeddedModal_injectHTML, _EmbeddedModal_attachEventListeners, _EmbeddedModal_removeEventListeners, _EmbeddedModal_handleKeyDown;
const modalHtml = `
<div class="mobile-wallet-adapter-embedded-modal-container" role="dialog" aria-modal="true" aria-labelledby="modal-title">
    <div data-modal-close style="position: absolute; width: 100%; height: 100%;"></div>
	<div class="mobile-wallet-adapter-embedded-modal-card">
		<div>
			<button data-modal-close class="mobile-wallet-adapter-embedded-modal-close">
				<svg width="14" height="14">
					<path d="M 6.7125,8.3036995 1.9082,13.108199 c -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 C 0.1056,12.896899 0,12.631699 0,12.312499 c 0,-0.3192 0.1056,-0.5844 0.3167,-0.7958 L 5.1212,6.7124995 0.3167,1.9082 C 0.1056,1.6969 0,1.4317 0,1.1125 0,0.7933 0.1056,0.5281 0.3167,0.3167 0.5281,0.1056 0.7933,0 1.1125,0 1.4317,0 1.6969,0.1056 1.9082,0.3167 L 6.7125,5.1212 11.5167,0.3167 C 11.7281,0.1056 11.9933,0 12.3125,0 c 0.3192,0 0.5844,0.1056 0.7957,0.3167 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 L 8.3037001,6.7124995 13.1082,11.516699 c 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 z" />
				</svg>
			</button>
		</div>
		<div class="mobile-wallet-adapter-embedded-modal-content"></div>
	</div>
</div>
`;
const css$2 = `
.mobile-wallet-adapter-embedded-modal-container {
    display: flex; /* Use flexbox to center content */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
    overflow-y: auto; /* enable scrolling */
}

.mobile-wallet-adapter-embedded-modal-card {
    display: flex;
    flex-direction: column;
    margin: auto 20px;
    max-width: 780px;
    padding: 20px;
    border-radius: 24px;
    background: #ffffff;
    font-family: "Inter Tight", "PT Sans", Calibri, sans-serif;
    transform: translateY(-200%);
    animation: slide-in 0.5s forwards;
}

@keyframes slide-in {
    100% { transform: translateY(0%); }
}

.mobile-wallet-adapter-embedded-modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: #e4e9e9;
    border: none;
    border-radius: 50%;
}

.mobile-wallet-adapter-embedded-modal-close:focus-visible {
    outline-color: red;
}

.mobile-wallet-adapter-embedded-modal-close svg {
    fill: #546266;
    transition: fill 200ms ease 0s;
}

.mobile-wallet-adapter-embedded-modal-close:hover svg {
    fill: #fff;
}
`;
const fonts = `
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
`;
class EmbeddedModal {
    constructor(){
        _EmbeddedModal_instances.add(this);
        _EmbeddedModal_root.set(this, null);
        _EmbeddedModal_eventListeners.set(this, {});
        _EmbeddedModal_listenersAttached.set(this, false);
        this.dom = null;
        this.open = ()=>{
            console.debug('Modal open');
            __classPrivateFieldGet$1(this, _EmbeddedModal_instances, "m", _EmbeddedModal_attachEventListeners).call(this);
            if (__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f")) {
                __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").style.display = 'flex';
            }
        };
        this.close = (event = undefined)=>{
            var _a;
            console.debug('Modal close');
            __classPrivateFieldGet$1(this, _EmbeddedModal_instances, "m", _EmbeddedModal_removeEventListeners).call(this);
            if (__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f")) {
                __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").style.display = 'none';
            }
            (_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")['close']) === null || _a === void 0 ? void 0 : _a.forEach((listener)=>listener(event));
        };
        _EmbeddedModal_handleKeyDown.set(this, (event)=>{
            if (event.key === 'Escape') this.close(event);
        });
        // Bind methods to ensure `this` context is correct
        this.init = this.init.bind(this);
        __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.getElementById('mobile-wallet-adapter-embedded-root-ui'), "f");
    }
    init() {
        return __awaiter(this, void 0, void 0, function*() {
            console.log('Injecting modal');
            __classPrivateFieldGet$1(this, _EmbeddedModal_instances, "m", _EmbeddedModal_injectHTML).call(this);
        });
    }
    addEventListener(event, listener) {
        var _a;
        ((_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")[event] = [
            listener
        ]);
        return ()=>this.removeEventListener(event, listener);
    }
    removeEventListener(event, listener) {
        var _a;
        __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")[event] = (_a = __classPrivateFieldGet$1(this, _EmbeddedModal_eventListeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener)=>listener !== existingListener);
    }
}
_EmbeddedModal_root = new WeakMap(), _EmbeddedModal_eventListeners = new WeakMap(), _EmbeddedModal_listenersAttached = new WeakMap(), _EmbeddedModal_handleKeyDown = new WeakMap(), _EmbeddedModal_instances = new WeakSet(), _EmbeddedModal_injectHTML = function _EmbeddedModal_injectHTML() {
    // Check if the HTML has already been injected
    if (document.getElementById('mobile-wallet-adapter-embedded-root-ui')) {
        if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f")) __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.getElementById('mobile-wallet-adapter-embedded-root-ui'), "f");
        return;
    }
    // Create a container for the modal
    __classPrivateFieldSet$1(this, _EmbeddedModal_root, document.createElement('div'), "f");
    __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").id = 'mobile-wallet-adapter-embedded-root-ui';
    __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").innerHTML = modalHtml;
    __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").style.display = 'none';
    // Add modal content
    const content = __classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").querySelector('.mobile-wallet-adapter-embedded-modal-content');
    if (content) content.innerHTML = this.contentHtml;
    // Apply styles
    const styles = document.createElement('style');
    styles.id = 'mobile-wallet-adapter-embedded-modal-styles';
    styles.textContent = css$2 + this.contentStyles;
    // Create a shadow DOM to encapsulate the modal
    const host = document.createElement('div');
    host.innerHTML = fonts;
    this.dom = host.attachShadow({
        mode: 'closed'
    });
    this.dom.appendChild(styles);
    this.dom.appendChild(__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f"));
    // Append the shadow DOM host to the body
    document.body.appendChild(host);
}, _EmbeddedModal_attachEventListeners = function _EmbeddedModal_attachEventListeners() {
    if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f") || __classPrivateFieldGet$1(this, _EmbeddedModal_listenersAttached, "f")) return;
    const closers = [
        ...__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").querySelectorAll('[data-modal-close]')
    ];
    closers.forEach((closer)=>closer === null || closer === void 0 ? void 0 : closer.addEventListener('click', this.close));
    window.addEventListener('load', this.close);
    document.addEventListener('keydown', __classPrivateFieldGet$1(this, _EmbeddedModal_handleKeyDown, "f"));
    __classPrivateFieldSet$1(this, _EmbeddedModal_listenersAttached, true, "f");
}, _EmbeddedModal_removeEventListeners = function _EmbeddedModal_removeEventListeners() {
    if (!__classPrivateFieldGet$1(this, _EmbeddedModal_listenersAttached, "f")) return;
    window.removeEventListener('load', this.close);
    document.removeEventListener('keydown', __classPrivateFieldGet$1(this, _EmbeddedModal_handleKeyDown, "f"));
    if (!__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f")) return;
    const closers = [
        ...__classPrivateFieldGet$1(this, _EmbeddedModal_root, "f").querySelectorAll('[data-modal-close]')
    ];
    closers.forEach((closer)=>closer === null || closer === void 0 ? void 0 : closer.removeEventListener('click', this.close));
    __classPrivateFieldSet$1(this, _EmbeddedModal_listenersAttached, false, "f");
};
class RemoteConnectionModal extends EmbeddedModal {
    constructor(){
        super(...arguments);
        this.contentStyles = css$1;
        this.contentHtml = QRCodeHtml;
    }
    initWithQR(qrCode) {
        const _super = Object.create(null, {
            init: {
                get: ()=>super.init
            }
        });
        return __awaiter(this, void 0, void 0, function*() {
            _super.init.call(this);
            this.populateQRCode(qrCode);
        });
    }
    populateQRCode(qrUrl) {
        var _a;
        return __awaiter(this, void 0, void 0, function*() {
            const qrcodeContainer = (_a = this.dom) === null || _a === void 0 ? void 0 : _a.getElementById('mobile-wallet-adapter-embedded-modal-qr-code-container');
            if (qrcodeContainer) {
                const qrCodeElement = yield __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qrcode$2f$lib$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].toCanvas(qrUrl, {
                    width: 200,
                    margin: 0
                });
                if (qrcodeContainer.firstElementChild !== null) {
                    qrcodeContainer.replaceChild(qrCodeElement, qrcodeContainer.firstElementChild);
                } else qrcodeContainer.appendChild(qrCodeElement);
            } else {
                console.error('QRCode Container not found');
            }
        });
    }
}
const QRCodeHtml = `
<div class="mobile-wallet-adapter-embedded-modal-qr-content">
    <div>
        <svg class="mobile-wallet-adapter-embedded-modal-icon" width="100%" height="100%">
            <circle r="52" cx="53" cy="53" fill="#99b3be" stroke="#000000" stroke-width="2"/>
            <path d="m 53,82.7305 c -3.3116,0 -6.1361,-1.169 -8.4735,-3.507 -2.338,-2.338 -3.507,-5.1625 -3.507,-8.4735 0,-3.3116 1.169,-6.1364 3.507,-8.4744 2.3374,-2.338 5.1619,-3.507 8.4735,-3.507 3.3116,0 6.1361,1.169 8.4735,3.507 2.338,2.338 3.507,5.1628 3.507,8.4744 0,3.311 -1.169,6.1355 -3.507,8.4735 -2.3374,2.338 -5.1619,3.507 -8.4735,3.507 z m 0.007,-5.25 c 1.8532,0 3.437,-0.6598 4.7512,-1.9793 1.3149,-1.3195 1.9723,-2.9058 1.9723,-4.7591 0,-1.8526 -0.6598,-3.4364 -1.9793,-4.7512 -1.3195,-1.3149 -2.9055,-1.9723 -4.7582,-1.9723 -1.8533,0 -3.437,0.6598 -4.7513,1.9793 -1.3148,1.3195 -1.9722,2.9058 -1.9722,4.7591 0,1.8527 0.6597,3.4364 1.9792,4.7512 1.3195,1.3149 2.9056,1.9723 4.7583,1.9723 z m -28,-33.5729 -3.85,-3.6347 c 4.1195,-4.025 8.8792,-7.1984 14.2791,-9.52 5.4005,-2.3223 11.2551,-3.4834 17.5639,-3.4834 6.3087,0 12.1634,1.1611 17.5639,3.4834 5.3999,2.3216 10.1596,5.495 14.2791,9.52 l -3.85,3.6347 C 77.2999,40.358 73.0684,37.5726 68.2985,35.5514 63.5292,33.5301 58.4296,32.5195 53,32.5195 c -5.4297,0 -10.5292,1.0106 -15.2985,3.0319 -4.7699,2.0212 -9.0014,4.8066 -12.6945,8.3562 z m 44.625,10.8771 c -2.2709,-2.1046 -4.7962,-3.7167 -7.5758,-4.8361 -2.7795,-1.12 -5.7983,-1.68 -9.0562,-1.68 -3.2579,0 -6.2621,0.56 -9.0125,1.68 -2.7504,1.1194 -5.2903,2.7315 -7.6195,4.8361 L 32.5189,51.15 c 2.8355,-2.6028 5.9777,-4.6086 9.4263,-6.0174 3.4481,-1.4087 7.133,-2.1131 11.0548,-2.1131 3.9217,0 7.5979,0.7044 11.0285,2.1131 3.43,1.4088 6.5631,3.4146 9.3992,6.0174 z"/>
        </svg>
        <div class="mobile-wallet-adapter-embedded-modal-title">Remote Mobile Wallet Adapter</div>
    </div>
    <div>
        <div>
            <h4 class="mobile-wallet-adapter-embedded-modal-qr-label">
                Open your wallet and scan this code
            </h4>
        </div>
        <div id="mobile-wallet-adapter-embedded-modal-qr-code-container" class="mobile-wallet-adapter-embedded-modal-qr-code-container"></div>
    </div>
</div>
<div class="mobile-wallet-adapter-embedded-modal-divider"><hr></div>
<div class="mobile-wallet-adapter-embedded-modal-footer">
    <div class="mobile-wallet-adapter-embedded-modal-subtitle">
        Follow the instructions on your device. When you're finished, this screen will update.
    </div>
    <div class="mobile-wallet-adapter-embedded-modal-progress-badge">
        <div>
            <div class="spinner">
                <div class="leftWrapper">
                    <div class="left">
                        <div class="circle"></div>
                    </div>
                </div>
                <div class="rightWrapper">
                    <div class="right">
                        <div class="circle"></div>
                    </div>
                </div>
            </div>
        </div>
        <div>Waiting for scan</div>
    </div>
</div>
`;
const css$1 = `
.mobile-wallet-adapter-embedded-modal-qr-content {
    display: flex; 
    margin-top: 10px;
    padding: 10px;
}

.mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {
    display: flex;
    flex-direction: column;
    flex: 2;
    margin-top: auto;
    margin-right: 30px;
}

.mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-left: auto;
}

.mobile-wallet-adapter-embedded-modal-footer {
    display: flex;
    padding: 10px;
}

.mobile-wallet-adapter-embedded-modal-icon {}

.mobile-wallet-adapter-embedded-modal-title {
    color: #000000;
    font-size: 2.5em;
    font-weight: 600;
}

.mobile-wallet-adapter-embedded-modal-qr-label {
    text-align: right;
    color: #000000;
}

.mobile-wallet-adapter-embedded-modal-qr-code-container {
    margin-left: auto;
}

.mobile-wallet-adapter-embedded-modal-divider {
    margin-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
}

.mobile-wallet-adapter-embedded-modal-divider hr {
    border-top: 1px solid #D9DEDE;
}

.mobile-wallet-adapter-embedded-modal-subtitle {
    margin: auto;
    margin-right: 60px;
    padding: 20px;
    color: #6E8286;
}

.mobile-wallet-adapter-embedded-modal-progress-badge {
    display: flex;
    background: #F7F8F8;
    height: 56px;
    min-width: 200px;
    margin: auto;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 18px;
    color: #A8B6B8;
    align-items: center;
}

.mobile-wallet-adapter-embedded-modal-progress-badge > div:first-child {
    margin-left: auto;
    margin-right: 20px;
}

.mobile-wallet-adapter-embedded-modal-progress-badge > div:nth-child(2) {
    margin-right: auto;
}

/* Smaller screens */
@media all and (max-width: 600px) {
    .mobile-wallet-adapter-embedded-modal-card {
        text-align: center;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content {
        flex-direction: column;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {
        margin: auto;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {
        margin: auto;
        flex: 2 auto;
    }
    .mobile-wallet-adapter-embedded-modal-footer {
        flex-direction: column;
    }
    .mobile-wallet-adapter-embedded-modal-icon {
        display: none;
    }
    .mobile-wallet-adapter-embedded-modal-title {
        font-size: 1.5em;
    }
    .mobile-wallet-adapter-embedded-modal-subtitle {
        margin-right: unset;
    }
    .mobile-wallet-adapter-embedded-modal-qr-label {
        text-align: center;
    }
    .mobile-wallet-adapter-embedded-modal-qr-code-container {
        margin: auto;
    }
}

/* Spinner */
@keyframes spinLeft {
    0% {
        transform: rotate(20deg);
    }
    50% {
        transform: rotate(160deg);
    }
    100% {
        transform: rotate(20deg);
    }
}
@keyframes spinRight {
    0% {
        transform: rotate(160deg);
    }
    50% {
        transform: rotate(20deg);
    }
    100% {
        transform: rotate(160deg);
    }
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(2520deg);
    }
}

.spinner {
    position: relative;
    width: 1.5em;
    height: 1.5em;
    margin: auto;
    animation: spin 10s linear infinite;
}
.spinner::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
.right, .rightWrapper, .left, .leftWrapper {
    position: absolute;
    top: 0;
    overflow: hidden;
    width: .75em;
    height: 1.5em;
}
.left, .leftWrapper {
    left: 0;
}
.right {
    left: -12px;
}
.rightWrapper {
    right: 0;
}
.circle {
    border: .125em solid #A8B6B8;
    width: 1.25em; /* 1.5em - 2*0.125em border */
    height: 1.25em; /* 1.5em - 2*0.125em border */
    border-radius: 0.75em; /* 0.5*1.5em spinner size 8 */
}
.left {
    transform-origin: 100% 50%;
    animation: spinLeft 2.5s cubic-bezier(.2,0,.8,1) infinite;
}
.right {
    transform-origin: 100% 50%;
    animation: spinRight 2.5s cubic-bezier(.2,0,.8,1) infinite;
}
`;
const icon = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03IDIuNUgxN0MxNy44Mjg0IDIuNSAxOC41IDMuMTcxNTcgMTguNSA0VjIwQzE4LjUgMjAuODI4NCAxNy44Mjg0IDIxLjUgMTcgMjEuNUg3QzYuMTcxNTcgMjEuNSA1LjUgMjAuODI4NCA1LjUgMjBWNEM1LjUgMy4xNzE1NyA2LjE3MTU3IDIuNSA3IDIuNVpNMyA0QzMgMS43OTA4NiA0Ljc5MDg2IDAgNyAwSDE3QzE5LjIwOTEgMCAyMSAxLjc5MDg2IDIxIDRWMjBDMjEgMjIuMjA5MSAxOS4yMDkxIDI0IDE3IDI0SDdDNC43OTA4NiAyNCAzIDIyLjIwOTEgMyAyMFY0Wk0xMSA0LjYxNTM4QzEwLjQ0NzcgNC42MTUzOCAxMCA1LjA2MzEgMTAgNS42MTUzOFY2LjM4NDYyQzEwIDYuOTM2OSAxMC40NDc3IDcuMzg0NjIgMTEgNy4zODQ2MkgxM0MxMy41NTIzIDcuMzg0NjIgMTQgNi45MzY5IDE0IDYuMzg0NjJWNS42MTUzOEMxNCA1LjA2MzEgMTMuNTUyMyA0LjYxNTM4IDEzIDQuNjE1MzhIMTFaIiBmaWxsPSIjRENCOEZGIi8+Cjwvc3ZnPgo=';
function isVersionedTransaction(transaction) {
    return 'version' in transaction;
}
function fromUint8Array(byteArray) {
    return window.btoa(String.fromCharCode.call(null, ...byteArray));
}
function toUint8Array(base64EncodedByteArray) {
    return new Uint8Array(window.atob(base64EncodedByteArray).split('').map((c)=>c.charCodeAt(0)));
}
var _LocalSolanaMobileWalletAdapterWallet_instances, _LocalSolanaMobileWalletAdapterWallet_listeners, _LocalSolanaMobileWalletAdapterWallet_version, _LocalSolanaMobileWalletAdapterWallet_name, _LocalSolanaMobileWalletAdapterWallet_url, _LocalSolanaMobileWalletAdapterWallet_icon, _LocalSolanaMobileWalletAdapterWallet_appIdentity, _LocalSolanaMobileWalletAdapterWallet_authorization, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, _LocalSolanaMobileWalletAdapterWallet_connecting, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, _LocalSolanaMobileWalletAdapterWallet_chains, _LocalSolanaMobileWalletAdapterWallet_chainSelector, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, _LocalSolanaMobileWalletAdapterWallet_on, _LocalSolanaMobileWalletAdapterWallet_emit, _LocalSolanaMobileWalletAdapterWallet_off, _LocalSolanaMobileWalletAdapterWallet_connect, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, _LocalSolanaMobileWalletAdapterWallet_disconnect, _LocalSolanaMobileWalletAdapterWallet_transact, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, _LocalSolanaMobileWalletAdapterWallet_performSignTransactions, _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, _LocalSolanaMobileWalletAdapterWallet_signTransaction, _LocalSolanaMobileWalletAdapterWallet_signMessage, _LocalSolanaMobileWalletAdapterWallet_signIn, _LocalSolanaMobileWalletAdapterWallet_performSignIn, _RemoteSolanaMobileWalletAdapterWallet_instances, _RemoteSolanaMobileWalletAdapterWallet_listeners, _RemoteSolanaMobileWalletAdapterWallet_version, _RemoteSolanaMobileWalletAdapterWallet_name, _RemoteSolanaMobileWalletAdapterWallet_url, _RemoteSolanaMobileWalletAdapterWallet_icon, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, _RemoteSolanaMobileWalletAdapterWallet_authorization, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, _RemoteSolanaMobileWalletAdapterWallet_connecting, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, _RemoteSolanaMobileWalletAdapterWallet_chains, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, _RemoteSolanaMobileWalletAdapterWallet_session, _RemoteSolanaMobileWalletAdapterWallet_on, _RemoteSolanaMobileWalletAdapterWallet_emit, _RemoteSolanaMobileWalletAdapterWallet_off, _RemoteSolanaMobileWalletAdapterWallet_connect, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, _RemoteSolanaMobileWalletAdapterWallet_disconnect, _RemoteSolanaMobileWalletAdapterWallet_transact, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions, _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, _RemoteSolanaMobileWalletAdapterWallet_signTransaction, _RemoteSolanaMobileWalletAdapterWallet_signMessage, _RemoteSolanaMobileWalletAdapterWallet_signIn, _RemoteSolanaMobileWalletAdapterWallet_performSignIn;
const SolanaMobileWalletAdapterWalletName = 'Mobile Wallet Adapter';
const SIGNATURE_LENGTH_IN_BYTES = 64;
const DEFAULT_FEATURES = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignAndSendTransaction"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignTransaction"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignMessage"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signIn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignIn"]
];
class LocalSolanaMobileWalletAdapterWallet {
    constructor(config){
        _LocalSolanaMobileWalletAdapterWallet_instances.add(this);
        _LocalSolanaMobileWalletAdapterWallet_listeners.set(this, {});
        _LocalSolanaMobileWalletAdapterWallet_version.set(this, '1.0.0'); // wallet-standard version
        _LocalSolanaMobileWalletAdapterWallet_name.set(this, SolanaMobileWalletAdapterWalletName);
        _LocalSolanaMobileWalletAdapterWallet_url.set(this, 'https://solanamobile.com/wallets');
        _LocalSolanaMobileWalletAdapterWallet_icon.set(this, icon);
        _LocalSolanaMobileWalletAdapterWallet_appIdentity.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_authorization.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_authorizationCache.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_connecting.set(this, false);
        /**
         * Every time the connection is recycled in some way (eg. `disconnect()` is called)
         * increment this and use it to make sure that `transact` calls from the previous
         * 'generation' don't continue to do work and throw exceptions.
         */ _LocalSolanaMobileWalletAdapterWallet_connectionGeneration.set(this, 0);
        _LocalSolanaMobileWalletAdapterWallet_chains.set(this, []);
        _LocalSolanaMobileWalletAdapterWallet_chainSelector.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_optionalFeatures.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound.set(this, void 0);
        _LocalSolanaMobileWalletAdapterWallet_on.set(this, (event, listener)=>{
            var _a;
            ((_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event] = [
                listener
            ]);
            return ()=>__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, "m", _LocalSolanaMobileWalletAdapterWallet_off).call(this, event, listener);
        });
        _LocalSolanaMobileWalletAdapterWallet_connect.set(this, ({ silent } = {})=>__awaiter(this, void 0, void 0, function*() {
                if (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, "f") || this.connected) {
                    return {
                        accounts: this.accounts
                    };
                }
                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, true, "f");
                try {
                    if (silent) {
                        const cachedAuthorization = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
                        if (cachedAuthorization) {
                            yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, cachedAuthorization);
                        } else {
                            return {
                                accounts: this.accounts
                            };
                        }
                    } else {
                        yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, "f").call(this);
                    }
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                } finally{
                    __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, "f");
                }
                return {
                    accounts: this.accounts
                };
            }));
        _LocalSolanaMobileWalletAdapterWallet_performAuthorization.set(this, (signInPayload)=>__awaiter(this, void 0, void 0, function*() {
                try {
                    const cachedAuthorizationResult = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
                    if (cachedAuthorizationResult) {
                        // TODO: Evaluate whether there's any threat to not `awaiting` this expression
                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, cachedAuthorizationResult);
                        return cachedAuthorizationResult;
                    }
                    const selectedChain = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chainSelector, "f").select(__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, "f"));
                    return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet)=>__awaiter(this, void 0, void 0, function*() {
                            const [capabilities, mwaAuthorizationResult] = yield Promise.all([
                                wallet.getCapabilities(),
                                wallet.authorize({
                                    chain: selectedChain,
                                    identity: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, "f"),
                                    sign_in_payload: signInPayload
                                })
                            ]);
                            const accounts = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, "f").call(this, mwaAuthorizationResult.accounts);
                            const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), {
                                accounts,
                                chain: selectedChain
                            });
                            // TODO: Evaluate whether there's any threat to not `awaiting` this expression
                            Promise.all([
                                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, "f").call(this, capabilities),
                                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").set(authorization),
                                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, authorization)
                            ]);
                            return authorization;
                        }));
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult.set(this, (authorization)=>__awaiter(this, void 0, void 0, function*() {
                var _a;
                const didPublicKeysChange = // Case 1: We started from having no authorization.
                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f") == null || // Case 2: The number of authorized accounts changed.
                ((_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _a === void 0 ? void 0 : _a.accounts.length) !== authorization.accounts.length || // Case 3: The new list of addresses isn't exactly the same as the old list, in the same order.
                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f").accounts.some((account, ii)=>account.address !== authorization.accounts[ii].address);
                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, authorization, "f");
                if (didPublicKeysChange) {
                    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, "m", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, 'change', {
                        accounts: this.accounts
                    });
                }
            }));
        _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult.set(this, (capabilities)=>__awaiter(this, void 0, void 0, function*() {
                // TODO: investigate why using SolanaSignTransactions constant breaks treeshaking
                const supportsSignTransaction = capabilities.features.includes('solana:signTransactions'); //SolanaSignTransactions);
                const supportsSignAndSendTransaction = capabilities.supports_sign_and_send_transactions;
                const didCapabilitiesChange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignAndSendTransaction"] in this.features !== supportsSignAndSendTransaction || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignTransaction"] in this.features !== supportsSignTransaction;
                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, Object.assign(Object.assign({}, (supportsSignAndSendTransaction || !supportsSignAndSendTransaction && !supportsSignTransaction) && {
                    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignAndSendTransaction"]]: {
                        version: '1.0.0',
                        supportedTransactionVersions: [
                            'legacy',
                            0
                        ],
                        signAndSendTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, "f")
                    }
                }), supportsSignTransaction && {
                    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignTransaction"]]: {
                        version: '1.0.0',
                        supportedTransactionVersions: [
                            'legacy',
                            0
                        ],
                        signTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signTransaction, "f")
                    }
                }), "f");
                if (didCapabilitiesChange) {
                    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, "m", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, 'change', {
                        features: this.features
                    });
                }
            }));
        _LocalSolanaMobileWalletAdapterWallet_performReauthorization.set(this, (wallet, authToken, chain)=>__awaiter(this, void 0, void 0, function*() {
                try {
                    const mwaAuthorizationResult = yield wallet.authorize({
                        auth_token: authToken,
                        identity: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, "f"),
                        chain: chain
                    });
                    const accounts = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, "f").call(this, mwaAuthorizationResult.accounts);
                    const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), {
                        accounts: accounts,
                        chain: chain
                    });
                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression
                    Promise.all([
                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").set(authorization),
                        __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, authorization)
                    ]);
                } catch (e) {
                    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_disconnect, "f").call(this);
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _LocalSolanaMobileWalletAdapterWallet_disconnect.set(this, ()=>__awaiter(this, void 0, void 0, function*() {
                var _b;
                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").clear(); // TODO: Evaluate whether there's any threat to not `awaiting` this expression
                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, "f");
                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, (_b = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, "f"), _b++, _b), "f");
                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, undefined, "f");
                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_instances, "m", _LocalSolanaMobileWalletAdapterWallet_emit).call(this, 'change', {
                    accounts: this.accounts
                });
            }));
        _LocalSolanaMobileWalletAdapterWallet_transact.set(this, (callback)=>__awaiter(this, void 0, void 0, function*() {
                var _c;
                const walletUriBase = (_c = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _c === void 0 ? void 0 : _c.wallet_uri_base;
                const config = walletUriBase ? {
                    baseUri: walletUriBase
                } : undefined;
                const currentConnectionGeneration = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, "f");
                try {
                    return yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$mobile$2d$wallet$2d$adapter$2d$protocol$2d$web3js$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transact"])(callback, config);
                } catch (e) {
                    if (__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connectionGeneration, "f") !== currentConnectionGeneration) {
                        yield new Promise(()=>{}); // Never resolve.
                    }
                    if (e instanceof Error && e.name === 'SolanaMobileWalletAdapterError' && e.code === 'ERROR_WALLET_NOT_FOUND') {
                        yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, "f").call(this, this);
                    }
                    throw e;
                }
            }));
        _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized.set(this, ()=>{
            if (!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f")) throw new Error('Wallet not connected');
            return {
                authToken: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f").auth_token,
                chain: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f").chain
            };
        });
        _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts.set(this, (accounts)=>{
            return accounts.map((account)=>{
                var _a, _b;
                const publicKey = toUint8Array(account.address);
                return {
                    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bs58$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].encode(publicKey),
                    publicKey,
                    label: account.label,
                    icon: account.icon,
                    chains: (_a = account.chains) !== null && _a !== void 0 ? _a : __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, "f"),
                    // TODO: get supported features from getCapabilities API 
                    features: (_b = account.features) !== null && _b !== void 0 ? _b : DEFAULT_FEATURES
                };
            });
        });
        _LocalSolanaMobileWalletAdapterWallet_performSignTransactions.set(this, (transactions)=>__awaiter(this, void 0, void 0, function*() {
                const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
                try {
                    return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet)=>__awaiter(this, void 0, void 0, function*() {
                            yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain);
                            const signedTransactions = yield wallet.signTransactions({
                                transactions
                            });
                            return signedTransactions;
                        }));
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction.set(this, (transaction, options)=>__awaiter(this, void 0, void 0, function*() {
                const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
                try {
                    return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet)=>__awaiter(this, void 0, void 0, function*() {
                            const [capabilities, _1] = yield Promise.all([
                                wallet.getCapabilities(),
                                __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain)
                            ]);
                            if (capabilities.supports_sign_and_send_transactions) {
                                const signatures = yield wallet.signAndSendTransactions(Object.assign(Object.assign({}, options), {
                                    transactions: [
                                        transaction
                                    ]
                                }));
                                return signatures[0];
                            } else {
                                throw new Error('connected wallet does not support signAndSendTransaction');
                            }
                        }));
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction.set(this, (...inputs)=>__awaiter(this, void 0, void 0, function*() {
                const outputs = [];
                for (const input of inputs){
                    const transaction = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VersionedTransaction"].deserialize(input.transaction);
                    const signature = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, "f").call(this, transaction, input.options);
                    outputs.push({
                        signature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bs58$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decode(signature)
                    });
                }
                return outputs;
            }));
        _LocalSolanaMobileWalletAdapterWallet_signTransaction.set(this, (...inputs)=>__awaiter(this, void 0, void 0, function*() {
                const transactions = inputs.map(({ transaction })=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VersionedTransaction"].deserialize(transaction));
                const signedTransactions = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignTransactions, "f").call(this, transactions);
                return signedTransactions.map((signedTransaction)=>{
                    const serializedTransaction = isVersionedTransaction(signedTransaction) ? signedTransaction.serialize() : new Uint8Array(signedTransaction.serialize({
                        requireAllSignatures: false,
                        verifySignatures: false
                    }));
                    return {
                        signedTransaction: serializedTransaction
                    };
                });
            }));
        _LocalSolanaMobileWalletAdapterWallet_signMessage.set(this, (...inputs)=>__awaiter(this, void 0, void 0, function*() {
                const { authToken, chain } = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
                const addresses = inputs.map(({ account })=>fromUint8Array(account.publicKey));
                const messages = inputs.map(({ message })=>message);
                try {
                    return yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet)=>__awaiter(this, void 0, void 0, function*() {
                            yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain);
                            const signedMessages = yield wallet.signMessages({
                                addresses: addresses,
                                payloads: messages
                            });
                            return signedMessages.map((signedMessage)=>{
                                return {
                                    signedMessage: signedMessage,
                                    signature: signedMessage.slice(-SIGNATURE_LENGTH_IN_BYTES)
                                };
                            });
                        }));
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _LocalSolanaMobileWalletAdapterWallet_signIn.set(this, (...inputs)=>__awaiter(this, void 0, void 0, function*() {
                const outputs = [];
                if (inputs.length > 1) {
                    for (const input of inputs){
                        outputs.push((yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignIn, "f").call(this, input)));
                    }
                } else {
                    return [
                        (yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performSignIn, "f").call(this, inputs[0]))
                    ];
                }
                return outputs;
            }));
        _LocalSolanaMobileWalletAdapterWallet_performSignIn.set(this, (input)=>__awaiter(this, void 0, void 0, function*() {
                var _d, _e;
                __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, true, "f");
                try {
                    const authorizationResult = yield __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_performAuthorization, "f").call(this, Object.assign(Object.assign({}, input), {
                        domain: (_d = input === null || input === void 0 ? void 0 : input.domain) !== null && _d !== void 0 ? _d : window.location.host
                    }));
                    if (!authorizationResult.sign_in_result) {
                        throw new Error("Sign in failed, no sign in result returned by wallet");
                    }
                    const signedInAddress = authorizationResult.sign_in_result.address;
                    const signedInAccount = Object.assign(Object.assign({}, (_e = authorizationResult.accounts.find((acc)=>acc.address == signedInAddress)) !== null && _e !== void 0 ? _e : {
                        address: signedInAddress
                    }), {
                        publicKey: toUint8Array(signedInAddress)
                    });
                    return {
                        account: signedInAccount,
                        signedMessage: toUint8Array(authorizationResult.sign_in_result.signed_message),
                        signature: toUint8Array(authorizationResult.sign_in_result.signature)
                    };
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                } finally{
                    __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_connecting, false, "f");
                }
            }));
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, config.authorizationCache, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_appIdentity, config.appIdentity, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, config.chains, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_chainSelector, config.chainSelector, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound, config.onWalletNotFound, "f");
        __classPrivateFieldSet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, {
            // We are forced to provide either SolanaSignAndSendTransaction or SolanaSignTransaction
            // because the wallet-adapter compatible wallet-standard wallet requires at least one of them.
            // MWA 2.0+ wallets must implement signAndSend and pre 2.0 wallets have always provided it so 
            // this is a safe assumption. We later update the features after we get the wallets capabilities. 
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignAndSendTransaction"]]: {
                version: '1.0.0',
                supportedTransactionVersions: [
                    'legacy',
                    0
                ],
                signAndSendTransaction: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction, "f")
            }
        }, "f");
    }
    get version() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_version, "f");
    }
    get name() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_name, "f");
    }
    get url() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_url, "f");
    }
    get icon() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_icon, "f");
    }
    get chains() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_chains, "f");
    }
    get features() {
        return Object.assign({
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wallet$2d$standard$2f$features$2f$lib$2f$esm$2f$connect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StandardConnect"]]: {
                version: '1.0.0',
                connect: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_connect, "f")
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wallet$2d$standard$2f$features$2f$lib$2f$esm$2f$disconnect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StandardDisconnect"]]: {
                version: '1.0.0',
                disconnect: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_disconnect, "f")
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wallet$2d$standard$2f$features$2f$lib$2f$esm$2f$events$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StandardEvents"]]: {
                version: '1.0.0',
                on: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_on, "f")
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignMessage"]]: {
                version: '1.0.0',
                signMessage: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signMessage, "f")
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signIn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignIn"]]: {
                version: '1.0.0',
                signIn: __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_signIn, "f")
            }
        }, __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_optionalFeatures, "f"));
    }
    get accounts() {
        var _a, _b;
        return (_b = (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _a === void 0 ? void 0 : _a.accounts) !== null && _b !== void 0 ? _b : [];
    }
    get connected() {
        return !!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f");
    }
    get isAuthorized() {
        return !!__classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f");
    }
    get currentAuthorization() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorization, "f");
    }
    get cachedAuthorizationResult() {
        return __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
    }
}
_LocalSolanaMobileWalletAdapterWallet_listeners = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_version = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_name = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_url = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_icon = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_appIdentity = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_authorization = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_authorizationCache = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connecting = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connectionGeneration = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_chains = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_chainSelector = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_optionalFeatures = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_onWalletNotFound = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_on = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_connect = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performAuthorization = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_handleAuthorizationResult = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performReauthorization = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_disconnect = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_transact = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_assertIsAuthorized = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignTransactions = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignAndSendTransaction = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signAndSendTransaction = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signTransaction = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signMessage = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_signIn = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_performSignIn = new WeakMap(), _LocalSolanaMobileWalletAdapterWallet_instances = new WeakSet(), _LocalSolanaMobileWalletAdapterWallet_emit = function _LocalSolanaMobileWalletAdapterWallet_emit(event, ...args) {
    var _a;
    // eslint-disable-next-line prefer-spread
    (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.forEach((listener)=>listener.apply(null, args));
}, _LocalSolanaMobileWalletAdapterWallet_off = function _LocalSolanaMobileWalletAdapterWallet_off(event, listener) {
    var _a;
    __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event] = (_a = __classPrivateFieldGet$1(this, _LocalSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener)=>listener !== existingListener);
};
class RemoteSolanaMobileWalletAdapterWallet {
    constructor(config){
        _RemoteSolanaMobileWalletAdapterWallet_instances.add(this);
        _RemoteSolanaMobileWalletAdapterWallet_listeners.set(this, {});
        _RemoteSolanaMobileWalletAdapterWallet_version.set(this, '1.0.0'); // wallet-standard version
        _RemoteSolanaMobileWalletAdapterWallet_name.set(this, SolanaMobileWalletAdapterWalletName);
        _RemoteSolanaMobileWalletAdapterWallet_url.set(this, 'https://solanamobile.com/wallets');
        _RemoteSolanaMobileWalletAdapterWallet_icon.set(this, icon);
        _RemoteSolanaMobileWalletAdapterWallet_appIdentity.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_authorization.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_authorizationCache.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_connecting.set(this, false);
        /**
         * Every time the connection is recycled in some way (eg. `disconnect()` is called)
         * increment this and use it to make sure that `transact` calls from the previous
         * 'generation' don't continue to do work and throw exceptions.
         */ _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration.set(this, 0);
        _RemoteSolanaMobileWalletAdapterWallet_chains.set(this, []);
        _RemoteSolanaMobileWalletAdapterWallet_chainSelector.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_hostAuthority.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_session.set(this, void 0);
        _RemoteSolanaMobileWalletAdapterWallet_on.set(this, (event, listener)=>{
            var _a;
            ((_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.push(listener)) || (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event] = [
                listener
            ]);
            return ()=>__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, "m", _RemoteSolanaMobileWalletAdapterWallet_off).call(this, event, listener);
        });
        _RemoteSolanaMobileWalletAdapterWallet_connect.set(this, ({ silent } = {})=>__awaiter(this, void 0, void 0, function*() {
                if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, "f") || this.connected) {
                    return {
                        accounts: this.accounts
                    };
                }
                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, true, "f");
                try {
                    yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, "f").call(this);
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                } finally{
                    __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, "f");
                }
                return {
                    accounts: this.accounts
                };
            }));
        _RemoteSolanaMobileWalletAdapterWallet_performAuthorization.set(this, (signInPayload)=>__awaiter(this, void 0, void 0, function*() {
                try {
                    const cachedAuthorizationResult = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
                    if (cachedAuthorizationResult) {
                        // TODO: Evaluate whether there's any threat to not `awaiting` this expression
                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, cachedAuthorizationResult);
                        return cachedAuthorizationResult;
                    }
                    if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f")) __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, undefined, "f");
                    const selectedChain = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, "f").select(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, "f"));
                    return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet)=>__awaiter(this, void 0, void 0, function*() {
                            const [capabilities, mwaAuthorizationResult] = yield Promise.all([
                                wallet.getCapabilities(),
                                wallet.authorize({
                                    chain: selectedChain,
                                    identity: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, "f"),
                                    sign_in_payload: signInPayload
                                })
                            ]);
                            const accounts = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, "f").call(this, mwaAuthorizationResult.accounts);
                            const authorizationResult = Object.assign(Object.assign({}, mwaAuthorizationResult), {
                                accounts,
                                chain: selectedChain
                            });
                            // TODO: Evaluate whether there's any threat to not `awaiting` this expression
                            Promise.all([
                                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult, "f").call(this, capabilities),
                                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").set(authorizationResult),
                                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, authorizationResult)
                            ]);
                            return authorizationResult;
                        }));
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult.set(this, (authorization)=>__awaiter(this, void 0, void 0, function*() {
                var _a;
                const didPublicKeysChange = // Case 1: We started from having no authorization.
                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f") == null || // Case 2: The number of authorized accounts changed.
                ((_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _a === void 0 ? void 0 : _a.accounts.length) !== authorization.accounts.length || // Case 3: The new list of addresses isn't exactly the same as the old list, in the same order.
                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f").accounts.some((account, ii)=>account.address !== authorization.accounts[ii].address);
                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, authorization, "f");
                if (didPublicKeysChange) {
                    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, "m", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, 'change', {
                        accounts: this.accounts
                    });
                }
            }));
        _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult.set(this, (capabilities)=>__awaiter(this, void 0, void 0, function*() {
                // TODO: investigate why using SolanaSignTransactions constant breaks treeshaking
                const supportsSignTransaction = capabilities.features.includes('solana:signTransactions'); //SolanaSignTransactions);
                const supportsSignAndSendTransaction = capabilities.supports_sign_and_send_transactions || capabilities.features.includes('solana:signAndSendTransaction');
                const didCapabilitiesChange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignAndSendTransaction"] in this.features !== supportsSignAndSendTransaction || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignTransaction"] in this.features !== supportsSignTransaction;
                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, Object.assign(Object.assign({}, supportsSignAndSendTransaction && {
                    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignAndSendTransaction"]]: {
                        version: '1.0.0',
                        supportedTransactionVersions: capabilities.supported_transaction_versions,
                        signAndSendTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, "f")
                    }
                }), supportsSignTransaction && {
                    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignTransaction"]]: {
                        version: '1.0.0',
                        supportedTransactionVersions: capabilities.supported_transaction_versions,
                        signTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signTransaction, "f")
                    }
                }), "f");
                if (didCapabilitiesChange) {
                    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, "m", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, 'change', {
                        features: this.features
                    });
                }
            }));
        _RemoteSolanaMobileWalletAdapterWallet_performReauthorization.set(this, (wallet, authToken, chain)=>__awaiter(this, void 0, void 0, function*() {
                try {
                    const mwaAuthorizationResult = yield wallet.authorize({
                        auth_token: authToken,
                        identity: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, "f")
                    });
                    const accounts = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts, "f").call(this, mwaAuthorizationResult.accounts);
                    const authorization = Object.assign(Object.assign({}, mwaAuthorizationResult), {
                        accounts: accounts,
                        chain: chain
                    });
                    // TODO: Evaluate whether there's any threat to not `awaiting` this expression
                    Promise.all([
                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").set(authorization),
                        __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult, "f").call(this, authorization)
                    ]);
                } catch (e) {
                    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_disconnect, "f").call(this);
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _RemoteSolanaMobileWalletAdapterWallet_disconnect.set(this, ()=>__awaiter(this, void 0, void 0, function*() {
                var _b;
                var _c;
                (_b = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f")) === null || _b === void 0 ? void 0 : _b.close();
                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").clear(); // TODO: Evaluate whether there's any threat to not `awaiting` this expression
                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, "f");
                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, (_c = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, "f"), _c++, _c), "f");
                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, undefined, "f");
                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, undefined, "f");
                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_instances, "m", _RemoteSolanaMobileWalletAdapterWallet_emit).call(this, 'change', {
                    accounts: this.accounts
                });
            }));
        _RemoteSolanaMobileWalletAdapterWallet_transact.set(this, (callback)=>__awaiter(this, void 0, void 0, function*() {
                var _d;
                const walletUriBase = (_d = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _d === void 0 ? void 0 : _d.wallet_uri_base;
                const baseConfig = walletUriBase ? {
                    baseUri: walletUriBase
                } : undefined;
                const remoteConfig = Object.assign(Object.assign({}, baseConfig), {
                    remoteHostAuthority: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, "f")
                });
                const currentConnectionGeneration = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, "f");
                const modal = new RemoteConnectionModal();
                if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f")) {
                    return callback(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f").wallet);
                }
                try {
                    const { associationUrl, close, wallet } = yield (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$mobile$2d$wallet$2d$adapter$2d$protocol$2d$web3js$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["startRemoteScenario"])(remoteConfig);
                    const removeCloseListener = modal.addEventListener('close', (event)=>{
                        if (event) close();
                    });
                    modal.initWithQR(associationUrl.toString());
                    modal.open();
                    __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, {
                        close,
                        wallet: yield wallet
                    }, "f");
                    removeCloseListener();
                    modal.close();
                    return yield callback(__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f").wallet);
                } catch (e) {
                    modal.close();
                    if (__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration, "f") !== currentConnectionGeneration) {
                        yield new Promise(()=>{}); // Never resolve.
                    }
                    if (e instanceof Error && e.name === 'SolanaMobileWalletAdapterError' && e.code === 'ERROR_WALLET_NOT_FOUND') {
                        yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, "f").call(this, this);
                    }
                    throw e;
                }
            }));
        _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized.set(this, ()=>{
            if (!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f")) throw new Error('Wallet not connected');
            return {
                authToken: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f").auth_token,
                chain: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f").chain
            };
        });
        _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts.set(this, (accounts)=>{
            return accounts.map((account)=>{
                var _a, _b;
                const publicKey = toUint8Array(account.address);
                return {
                    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bs58$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].encode(publicKey),
                    publicKey,
                    label: account.label,
                    icon: account.icon,
                    chains: (_a = account.chains) !== null && _a !== void 0 ? _a : __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, "f"),
                    // TODO: get supported features from getCapabilities API 
                    features: (_b = account.features) !== null && _b !== void 0 ? _b : DEFAULT_FEATURES
                };
            });
        });
        _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions.set(this, (transactions)=>__awaiter(this, void 0, void 0, function*() {
                const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
                try {
                    return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet)=>__awaiter(this, void 0, void 0, function*() {
                            yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain);
                            const signedTransactions = yield wallet.signTransactions({
                                transactions
                            });
                            return signedTransactions;
                        }));
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction.set(this, (transaction, options)=>__awaiter(this, void 0, void 0, function*() {
                const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
                try {
                    return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet)=>__awaiter(this, void 0, void 0, function*() {
                            const [capabilities, _1] = yield Promise.all([
                                wallet.getCapabilities(),
                                __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain)
                            ]);
                            if (capabilities.supports_sign_and_send_transactions) {
                                const signatures = yield wallet.signAndSendTransactions(Object.assign(Object.assign({}, options), {
                                    transactions: [
                                        transaction
                                    ]
                                }));
                                return signatures[0];
                            } else {
                                throw new Error('connected wallet does not support signAndSendTransaction');
                            }
                        }));
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction.set(this, (...inputs)=>__awaiter(this, void 0, void 0, function*() {
                const outputs = [];
                for (const input of inputs){
                    const transaction = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VersionedTransaction"].deserialize(input.transaction);
                    const signature = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction, "f").call(this, transaction, input.options);
                    outputs.push({
                        signature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bs58$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].decode(signature)
                    });
                }
                return outputs;
            }));
        _RemoteSolanaMobileWalletAdapterWallet_signTransaction.set(this, (...inputs)=>__awaiter(this, void 0, void 0, function*() {
                const transactions = inputs.map(({ transaction })=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VersionedTransaction"].deserialize(transaction));
                const signedTransactions = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions, "f").call(this, transactions);
                return signedTransactions.map((signedTransaction)=>{
                    const serializedTransaction = isVersionedTransaction(signedTransaction) ? signedTransaction.serialize() : new Uint8Array(signedTransaction.serialize({
                        requireAllSignatures: false,
                        verifySignatures: false
                    }));
                    return {
                        signedTransaction: serializedTransaction
                    };
                });
            }));
        _RemoteSolanaMobileWalletAdapterWallet_signMessage.set(this, (...inputs)=>__awaiter(this, void 0, void 0, function*() {
                const { authToken, chain } = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized, "f").call(this);
                const addresses = inputs.map(({ account })=>fromUint8Array(account.publicKey));
                const messages = inputs.map(({ message })=>message);
                try {
                    return yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_transact, "f").call(this, (wallet)=>__awaiter(this, void 0, void 0, function*() {
                            yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performReauthorization, "f").call(this, wallet, authToken, chain);
                            const signedMessages = yield wallet.signMessages({
                                addresses: addresses,
                                payloads: messages
                            });
                            return signedMessages.map((signedMessage)=>{
                                return {
                                    signedMessage: signedMessage,
                                    signature: signedMessage.slice(-SIGNATURE_LENGTH_IN_BYTES)
                                };
                            });
                        }));
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                }
            }));
        _RemoteSolanaMobileWalletAdapterWallet_signIn.set(this, (...inputs)=>__awaiter(this, void 0, void 0, function*() {
                const outputs = [];
                if (inputs.length > 1) {
                    for (const input of inputs){
                        outputs.push((yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignIn, "f").call(this, input)));
                    }
                } else {
                    return [
                        (yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performSignIn, "f").call(this, inputs[0]))
                    ];
                }
                return outputs;
            }));
        _RemoteSolanaMobileWalletAdapterWallet_performSignIn.set(this, (input)=>__awaiter(this, void 0, void 0, function*() {
                var _e, _f;
                __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, true, "f");
                try {
                    const authorizationResult = yield __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_performAuthorization, "f").call(this, Object.assign(Object.assign({}, input), {
                        domain: (_e = input === null || input === void 0 ? void 0 : input.domain) !== null && _e !== void 0 ? _e : window.location.host
                    }));
                    if (!authorizationResult.sign_in_result) {
                        throw new Error("Sign in failed, no sign in result returned by wallet");
                    }
                    const signedInAddress = authorizationResult.sign_in_result.address;
                    const signedInAccount = Object.assign(Object.assign({}, (_f = authorizationResult.accounts.find((acc)=>acc.address == signedInAddress)) !== null && _f !== void 0 ? _f : {
                        address: signedInAddress
                    }), {
                        publicKey: toUint8Array(signedInAddress)
                    });
                    return {
                        account: signedInAccount,
                        signedMessage: toUint8Array(authorizationResult.sign_in_result.signed_message),
                        signature: toUint8Array(authorizationResult.sign_in_result.signature)
                    };
                } catch (e) {
                    throw new Error(e instanceof Error && e.message || 'Unknown error');
                } finally{
                    __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connecting, false, "f");
                }
            }));
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, config.authorizationCache, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_appIdentity, config.appIdentity, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, config.chains, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chainSelector, config.chainSelector, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_hostAuthority, config.remoteHostAuthority, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound, config.onWalletNotFound, "f");
        __classPrivateFieldSet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, {
            // We are forced to provide either SolanaSignAndSendTransaction or SolanaSignTransaction
            // because the wallet-adapter compatible wallet-standard wallet requires at least one of them.
            // MWA 2.0+ wallets must implement signAndSend and pre 2.0 wallets have always provided it so 
            // this is a safe assumption. We later update the features after we get the wallets capabilities. 
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignAndSendTransaction"]]: {
                version: '1.0.0',
                supportedTransactionVersions: [
                    'legacy',
                    0
                ],
                signAndSendTransaction: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction, "f")
            }
        }, "f");
    }
    get version() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_version, "f");
    }
    get name() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_name, "f");
    }
    get url() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_url, "f");
    }
    get icon() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_icon, "f");
    }
    get chains() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_chains, "f");
    }
    get features() {
        return Object.assign({
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wallet$2d$standard$2f$features$2f$lib$2f$esm$2f$connect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StandardConnect"]]: {
                version: '1.0.0',
                connect: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_connect, "f")
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wallet$2d$standard$2f$features$2f$lib$2f$esm$2f$disconnect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StandardDisconnect"]]: {
                version: '1.0.0',
                disconnect: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_disconnect, "f")
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$wallet$2d$standard$2f$features$2f$lib$2f$esm$2f$events$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StandardEvents"]]: {
                version: '1.0.0',
                on: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_on, "f")
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignMessage"]]: {
                version: '1.0.0',
                signMessage: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signMessage, "f")
            },
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signIn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignIn"]]: {
                version: '1.0.0',
                signIn: __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_signIn, "f")
            }
        }, __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures, "f"));
    }
    get accounts() {
        var _a, _b;
        return (_b = (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f")) === null || _a === void 0 ? void 0 : _a.accounts) !== null && _b !== void 0 ? _b : [];
    }
    get connected() {
        return !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_session, "f") && !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f");
    }
    get isAuthorized() {
        return !!__classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f");
    }
    get currentAuthorization() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorization, "f");
    }
    get cachedAuthorizationResult() {
        return __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_authorizationCache, "f").get();
    }
}
_RemoteSolanaMobileWalletAdapterWallet_listeners = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_version = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_name = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_url = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_icon = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_appIdentity = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_authorization = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_authorizationCache = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connecting = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connectionGeneration = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_chains = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_chainSelector = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_optionalFeatures = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_onWalletNotFound = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_hostAuthority = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_session = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_on = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_connect = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performAuthorization = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_handleAuthorizationResult = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_handleWalletCapabilitiesResult = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performReauthorization = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_disconnect = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_transact = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_assertIsAuthorized = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_accountsToWalletStandardAccounts = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignTransactions = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignAndSendTransaction = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signAndSendTransaction = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signTransaction = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signMessage = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_signIn = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_performSignIn = new WeakMap(), _RemoteSolanaMobileWalletAdapterWallet_instances = new WeakSet(), _RemoteSolanaMobileWalletAdapterWallet_emit = function _RemoteSolanaMobileWalletAdapterWallet_emit(event, ...args) {
    var _a;
    // eslint-disable-next-line prefer-spread
    (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.forEach((listener)=>listener.apply(null, args));
}, _RemoteSolanaMobileWalletAdapterWallet_off = function _RemoteSolanaMobileWalletAdapterWallet_off(event, listener) {
    var _a;
    __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event] = (_a = __classPrivateFieldGet$1(this, _RemoteSolanaMobileWalletAdapterWallet_listeners, "f")[event]) === null || _a === void 0 ? void 0 : _a.filter((existingListener)=>listener !== existingListener);
};
var __classPrivateFieldSet = undefined && undefined.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
var __classPrivateFieldGet = undefined && undefined.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _RegisterWalletEvent_detail;
/**
 * Register a {@link "@wallet-standard/base".Wallet} as a Standard Wallet with the app.
 *
 * This dispatches a {@link "@wallet-standard/base".WindowRegisterWalletEvent} to notify the app that the Wallet is
 * ready to be registered.
 *
 * This also adds a listener for {@link "@wallet-standard/base".WindowAppReadyEvent} to listen for a notification from
 * the app that the app is ready to register the Wallet.
 *
 * This combination of event dispatch and listener guarantees that the Wallet will be registered synchronously as soon
 * as the app is ready whether the Wallet loads before or after the app.
 *
 * @param wallet Wallet to register.
 *
 * @group Wallet
 */ function registerWallet(wallet) {
    const callback = ({ register })=>register(wallet);
    try {
        window.dispatchEvent(new RegisterWalletEvent(callback));
    } catch (error) {
        console.error('wallet-standard:register-wallet event could not be dispatched\n', error);
    }
    try {
        window.addEventListener('wallet-standard:app-ready', ({ detail: api })=>callback(api));
    } catch (error) {
        console.error('wallet-standard:app-ready event listener could not be added\n', error);
    }
}
class RegisterWalletEvent extends Event {
    constructor(callback){
        super('wallet-standard:register-wallet', {
            bubbles: false,
            cancelable: false,
            composed: false
        });
        _RegisterWalletEvent_detail.set(this, void 0);
        __classPrivateFieldSet(this, _RegisterWalletEvent_detail, callback, "f");
    }
    get detail() {
        return __classPrivateFieldGet(this, _RegisterWalletEvent_detail, "f");
    }
    get type() {
        return 'wallet-standard:register-wallet';
    }
    /** @deprecated */ preventDefault() {
        throw new Error('preventDefault cannot be called');
    }
    /** @deprecated */ stopImmediatePropagation() {
        throw new Error('stopImmediatePropagation cannot be called');
    }
    /** @deprecated */ stopPropagation() {
        throw new Error('stopPropagation cannot be called');
    }
}
_RegisterWalletEvent_detail = new WeakMap();
undefined && undefined.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
undefined && undefined.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
function getIsLocalAssociationSupported() {
    return typeof window !== 'undefined' && window.isSecureContext && typeof document !== 'undefined' && /android/i.test(navigator.userAgent);
}
function getIsRemoteAssociationSupported() {
    return typeof window !== 'undefined' && window.isSecureContext && typeof document !== 'undefined' && !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}
function registerMwa(config) {
    if (getIsLocalAssociationSupported()) {
        registerWallet(new LocalSolanaMobileWalletAdapterWallet(config));
    } else if (getIsRemoteAssociationSupported() && config.remoteHostAuthority !== undefined) {
        registerWallet(new RemoteSolanaMobileWalletAdapterWallet(Object.assign(Object.assign({}, config), {
            remoteHostAuthority: config.remoteHostAuthority
        })));
    } else ;
}
const WALLET_NOT_FOUND_ERROR_MESSAGE = 'To use mobile wallet adapter, you must have a compatible mobile wallet application installed on your device.';
const BROWSER_NOT_SUPPORTED_ERROR_MESSAGE = 'This browser appears to be incompatible with mobile wallet adapter. Open this page in a compatible mobile browser app and try again.';
class ErrorModal extends EmbeddedModal {
    constructor(){
        super(...arguments);
        this.contentStyles = css;
        this.contentHtml = ErrorDialogHtml;
    }
    initWithError(error) {
        super.init();
        this.populateError(error);
    }
    populateError(error) {
        var _a, _b;
        const errorMessageElement = (_a = this.dom) === null || _a === void 0 ? void 0 : _a.getElementById('mobile-wallet-adapter-error-message');
        const actionBtn = (_b = this.dom) === null || _b === void 0 ? void 0 : _b.getElementById('mobile-wallet-adapter-error-action');
        if (errorMessageElement) {
            if (error.name === 'SolanaMobileWalletAdapterError') {
                switch(error.code){
                    case 'ERROR_WALLET_NOT_FOUND':
                        errorMessageElement.innerHTML = WALLET_NOT_FOUND_ERROR_MESSAGE;
                        if (actionBtn) actionBtn.addEventListener('click', ()=>{
                            window.location.href = 'https://solanamobile.com/wallets';
                        });
                        return;
                    case 'ERROR_BROWSER_NOT_SUPPORTED':
                        errorMessageElement.innerHTML = BROWSER_NOT_SUPPORTED_ERROR_MESSAGE;
                        if (actionBtn) actionBtn.style.display = 'none';
                        return;
                }
            }
            errorMessageElement.innerHTML = `An unexpected error occurred: ${error.message}`;
        } else {
            console.log('Failed to locate error dialog element');
        }
    }
}
const ErrorDialogHtml = `
<svg class="mobile-wallet-adapter-embedded-modal-error-icon" xmlns="http://www.w3.org/2000/svg" height="50px" viewBox="0 -960 960 960" width="50px" fill="#000000"><path d="M 280,-80 Q 197,-80 138.5,-138.5 80,-197 80,-280 80,-363 138.5,-421.5 197,-480 280,-480 q 83,0 141.5,58.5 58.5,58.5 58.5,141.5 0,83 -58.5,141.5 Q 363,-80 280,-80 Z M 824,-120 568,-376 Q 556,-389 542.5,-402.5 529,-416 516,-428 q 38,-24 61,-64 23,-40 23,-88 0,-75 -52.5,-127.5 Q 495,-760 420,-760 345,-760 292.5,-707.5 240,-655 240,-580 q 0,6 0.5,11.5 0.5,5.5 1.5,11.5 -18,2 -39.5,8 -21.5,6 -38.5,14 -2,-11 -3,-22 -1,-11 -1,-23 0,-109 75.5,-184.5 Q 311,-840 420,-840 q 109,0 184.5,75.5 75.5,75.5 75.5,184.5 0,43 -13.5,81.5 Q 653,-460 629,-428 l 251,252 z m -615,-61 71,-71 70,71 29,-28 -71,-71 71,-71 -28,-28 -71,71 -71,-71 -28,28 71,71 -71,71 z"/></svg>
<div class="mobile-wallet-adapter-embedded-modal-title">We can't find a wallet.</div>
<div id="mobile-wallet-adapter-error-message" class="mobile-wallet-adapter-embedded-modal-subtitle"></div>
<div>
    <button data-error-action id="mobile-wallet-adapter-error-action" class="mobile-wallet-adapter-embedded-modal-error-action">
        Find a wallet
    </button>
</div>
`;
const css = `
.mobile-wallet-adapter-embedded-modal-content {
    text-align: center;
}

.mobile-wallet-adapter-embedded-modal-error-icon {
    margin-top: 24px;
}

.mobile-wallet-adapter-embedded-modal-title {
    margin: 18px 100px auto 100px;
    color: #000000;
    font-size: 2.75em;
    font-weight: 600;
}

.mobile-wallet-adapter-embedded-modal-subtitle {
    margin: 30px 60px 40px 60px;
    color: #000000;
    font-size: 1.25em;
    font-weight: 400;
}

.mobile-wallet-adapter-embedded-modal-error-action {
    display: block;
    width: 100%;
    height: 56px;
    /*margin-top: 40px;*/
    font-size: 1.25em;
    /*line-height: 24px;*/
    /*letter-spacing: -1%;*/
    background: #000000;
    color: #FFFFFF;
    border-radius: 18px;
}

/* Smaller screens */
@media all and (max-width: 600px) {
    .mobile-wallet-adapter-embedded-modal-title {
        font-size: 1.5em;
        margin-right: 12px;
        margin-left: 12px;
    }
    .mobile-wallet-adapter-embedded-modal-subtitle {
        margin-right: 12px;
        margin-left: 12px;
    }
}
`;
function defaultErrorModalWalletNotFoundHandler() {
    return __awaiter(this, void 0, void 0, function*() {
        if (typeof window !== 'undefined') {
            const userAgent = window.navigator.userAgent.toLowerCase();
            const errorDialog = new ErrorModal();
            if (userAgent.includes('wv')) {
                // MWA is not supported in this browser so we inform the user
                // errorDialog.initWithError(
                //     new SolanaMobileWalletAdapterError(
                //         SolanaMobileWalletAdapterErrorCode.ERROR_BROWSER_NOT_SUPPORTED, 
                //         ''
                //     )
                // );
                // TODO: investigate why instantiating a new SolanaMobileWalletAdapterError here breaks treeshaking 
                errorDialog.initWithError({
                    name: 'SolanaMobileWalletAdapterError',
                    code: 'ERROR_BROWSER_NOT_SUPPORTED',
                    message: ''
                });
            } else {
                // errorDialog.initWithError(
                //     new SolanaMobileWalletAdapterError(
                //         SolanaMobileWalletAdapterErrorCode.ERROR_WALLET_NOT_FOUND, 
                //         ''
                //     )
                // );
                // TODO: investigate why instantiating a new SolanaMobileWalletAdapterError here breaks treeshaking 
                errorDialog.initWithError({
                    name: 'SolanaMobileWalletAdapterError',
                    code: 'ERROR_WALLET_NOT_FOUND',
                    message: ''
                });
            }
            errorDialog.open();
        }
    });
}
function createDefaultWalletNotFoundHandler() {
    return ()=>__awaiter(this, void 0, void 0, function*() {
            defaultErrorModalWalletNotFoundHandler();
        });
}
const CACHE_KEY = 'SolanaMobileWalletAdapterDefaultAuthorizationCache';
function createDefaultAuthorizationCache() {
    let storage;
    try {
        storage = window.localStorage;
    // eslint-disable-next-line no-empty
    } catch (_a) {}
    return {
        clear () {
            return __awaiter(this, void 0, void 0, function*() {
                if (!storage) {
                    return;
                }
                try {
                    storage.removeItem(CACHE_KEY);
                // eslint-disable-next-line no-empty
                } catch (_a) {}
            });
        },
        get () {
            return __awaiter(this, void 0, void 0, function*() {
                if (!storage) {
                    return;
                }
                try {
                    const parsed = JSON.parse(storage.getItem(CACHE_KEY));
                    if (parsed && parsed.accounts) {
                        const parsedAccounts = parsed.accounts.map((account)=>{
                            return Object.assign(Object.assign({}, account), {
                                publicKey: 'publicKey' in account ? new Uint8Array(Object.values(account.publicKey)) // Rebuild publicKey for WalletAccount
                                 : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PublicKey"](account.address).toBytes()
                            });
                        });
                        return Object.assign(Object.assign({}, parsed), {
                            accounts: parsedAccounts
                        });
                    } else return parsed || undefined;
                // eslint-disable-next-line no-empty
                } catch (_a) {}
            });
        },
        set (authorizationResult) {
            return __awaiter(this, void 0, void 0, function*() {
                if (!storage) {
                    return;
                }
                try {
                    storage.setItem(CACHE_KEY, JSON.stringify(authorizationResult));
                // eslint-disable-next-line no-empty
                } catch (_a) {}
            });
        }
    };
}
function createDefaultChainSelector() {
    return {
        select (chains) {
            return __awaiter(this, void 0, void 0, function*() {
                if (chains.length === 1) {
                    return chains[0];
                } else if (chains.includes(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$chains$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SOLANA_MAINNET_CHAIN"])) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$chains$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SOLANA_MAINNET_CHAIN"];
                } else return chains[0];
            });
        }
    };
}
;
}}),
"[project]/node_modules/@solana-mobile/wallet-adapter-mobile/lib/esm/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LocalSolanaMobileWalletAdapter": (()=>LocalSolanaMobileWalletAdapter),
    "RemoteSolanaMobileWalletAdapter": (()=>RemoteSolanaMobileWalletAdapter),
    "SolanaMobileWalletAdapter": (()=>SolanaMobileWalletAdapter),
    "SolanaMobileWalletAdapterWalletName": (()=>SolanaMobileWalletAdapterWalletName),
    "createDefaultAddressSelector": (()=>createDefaultAddressSelector),
    "createDefaultAuthorizationResultCache": (()=>createDefaultAuthorizationResultCache),
    "createDefaultWalletNotFoundHandler": (()=>createDefaultWalletNotFoundHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$signer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-adapter-base/lib/esm/signer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-adapter-base/lib/esm/adapter.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-adapter-base/lib/esm/errors.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/web3.js/lib/index.esm.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signIn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-features/lib/esm/signIn.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-features/lib/esm/signMessage.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-features/lib/esm/signAndSendTransaction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana/wallet-standard-features/lib/esm/signTransaction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$wallet$2d$standard$2d$mobile$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@solana-mobile/wallet-standard-mobile/lib/esm/index.js [app-ssr] (ecmascript)");
;
;
;
;
/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */ function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}
function __classPrivateFieldGet(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}
function __classPrivateFieldSet(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
}
undefined && undefined.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
undefined && undefined.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
/** Name of the feature. */ const StandardConnect = 'standard:connect';
/** Name of the feature. */ const StandardDisconnect = 'standard:disconnect';
/** Name of the feature. */ const StandardEvents = 'standard:events';
undefined && undefined.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
undefined && undefined.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
undefined && undefined.__classPrivateFieldSet || function(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
};
undefined && undefined.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
function fromUint8Array(byteArray) {
    return window.btoa(String.fromCharCode.call(null, ...byteArray));
}
function getIsSupported() {
    return typeof window !== 'undefined' && window.isSecureContext && typeof document !== 'undefined' && /android/i.test(navigator.userAgent);
}
var _BaseSolanaMobileWalletAdapter_instances, _BaseSolanaMobileWalletAdapter_wallet, _BaseSolanaMobileWalletAdapter_connecting, _BaseSolanaMobileWalletAdapter_readyState, _BaseSolanaMobileWalletAdapter_accountSelector, _BaseSolanaMobileWalletAdapter_selectedAccount, _BaseSolanaMobileWalletAdapter_publicKey, _BaseSolanaMobileWalletAdapter_handleChangeEvent, _BaseSolanaMobileWalletAdapter_connect, _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled, _BaseSolanaMobileWalletAdapter_assertIsAuthorized, _BaseSolanaMobileWalletAdapter_performSignTransactions, _BaseSolanaMobileWalletAdapter_runWithGuard;
const SolanaMobileWalletAdapterWalletName = 'Mobile Wallet Adapter';
const SIGNATURE_LENGTH_IN_BYTES = 64;
function isVersionedTransaction(transaction) {
    return 'version' in transaction;
}
function chainOrClusterToChainId(chain) {
    switch(chain){
        case 'mainnet-beta':
            return 'solana:mainnet';
        case 'testnet':
            return 'solana:testnet';
        case 'devnet':
            return 'solana:devnet';
        default:
            return chain;
    }
}
class BaseSolanaMobileWalletAdapter extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$signer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseSignInMessageSignerWalletAdapter"] {
    constructor(wallet, config){
        super();
        _BaseSolanaMobileWalletAdapter_instances.add(this);
        this.supportedTransactionVersions = new Set(// FIXME(#244): We can't actually know what versions are supported until we know which wallet we're talking to.
        [
            'legacy',
            0
        ]);
        _BaseSolanaMobileWalletAdapter_wallet.set(this, void 0);
        _BaseSolanaMobileWalletAdapter_connecting.set(this, false);
        _BaseSolanaMobileWalletAdapter_readyState.set(this, getIsSupported() ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["WalletReadyState"].Loadable : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["WalletReadyState"].Unsupported);
        _BaseSolanaMobileWalletAdapter_accountSelector.set(this, void 0);
        _BaseSolanaMobileWalletAdapter_selectedAccount.set(this, void 0);
        _BaseSolanaMobileWalletAdapter_publicKey.set(this, void 0);
        _BaseSolanaMobileWalletAdapter_handleChangeEvent.set(this, (properties)=>__awaiter(this, void 0, void 0, function*() {
                if (properties.accounts && properties.accounts.length > 0) {
                    __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled).call(this);
                    const nextSelectedAccount = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_accountSelector, "f").call(this, properties.accounts);
                    if (nextSelectedAccount !== __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f")) {
                        __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, nextSelectedAccount, "f");
                        __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_publicKey, undefined, "f");
                        this.emit('connect', // Having just set `this.#selectedAccount`, `this.publicKey` is definitely non-null
                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                        this.publicKey);
                    }
                }
            }));
        // this.#chain = chainOrClusterToChainId(config.chain);
        __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_accountSelector, (accounts)=>__awaiter(this, void 0, void 0, function*() {
                var _a;
                const selectedBase64EncodedAddress = yield config.addressSelector.select(accounts.map(({ publicKey })=>fromUint8Array(publicKey)));
                return (_a = accounts.find(({ publicKey })=>fromUint8Array(publicKey) === selectedBase64EncodedAddress)) !== null && _a !== void 0 ? _a : accounts[0];
            }), "f");
        __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_wallet, wallet, "f");
        __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardEvents].on('change', __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_handleChangeEvent, "f"));
        this.name = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").name;
        this.icon = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").icon;
        this.url = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").url;
    // TODO: evaluate if this logic should be kept - it seems to create a nasty bug where 
    //  the wallet tries to auto connect on page load and gets blocked by the popup blocker
    // if (this.#readyState !== WalletReadyState.Unsupported) {
    //     config.authorizationResultCache.get().then((authorizationResult) => {
    //         if (authorizationResult) {
    //             // Having a prior authorization result is, right now, the best
    //             // indication that a mobile wallet is installed. There is no API
    //             // we can use to test for whether the association URI is supported.
    //             this.#declareWalletAsInstalled();
    //         }
    //     });
    // }
    }
    get publicKey() {
        var _a;
        if (!__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_publicKey, "f") && __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f")) {
            try {
                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_publicKey, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PublicKey"](__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f").publicKey), "f");
            } catch (e) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletPublicKeyError"](e instanceof Error && (e === null || e === void 0 ? void 0 : e.message) || 'Unknown error', e);
            }
        }
        return (_a = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_publicKey, "f")) !== null && _a !== void 0 ? _a : null;
    }
    get connected() {
        return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").connected;
    }
    get connecting() {
        return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_connecting, "f");
    }
    get readyState() {
        return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, "f");
    }
    /** @deprecated Use `autoConnect()` instead. */ autoConnect_DO_NOT_USE_OR_YOU_WILL_BE_FIRED() {
        return __awaiter(this, void 0, void 0, function*() {
            return yield this.autoConnect();
        });
    }
    autoConnect() {
        return __awaiter(this, void 0, void 0, function*() {
            __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_connect).call(this, true);
        });
    }
    connect() {
        return __awaiter(this, void 0, void 0, function*() {
            __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_connect).call(this);
        });
    }
    /** @deprecated Use `connect()` or `autoConnect()` instead. */ performAuthorization(signInPayload) {
        return __awaiter(this, void 0, void 0, function*() {
            try {
                const cachedAuthorizationResult = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").cachedAuthorizationResult;
                if (cachedAuthorizationResult) {
                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardConnect].connect({
                        silent: true
                    });
                    return cachedAuthorizationResult;
                }
                if (signInPayload) {
                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signIn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignIn"]].signIn(signInPayload);
                } else yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardConnect].connect();
                const authorizationResult = yield yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").cachedAuthorizationResult;
                return authorizationResult;
            } catch (e) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletConnectionError"](e instanceof Error && e.message || 'Unknown error', e);
            }
        });
    }
    disconnect() {
        return __awaiter(this, void 0, void 0, function*() {
            // return await this.#runWithGuard(this.#wallet.features[StandardDisconnect].disconnect);
            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, ()=>__awaiter(this, void 0, void 0, function*() {
                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, false, "f");
                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_publicKey, undefined, "f");
                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, undefined, "f");
                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardDisconnect].disconnect();
                    this.emit('disconnect');
                }));
        });
    }
    signIn(input) {
        return __awaiter(this, void 0, void 0, function*() {
            return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, ()=>__awaiter(this, void 0, void 0, function*() {
                    var _a;
                    if (__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["WalletReadyState"].Installed && __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["WalletReadyState"].Loadable) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletNotReadyError"]();
                    }
                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, true, "f");
                    try {
                        const outputs = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signIn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignIn"]].signIn(Object.assign(Object.assign({}, input), {
                            domain: (_a = input === null || input === void 0 ? void 0 : input.domain) !== null && _a !== void 0 ? _a : window.location.host
                        }));
                        if (outputs.length > 0) {
                            return outputs[0];
                        } else {
                            throw new Error("Sign in failed, no sign in result returned by wallet");
                        }
                    } catch (e) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletConnectionError"](e instanceof Error && e.message || 'Unknown error', e);
                    } finally{
                        __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, false, "f");
                    }
                }));
        });
    }
    signMessage(message) {
        return __awaiter(this, void 0, void 0, function*() {
            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, ()=>__awaiter(this, void 0, void 0, function*() {
                    const account = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);
                    try {
                        const outputs = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignMessage"]].signMessage({
                            account,
                            message: message
                        });
                        return outputs[0].signature;
                    } catch (error) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletSignMessageError"](error === null || error === void 0 ? void 0 : error.message, error);
                    }
                }));
        });
    }
    sendTransaction(transaction, connection, options) {
        return __awaiter(this, void 0, void 0, function*() {
            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, ()=>__awaiter(this, void 0, void 0, function*() {
                    const account = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);
                    try {
                        function getTargetCommitment() {
                            let targetCommitment;
                            switch(connection.commitment){
                                case 'confirmed':
                                case 'finalized':
                                case 'processed':
                                    targetCommitment = connection.commitment;
                                    break;
                                default:
                                    targetCommitment = 'finalized';
                            }
                            let targetPreflightCommitment;
                            switch(options === null || options === void 0 ? void 0 : options.preflightCommitment){
                                case 'confirmed':
                                case 'finalized':
                                case 'processed':
                                    targetPreflightCommitment = options.preflightCommitment;
                                    break;
                                case undefined:
                                    targetPreflightCommitment = targetCommitment;
                                    break;
                                default:
                                    targetPreflightCommitment = 'finalized';
                            }
                            const preflightCommitmentScore = targetPreflightCommitment === 'finalized' ? 2 : targetPreflightCommitment === 'confirmed' ? 1 : 0;
                            const targetCommitmentScore = targetCommitment === 'finalized' ? 2 : targetCommitment === 'confirmed' ? 1 : 0;
                            return preflightCommitmentScore < targetCommitmentScore ? targetPreflightCommitment : targetCommitment;
                        }
                        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignAndSendTransaction"] in __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features) {
                            const chain = chainOrClusterToChainId(__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").currentAuthorization.chain);
                            const [signature] = (yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signAndSendTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignAndSendTransaction"]].signAndSendTransaction({
                                account,
                                transaction: transaction.serialize(),
                                chain: chain,
                                options: options ? {
                                    skipPreflight: options.skipPreflight,
                                    maxRetries: options.maxRetries
                                } : undefined
                            })).map((output)=>{
                                return fromUint8Array(output.signature);
                            });
                            return signature;
                        } else {
                            const [signedTransaction] = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, [
                                transaction
                            ]);
                            if (isVersionedTransaction(signedTransaction)) {
                                return yield connection.sendTransaction(signedTransaction);
                            } else {
                                const serializedTransaction = signedTransaction.serialize();
                                return yield connection.sendRawTransaction(serializedTransaction, Object.assign(Object.assign({}, options), {
                                    preflightCommitment: getTargetCommitment()
                                }));
                            }
                        }
                    } catch (error) {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletSendTransactionError"](error === null || error === void 0 ? void 0 : error.message, error);
                    }
                }));
        });
    }
    signTransaction(transaction) {
        return __awaiter(this, void 0, void 0, function*() {
            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, ()=>__awaiter(this, void 0, void 0, function*() {
                    const [signedTransaction] = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, [
                        transaction
                    ]);
                    return signedTransaction;
                }));
        });
    }
    signAllTransactions(transactions) {
        return __awaiter(this, void 0, void 0, function*() {
            return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, ()=>__awaiter(this, void 0, void 0, function*() {
                    const signedTransactions = yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_performSignTransactions).call(this, transactions);
                    return signedTransactions;
                }));
        });
    }
}
_BaseSolanaMobileWalletAdapter_wallet = new WeakMap(), _BaseSolanaMobileWalletAdapter_connecting = new WeakMap(), _BaseSolanaMobileWalletAdapter_readyState = new WeakMap(), _BaseSolanaMobileWalletAdapter_accountSelector = new WeakMap(), _BaseSolanaMobileWalletAdapter_selectedAccount = new WeakMap(), _BaseSolanaMobileWalletAdapter_publicKey = new WeakMap(), _BaseSolanaMobileWalletAdapter_handleChangeEvent = new WeakMap(), _BaseSolanaMobileWalletAdapter_instances = new WeakSet(), _BaseSolanaMobileWalletAdapter_connect = function _BaseSolanaMobileWalletAdapter_connect(autoConnect = false) {
    return __awaiter(this, void 0, void 0, function*() {
        if (this.connecting || this.connected) {
            return;
        }
        return yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_runWithGuard).call(this, ()=>__awaiter(this, void 0, void 0, function*() {
                if (__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["WalletReadyState"].Installed && __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["WalletReadyState"].Loadable) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletNotReadyError"]();
                }
                __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, true, "f");
                try {
                    yield __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[StandardConnect].connect({
                        silent: autoConnect
                    });
                } catch (e) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletConnectionError"](e instanceof Error && e.message || 'Unknown error', e);
                } finally{
                    __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_connecting, false, "f");
                }
            }));
    });
}, _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled = function _BaseSolanaMobileWalletAdapter_declareWalletAsInstalled() {
    if (__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_readyState, "f") !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["WalletReadyState"].Installed) {
        this.emit('readyStateChange', __classPrivateFieldSet(this, _BaseSolanaMobileWalletAdapter_readyState, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["WalletReadyState"].Installed, "f"));
    }
}, _BaseSolanaMobileWalletAdapter_assertIsAuthorized = function _BaseSolanaMobileWalletAdapter_assertIsAuthorized() {
    if (!__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").isAuthorized || !__classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f")) throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletNotConnectedError"]();
    return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_selectedAccount, "f");
}, _BaseSolanaMobileWalletAdapter_performSignTransactions = function _BaseSolanaMobileWalletAdapter_performSignTransactions(transactions) {
    return __awaiter(this, void 0, void 0, function*() {
        const account = __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_instances, "m", _BaseSolanaMobileWalletAdapter_assertIsAuthorized).call(this);
        try {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignTransaction"] in __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features) {
                return __classPrivateFieldGet(this, _BaseSolanaMobileWalletAdapter_wallet, "f").features[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$standard$2d$features$2f$lib$2f$esm$2f$signTransaction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SolanaSignTransaction"]].signTransaction(...transactions.map((value)=>{
                    return {
                        account,
                        transaction: value.serialize()
                    };
                })).then((outputs)=>{
                    return outputs.map((output)=>{
                        const byteArray = output.signedTransaction;
                        const numSignatures = byteArray[0];
                        const messageOffset = numSignatures * SIGNATURE_LENGTH_IN_BYTES + 1;
                        const version = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VersionedMessage"].deserializeMessageVersion(byteArray.slice(messageOffset, byteArray.length));
                        if (version === 'legacy') {
                            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Transaction"].from(byteArray);
                        } else {
                            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$web3$2e$js$2f$lib$2f$index$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VersionedTransaction"].deserialize(byteArray);
                        }
                    });
                });
            } else {
                throw new Error('Connected wallet does not support signing transactions');
            }
        } catch (error) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2f$wallet$2d$adapter$2d$base$2f$lib$2f$esm$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WalletSignTransactionError"](error === null || error === void 0 ? void 0 : error.message, error);
        }
    });
}, _BaseSolanaMobileWalletAdapter_runWithGuard = function _BaseSolanaMobileWalletAdapter_runWithGuard(callback) {
    return __awaiter(this, void 0, void 0, function*() {
        try {
            return yield callback();
        } catch (e) {
            this.emit('error', e);
            throw e;
        }
    });
};
class LocalSolanaMobileWalletAdapter extends BaseSolanaMobileWalletAdapter {
    constructor(config){
        var _a;
        const chain = chainOrClusterToChainId((_a = config.chain) !== null && _a !== void 0 ? _a : config.cluster);
        super(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$wallet$2d$standard$2d$mobile$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocalSolanaMobileWalletAdapterWallet"]({
            appIdentity: config.appIdentity,
            authorizationCache: {
                set: config.authorizationResultCache.set,
                get: ()=>__awaiter(this, void 0, void 0, function*() {
                        const authorizationResult = yield config.authorizationResultCache.get();
                        if (authorizationResult && 'chain' in authorizationResult) {
                            return authorizationResult;
                        } else if (authorizationResult) {
                            return Object.assign(Object.assign({}, authorizationResult), {
                                chain: chain
                            });
                        } else return undefined;
                    }),
                clear: config.authorizationResultCache.clear
            },
            chains: [
                chain
            ],
            chainSelector: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$wallet$2d$standard$2d$mobile$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createDefaultChainSelector"])(),
            onWalletNotFound: ()=>__awaiter(this, void 0, void 0, function*() {
                    config.onWalletNotFound(this);
                })
        }), {
            addressSelector: config.addressSelector,
            chain: chain
        });
    }
}
class RemoteSolanaMobileWalletAdapter extends BaseSolanaMobileWalletAdapter {
    constructor(config){
        const chain = chainOrClusterToChainId(config.chain);
        super(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$wallet$2d$standard$2d$mobile$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RemoteSolanaMobileWalletAdapterWallet"]({
            appIdentity: config.appIdentity,
            authorizationCache: {
                set: config.authorizationResultCache.set,
                get: ()=>__awaiter(this, void 0, void 0, function*() {
                        const authorizationResult = yield config.authorizationResultCache.get();
                        if (authorizationResult && 'chain' in authorizationResult) {
                            return authorizationResult;
                        } else if (authorizationResult) {
                            return Object.assign(Object.assign({}, authorizationResult), {
                                chain: chain
                            });
                        } else return undefined;
                    }),
                clear: config.authorizationResultCache.clear
            },
            chains: [
                chain
            ],
            chainSelector: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$wallet$2d$standard$2d$mobile$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createDefaultChainSelector"])(),
            remoteHostAuthority: config.remoteHostAuthority,
            onWalletNotFound: ()=>__awaiter(this, void 0, void 0, function*() {
                    config.onWalletNotFound(this);
                })
        }), {
            addressSelector: config.addressSelector,
            chain: chain
        });
    }
}
class SolanaMobileWalletAdapter extends LocalSolanaMobileWalletAdapter {
}
function createDefaultAddressSelector() {
    return {
        select (addresses) {
            return __awaiter(this, void 0, void 0, function*() {
                return addresses[0];
            });
        }
    };
}
function createDefaultAuthorizationResultCache() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$wallet$2d$standard$2d$mobile$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createDefaultAuthorizationCache"])();
}
function defaultWalletNotFoundHandler(mobileWalletAdapter) {
    return __awaiter(this, void 0, void 0, function*() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$solana$2d$mobile$2f$wallet$2d$standard$2d$mobile$2f$lib$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["defaultErrorModalWalletNotFoundHandler"])();
    });
}
function createDefaultWalletNotFoundHandler() {
    return defaultWalletNotFoundHandler;
}
;
}}),

};

//# sourceMappingURL=node_modules_%40solana-mobile_cfc9e909._.js.map