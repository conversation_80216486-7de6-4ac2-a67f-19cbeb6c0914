/**
 * Tweet Card Component
 * Displays individual tweets with launch button
 */

'use client';

import React, { useState } from 'react';
import { EnrichedTweet } from '@/types/twitter';
import { formatDistanceToNow } from '@/utils/dateUtils';

interface TweetCardProps {
  tweet: EnrichedTweet;
  onLaunchClick: (tweet: EnrichedTweet) => void;
  onTweetClick?: (tweet: EnrichedTweet) => void;
  className?: string;
}

export function TweetCard({ tweet, onLaunchClick, onTweetClick, className = '' }: TweetCardProps) {
  const [imageError, setImageError] = useState(false);
  const [isLaunching, setIsLaunching] = useState(false);

  const handleLaunchClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsLaunching(true);
    try {
      await onLaunchClick(tweet);
    } finally {
      setIsLaunching(false);
    }
  };

  const handleTweetClick = () => {
    // Open tweet in new tab
    const tweetUrl = `https://twitter.com/${tweet.author.username}/status/${tweet.id}`;
    window.open(tweetUrl, '_blank', 'noopener,noreferrer');

    // Also call the optional callback
    if (onTweetClick) {
      onTweetClick(tweet);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getEngagementScore = (): number => {
    const metrics = tweet.public_metrics;
    if (!metrics) return 0;
    return metrics.like_count + metrics.retweet_count * 2 + metrics.reply_count;
  };

  const renderTweetText = (text: string) => {
    // Simple regex to highlight hashtags and cashtags
    return text.replace(
      /(#\w+|\$\w+)/g,
      '<span class="text-cyan-400 font-semibold">$1</span>'
    );
  };

  const timeAgo = formatDistanceToNow(new Date(tweet.created_at));
  const engagementScore = getEngagementScore();

  return (
    <div 
      className={`bg-gray-900/50 border border-gray-700/50 rounded-xl p-6 hover:bg-gray-900/70 transition-all duration-300 cursor-pointer hover:border-gray-600/50 ${className}`}
      onClick={handleTweetClick}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          {/* Profile Image */}
          <div className="relative">
            {tweet.author.profile_image_url && !imageError ? (
              <img
                src={tweet.author.profile_image_url}
                alt={`${tweet.author.name} avatar`}
                className="w-12 h-12 rounded-full object-cover"
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                <span className="text-white font-bold text-lg">
                  {tweet.author.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            {tweet.author.verified && (
              <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </div>

          {/* User Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="text-white font-semibold truncate">{tweet.author.name}</h3>
              {tweet.author.verified && (
                <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <p className="text-gray-400 text-sm">@{tweet.author.username}</p>
          </div>
        </div>

        {/* Time and Engagement Score */}
        <div className="text-right">
          <p className="text-gray-500 text-sm">{timeAgo}</p>
          <div className="flex items-center space-x-1 mt-1">
            <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <span className="text-yellow-500 text-sm font-medium">{engagementScore}</span>
          </div>
        </div>
      </div>

      {/* Tweet Content */}
      <div className="mb-4">
        <p 
          className="text-gray-200 leading-relaxed"
          dangerouslySetInnerHTML={{ __html: renderTweetText(tweet.text) }}
        />
      </div>

      {/* Media */}
      {tweet.media && tweet.media.length > 0 && (
        <div className="mb-4">
          <div className="grid grid-cols-1 gap-2">
            {tweet.media.slice(0, 1).map((media, index) => (
              <div key={index} className="relative rounded-lg overflow-hidden">
                {media.type === 'photo' && media.url && (
                  <img
                    src={media.url}
                    alt={media.alt_text || 'Tweet image'}
                    className="w-full h-48 object-cover"
                  />
                )}
                {media.type === 'video' && media.preview_image_url && (
                  <div className="relative">
                    <img
                      src={media.preview_image_url}
                      alt="Video preview"
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-white ml-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Token Potential Preview */}
      {tweet.token_potential && (
        <div className="mb-4 p-3 bg-purple-900/20 border border-purple-500/30 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span className="text-purple-400 text-sm font-medium">Token Potential</span>
          </div>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-gray-400">Name:</span>
              <span className="text-white ml-2">{tweet.token_potential.name_suggestion}</span>
            </div>
            <div>
              <span className="text-gray-400">Symbol:</span>
              <span className="text-cyan-400 ml-2">${tweet.token_potential.symbol_suggestion}</span>
            </div>
          </div>
        </div>
      )}

      {/* Engagement Stats */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-6 text-gray-400">
          <div className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <span className="text-sm">{formatNumber(tweet.public_metrics?.reply_count || 0)}</span>
          </div>
          <div className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span className="text-sm">{formatNumber(tweet.public_metrics?.retweet_count || 0)}</span>
          </div>
          <div className="flex items-center space-x-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            <span className="text-sm">{formatNumber(tweet.public_metrics?.like_count || 0)}</span>
          </div>
        </div>

        {/* Launch Status */}
        {tweet.launch_status && tweet.launch_status !== 'not_launched' && (
          <div className="flex items-center space-x-2">
            {tweet.launch_status === 'launched' && (
              <span className="px-2 py-1 bg-green-900/30 border border-green-500/30 text-green-400 text-xs rounded-full">
                Launched
              </span>
            )}
            {tweet.launch_status === 'launching' && (
              <span className="px-2 py-1 bg-yellow-900/30 border border-yellow-500/30 text-yellow-400 text-xs rounded-full">
                Launching...
              </span>
            )}
            {tweet.launch_status === 'failed' && (
              <span className="px-2 py-1 bg-red-900/30 border border-red-500/30 text-red-400 text-xs rounded-full">
                Failed
              </span>
            )}
          </div>
        )}
      </div>

      {/* Launch Button */}
      <button
        type="button"
        onClick={handleLaunchClick}
        disabled={isLaunching || tweet.launch_status === 'launched' || tweet.launch_status === 'launching'}
        className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:to-gray-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center justify-center space-x-2 border border-orange-400/30 hover:border-orange-300/50 disabled:border-gray-600/30"
      >
        {isLaunching ? (
          <>
            <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Launching...</span>
          </>
        ) : tweet.launch_status === 'launched' ? (
          <>
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            <span>Launched</span>
          </>
        ) : (
          <>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span>Launch with AI Art 🎨</span>
          </>
        )}
      </button>
    </div>
  );
}
