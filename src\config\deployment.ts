/**
 * Multi-Chain Deployment Configuration
 * Defines deployment strategies and parameters
 */

import { SupportedChain } from './chains';

export interface DeploymentStrategy {
  mode: 'sequential' | 'parallel' | 'optimized';
  maxConcurrentDeployments: number;
  retryAttempts: number;
  timeoutMs: number;
  rollbackOnFailure: boolean;
}

export interface LiquidityDistribution {
  chain: SupportedChain;
  percentage: number;
  minimumAmount: number;
  priority: number;
}

export interface DeploymentConfig {
  strategy: DeploymentStrategy;
  liquidityDistribution: LiquidityDistribution[];
  gasSettings: Record<SupportedChain, {
    maxGasPrice: number;
    gasLimit: number;
    priorityFee?: number;
  }>;
  validation: {
    requireAllChainsSuccess: boolean;
    minimumSuccessfulChains: number;
    validateLiquidity: boolean;
  };
}

export const DEFAULT_DEPLOYMENT_CONFIG: DeploymentConfig = {
  strategy: {
    mode: 'optimized',
    maxConcurrentDeployments: 3,
    retryAttempts: 3,
    timeoutMs: 300000, // 5 minutes
    rollbackOnFailure: true,
  },
  liquidityDistribution: [
    {
      chain: SupportedChain.SOLANA,
      percentage: 40,
      minimumAmount: 0.2, // Raydium Standard AMM minimum ~0.2 SOL
      priority: 1,
    },
    {
      chain: SupportedChain.BNB_CHAIN,
      percentage: 30,
      minimumAmount: 0.05, // PancakeSwap minimum ~0.05 BNB
      priority: 2,
    },
    {
      chain: SupportedChain.AVALANCHE,
      percentage: 20,
      minimumAmount: 0.5, // Trader Joe minimum ~0.5 AVAX
      priority: 3,
    },
    {
      chain: SupportedChain.POLKADOT,
      percentage: 10,
      minimumAmount: 2, // HydraDX/Basilisk minimum ~2 DOT
      priority: 4,
    },
  ],
  gasSettings: {
    [SupportedChain.SOLANA]: {
      maxGasPrice: 0.000005, // SOL
      gasLimit: 200000,
      priorityFee: 0.000001,
    },
    [SupportedChain.BNB_CHAIN]: {
      maxGasPrice: 5, // Gwei
      gasLimit: 500000,
    },
    [SupportedChain.AVALANCHE]: {
      maxGasPrice: 25, // Gwei
      gasLimit: 500000,
    },
    [SupportedChain.POLKADOT]: {
      maxGasPrice: 0.01, // DOT
      gasLimit: 1000000,
    },
  },
  validation: {
    requireAllChainsSuccess: false,
    minimumSuccessfulChains: 2,
    validateLiquidity: true,
  },
};

export interface TokenDeploymentParams {
  name: string;
  symbol: string;
  totalSupply: string;
  decimals: number;
  description?: string;
  imageUrl?: string;
  website?: string;
  twitter?: string;
  telegram?: string;
  targetChains: SupportedChain[];
  liquidityDistribution: LiquidityDistribution[];
  initialLiquidity: number;
}

export interface DeploymentResult {
  success: boolean;
  chain: SupportedChain;
  contractAddress?: string;
  transactionHash?: string;
  blockNumber?: number;
  gasUsed?: number;
  error?: string;
  timestamp: number;
}

export interface MultiChainDeploymentResult {
  launchId: string;
  totalSuccess: boolean;
  deployments: DeploymentResult[];
  totalGasUsed: number;
  totalCost: number;
  duration: number;
  bridgeTransactions?: string[];
}

export function validateLiquidityDistribution(distribution: LiquidityDistribution[]): boolean {
  const totalPercentage = distribution.reduce((sum, dist) => sum + dist.percentage, 0);
  return Math.abs(totalPercentage - 100) < 0.01; // Allow for small floating point errors
}

export function calculateOptimalDistribution(
  targetChains: SupportedChain[],
  totalLiquidity: number
): LiquidityDistribution[] {
  const defaultDistribution = DEFAULT_DEPLOYMENT_CONFIG.liquidityDistribution;
  const filteredDistribution = defaultDistribution.filter(dist =>
    targetChains.includes(dist.chain)
  );
  
  // Normalize percentages to sum to 100%
  const totalPercentage = filteredDistribution.reduce((sum, dist) => sum + dist.percentage, 0);
  
  return filteredDistribution.map(dist => ({
    ...dist,
    percentage: (dist.percentage / totalPercentage) * 100,
  }));
}

export function estimateDeploymentCost(
  params: TokenDeploymentParams,
  config: DeploymentConfig = DEFAULT_DEPLOYMENT_CONFIG
): Record<SupportedChain, number> {
  const costs: Record<SupportedChain, number> = {} as any;
  
  params.targetChains.forEach(chain => {
    const gasSettings = config.gasSettings[chain];
    const baseCost = gasSettings.maxGasPrice * gasSettings.gasLimit;
    
    // Add chain-specific deployment costs
    switch (chain) {
      case SupportedChain.SOLANA:
        costs[chain] = baseCost + 0.002; // Token creation + metadata
        break;
      case SupportedChain.BNB_CHAIN:
        costs[chain] = baseCost + 0.001; // Contract deployment
        break;
      case SupportedChain.AVALANCHE:
        costs[chain] = baseCost + 0.002; // Contract deployment
        break;
      case SupportedChain.POLKADOT:
        costs[chain] = baseCost + 0.01; // Asset creation
        break;
    }
  });
  
  return costs;
}
