/**
 * Base Chain Deployer Interface
 * Abstract interface that all chain deployers must implement
 */

import { SupportedChain } from '@/config/chains';
import { TokenDeploymentParams, DeploymentResult } from '@/config/deployment';

export interface TokenParams {
  name: string;
  symbol: string;
  totalSupply: string;
  decimals: number;
  description?: string;
  imageUrl?: string;
  website?: string;
  twitter?: string;
  telegram?: string;
}

export interface LiquidityParams {
  tokenAddress: string;
  baseAmount: number;
  quoteAmount: number;
  slippage?: number;
}

export interface GasEstimate {
  gasLimit: number;
  gasPrice: number;
  totalCost: number;
  nativeTokenCost: number;
}

export interface ChainDeploymentResult extends DeploymentResult {
  contractAddress: string;
  transactionHash: string;
  blockNumber: number;
  gasUsed: number;
  nativeTokenUsed: number;
  additionalData?: Record<string, any>;
}

export interface LiquidityResult {
  poolAddress: string;
  lpTokenAddress?: string;
  transactionHash: string;
  liquidityAdded: number;
  priceImpact: number;
}

/**
 * Abstract base class for chain deployers
 * Each chain implementation must extend this class
 */
export abstract class BaseChainDeployer {
  protected chain: SupportedChain;
  protected rpcUrl: string;
  protected testnet: boolean;

  constructor(chain: SupportedChain, rpcUrl: string, testnet: boolean = false) {
    this.chain = chain;
    this.rpcUrl = rpcUrl;
    this.testnet = testnet;
  }

  /**
   * Deploy a token on the specific chain
   */
  abstract deployToken(params: TokenParams): Promise<ChainDeploymentResult>;

  /**
   * Add liquidity for the token
   */
  abstract addLiquidity(params: LiquidityParams): Promise<LiquidityResult>;

  /**
   * Estimate gas costs for token deployment
   */
  abstract estimateGas(params: TokenParams): Promise<GasEstimate>;

  /**
   * Validate token parameters for the specific chain
   */
  abstract validateParams(params: TokenParams): Promise<boolean>;

  /**
   * Get the native token balance for an address
   */
  abstract getNativeBalance(address: string): Promise<number>;

  /**
   * Get token balance for an address
   */
  abstract getTokenBalance(tokenAddress: string, walletAddress: string): Promise<number>;

  /**
   * Check if a token exists on the chain
   */
  abstract tokenExists(tokenAddress: string): Promise<boolean>;

  /**
   * Get transaction status
   */
  abstract getTransactionStatus(txHash: string): Promise<{
    confirmed: boolean;
    blockNumber?: number;
    gasUsed?: number;
    error?: string;
  }>;

  /**
   * Get chain-specific configuration
   */
  getChain(): SupportedChain {
    return this.chain;
  }

  getRpcUrl(): string {
    return this.rpcUrl;
  }

  isTestnet(): boolean {
    return this.testnet;
  }

  /**
   * Format address for display (chain-specific formatting)
   */
  abstract formatAddress(address: string): string;

  /**
   * Validate address format
   */
  abstract isValidAddress(address: string): boolean;

  /**
   * Get block explorer URL for transaction
   */
  abstract getExplorerUrl(txHash: string): string;

  /**
   * Get block explorer URL for token
   */
  abstract getTokenExplorerUrl(tokenAddress: string): string;
}

/**
 * Factory function to create chain deployers
 */
export interface ChainDeployerFactory {
  createDeployer(chain: SupportedChain, testnet?: boolean): BaseChainDeployer;
}

/**
 * Registry for chain deployers
 */
export class ChainDeployerRegistry {
  private static deployers: Map<SupportedChain, () => BaseChainDeployer> = new Map();

  static register(chain: SupportedChain, factory: () => BaseChainDeployer): void {
    this.deployers.set(chain, factory);
  }

  static getDeployer(chain: SupportedChain): BaseChainDeployer {
    const factory = this.deployers.get(chain);
    if (!factory) {
      throw new Error(`No deployer registered for chain: ${chain}`);
    }
    return factory();
  }

  static getSupportedChains(): SupportedChain[] {
    return Array.from(this.deployers.keys());
  }

  static isSupported(chain: SupportedChain): boolean {
    return this.deployers.has(chain);
  }
}
