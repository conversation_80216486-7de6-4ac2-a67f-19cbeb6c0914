{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { WalletMultiButton } from '@solana/wallet-adapter-react-ui';\nimport { useWallet } from '@solana/wallet-adapter-react';\nimport { SOLANA_NETWORK } from '@/lib/constants';\nimport { ChainSelector, ConnectionStatus } from '@/components/cross-chain/MultiChainWalletProvider';\n\nexport function Header() {\n  const { connected } = useWallet();\n\n  return (\n    <header className=\"bg-black/95 backdrop-blur-sm border-b border-gray-800/50 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-3\">\n              {/* Logo */}\n              <div className=\"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">\n                  TokenLaunch\n                </h1>\n                <p className=\"text-xs text-gray-400\">Cross-Chain Token Creator</p>\n              </div>\n            </div>\n\n            {/* Multi-Chain Status */}\n            <div className=\"hidden lg:flex items-center space-x-4\">\n              <ConnectionStatus />\n              <div className=\"w-px h-6 bg-gray-700\"></div>\n              <div className=\"flex items-center space-x-2\">\n                <div className={`w-2 h-2 rounded-full ${\n                  SOLANA_NETWORK === 'mainnet-beta' ? 'bg-green-400' : 'bg-yellow-400'\n                } animate-pulse`}></div>\n                <span className=\"text-sm text-gray-400 font-medium\">\n                  Solana {SOLANA_NETWORK === 'mainnet-beta' ? 'Mainnet' : 'Devnet'}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a href=\"#features\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Cross-Chain\n            </a>\n            <a href=\"#launch\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Launch\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Docs\n            </a>\n          </nav>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* Chain Selector */}\n            <div className=\"hidden xl:block\">\n              <ChainSelector />\n            </div>\n\n            {/* Solana Connection Status */}\n            {connected && (\n              <div className=\"hidden sm:flex items-center space-x-2 bg-green-900/30 border border-green-500/30 px-3 py-1 rounded-full\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-green-400 font-medium\">Solana Connected</span>\n              </div>\n            )}\n\n            {/* Primary Solana Wallet Button */}\n            <WalletMultiButton className=\"!bg-gradient-to-r !from-purple-500 !to-pink-500 hover:!from-purple-600 hover:!to-pink-600 !text-white !rounded-xl !px-6 !py-2 !text-sm !font-semibold !transition-all !duration-300 !border !border-purple-400/30 hover:!border-purple-300/50 hover:!scale-105\" />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD;IAE9B,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC5E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAgG;;;;;;0DAG9G,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mKAAA,CAAA,mBAAgB;;;;;kDACjB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,0HAAA,CAAA,iBAAc,KAAK,iBAAiB,iBAAiB,gBACtD,cAAc,CAAC;;;;;;0DAChB,6LAAC;gDAAK,WAAU;;oDAAoC;oDAC1C,0HAAA,CAAA,iBAAc,KAAK,iBAAiB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAoE;;;;;;0CAGlG,6LAAC;gCAAE,MAAK;gCAAU,WAAU;0CAAoE;;;;;;0CAGhG,6LAAC;gCAAE,MAAK;gCAAI,WAAU;0CAAoE;;;;;;;;;;;;kCAK5F,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mKAAA,CAAA,gBAAa;;;;;;;;;;4BAIf,2BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAqC;;;;;;;;;;;;0CAKzD,6LAAC,kMAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAxEgB;;QACQ,oLAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Head<PERSON> } from \"@/components/Header\";\nimport { CrossChainTokenCreator } from \"@/components/cross-chain/CrossChainTokenCreator\";\n\nexport default function Home() {\n  return (\n    <>\n      <Header />\n\n      {/* Main Content - Token Creation Focus */}\n      <main className=\"relative overflow-hidden bg-black min-h-screen\">\n        {/* Animated background elements */}\n        <div className=\"absolute inset-0\">\n          {/* Grid pattern overlay */}\n          <div className=\"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]\"></div>\n\n          {/* Floating orbs */}\n          <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse\"></div>\n          <div className=\"absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-r from-cyan-500/20 to-green-500/20 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse delay-500\"></div>\n\n          {/* Additional floating elements */}\n          <div className=\"absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-full blur-2xl animate-pulse delay-2000\"></div>\n          <div className=\"absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-full blur-2xl animate-pulse delay-3000\"></div>\n        </div>\n\n        <div className=\"relative container mx-auto px-4 py-8\">\n          {/* Simplified header section */}\n          <div className=\"text-center mb-12\">\n            <div className=\"inline-flex items-center bg-gray-900/50 border border-purple-500/30 rounded-full px-6 py-3 mb-6\">\n              <span className=\"w-2 h-2 bg-cyan-400 rounded-full animate-pulse mr-3\"></span>\n              <span className=\"text-gray-300 text-sm font-medium\">Cross-Chain Platform • 4 Blockchains</span>\n            </div>\n\n            <h1 className=\"text-4xl lg:text-6xl font-black text-white mb-4 leading-tight tracking-tight\">\n              Launch Everywhere\n              <span className=\"block bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent\">\n                Cross-Chain Tokens\n              </span>\n            </h1>\n\n            <p className=\"text-lg lg:text-xl text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed\">\n              Deploy tokens simultaneously across <span className=\"text-purple-400 font-semibold\">Solana, BNB Chain, Avalanche, and Polkadot</span> through a single interface.\n              <span className=\"block mt-2 text-cyan-400 font-semibold\">No bridging required. No capital fragmentation.</span>\n            </p>\n          </div>\n\n          {/* Quick feature highlights */}\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">4 Chains</div>\n              <div className=\"text-gray-400 text-sm\">Simultaneous Deploy</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">No Bridging</div>\n              <div className=\"text-gray-400 text-sm\">Direct Launch</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">Auto Liquidity</div>\n              <div className=\"text-gray-400 text-sm\">Cross-Chain Pools</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">One Interface</div>\n              <div className=\"text-gray-400 text-sm\">Unified Control</div>\n            </div>\n          </div>\n\n          {/* Main Token Creation Form */}\n          <div className=\"max-w-5xl mx-auto\">\n            <TokenLaunchModal\n              isOpen={true}\n              onClose={() => {}}\n            />\n          </div>\n\n          {/* Trust indicators */}\n          <div className=\"mt-16 pt-8 border-t border-gray-800/30 text-center\">\n            <p className=\"text-gray-500 text-sm mb-6\">Built with industry-leading protocols</p>\n            <div className=\"flex flex-wrap justify-center items-center gap-6\">\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium\">Solana</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium\">Raydium</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium\">Metaplex</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium\">SPL Token</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,qBACE;;0BACE,6LAAC,+HAAA,CAAA,SAAM;;;;;0BAGP,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAGtD,6LAAC;wCAAG,WAAU;;4CAA+E;0DAE3F,6LAAC;gDAAK,WAAU;0DAAgG;;;;;;;;;;;;kDAKlH,6LAAC;wCAAE,WAAU;;4CAA0E;0DACjD,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;4CAAiD;0DACrI,6LAAC;gDAAK,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;0CAK7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK3C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,QAAQ;oCACR,SAAS,KAAO;;;;;;;;;;;0CAKpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5D;KAlGwB", "debugId": null}}, {"offset": {"line": 703, "column": 0}, "map": {"version": 3, "file": "useWalletMultiButton.js", "sourceRoot": "", "sources": ["../../src/useWalletMultiButton.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAe,MAAM,8BAA8B,CAAC;AAEtE,OAAO,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;;;AAmB9B,SAAU,oBAAoB,CAAC,EAAE,cAAc,EAAU;IAC3D,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,4LACnG,YAAA,AAAS,EAAE,CAAC;IAChB,IAAI,WAAuC,CAAC;IAC5C,IAAI,UAAU,EAAE,CAAC;QACb,WAAW,GAAG,YAAY,CAAC;IAC/B,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QACnB,WAAW,GAAG,WAAW,CAAC;IAC9B,CAAC,MAAM,IAAI,aAAa,EAAE,CAAC;QACvB,WAAW,GAAG,eAAe,CAAC;IAClC,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC;QAChB,WAAW,GAAG,YAAY,CAAC;IAC/B,CAAC,MAAM,CAAC;QACJ,WAAW,GAAG,WAAW,CAAC;IAC9B,CAAC;IACD,MAAM,aAAa,GAAG,gLAAA,AAAW;2DAAC,GAAG,EAAE;YACnC,OAAO,EAAE,CAAC,KAAK;mEAAC,GAAG,EAAE;gBACjB,gFAAgF;gBACpF,CAAC,CAAC,CAAC;;QACP,CAAC;0DAAE;QAAC,OAAO;KAAC,CAAC,CAAC;IACd,MAAM,gBAAgB,GAAG,gLAAA,AAAW;8DAAC,GAAG,EAAE;YACtC,UAAU,EAAE,CAAC,KAAK;sEAAC,GAAG,EAAE;gBACpB,gFAAgF;gBACpF,CAAC,CAAC,CAAC;;QACP,CAAC;6DAAE;QAAC,UAAU;KAAC,CAAC,CAAC;IACjB,MAAM,kBAAkB,IAAG,+KAAA,AAAW;gEAAC,GAAG,EAAE;YACxC,cAAc,CAAC;gBAAE,cAAc,EAAE,MAAM;gBAAE,OAAO;YAAA,CAAE,CAAC,CAAC;QACxD,CAAC;+DAAE;QAAC,cAAc;QAAE,MAAM;QAAE,OAAO;KAAC,CAAC,CAAC;IACtC,OAAO;QACH,WAAW;QACX,SAAS,EAAE,WAAW,KAAK,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;QACnE,YAAY,EAAE,WAAW,KAAK,eAAe,IAAI,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;QAC3G,cAAc,EAAE,kBAAkB;QAClC,SAAS,EAAE,SAAS,IAAI,SAAS;QACjC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI;QAChC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,IAAI;KACnC,CAAC;AACN,CAAC", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "file": "BaseWalletConnectionButton.js", "sourceRoot": "", "sources": ["../../src/BaseWalletConnectionButton.tsx"], "names": [], "mappings": ";;;AACA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;;;;AAOvC,SAAU,0BAA0B,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,KAAK,EAAS;IAClF,OAAO,8JACH,UAAA,CAAA,aAAA,yLAAC,SAAM,EAAA;QAAA,GACC,KAAK;QACT,SAAS,EAAC,+BAA+B;QACzC,SAAS,EACL,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,8JACvB,UAAA,CAAA,aAAA,6LAAC,aAAU,EAAA;YAAC,MAAM,EAAE;gBAAE,OAAO,EAAE;oBAAE,IAAI,EAAE,UAAU;oBAAE,IAAI,EAAE,UAAU;gBAAA,CAAE;YAAA,CAAE;QAAA,EAAI,CAC9E,CAAC,CAAC,AAAC,SAAS;IAAA,EAEnB,CACL,CAAC;AACN,CAAC", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "file": "BaseWalletMultiButton.js", "sourceRoot": "", "sources": ["../../src/BaseWalletMultiButton.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,gCAAgC,CAAC;AACtE,OAAO,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACpE,OAAO,EAAE,0BAA0B,EAAE,MAAM,iCAAiC,CAAC;AAE7E,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;;;;;AAc/C,SAAU,qBAAqB,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,KAAK,EAAS;IACvE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,uMAAG,iBAAA,AAAc,EAAE,CAAC;IACzD,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,gOAAA,AAAoB,EAAC;QACrG,cAAc;YACV,eAAe,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;KACJ,CAAC,CAAC;IACH,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,qKAAG,WAAA,AAAQ,EAAC,KAAK,CAAC,CAAC;IAC5C,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,qKAAG,WAAQ,AAAR,EAAS,KAAK,CAAC,CAAC;IAChD,MAAM,GAAG,qKAAG,SAAA,AAAM,EAAmB,IAAI,CAAC,CAAC;QAC3C,0KAAA,AAAS;2CAAC,GAAG,EAAE;YACX,MAAM,QAAQ;4DAAG,CAAC,KAA8B,EAAE,EAAE;oBAChD,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC;oBAEzB,qDAAqD;oBACrD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAc,CAAC,EAAE,OAAO;oBAEzD,WAAW,CAAC,KAAK,CAAC,CAAC;gBACvB,CAAC,CAAC;;YAEF,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YACjD,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAElD;mDAAO,GAAG,EAAE;oBACR,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;oBACpD,QAAQ,CAAC,mBAAmB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;gBACzD,CAAC,CAAC;;QACN,CAAC;0CAAE,EAAE,CAAC,CAAC;IACP,MAAM,OAAO,qKAAG,UAAA,AAAO;kDAAC,GAAG,EAAE;YACzB,IAAI,QAAQ,EAAE,CAAC;gBACX,OAAO,QAAQ,CAAC;YACpB,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;gBACnB,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACpC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,MAAM,IAAI,WAAW,KAAK,YAAY,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;gBACtE,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;YAC/B,CAAC,MAAM,CAAC;gBACJ,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;iDAAE;QAAC,WAAW;QAAE,QAAQ;QAAE,MAAM;QAAE,SAAS;KAAC,CAAC,CAAC;IAC/C,OAAO,8JACH,UAAA,CAAA,aAAA,CAAA,OAAA;QAAK,SAAS,EAAC,yBAAyB;IAAA,iKACpC,UAAA,CAAA,aAAA,6MAAC,6BAA0B,EAAA;QAAA,GACnB,KAAK;QAAA,iBACM,QAAQ;QACvB,KAAK,EAAE;YAAE,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YAAE,GAAG,KAAK,CAAC,KAAK;QAAA,CAAE;QACpE,OAAO,EAAE,GAAG,EAAE;YACV,OAAQ,WAAW,EAAE,CAAC;gBAClB,KAAK,WAAW;oBACZ,eAAe,CAAC,IAAI,CAAC,CAAC;oBACtB,MAAM;gBACV,KAAK,YAAY;oBACb,IAAI,SAAS,EAAE,CAAC;wBACZ,SAAS,EAAE,CAAC;oBAChB,CAAC;oBACD,MAAM;gBACV,KAAK,WAAW;oBACZ,WAAW,CAAC,IAAI,CAAC,CAAC;oBAClB,MAAM;YACd,CAAC;QACL,CAAC;QACD,UAAU,EAAE,UAAU;QACtB,UAAU,EAAE,UAAU;IAAA,GAErB,OAAO,CACiB,gKAC7B,UAAA,CAAA,aAAA,CAAA,MAAA;QAAA,cACe,eAAe;QAC1B,SAAS,EAAE,CAAA,6BAAA,EAAgC,QAAQ,IAAI,qCAAqC,EAAE;QAC9F,GAAG,EAAE,GAAG;QACR,IAAI,EAAC,MAAM;IAAA,GAEV,SAAS,CAAC,CAAC,CAAC,8JACT,UAAA,CAAA,aAAA,CAAA,MAAA;QACI,SAAS,EAAC,mCAAmC;QAC7C,OAAO,EAAE,KAAK,IAAI,EAAE;YAChB,MAAM,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1D,SAAS,CAAC,IAAI,CAAC,CAAC;YAChB,UAAU,CAAC,GAAG,CAAG,CAAD,QAAU,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,EAAC,UAAU;IAAA,GAEd,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAClD,CACR,CAAC,CAAC,AAAC,IAAI,gKACR,UAAA,CAAA,aAAA,CAAA,MAAA;QACI,SAAS,EAAC,mCAAmC;QAC7C,OAAO,EAAE,GAAG,EAAE;YACV,eAAe,CAAC,IAAI,CAAC,CAAC;YACtB,WAAW,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;QACD,IAAI,EAAC,UAAU;IAAA,GAEd,MAAM,CAAC,eAAe,CAAC,CACvB,EACJ,YAAY,CAAC,CAAC,CAAC,8JACZ,UAAA,CAAA,aAAA,CAAA,MAAA;QACI,SAAS,EAAC,mCAAmC;QAC7C,OAAO,EAAE,GAAG,EAAE;YACV,YAAY,EAAE,CAAC;YACf,WAAW,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;QACD,IAAI,EAAC,UAAU;IAAA,GAEd,MAAM,CAAC,YAAY,CAAC,CACpB,CACR,CAAC,CAAC,AAAC,IAAI,CACP,CACH,CACT,CAAC;AACN,CAAC", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "file": "WalletMultiButton.js", "sourceRoot": "", "sources": ["../../src/WalletMultiButton.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,qBAAqB,EAAE,MAAM,4BAA4B,CAAC;;;AAGnE,MAAM,MAAM,GAAG;IACX,eAAe,EAAE,eAAe;IAChC,UAAU,EAAE,gBAAgB;IAC5B,cAAc,EAAE,cAAc;IAC9B,MAAM,EAAE,QAAQ;IAChB,UAAU,EAAE,YAAY;IACxB,YAAY,EAAE,SAAS;IACvB,WAAW,EAAE,eAAe;CACtB,CAAC;AAEL,SAAU,iBAAiB,CAAC,KAAkB;IAChD,qKAAO,UAAA,CAAA,aAAA,wMAAC,wBAAqB,EAAA;QAAA,GAAK,KAAK;QAAE,MAAM,EAAE,MAAM;IAAA,EAAI,CAAC;AAChE,CAAC", "debugId": null}}]}