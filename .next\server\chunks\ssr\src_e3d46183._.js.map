{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { WalletMultiButton } from '@solana/wallet-adapter-react-ui';\nimport { useWallet } from '@solana/wallet-adapter-react';\nimport { SOLANA_NETWORK } from '@/lib/constants';\n\nexport function Header() {\n  const { connected } = useWallet();\n\n  return (\n    <header className=\"bg-black/95 backdrop-blur-sm border-b border-gray-800/50 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-3\">\n              {/* Logo */}\n              <div className=\"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent\">\n                  Tweet-Pow\n                </h1>\n                <p className=\"text-xs text-gray-400\">Tweet-Powered Coin Launcher</p>\n              </div>\n            </div>\n\n            {/* Viral Feed Status */}\n            <div className=\"hidden lg:flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 bg-gray-800/50 border border-gray-700/30 rounded-xl px-3 py-1\">\n                <span className=\"text-cyan-400 text-sm\">🔥</span>\n                <span className=\"text-sm text-gray-400 font-medium\">Live Viral Feed</span>\n              </div>\n              <div className=\"w-px h-6 bg-gray-700\"></div>\n              <div className=\"flex items-center space-x-2\">\n                <div className={`w-2 h-2 rounded-full ${\n                  SOLANA_NETWORK === 'mainnet-beta' ? 'bg-green-400' : 'bg-yellow-400'\n                } animate-pulse`}></div>\n                <span className=\"text-sm text-gray-400 font-medium\">\n                  Raydium {SOLANA_NETWORK === 'mainnet-beta' ? 'Mainnet' : 'Devnet'}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a href=\"#feed\" className=\"text-gray-400 hover:text-orange-400 font-medium transition-colors\">\n              Viral Feed\n            </a>\n            <a href=\"#trending\" className=\"text-gray-400 hover:text-orange-400 font-medium transition-colors\">\n              Popular\n            </a>\n            <a href=\"#creators\" className=\"text-gray-400 hover:text-orange-400 font-medium transition-colors\">\n              Influencers\n            </a>\n          </nav>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* Platform Indicators */}\n            <div className=\"hidden xl:flex items-center gap-2 p-2 bg-gray-800/50 rounded-xl border border-gray-700/50\">\n              <span className=\"text-gray-400 text-sm font-medium mr-2\">Powered by:</span>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg\" title=\"Solana\">\n                <span className=\"text-sm\">◎</span>\n              </div>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg\" title=\"Raydium\">\n                <span className=\"text-xs\">R</span>\n              </div>\n              <div className=\"flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg\" title=\"Twitter API\">\n                <span className=\"text-xs\">🐦</span>\n              </div>\n            </div>\n\n            {/* Solana Connection Status */}\n            {connected && (\n              <div className=\"hidden sm:flex items-center space-x-2 bg-green-900/30 border border-green-500/30 px-3 py-1 rounded-full\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-green-400 font-medium\">Solana Connected</span>\n              </div>\n            )}\n\n            {/* Primary Solana Wallet Button */}\n            <WalletMultiButton className=\"!bg-gradient-to-r !from-orange-500 !to-red-500 hover:!from-orange-600 hover:!to-red-600 !text-white !rounded-xl !px-6 !py-2 !text-sm !font-semibold !transition-all !duration-300 !border !border-orange-400/30 hover:!border-orange-300/50 hover:!scale-105\" />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD;IAE9B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC5E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+F;;;;;;0DAG7G,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EACpC,uHAAA,CAAA,iBAAc,KAAK,iBAAiB,iBAAiB,gBACtD,cAAc,CAAC;;;;;;0DAChB,8OAAC;gDAAK,WAAU;;oDAAoC;oDACzC,uHAAA,CAAA,iBAAc,KAAK,iBAAiB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAQ,WAAU;0CAAoE;;;;;;0CAG9F,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAoE;;;;;;0CAGlG,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAoE;;;;;;;;;;;;kCAKpG,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAyC;;;;;;kDACzD,8OAAC;wCAAI,WAAU;wCAAwH,OAAM;kDAC3I,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;wCAA0H,OAAM;kDAC7I,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;wCAAsH,OAAM;kDACzI,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;4BAK7B,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAqC;;;;;;;;;;;;0CAKzD,8OAAC,+LAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/utils/dateUtils.ts"], "sourcesContent": ["/**\n * Simple date utilities to replace date-fns\n */\n\nexport function formatDistanceToNow(date: Date): string {\n  const now = new Date();\n  const diffInMs = now.getTime() - date.getTime();\n  const diffInSeconds = Math.floor(diffInMs / 1000);\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  const diffInDays = Math.floor(diffInHours / 24);\n\n  if (diffInSeconds < 60) {\n    return `${diffInSeconds}s ago`;\n  } else if (diffInMinutes < 60) {\n    return `${diffInMinutes}m ago`;\n  } else if (diffInHours < 24) {\n    return `${diffInHours}h ago`;\n  } else if (diffInDays < 7) {\n    return `${diffInDays}d ago`;\n  } else {\n    const weeks = Math.floor(diffInDays / 7);\n    return `${weeks}w ago`;\n  }\n}\n\nexport function formatDate(date: Date): string {\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\nexport function isValidDate(date: any): boolean {\n  return date instanceof Date && !isNaN(date.getTime());\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAEM,SAAS,oBAAoB,IAAU;IAC5C,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;IAC7C,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW;IAC5C,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAE5C,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,KAAK,CAAC;IAChC,OAAO,IAAI,gBAAgB,IAAI;QAC7B,OAAO,GAAG,cAAc,KAAK,CAAC;IAChC,OAAO,IAAI,cAAc,IAAI;QAC3B,OAAO,GAAG,YAAY,KAAK,CAAC;IAC9B,OAAO,IAAI,aAAa,GAAG;QACzB,OAAO,GAAG,WAAW,KAAK,CAAC;IAC7B,OAAO;QACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,GAAG,MAAM,KAAK,CAAC;IACxB;AACF;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,YAAY,IAAS;IACnC,OAAO,gBAAgB,QAAQ,CAAC,MAAM,KAAK,OAAO;AACpD", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/tweet/TweetCard.tsx"], "sourcesContent": ["/**\n * Tweet Card Component\n * Displays individual tweets with launch button\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { EnrichedTweet } from '@/types/twitter';\nimport { formatDistanceToNow } from '@/utils/dateUtils';\n\ninterface TweetCardProps {\n  tweet: EnrichedTweet;\n  onLaunchClick: (tweet: EnrichedTweet) => void;\n  onTweetClick?: (tweet: EnrichedTweet) => void;\n  className?: string;\n}\n\nexport function TweetCard({ tweet, onLaunchClick, onTweetClick, className = '' }: TweetCardProps) {\n  const [imageError, setImageError] = useState(false);\n  const [isLaunching, setIsLaunching] = useState(false);\n\n  const handleLaunchClick = async (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setIsLaunching(true);\n    try {\n      await onLaunchClick(tweet);\n    } finally {\n      setIsLaunching(false);\n    }\n  };\n\n  const handleTweetClick = () => {\n    // Open tweet in new tab\n    const tweetUrl = `https://twitter.com/${tweet.author.username}/status/${tweet.id}`;\n    window.open(tweetUrl, '_blank', 'noopener,noreferrer');\n\n    // Also call the optional callback\n    if (onTweetClick) {\n      onTweetClick(tweet);\n    }\n  };\n\n  const formatNumber = (num: number): string => {\n    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;\n    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;\n    return num.toString();\n  };\n\n  const getEngagementScore = (): number => {\n    const metrics = tweet.public_metrics;\n    if (!metrics) return 0;\n    return metrics.like_count + metrics.retweet_count * 2 + metrics.reply_count;\n  };\n\n  const renderTweetText = (text: string) => {\n    // Simple regex to highlight hashtags and cashtags\n    return text.replace(\n      /(#\\w+|\\$\\w+)/g,\n      '<span class=\"text-cyan-400 font-semibold\">$1</span>'\n    );\n  };\n\n  const timeAgo = formatDistanceToNow(new Date(tweet.created_at));\n  const engagementScore = getEngagementScore();\n\n  return (\n    <div \n      className={`bg-gray-900/50 border border-gray-700/50 rounded-xl p-6 hover:bg-gray-900/70 transition-all duration-300 cursor-pointer hover:border-gray-600/50 ${className}`}\n      onClick={handleTweetClick}\n    >\n      {/* Header */}\n      <div className=\"flex items-start justify-between mb-4\">\n        <div className=\"flex items-center space-x-3\">\n          {/* Profile Image */}\n          <div className=\"relative\">\n            {tweet.author.profile_image_url && !imageError ? (\n              <img\n                src={tweet.author.profile_image_url}\n                alt={`${tweet.author.name} avatar`}\n                className=\"w-12 h-12 rounded-full object-cover\"\n                onError={() => setImageError(true)}\n              />\n            ) : (\n              <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">\n                  {tweet.author.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n            )}\n            {tweet.author.verified && (\n              <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center\">\n                <svg className=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n            )}\n          </div>\n\n          {/* User Info */}\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-center space-x-2\">\n              <h3 className=\"text-white font-semibold truncate\">{tweet.author.name}</h3>\n              {tweet.author.verified && (\n                <svg className=\"w-4 h-4 text-blue-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n              )}\n            </div>\n            <p className=\"text-gray-400 text-sm\">@{tweet.author.username}</p>\n          </div>\n        </div>\n\n        {/* Time and Engagement Score */}\n        <div className=\"text-right\">\n          <p className=\"text-gray-500 text-sm\">{timeAgo}</p>\n          <div className=\"flex items-center space-x-1 mt-1\">\n            <svg className=\"w-4 h-4 text-yellow-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n            </svg>\n            <span className=\"text-yellow-500 text-sm font-medium\">{engagementScore}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Tweet Content */}\n      <div className=\"mb-4\">\n        <p \n          className=\"text-gray-200 leading-relaxed\"\n          dangerouslySetInnerHTML={{ __html: renderTweetText(tweet.text) }}\n        />\n      </div>\n\n      {/* Media */}\n      {tweet.media && tweet.media.length > 0 && (\n        <div className=\"mb-4\">\n          <div className=\"grid grid-cols-1 gap-2\">\n            {tweet.media.slice(0, 1).map((media, index) => (\n              <div key={index} className=\"relative rounded-lg overflow-hidden\">\n                {media.type === 'photo' && media.url && (\n                  <img\n                    src={media.url}\n                    alt={media.alt_text || 'Tweet image'}\n                    className=\"w-full h-48 object-cover\"\n                  />\n                )}\n                {media.type === 'video' && media.preview_image_url && (\n                  <div className=\"relative\">\n                    <img\n                      src={media.preview_image_url}\n                      alt=\"Video preview\"\n                      className=\"w-full h-48 object-cover\"\n                    />\n                    <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\n                      <div className=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center\">\n                        <svg className=\"w-6 h-6 text-white ml-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n                        </svg>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Token Potential Preview */}\n      {tweet.token_potential && (\n        <div className=\"mb-4 p-3 bg-purple-900/20 border border-purple-500/30 rounded-lg\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <svg className=\"w-4 h-4 text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <span className=\"text-purple-400 text-sm font-medium\">Token Potential</span>\n          </div>\n          <div className=\"grid grid-cols-2 gap-2 text-sm\">\n            <div>\n              <span className=\"text-gray-400\">Name:</span>\n              <span className=\"text-white ml-2\">{tweet.token_potential.name_suggestion}</span>\n            </div>\n            <div>\n              <span className=\"text-gray-400\">Symbol:</span>\n              <span className=\"text-cyan-400 ml-2\">${tweet.token_potential.symbol_suggestion}</span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Engagement Stats */}\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-6 text-gray-400\">\n          <div className=\"flex items-center space-x-1\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n            </svg>\n            <span className=\"text-sm\">{formatNumber(tweet.public_metrics?.reply_count || 0)}</span>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n            </svg>\n            <span className=\"text-sm\">{formatNumber(tweet.public_metrics?.retweet_count || 0)}</span>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n            </svg>\n            <span className=\"text-sm\">{formatNumber(tweet.public_metrics?.like_count || 0)}</span>\n          </div>\n        </div>\n\n        {/* Launch Status */}\n        {tweet.launch_status && tweet.launch_status !== 'not_launched' && (\n          <div className=\"flex items-center space-x-2\">\n            {tweet.launch_status === 'launched' && (\n              <span className=\"px-2 py-1 bg-green-900/30 border border-green-500/30 text-green-400 text-xs rounded-full\">\n                Launched\n              </span>\n            )}\n            {tweet.launch_status === 'launching' && (\n              <span className=\"px-2 py-1 bg-yellow-900/30 border border-yellow-500/30 text-yellow-400 text-xs rounded-full\">\n                Launching...\n              </span>\n            )}\n            {tweet.launch_status === 'failed' && (\n              <span className=\"px-2 py-1 bg-red-900/30 border border-red-500/30 text-red-400 text-xs rounded-full\">\n                Failed\n              </span>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Launch Button */}\n      <button\n        type=\"button\"\n        onClick={handleLaunchClick}\n        disabled={isLaunching || tweet.launch_status === 'launched' || tweet.launch_status === 'launching'}\n        className=\"w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:to-gray-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center justify-center space-x-2 border border-orange-400/30 hover:border-orange-300/50 disabled:border-gray-600/30\"\n      >\n        {isLaunching ? (\n          <>\n            <svg className=\"animate-spin w-5 h-5\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <span>Launching...</span>\n          </>\n        ) : tweet.launch_status === 'launched' ? (\n          <>\n            <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n            </svg>\n            <span>Launched</span>\n          </>\n        ) : (\n          <>\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <span>Launch with AI Art 🎨</span>\n          </>\n        )}\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAEA;AAJA;;;;AAaO,SAAS,UAAU,EAAE,KAAK,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,EAAkB;IAC9F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB,OAAO;QAC/B,EAAE,eAAe;QACjB,eAAe;QACf,IAAI;YACF,MAAM,cAAc;QACtB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB;QACvB,wBAAwB;QACxB,MAAM,WAAW,CAAC,oBAAoB,EAAE,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;QAClF,OAAO,IAAI,CAAC,UAAU,UAAU;QAEhC,kCAAkC;QAClC,IAAI,cAAc;YAChB,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS,OAAO,GAAG,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3D,IAAI,OAAO,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACrD,OAAO,IAAI,QAAQ;IACrB;IAEA,MAAM,qBAAqB;QACzB,MAAM,UAAU,MAAM,cAAc;QACpC,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO,QAAQ,UAAU,GAAG,QAAQ,aAAa,GAAG,IAAI,QAAQ,WAAW;IAC7E;IAEA,MAAM,kBAAkB,CAAC;QACvB,kDAAkD;QAClD,OAAO,KAAK,OAAO,CACjB,iBACA;IAEJ;IAEA,MAAM,UAAU,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,MAAM,UAAU;IAC7D,MAAM,kBAAkB;IAExB,qBACE,8OAAC;QACC,WAAW,CAAC,iJAAiJ,EAAE,WAAW;QAC1K,SAAS;;0BAGT,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,MAAM,CAAC,iBAAiB,IAAI,CAAC,2BAClC,8OAAC;wCACC,KAAK,MAAM,MAAM,CAAC,iBAAiB;wCACnC,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;wCAClC,WAAU;wCACV,SAAS,IAAM,cAAc;;;;;6DAG/B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;oCAI7C,MAAM,MAAM,CAAC,QAAQ,kBACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAe,SAAQ;sDAC9D,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;;;;;;;;;;;;0CAOjK,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC,MAAM,MAAM,CAAC,IAAI;;;;;;4CACnE,MAAM,MAAM,CAAC,QAAQ,kBACpB,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;kDAI/J,8OAAC;wCAAE,WAAU;;4CAAwB;4CAAE,MAAM,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;kCAKhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAe,SAAQ;kDACnE,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;kDAEV,8OAAC;wCAAK,WAAU;kDAAuC;;;;;;;;;;;;;;;;;;;;;;;;0BAM7D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,yBAAyB;wBAAE,QAAQ,gBAAgB,MAAM,IAAI;oBAAE;;;;;;;;;;;YAKlE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,mBACnC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACnC,8OAAC;4BAAgB,WAAU;;gCACxB,MAAM,IAAI,KAAK,WAAW,MAAM,GAAG,kBAClC,8OAAC;oCACC,KAAK,MAAM,GAAG;oCACd,KAAK,MAAM,QAAQ,IAAI;oCACvB,WAAU;;;;;;gCAGb,MAAM,IAAI,KAAK,WAAW,MAAM,iBAAiB,kBAChD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK,MAAM,iBAAiB;4CAC5B,KAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAe,SAAQ;8DACnE,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA0G,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAlBhJ;;;;;;;;;;;;;;;YA+BjB,MAAM,eAAe,kBACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA0B,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjF,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;0CAEvE,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;kCAExD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAmB,MAAM,eAAe,CAAC,eAAe;;;;;;;;;;;;0CAE1E,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CAAqB;4CAAE,MAAM,eAAe,CAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCAAK,WAAU;kDAAW,aAAa,MAAM,cAAc,EAAE,eAAe;;;;;;;;;;;;0CAE/E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCAAK,WAAU;kDAAW,aAAa,MAAM,cAAc,EAAE,iBAAiB;;;;;;;;;;;;0CAEjF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCAAK,WAAU;kDAAW,aAAa,MAAM,cAAc,EAAE,cAAc;;;;;;;;;;;;;;;;;;oBAK/E,MAAM,aAAa,IAAI,MAAM,aAAa,KAAK,gCAC9C,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,aAAa,KAAK,4BACvB,8OAAC;gCAAK,WAAU;0CAA2F;;;;;;4BAI5G,MAAM,aAAa,KAAK,6BACvB,8OAAC;gCAAK,WAAU;0CAA8F;;;;;;4BAI/G,MAAM,aAAa,KAAK,0BACvB,8OAAC;gCAAK,WAAU;0CAAqF;;;;;;;;;;;;;;;;;;0BAS7G,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,UAAU,eAAe,MAAM,aAAa,KAAK,cAAc,MAAM,aAAa,KAAK;gBACvF,WAAU;0BAET,4BACC;;sCACE,8OAAC;4BAAI,WAAU;4BAAuB,MAAK;4BAAO,SAAQ;;8CACxD,8OAAC;oCAAO,WAAU;oCAAa,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,QAAO;oCAAe,aAAY;;;;;;8CACxF,8OAAC;oCAAK,WAAU;oCAAa,MAAK;oCAAe,GAAE;;;;;;;;;;;;sCAErD,8OAAC;sCAAK;;;;;;;mCAEN,MAAM,aAAa,KAAK,2BAC1B;;sCACE,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAe,SAAQ;sCACnD,cAAA,8OAAC;gCAAK,UAAS;gCAAU,GAAE;gCAAqH,UAAS;;;;;;;;;;;sCAE3J,8OAAC;sCAAK;;;;;;;iDAGR;;sCACE,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/services/twitterApi.ts"], "sourcesContent": ["/**\n * Twitter API Service\n * Handles all Twitter API interactions for Tweet-Powered Raydium Coin Launcher\n */\n\nimport { \n  TwitterApiResponse, \n  TwitterSearchParams, \n  EnrichedTweet, \n  FavoriteCreator,\n  TwitterApiError,\n  MockTweetData\n} from '@/types/twitter';\n\nclass TwitterApiService {\n  private bearerToken: string | null = null;\n  private baseUrl = 'https://api.twitter.com/2';\n  private useMockData = process.env.NODE_ENV === 'development';\n\n  constructor() {\n    this.bearerToken = process.env.NEXT_PUBLIC_TWITTER_BEARER_TOKEN || null;\n  }\n\n  /**\n   * Search for tweets based on query parameters\n   */\n  async searchTweets(params: TwitterSearchParams): Promise<EnrichedTweet[]> {\n    if (this.useMockData) {\n      return this.getMockTweets();\n    }\n\n    if (!this.bearerToken) {\n      console.warn('Twitter Bearer Token not configured, using mock data');\n      return this.getMockTweets();\n    }\n\n    try {\n      const queryParams = new URLSearchParams({\n        query: params.query,\n        max_results: (params.max_results || 10).toString(),\n        'tweet.fields': (params.tweet_fields || [\n          'created_at',\n          'public_metrics',\n          'entities',\n          'attachments',\n          'referenced_tweets'\n        ]).join(','),\n        'user.fields': (params.user_fields || [\n          'username',\n          'name',\n          'profile_image_url',\n          'verified',\n          'public_metrics'\n        ]).join(','),\n        'media.fields': (params.media_fields || [\n          'url',\n          'preview_image_url',\n          'type',\n          'width',\n          'height',\n          'alt_text'\n        ]).join(','),\n        expansions: (params.expansions || [\n          'author_id',\n          'attachments.media_keys',\n          'referenced_tweets.id'\n        ]).join(',')\n      });\n\n      const response = await fetch(`${this.baseUrl}/tweets/search/recent?${queryParams}`, {\n        headers: {\n          'Authorization': `Bearer ${this.bearerToken}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        throw new Error(`Twitter API error: ${response.status} ${response.statusText}`);\n      }\n\n      const data: TwitterApiResponse = await response.json();\n      return this.enrichTweets(data);\n    } catch (error) {\n      console.error('Error fetching tweets:', error);\n      // Fallback to mock data on error\n      return this.getMockTweets();\n    }\n  }\n\n  /**\n   * Get tweets from favorite creators\n   */\n  async getCreatorTweets(creators: FavoriteCreator[]): Promise<EnrichedTweet[]> {\n    if (this.useMockData) {\n      return this.getMockCreatorTweets();\n    }\n\n    const allTweets: EnrichedTweet[] = [];\n\n    for (const creator of creators.slice(0, 5)) { // Limit to avoid rate limits\n      try {\n        const tweets = await this.searchTweets({\n          query: `from:${creator.username} -is:retweet`,\n          max_results: 5,\n        });\n        allTweets.push(...tweets);\n      } catch (error) {\n        console.error(`Error fetching tweets for ${creator.username}:`, error);\n      }\n    }\n\n    return allTweets.sort((a, b) => \n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n    );\n  }\n\n  /**\n   * Get trending popular tweets (general viral content)\n   */\n  async getTrendingTweets(): Promise<EnrichedTweet[]> {\n    const trendingQueries = [\n      '-is:retweet lang:en has:images min_faves:1000',\n      '-is:retweet lang:en min_faves:5000',\n      'viral -is:retweet lang:en min_faves:2000',\n      'trending -is:retweet lang:en min_faves:1500',\n      'meme -is:retweet lang:en has:images min_faves:1000',\n      'funny -is:retweet lang:en min_faves:2000',\n    ];\n\n    const allTweets: EnrichedTweet[] = [];\n\n    for (const query of trendingQueries) {\n      try {\n        const tweets = await this.searchTweets({\n          query,\n          max_results: 15,\n        });\n        allTweets.push(...tweets);\n      } catch (error) {\n        console.error(`Error fetching trending tweets for query \"${query}\":`, error);\n      }\n    }\n\n    // Remove duplicates and sort by engagement\n    const uniqueTweets = this.removeDuplicateTweets(allTweets);\n    return this.sortByEngagement(uniqueTweets).slice(0, 25);\n  }\n\n  /**\n   * Enrich tweets with additional data and token potential analysis\n   */\n  private enrichTweets(apiResponse: TwitterApiResponse): EnrichedTweet[] {\n    if (!apiResponse.data) return [];\n\n    const users = apiResponse.includes?.users || [];\n    const media = apiResponse.includes?.media || [];\n\n    return apiResponse.data.map(tweet => {\n      const author = users.find(user => user.id === tweet.author_id);\n      const tweetMedia = tweet.attachments?.media_keys?.map(key => \n        media.find(m => m.media_key === key)\n      ).filter(Boolean) || [];\n\n      const enriched: EnrichedTweet = {\n        ...tweet,\n        author: author || {\n          id: tweet.author_id,\n          username: 'unknown',\n          name: 'Unknown User'\n        },\n        media: tweetMedia,\n        token_potential: this.analyzeTokenPotential(tweet),\n        launch_status: 'not_launched'\n      };\n\n      return enriched;\n    });\n  }\n\n  /**\n   * Analyze tweet for token creation potential\n   */\n  private analyzeTokenPotential(tweet: any) {\n    const text = tweet.text.toLowerCase();\n    const hashtags = tweet.entities?.hashtags?.map((h: any) => h.tag) || [];\n    const cashtags = tweet.entities?.cashtags?.map((c: any) => c.tag) || [];\n\n    // Extract potential token name from tweet\n    let name_suggestion = '';\n    let symbol_suggestion = '';\n\n    // Look for cashtags first\n    if (cashtags.length > 0) {\n      symbol_suggestion = cashtags[0].toUpperCase();\n      name_suggestion = cashtags[0];\n    } else if (hashtags.length > 0) {\n      // Use first hashtag as potential name\n      name_suggestion = hashtags[0];\n      symbol_suggestion = hashtags[0].substring(0, 6).toUpperCase();\n    } else {\n      // Extract from text\n      const words = text.split(' ').filter(word => \n        word.length > 2 && \n        word.length < 15 && \n        !word.includes('http') &&\n        !word.includes('@')\n      );\n      if (words.length > 0) {\n        name_suggestion = words[0];\n        symbol_suggestion = words[0].substring(0, 6).toUpperCase();\n      }\n    }\n\n    return {\n      name_suggestion: name_suggestion || 'MEME',\n      symbol_suggestion: symbol_suggestion || 'MEME',\n      description_suggestion: tweet.text.substring(0, 100) + (tweet.text.length > 100 ? '...' : ''),\n      hashtags,\n      cashtags\n    };\n  }\n\n  /**\n   * Remove duplicate tweets\n   */\n  private removeDuplicateTweets(tweets: EnrichedTweet[]): EnrichedTweet[] {\n    const seen = new Set();\n    return tweets.filter(tweet => {\n      if (seen.has(tweet.id)) return false;\n      seen.add(tweet.id);\n      return true;\n    });\n  }\n\n  /**\n   * Sort tweets by engagement score\n   */\n  private sortByEngagement(tweets: EnrichedTweet[]): EnrichedTweet[] {\n    return tweets.sort((a, b) => {\n      const aScore = (a.public_metrics?.like_count || 0) + \n                    (a.public_metrics?.retweet_count || 0) * 2 +\n                    (a.public_metrics?.reply_count || 0);\n      const bScore = (b.public_metrics?.like_count || 0) + \n                    (b.public_metrics?.retweet_count || 0) * 2 +\n                    (b.public_metrics?.reply_count || 0);\n      return bScore - aScore;\n    });\n  }\n\n  /**\n   * Mock data for development - Popular viral tweets\n   */\n  private getMockTweets(): EnrichedTweet[] {\n    return [\n      {\n        id: '1',\n        text: 'POV: You\\'re explaining to your mom why you bought a digital picture of a monkey for $50,000 💀',\n        author_id: '1',\n        created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago\n        public_metrics: {\n          retweet_count: 12500,\n          like_count: 89000,\n          reply_count: 3400,\n          quote_count: 2100\n        },\n        author: {\n          id: '1',\n          username: 'memegod',\n          name: 'Meme Lord 👑',\n          profile_image_url: 'https://pbs.twimg.com/profile_images/1234567890/avatar.jpg',\n          verified: false\n        },\n        token_potential: {\n          name_suggestion: 'MONKEY',\n          symbol_suggestion: 'MONKEY',\n          description_suggestion: 'POV: You\\'re explaining to your mom why you bought a digital picture of a monkey for $50,000 💀',\n          hashtags: [],\n          cashtags: []\n        },\n        launch_status: 'not_launched'\n      },\n      {\n        id: '2',\n        text: 'Me: I\\'m going to be productive today\\n\\nAlso me: *watches 47 TikToks about cats wearing tiny hats*',\n        author_id: '2',\n        created_at: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago\n        public_metrics: {\n          retweet_count: 8900,\n          like_count: 156000,\n          reply_count: 2800,\n          quote_count: 1200\n        },\n        author: {\n          id: '2',\n          username: 'relatablequeen',\n          name: 'Sarah ✨',\n          profile_image_url: 'https://pbs.twimg.com/profile_images/1234567891/avatar2.jpg',\n          verified: true\n        },\n        token_potential: {\n          name_suggestion: 'CATHAT',\n          symbol_suggestion: 'CATHAT',\n          description_suggestion: 'Me: I\\'m going to be productive today Also me: *watches 47 TikToks about cats wearing tiny hats*',\n          hashtags: [],\n          cashtags: []\n        },\n        launch_status: 'not_launched'\n      },\n      {\n        id: '3',\n        text: 'Breaking: Local man discovers that \"just one more episode\" is actually a lie told by Netflix to keep you awake until 3am',\n        author_id: '3',\n        created_at: new Date(Date.now() - 1000 * 60 * 90).toISOString(), // 1.5 hours ago\n        public_metrics: {\n          retweet_count: 15600,\n          like_count: 203000,\n          reply_count: 4200,\n          quote_count: 3100\n        },\n        author: {\n          id: '3',\n          username: 'netflixaddict',\n          name: 'Binge Watcher 📺',\n          profile_image_url: 'https://pbs.twimg.com/profile_images/1234567892/avatar3.jpg',\n          verified: false\n        },\n        token_potential: {\n          name_suggestion: 'ONEMORE',\n          symbol_suggestion: 'ONEMORE',\n          description_suggestion: 'Breaking: Local man discovers that \"just one more episode\" is actually a lie told by Netflix',\n          hashtags: [],\n          cashtags: []\n        },\n        launch_status: 'not_launched'\n      },\n      {\n        id: '4',\n        text: 'Normalize saying \"I don\\'t know\" instead of making up random facts that sound convincing',\n        author_id: '4',\n        created_at: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago\n        public_metrics: {\n          retweet_count: 22000,\n          like_count: 178000,\n          reply_count: 5600,\n          quote_count: 4300\n        },\n        author: {\n          id: '4',\n          username: 'wisdomtweets',\n          name: 'Daily Wisdom 🧠',\n          profile_image_url: 'https://pbs.twimg.com/profile_images/1234567893/avatar4.jpg',\n          verified: true\n        },\n        token_potential: {\n          name_suggestion: 'IDK',\n          symbol_suggestion: 'IDK',\n          description_suggestion: 'Normalize saying \"I don\\'t know\" instead of making up random facts that sound convincing',\n          hashtags: [],\n          cashtags: []\n        },\n        launch_status: 'not_launched'\n      },\n      {\n        id: '5',\n        text: 'The four stages of online shopping:\\n1. I need this\\n2. I want this\\n3. I deserve this\\n4. *clicks buy now*',\n        author_id: '5',\n        created_at: new Date(Date.now() - 1000 * 60 * 180).toISOString(), // 3 hours ago\n        public_metrics: {\n          retweet_count: 18700,\n          like_count: 267000,\n          reply_count: 7800,\n          quote_count: 5200\n        },\n        author: {\n          id: '5',\n          username: 'shoppingmemes',\n          name: 'Retail Therapy 🛍️',\n          profile_image_url: 'https://pbs.twimg.com/profile_images/1234567894/avatar5.jpg',\n          verified: false\n        },\n        token_potential: {\n          name_suggestion: 'BUYNOW',\n          symbol_suggestion: 'BUYNOW',\n          description_suggestion: 'The four stages of online shopping: I need this, I want this, I deserve this, *clicks buy now*',\n          hashtags: [],\n          cashtags: []\n        },\n        launch_status: 'not_launched'\n      },\n      {\n        id: '6',\n        text: 'Shoutout to everyone who\\'s been \"just leaving in 5 minutes\" for the past 2 hours',\n        author_id: '6',\n        created_at: new Date(Date.now() - 1000 * 60 * 240).toISOString(), // 4 hours ago\n        public_metrics: {\n          retweet_count: 31200,\n          like_count: 445000,\n          reply_count: 12000,\n          quote_count: 8900\n        },\n        author: {\n          id: '6',\n          username: 'procrastinator',\n          name: 'Always Late ⏰',\n          profile_image_url: 'https://pbs.twimg.com/profile_images/1234567895/avatar6.jpg',\n          verified: false\n        },\n        token_potential: {\n          name_suggestion: 'FIVEMIN',\n          symbol_suggestion: 'FIVEMIN',\n          description_suggestion: 'Shoutout to everyone who\\'s been \"just leaving in 5 minutes\" for the past 2 hours',\n          hashtags: [],\n          cashtags: []\n        },\n        launch_status: 'not_launched'\n      }\n    ];\n  }\n\n  private getMockCreatorTweets(): EnrichedTweet[] {\n    return this.getMockTweets();\n  }\n}\n\nexport const twitterApi = new TwitterApiService();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAWD,MAAM;IACI,cAA6B,KAAK;IAClC,UAAU,4BAA4B;IACtC,cAAc,oDAAyB,cAAc;IAE7D,aAAc;QACZ,IAAI,CAAC,WAAW,GAAG,QAAQ,GAAG,CAAC,gCAAgC,IAAI;IACrE;IAEA;;GAEC,GACD,MAAM,aAAa,MAA2B,EAA4B;QACxE,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAI,CAAC,aAAa;QAC3B;QAEA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,aAAa;QAC3B;QAEA,IAAI;YACF,MAAM,cAAc,IAAI,gBAAgB;gBACtC,OAAO,OAAO,KAAK;gBACnB,aAAa,CAAC,OAAO,WAAW,IAAI,EAAE,EAAE,QAAQ;gBAChD,gBAAgB,CAAC,OAAO,YAAY,IAAI;oBACtC;oBACA;oBACA;oBACA;oBACA;iBACD,EAAE,IAAI,CAAC;gBACR,eAAe,CAAC,OAAO,WAAW,IAAI;oBACpC;oBACA;oBACA;oBACA;oBACA;iBACD,EAAE,IAAI,CAAC;gBACR,gBAAgB,CAAC,OAAO,YAAY,IAAI;oBACtC;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD,EAAE,IAAI,CAAC;gBACR,YAAY,CAAC,OAAO,UAAU,IAAI;oBAChC;oBACA;oBACA;iBACD,EAAE,IAAI,CAAC;YACV;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,aAAa,EAAE;gBAClF,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;oBAC7C,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAChF;YAEA,MAAM,OAA2B,MAAM,SAAS,IAAI;YACpD,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,iCAAiC;YACjC,OAAO,IAAI,CAAC,aAAa;QAC3B;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,QAA2B,EAA4B;QAC5E,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAI,CAAC,oBAAoB;QAClC;QAEA,MAAM,YAA6B,EAAE;QAErC,KAAK,MAAM,WAAW,SAAS,KAAK,CAAC,GAAG,GAAI;YAC1C,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;oBACrC,OAAO,CAAC,KAAK,EAAE,QAAQ,QAAQ,CAAC,YAAY,CAAC;oBAC7C,aAAa;gBACf;gBACA,UAAU,IAAI,IAAI;YACpB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,QAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE;YAClE;QACF;QAEA,OAAO,UAAU,IAAI,CAAC,CAAC,GAAG,IACxB,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA;;GAEC,GACD,MAAM,oBAA8C;QAClD,MAAM,kBAAkB;YACtB;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,YAA6B,EAAE;QAErC,KAAK,MAAM,SAAS,gBAAiB;YACnC,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;oBACrC;oBACA,aAAa;gBACf;gBACA,UAAU,IAAI,IAAI;YACpB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,MAAM,EAAE,CAAC,EAAE;YACxE;QACF;QAEA,2CAA2C;QAC3C,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;QAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,KAAK,CAAC,GAAG;IACtD;IAEA;;GAEC,GACD,AAAQ,aAAa,WAA+B,EAAmB;QACrE,IAAI,CAAC,YAAY,IAAI,EAAE,OAAO,EAAE;QAEhC,MAAM,QAAQ,YAAY,QAAQ,EAAE,SAAS,EAAE;QAC/C,MAAM,QAAQ,YAAY,QAAQ,EAAE,SAAS,EAAE;QAE/C,OAAO,YAAY,IAAI,CAAC,GAAG,CAAC,CAAA;YAC1B,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,SAAS;YAC7D,MAAM,aAAa,MAAM,WAAW,EAAE,YAAY,IAAI,CAAA,MACpD,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,MAChC,OAAO,YAAY,EAAE;YAEvB,MAAM,WAA0B;gBAC9B,GAAG,KAAK;gBACR,QAAQ,UAAU;oBAChB,IAAI,MAAM,SAAS;oBACnB,UAAU;oBACV,MAAM;gBACR;gBACA,OAAO;gBACP,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;gBAC5C,eAAe;YACjB;YAEA,OAAO;QACT;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,KAAU,EAAE;QACxC,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW;QACnC,MAAM,WAAW,MAAM,QAAQ,EAAE,UAAU,IAAI,CAAC,IAAW,EAAE,GAAG,KAAK,EAAE;QACvE,MAAM,WAAW,MAAM,QAAQ,EAAE,UAAU,IAAI,CAAC,IAAW,EAAE,GAAG,KAAK,EAAE;QAEvE,0CAA0C;QAC1C,IAAI,kBAAkB;QACtB,IAAI,oBAAoB;QAExB,0BAA0B;QAC1B,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,oBAAoB,QAAQ,CAAC,EAAE,CAAC,WAAW;YAC3C,kBAAkB,QAAQ,CAAC,EAAE;QAC/B,OAAO,IAAI,SAAS,MAAM,GAAG,GAAG;YAC9B,sCAAsC;YACtC,kBAAkB,QAAQ,CAAC,EAAE;YAC7B,oBAAoB,QAAQ,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW;QAC7D,OAAO;YACL,oBAAoB;YACpB,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,OACnC,KAAK,MAAM,GAAG,KACd,KAAK,MAAM,GAAG,MACd,CAAC,KAAK,QAAQ,CAAC,WACf,CAAC,KAAK,QAAQ,CAAC;YAEjB,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,kBAAkB,KAAK,CAAC,EAAE;gBAC1B,oBAAoB,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW;YAC1D;QACF;QAEA,OAAO;YACL,iBAAiB,mBAAmB;YACpC,mBAAmB,qBAAqB;YACxC,wBAAwB,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,QAAQ,EAAE;YAC5F;YACA;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,MAAuB,EAAmB;QACtE,MAAM,OAAO,IAAI;QACjB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,IAAI,KAAK,GAAG,CAAC,MAAM,EAAE,GAAG,OAAO;YAC/B,KAAK,GAAG,CAAC,MAAM,EAAE;YACjB,OAAO;QACT;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAiB,MAAuB,EAAmB;QACjE,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG;YACrB,MAAM,SAAS,CAAC,EAAE,cAAc,EAAE,cAAc,CAAC,IACnC,CAAC,EAAE,cAAc,EAAE,iBAAiB,CAAC,IAAI,IACzC,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC;YACjD,MAAM,SAAS,CAAC,EAAE,cAAc,EAAE,cAAc,CAAC,IACnC,CAAC,EAAE,cAAc,EAAE,iBAAiB,CAAC,IAAI,IACzC,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC;YACjD,OAAO,SAAS;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAiC;QACvC,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,IAAI,WAAW;gBAC7D,gBAAgB;oBACd,eAAe;oBACf,YAAY;oBACZ,aAAa;oBACb,aAAa;gBACf;gBACA,QAAQ;oBACN,IAAI;oBACJ,UAAU;oBACV,MAAM;oBACN,mBAAmB;oBACnB,UAAU;gBACZ;gBACA,iBAAiB;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,wBAAwB;oBACxB,UAAU,EAAE;oBACZ,UAAU,EAAE;gBACd;gBACA,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,IAAI,WAAW;gBAC7D,gBAAgB;oBACd,eAAe;oBACf,YAAY;oBACZ,aAAa;oBACb,aAAa;gBACf;gBACA,QAAQ;oBACN,IAAI;oBACJ,UAAU;oBACV,MAAM;oBACN,mBAAmB;oBACnB,UAAU;gBACZ;gBACA,iBAAiB;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,wBAAwB;oBACxB,UAAU,EAAE;oBACZ,UAAU,EAAE;gBACd;gBACA,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,IAAI,WAAW;gBAC7D,gBAAgB;oBACd,eAAe;oBACf,YAAY;oBACZ,aAAa;oBACb,aAAa;gBACf;gBACA,QAAQ;oBACN,IAAI;oBACJ,UAAU;oBACV,MAAM;oBACN,mBAAmB;oBACnB,UAAU;gBACZ;gBACA,iBAAiB;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,wBAAwB;oBACxB,UAAU,EAAE;oBACZ,UAAU,EAAE;gBACd;gBACA,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,WAAW;gBAC9D,gBAAgB;oBACd,eAAe;oBACf,YAAY;oBACZ,aAAa;oBACb,aAAa;gBACf;gBACA,QAAQ;oBACN,IAAI;oBACJ,UAAU;oBACV,MAAM;oBACN,mBAAmB;oBACnB,UAAU;gBACZ;gBACA,iBAAiB;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,wBAAwB;oBACxB,UAAU,EAAE;oBACZ,UAAU,EAAE;gBACd;gBACA,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,WAAW;gBAC9D,gBAAgB;oBACd,eAAe;oBACf,YAAY;oBACZ,aAAa;oBACb,aAAa;gBACf;gBACA,QAAQ;oBACN,IAAI;oBACJ,UAAU;oBACV,MAAM;oBACN,mBAAmB;oBACnB,UAAU;gBACZ;gBACA,iBAAiB;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,wBAAwB;oBACxB,UAAU,EAAE;oBACZ,UAAU,EAAE;gBACd;gBACA,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,WAAW;gBAC9D,gBAAgB;oBACd,eAAe;oBACf,YAAY;oBACZ,aAAa;oBACb,aAAa;gBACf;gBACA,QAAQ;oBACN,IAAI;oBACJ,UAAU;oBACV,MAAM;oBACN,mBAAmB;oBACnB,UAAU;gBACZ;gBACA,iBAAiB;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,wBAAwB;oBACxB,UAAU,EAAE;oBACZ,UAAU,EAAE;gBACd;gBACA,eAAe;YACjB;SACD;IACH;IAEQ,uBAAwC;QAC9C,OAAO,IAAI,CAAC,aAAa;IAC3B;AACF;AAEO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/config/favoriteCreators.ts"], "sourcesContent": ["/**\n * Favorite Creators Configuration\n * Curated list of crypto/meme coin creators to track\n */\n\nimport { FavoriteCreator } from '@/types/twitter';\n\nexport const favoriteCreators: FavoriteCreator[] = [\n  {\n    id: '44196397',\n    username: '<PERSON><PERSON><PERSON><PERSON>',\n    name: '<PERSON><PERSON>',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/1683325380441128960/yRsRRjGO_400x400.jpg',\n    description: '<PERSON><PERSON>, SpaceX, Neuralink, The Boring Company',\n    verified: true,\n    category: 'influencer',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 10\n  },\n  {\n    id: '1234567890',\n    username: 'mrb<PERSON><PERSON>',\n    name: '<PERSON><PERSON><PERSON><PERSON>',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/1234567890/mrbeast_400x400.jpg',\n    description: 'Helping 1,000,000 people',\n    verified: true,\n    category: 'influencer',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 9\n  },\n  {\n    id: '*********',\n    username: 'r<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    name: '<PERSON>',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/*********/ryan_400x400.jpg',\n    description: 'Actor, producer, and occasional gin salesman',\n    verified: true,\n    category: 'influencer',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 8\n  },\n  {\n    id: '1111111111',\n    username: 'therock',\n    name: 'Dwayne Johnson',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/1111111111/rock_400x400.jpg',\n    description: 'builder of stuff chaser of dreams drunk on ambition',\n    verified: true,\n    category: 'influencer',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 7\n  },\n  {\n    id: '2222222222',\n    username: 'pewdiepie',\n    name: 'PewDiePie',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/2222222222/pewds_400x400.jpg',\n    description: 'Swedish YouTuber and gamer',\n    verified: true,\n    category: 'meme',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 6\n  },\n  {\n    id: '3333333333',\n    username: 'kanyewest',\n    name: 'ye',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/3333333333/ye_400x400.jpg',\n    description: 'Artist, designer, visionary',\n    verified: true,\n    category: 'influencer',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 5\n  },\n  {\n    id: '4444444444',\n    username: 'neiltyson',\n    name: 'Neil deGrasse Tyson',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/4444444444/neil_400x400.jpg',\n    description: 'Astrophysicist and author',\n    verified: true,\n    category: 'other',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 4\n  },\n  {\n    id: '5555555555',\n    username: 'rickygervais',\n    name: 'Ricky Gervais',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/5555555555/ricky_400x400.jpg',\n    description: 'Comedian, actor, writer, director',\n    verified: true,\n    category: 'meme',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 3\n  },\n  {\n    id: '6666666666',\n    username: 'garyvee',\n    name: 'Gary Vaynerchuk',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/6666666666/gary_400x400.jpg',\n    description: 'Entrepreneur, CEO, investor, and public speaker',\n    verified: true,\n    category: 'influencer',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 2\n  },\n  {\n    id: '**********',\n    username: 'chancetherapper',\n    name: 'Chance The Rapper',\n    profile_image_url: 'https://pbs.twimg.com/profile_images/**********/chance_400x400.jpg',\n    description: 'Rapper, singer, songwriter, record producer',\n    verified: true,\n    category: 'influencer',\n    added_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n    priority: 1\n  }\n];\n\nexport const creatorCategories = {\n  crypto: {\n    name: 'Crypto Projects',\n    description: 'Official accounts of crypto projects and protocols',\n    color: 'purple'\n  },\n  meme: {\n    name: 'Meme Coins',\n    description: 'Official meme coin project accounts',\n    color: 'yellow'\n  },\n  influencer: {\n    name: 'Influencers',\n    description: 'Crypto influencers and thought leaders',\n    color: 'blue'\n  },\n  trader: {\n    name: 'Traders',\n    description: 'Professional traders and analysts',\n    color: 'green'\n  },\n  other: {\n    name: 'Other',\n    description: 'Other relevant accounts',\n    color: 'gray'\n  }\n};\n\nexport const trendingQueries = [\n  '-is:retweet lang:en has:images min_faves:1000',\n  '-is:retweet lang:en min_faves:5000',\n  'viral -is:retweet lang:en min_faves:2000',\n  'trending -is:retweet lang:en min_faves:1500',\n  'meme -is:retweet lang:en has:images min_faves:1000',\n  'funny -is:retweet lang:en min_faves:2000',\n  'relatable -is:retweet lang:en min_faves:1500',\n  'POV -is:retweet lang:en min_faves:1000',\n  'normalize -is:retweet lang:en min_faves:1000',\n  'shoutout -is:retweet lang:en min_faves:1500'\n];\n\nexport const feedConfig = {\n  refresh_interval: 30000, // 30 seconds for more dynamic feel\n  max_tweets_per_fetch: 25,\n  cache_duration: 180000, // 3 minutes for fresher content\n  trending_weight: 0.6, // 60% trending tweets\n  creator_weight: 0.4, // 40% creator tweets\n  min_engagement_score: 5, // Minimum likes + retweets\n  max_tweet_age_hours: 24, // Only show tweets from last 24 hours\n};\n\n// Helper functions\nexport function getActiveCreators(): FavoriteCreator[] {\n  return favoriteCreators\n    .filter(creator => creator.is_active)\n    .sort((a, b) => (b.priority || 0) - (a.priority || 0));\n}\n\nexport function getCreatorsByCategory(category: string): FavoriteCreator[] {\n  return favoriteCreators.filter(creator => \n    creator.category === category && creator.is_active\n  );\n}\n\nexport function getHighPriorityCreators(limit: number = 5): FavoriteCreator[] {\n  return getActiveCreators().slice(0, limit);\n}\n\nexport function addCreator(creator: Omit<FavoriteCreator, 'added_at'>): FavoriteCreator {\n  const newCreator: FavoriteCreator = {\n    ...creator,\n    added_at: new Date().toISOString()\n  };\n  favoriteCreators.push(newCreator);\n  return newCreator;\n}\n\nexport function updateCreator(id: string, updates: Partial<FavoriteCreator>): boolean {\n  const index = favoriteCreators.findIndex(creator => creator.id === id);\n  if (index === -1) return false;\n  \n  favoriteCreators[index] = { ...favoriteCreators[index], ...updates };\n  return true;\n}\n\nexport function removeCreator(id: string): boolean {\n  const index = favoriteCreators.findIndex(creator => creator.id === id);\n  if (index === -1) return false;\n  \n  favoriteCreators.splice(index, 1);\n  return true;\n}\n\nexport function toggleCreatorStatus(id: string): boolean {\n  const creator = favoriteCreators.find(c => c.id === id);\n  if (!creator) return false;\n  \n  creator.is_active = !creator.is_active;\n  return true;\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAIM,MAAM,mBAAsC;IACjD;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,mBAAmB;QACnB,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;IACZ;CACD;AAEM,MAAM,oBAAoB;IAC/B,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA,MAAM;QACJ,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,aAAa;QACb,OAAO;IACT;AACF;AAEO,MAAM,kBAAkB;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,aAAa;IACxB,kBAAkB;IAClB,sBAAsB;IACtB,gBAAgB;IAChB,iBAAiB;IACjB,gBAAgB;IAChB,sBAAsB;IACtB,qBAAqB;AACvB;AAGO,SAAS;IACd,OAAO,iBACJ,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EACnC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC;AACxD;AAEO,SAAS,sBAAsB,QAAgB;IACpD,OAAO,iBAAiB,MAAM,CAAC,CAAA,UAC7B,QAAQ,QAAQ,KAAK,YAAY,QAAQ,SAAS;AAEtD;AAEO,SAAS,wBAAwB,QAAgB,CAAC;IACvD,OAAO,oBAAoB,KAAK,CAAC,GAAG;AACtC;AAEO,SAAS,WAAW,OAA0C;IACnE,MAAM,aAA8B;QAClC,GAAG,OAAO;QACV,UAAU,IAAI,OAAO,WAAW;IAClC;IACA,iBAAiB,IAAI,CAAC;IACtB,OAAO;AACT;AAEO,SAAS,cAAc,EAAU,EAAE,OAAiC;IACzE,MAAM,QAAQ,iBAAiB,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACnE,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,gBAAgB,CAAC,MAAM,GAAG;QAAE,GAAG,gBAAgB,CAAC,MAAM;QAAE,GAAG,OAAO;IAAC;IACnE,OAAO;AACT;AAEO,SAAS,cAAc,EAAU;IACtC,MAAM,QAAQ,iBAAiB,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACnE,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,iBAAiB,MAAM,CAAC,OAAO;IAC/B,OAAO;AACT;AAEO,SAAS,oBAAoB,EAAU;IAC5C,MAAM,UAAU,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACpD,IAAI,CAAC,SAAS,OAAO;IAErB,QAAQ,SAAS,GAAG,CAAC,QAAQ,SAAS;IACtC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1703, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/tweet/TweetFeed.tsx"], "sourcesContent": ["/**\n * Tweet Feed Component\n * Main feed displaying trending tweets and creator tweets\n */\n\n'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { TweetCard } from './TweetCard';\nimport { EnrichedTweet } from '@/types/twitter';\nimport { twitterApi } from '@/services/twitterApi';\nimport { getActiveCreators, feedConfig } from '@/config/favoriteCreators';\n\ninterface TweetFeedProps {\n  onTweetLaunch: (tweet: EnrichedTweet) => void;\n  onTweetClick?: (tweet: EnrichedTweet) => void;\n}\n\nexport function TweetFeed({ onTweetLaunch, onTweetClick }: TweetFeedProps) {\n  const [tweets, setTweets] = useState<EnrichedTweet[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [refreshing, setRefreshing] = useState(false);\n  const [autoRefreshing, setAutoRefreshing] = useState(false);\n  const [filter, setFilter] = useState<'all' | 'trending' | 'creators'>('all');\n  const [sortBy, setSortBy] = useState<'recent' | 'engagement'>('recent');\n  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());\n\n  const fetchTweets = useCallback(async () => {\n    try {\n      setError(null);\n      \n      const [trendingTweets, creatorTweets] = await Promise.all([\n        twitterApi.getTrendingTweets(),\n        twitterApi.getCreatorTweets(getActiveCreators())\n      ]);\n\n      // Combine and deduplicate tweets\n      const allTweets = [...trendingTweets, ...creatorTweets];\n      const uniqueTweets = allTweets.filter((tweet, index, self) => \n        index === self.findIndex(t => t.id === tweet.id)\n      );\n\n      // Sort tweets\n      const sortedTweets = sortTweets(uniqueTweets, sortBy);\n      \n      setTweets(sortedTweets);\n      setLastRefresh(new Date());\n    } catch (err) {\n      console.error('Error fetching tweets:', err);\n      setError('Failed to load tweets. Please try again.');\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n      setAutoRefreshing(false);\n    }\n  }, [sortBy]);\n\n  const sortTweets = (tweets: EnrichedTweet[], sortType: 'recent' | 'engagement'): EnrichedTweet[] => {\n    return [...tweets].sort((a, b) => {\n      if (sortType === 'engagement') {\n        const aScore = (a.public_metrics?.like_count || 0) + \n                      (a.public_metrics?.retweet_count || 0) * 2 +\n                      (a.public_metrics?.reply_count || 0);\n        const bScore = (b.public_metrics?.like_count || 0) + \n                      (b.public_metrics?.retweet_count || 0) * 2 +\n                      (b.public_metrics?.reply_count || 0);\n        return bScore - aScore;\n      } else {\n        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\n      }\n    });\n  };\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchTweets();\n  };\n\n  const getFilteredTweets = (): EnrichedTweet[] => {\n    if (filter === 'all') return tweets;\n    \n    const activeCreators = getActiveCreators();\n    const creatorIds = activeCreators.map(c => c.id);\n    \n    if (filter === 'creators') {\n      return tweets.filter(tweet => creatorIds.includes(tweet.author_id));\n    } else if (filter === 'trending') {\n      return tweets.filter(tweet => !creatorIds.includes(tweet.author_id));\n    }\n    \n    return tweets;\n  };\n\n  useEffect(() => {\n    fetchTweets();\n\n    // Set up auto-refresh with visual indicator\n    const interval = setInterval(() => {\n      console.log('Auto-refreshing tweets...');\n      setAutoRefreshing(true);\n      fetchTweets();\n    }, feedConfig.refresh_interval);\n\n    return () => clearInterval(interval);\n  }, [fetchTweets]);\n\n  const filteredTweets = getFilteredTweets();\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {/* Loading skeleton */}\n        {[...Array(5)].map((_, i) => (\n          <div key={i} className=\"bg-gray-900/50 border border-gray-700/50 rounded-xl p-6 animate-pulse\">\n            <div className=\"flex items-start space-x-3 mb-4\">\n              <div className=\"w-12 h-12 bg-gray-700 rounded-full\"></div>\n              <div className=\"flex-1\">\n                <div className=\"h-4 bg-gray-700 rounded w-1/3 mb-2\"></div>\n                <div className=\"h-3 bg-gray-700 rounded w-1/4\"></div>\n              </div>\n            </div>\n            <div className=\"space-y-2 mb-4\">\n              <div className=\"h-4 bg-gray-700 rounded w-full\"></div>\n              <div className=\"h-4 bg-gray-700 rounded w-3/4\"></div>\n            </div>\n            <div className=\"h-12 bg-gray-700 rounded\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"bg-red-900/20 border border-red-500/30 rounded-xl p-6 max-w-md mx-auto\">\n          <svg className=\"w-12 h-12 text-red-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n          </svg>\n          <h3 className=\"text-red-400 font-semibold mb-2\">Error Loading Tweets</h3>\n          <p className=\"text-gray-400 mb-4\">{error}</p>\n          <button\n            onClick={handleRefresh}\n            className=\"bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors\"\n          >\n            Try Again\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Feed Controls */}\n      <div className=\"bg-gray-900/50 border border-gray-700/50 rounded-xl p-4\">\n        {/* Auto-refresh indicator */}\n        {autoRefreshing && (\n          <div className=\"flex items-center justify-center mb-3 p-2 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n            <svg className=\"animate-spin w-4 h-4 text-blue-400 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <span className=\"text-blue-400 text-sm font-medium\">Refreshing viral tweets...</span>\n          </div>\n        )}\n\n        <div className=\"flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-4 sm:space-y-0\">\n          {/* Filter Tabs */}\n          <div className=\"flex items-center space-x-1 bg-gray-800/50 rounded-lg p-1\">\n            <button\n              onClick={() => setFilter('all')}\n              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                filter === 'all'\n                  ? 'bg-purple-600 text-white'\n                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'\n              }`}\n            >\n              All ({tweets.length})\n            </button>\n            <button\n              onClick={() => setFilter('trending')}\n              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                filter === 'trending'\n                  ? 'bg-orange-600 text-white'\n                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'\n              }`}\n            >\n              Trending\n            </button>\n            <button\n              onClick={() => setFilter('creators')}\n              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                filter === 'creators'\n                  ? 'bg-blue-600 text-white'\n                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'\n              }`}\n            >\n              Creators\n            </button>\n          </div>\n\n          {/* Sort and Refresh */}\n          <div className=\"flex items-center space-x-3\">\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value as 'recent' | 'engagement')}\n              className=\"bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500\"\n            >\n              <option value=\"recent\">Most Recent</option>\n              <option value=\"engagement\">Most Engaging</option>\n            </select>\n            \n            <button\n              onClick={handleRefresh}\n              disabled={refreshing}\n              className=\"bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n            >\n              <svg \n                className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} \n                fill=\"none\" \n                stroke=\"currentColor\" \n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n              </svg>\n              <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Tweet Feed */}\n      {filteredTweets.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <div className=\"bg-gray-900/50 border border-gray-700/50 rounded-xl p-8\">\n            <svg className=\"w-16 h-16 text-gray-500 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\" />\n            </svg>\n            <h3 className=\"text-gray-400 font-semibold mb-2\">No Tweets Found</h3>\n            <p className=\"text-gray-500\">\n              {filter === 'all' \n                ? 'No tweets available at the moment. Try refreshing the feed.'\n                : `No ${filter} tweets found. Try switching to a different filter.`\n              }\n            </p>\n          </div>\n        </div>\n      ) : (\n        <div className=\"space-y-6\">\n          {filteredTweets.map((tweet) => (\n            <TweetCard\n              key={tweet.id}\n              tweet={tweet}\n              onLaunchClick={onTweetLaunch}\n              onTweetClick={onTweetClick}\n            />\n          ))}\n        </div>\n      )}\n\n      {/* Load More Button */}\n      {filteredTweets.length > 0 && (\n        <div className=\"text-center\">\n          <button\n            onClick={handleRefresh}\n            className=\"bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-medium py-3 px-6 rounded-xl transition-all duration-300\"\n          >\n            Load More Tweets\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AACA;AAEA;AACA;AANA;;;;;;AAaO,SAAS,UAAU,EAAE,aAAa,EAAE,YAAY,EAAkB;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAEzD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI;YACF,SAAS;YAET,MAAM,CAAC,gBAAgB,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACxD,6HAAA,CAAA,aAAU,CAAC,iBAAiB;gBAC5B,6HAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;aAC7C;YAED,iCAAiC;YACjC,MAAM,YAAY;mBAAI;mBAAmB;aAAc;YACvD,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,OAAO,OAAO,OACnD,UAAU,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;YAGjD,cAAc;YACd,MAAM,eAAe,WAAW,cAAc;YAE9C,UAAU;YACV,eAAe,IAAI;QACrB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX,SAAU;YACR,WAAW;YACX,cAAc;YACd,kBAAkB;QACpB;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,aAAa,CAAC,QAAyB;QAC3C,OAAO;eAAI;SAAO,CAAC,IAAI,CAAC,CAAC,GAAG;YAC1B,IAAI,aAAa,cAAc;gBAC7B,MAAM,SAAS,CAAC,EAAE,cAAc,EAAE,cAAc,CAAC,IACnC,CAAC,EAAE,cAAc,EAAE,iBAAiB,CAAC,IAAI,IACzC,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC;gBACjD,MAAM,SAAS,CAAC,EAAE,cAAc,EAAE,cAAc,CAAC,IACnC,CAAC,EAAE,cAAc,EAAE,iBAAiB,CAAC,IAAI,IACzC,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC;gBACjD,OAAO,SAAS;YAClB,OAAO;gBACL,OAAO,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAC1E;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;QACd,MAAM;IACR;IAEA,MAAM,oBAAoB;QACxB,IAAI,WAAW,OAAO,OAAO;QAE7B,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;QACvC,MAAM,aAAa,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAE/C,IAAI,WAAW,YAAY;YACzB,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,WAAW,QAAQ,CAAC,MAAM,SAAS;QACnE,OAAO,IAAI,WAAW,YAAY;YAChC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,WAAW,QAAQ,CAAC,MAAM,SAAS;QACpE;QAEA,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,4CAA4C;QAC5C,MAAM,WAAW,YAAY;YAC3B,QAAQ,GAAG,CAAC;YACZ,kBAAkB;YAClB;QACF,GAAG,iIAAA,CAAA,aAAU,CAAC,gBAAgB;QAE9B,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAY;IAEhB,MAAM,iBAAiB;IAEvB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBAEZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAGnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;;;;;;mBAZP;;;;;;;;;;IAiBlB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAAsC,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC7F,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,8OAAC;wBAAG,WAAU;kCAAkC;;;;;;kCAChD,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;oBAEZ,gCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA0C,MAAK;gCAAO,SAAQ;;kDAC3E,8OAAC;wCAAO,WAAU;wCAAa,IAAG;wCAAK,IAAG;wCAAK,GAAE;wCAAK,QAAO;wCAAe,aAAY;;;;;;kDACxF,8OAAC;wCAAK,WAAU;wCAAa,MAAK;wCAAe,GAAE;;;;;;;;;;;;0CAErD,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;kCAIxD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,QACP,6BACA,uDACJ;;4CACH;4CACO,OAAO,MAAM;4CAAC;;;;;;;kDAEtB,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,aACP,6BACA,uDACJ;kDACH;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,aACP,2BACA,uDACJ;kDACH;;;;;;;;;;;;0CAMH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAa;;;;;;;;;;;;kDAG7B,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC;gDACC,WAAW,CAAC,QAAQ,EAAE,aAAa,iBAAiB,IAAI;gDACxD,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;0DAAM,aAAa,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO7C,eAAe,MAAM,KAAK,kBACzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAuC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC9F,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCACV,WAAW,QACR,gEACA,CAAC,GAAG,EAAE,OAAO,mDAAmD,CAAC;;;;;;;;;;;;;;;;qCAM3E,8OAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC,wIAAA,CAAA,YAAS;wBAER,OAAO;wBACP,eAAe;wBACf,cAAc;uBAHT,MAAM,EAAE;;;;;;;;;;YAUpB,eAAe,MAAM,GAAG,mBACvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/services/aiImageService.ts"], "sourcesContent": ["/**\n * AI Image Generation Service\n * Generates token artwork using OpenAI DALL-E based on tweet content\n */\n\nimport { EnrichedTweet } from '@/types/twitter';\n\nexport interface TokenArtworkRequest {\n  tweet: EnrichedTweet;\n  tokenName: string;\n  tokenSymbol: string;\n  style?: 'meme' | 'abstract' | 'cartoon' | 'minimalist' | 'cyberpunk';\n  size?: '256x256' | '512x512' | '1024x1024';\n}\n\nexport interface TokenArtworkResult {\n  success: boolean;\n  imageUrl?: string;\n  prompt?: string;\n  error?: string;\n  generationTime?: number;\n}\n\nclass AIImageService {\n  private apiKey: string | null = null;\n  private baseUrl = 'https://api.openai.com/v1/images/generations';\n  private useMockData = process.env.NODE_ENV === 'development';\n\n  constructor() {\n    this.apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || null;\n  }\n\n  /**\n   * Generate token artwork based on tweet content\n   */\n  async generateTokenArtwork(request: TokenArtworkRequest): Promise<TokenArtworkResult> {\n    if (this.useMockData || !this.apiKey) {\n      console.log('Using mock image generation for development');\n      return this.getMockArtwork(request);\n    }\n\n    try {\n      const startTime = Date.now();\n      const prompt = this.createArtworkPrompt(request);\n\n      const response = await fetch(this.baseUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          model: 'dall-e-3',\n          prompt: prompt,\n          n: 1,\n          size: request.size || '1024x1024',\n          quality: 'standard',\n          style: 'vivid'\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      const generationTime = Date.now() - startTime;\n\n      if (data.data && data.data.length > 0) {\n        return {\n          success: true,\n          imageUrl: data.data[0].url,\n          prompt: prompt,\n          generationTime\n        };\n      } else {\n        throw new Error('No image generated');\n      }\n    } catch (error) {\n      console.error('Error generating artwork:', error);\n      // Fallback to mock data on error\n      return this.getMockArtwork(request);\n    }\n  }\n\n  /**\n   * Create an optimized prompt for memecoin artwork generation\n   */\n  private createArtworkPrompt(request: TokenArtworkRequest): string {\n    const { tweet, tokenName, tokenSymbol, style = 'meme' } = request;\n\n    // Extract key themes and concepts from tweet\n    const tweetText = tweet.text.toLowerCase();\n    const themes = this.extractThemes(tweetText);\n\n    // Build memecoin-focused prompt\n    let prompt = '';\n\n    // Style-specific memecoin prompt beginnings\n    switch (style) {\n      case 'meme':\n        prompt = `Create a hilarious meme-style cryptocurrency token logo for \"${tokenName}\" ($${tokenSymbol}). `;\n        prompt += `This should look like a popular internet meme coin with bold, eye-catching design. `;\n        break;\n      case 'abstract':\n        prompt = `Design an abstract memecoin logo for \"${tokenName}\" ($${tokenSymbol}) with geometric shapes and vibrant colors. `;\n        break;\n      case 'cartoon':\n        prompt = `Illustrate a cute, cartoon mascot character for the memecoin \"${tokenName}\" ($${tokenSymbol}). `;\n        prompt += `Make it adorable and shareable, perfect for social media. `;\n        break;\n      case 'minimalist':\n        prompt = `Create a clean, minimalist memecoin symbol for \"${tokenName}\" ($${tokenSymbol}) with simple but memorable design. `;\n        break;\n      case 'cyberpunk':\n        prompt = `Design a futuristic cyberpunk memecoin logo for \"${tokenName}\" ($${tokenSymbol}) with neon colors and digital aesthetics. `;\n        break;\n    }\n\n    // Incorporate the actual tweet content as the core concept\n    const tweetConcept = this.sanitizeTweetForPrompt(tweet.text);\n    if (tweetConcept) {\n      prompt += `The design is directly inspired by this viral tweet: \"${tweetConcept}\". `;\n      prompt += `Capture the essence and humor of this tweet in the visual design. `;\n    }\n\n    // Add meme-specific elements found in the tweet\n    const memeElements = this.extractMemeElements(tweet.text);\n    if (memeElements.length > 0) {\n      prompt += `Include visual references to: ${memeElements.join(', ')}. `;\n    }\n\n    // Add theme-based elements for additional context\n    if (themes.length > 0) {\n      prompt += `Incorporate thematic elements: ${themes.join(', ')}. `;\n    }\n\n    // Memecoin-specific technical requirements\n    prompt += `This is a MEMECOIN logo, so make it: `;\n    prompt += `- Instantly recognizable and memorable `;\n    prompt += `- Perfect for social media sharing `;\n    prompt += `- Funny, relatable, or endearing `;\n    prompt += `- Bold and vibrant colors `;\n    prompt += `- Clear at small sizes (profile pictures) `;\n    prompt += `- Include \"$${tokenSymbol}\" text prominently `;\n\n    // Final style specifications\n    switch (style) {\n      case 'meme':\n        prompt += `Style: internet meme aesthetic, bold outlines, bright colors, comic-style, viral-ready design. `;\n        break;\n      case 'cartoon':\n        prompt += `Style: cute cartoon character, friendly expression, rounded shapes, appealing to wide audience. `;\n        break;\n      case 'abstract':\n        prompt += `Style: modern abstract art, geometric patterns, gradient colors, contemporary feel. `;\n        break;\n      case 'minimalist':\n        prompt += `Style: clean lines, simple shapes, limited color palette, elegant simplicity. `;\n        break;\n      case 'cyberpunk':\n        prompt += `Style: neon colors, digital glitch effects, futuristic typography, tech-inspired. `;\n        break;\n    }\n\n    prompt += `Background: solid color or simple gradient. No complex backgrounds. `;\n    prompt += `Format: circular token logo design, suitable for cryptocurrency branding.`;\n\n    return prompt;\n  }\n\n  /**\n   * Extract meme-specific elements from tweet text\n   */\n  private extractMemeElements(text: string): string[] {\n    const elements: string[] = [];\n\n    // Common meme patterns and references\n    const memePatterns = {\n      'POV': /POV:/i,\n      'when you': /when you/i,\n      'me explaining': /me explaining/i,\n      'nobody asked but': /nobody.*asked/i,\n      'normalize': /normalize/i,\n      'shoutout to': /shoutout to/i,\n      'breaking news': /breaking/i,\n      'local man': /local (man|woman)/i,\n      'four stages': /stages/i,\n      'just one more': /just one more/i,\n      'mom': /mom|mother/i,\n      'dad': /dad|father/i,\n      'netflix': /netflix/i,\n      'tiktok': /tiktok/i,\n      'amazon': /amazon/i,\n      'online shopping': /shopping|buy/i,\n      'procrastination': /procrastinat|lazy/i,\n      'productivity': /productive/i,\n      'relatable': /relatable/i,\n      'mood': /mood/i,\n      'vibe': /vibe/i,\n      'energy': /energy/i\n    };\n\n    for (const [element, pattern] of Object.entries(memePatterns)) {\n      if (pattern.test(text)) {\n        elements.push(element);\n      }\n    }\n\n    return elements.slice(0, 4); // Limit to top 4 elements\n  }\n\n  /**\n   * Extract themes from tweet text for artwork inspiration\n   */\n  private extractThemes(text: string): string[] {\n    const themes: string[] = [];\n    \n    // Common meme/viral themes\n    const themePatterns = {\n      'cats': /cat|kitten|feline/i,\n      'dogs': /dog|puppy|canine|woof/i,\n      'food': /food|eating|hungry|pizza|burger/i,\n      'money': /money|rich|broke|expensive|cheap/i,\n      'technology': /phone|computer|app|internet|wifi/i,\n      'work': /work|job|boss|office|meeting/i,\n      'relationships': /love|dating|relationship|crush/i,\n      'entertainment': /netflix|movie|tv|show|binge/i,\n      'shopping': /shopping|buy|purchase|amazon|store/i,\n      'time': /time|late|early|schedule|deadline/i,\n      'social media': /twitter|instagram|tiktok|social|viral/i,\n      'emotions': /happy|sad|angry|excited|tired/i,\n      'space': /space|moon|star|planet|rocket/i,\n      'nature': /tree|flower|ocean|mountain|sun/i,\n      'gaming': /game|gaming|player|console|pc/i\n    };\n\n    for (const [theme, pattern] of Object.entries(themePatterns)) {\n      if (pattern.test(text)) {\n        themes.push(theme);\n      }\n    }\n\n    return themes.slice(0, 3); // Limit to top 3 themes\n  }\n\n  /**\n   * Sanitize tweet text for use in AI prompts\n   */\n  private sanitizeTweetForPrompt(text: string): string {\n    // Remove URLs, mentions, and hashtags\n    let sanitized = text\n      .replace(/https?:\\/\\/[^\\s]+/g, '')\n      .replace(/@\\w+/g, '')\n      .replace(/#\\w+/g, '')\n      .replace(/\\n+/g, ' ')\n      .trim();\n\n    // Limit length and clean up\n    if (sanitized.length > 100) {\n      sanitized = sanitized.substring(0, 100) + '...';\n    }\n\n    return sanitized;\n  }\n\n  /**\n   * Generate mock artwork for development\n   */\n  private getMockArtwork(request: TokenArtworkRequest): Promise<TokenArtworkResult> {\n    return new Promise((resolve) => {\n      // Simulate API delay\n      setTimeout(() => {\n        const mockImages = [\n          'https://picsum.photos/1024/1024?random=1',\n          'https://picsum.photos/1024/1024?random=2',\n          'https://picsum.photos/1024/1024?random=3',\n          'https://picsum.photos/1024/1024?random=4',\n          'https://picsum.photos/1024/1024?random=5',\n        ];\n\n        const randomImage = mockImages[Math.floor(Math.random() * mockImages.length)];\n        const prompt = this.createArtworkPrompt(request);\n\n        resolve({\n          success: true,\n          imageUrl: randomImage,\n          prompt: prompt,\n          generationTime: 2000 + Math.random() * 3000 // 2-5 seconds\n        });\n      }, 1000 + Math.random() * 2000); // 1-3 second delay\n    });\n  }\n\n  /**\n   * Generate multiple artwork variations\n   */\n  async generateArtworkVariations(\n    request: TokenArtworkRequest, \n    count: number = 3\n  ): Promise<TokenArtworkResult[]> {\n    const styles: Array<TokenArtworkRequest['style']> = ['meme', 'abstract', 'cartoon'];\n    const promises = styles.slice(0, count).map(style => \n      this.generateTokenArtwork({ ...request, style })\n    );\n\n    try {\n      return await Promise.all(promises);\n    } catch (error) {\n      console.error('Error generating artwork variations:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Validate image URL and ensure it's accessible\n   */\n  async validateImageUrl(url: string): Promise<boolean> {\n    try {\n      const response = await fetch(url, { method: 'HEAD' });\n      return response.ok;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Get artwork generation cost estimate\n   */\n  getGenerationCost(size: string = '1024x1024'): number {\n    // OpenAI DALL-E 3 pricing (as of 2024)\n    const costs = {\n      '256x256': 0.016,\n      '512x512': 0.018,\n      '1024x1024': 0.020\n    };\n    return costs[size as keyof typeof costs] || 0.020;\n  }\n\n  /**\n   * Check if API key is configured\n   */\n  isConfigured(): boolean {\n    return !!this.apiKey && !this.useMockData;\n  }\n\n  /**\n   * Get service status\n   */\n  getStatus(): { configured: boolean; mockMode: boolean; apiKey: boolean } {\n    return {\n      configured: this.isConfigured(),\n      mockMode: this.useMockData,\n      apiKey: !!this.apiKey\n    };\n  }\n}\n\nexport const aiImageService = new AIImageService();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAoBD,MAAM;IACI,SAAwB,KAAK;IAC7B,UAAU,+CAA+C;IACzD,cAAc,oDAAyB,cAAc;IAE7D,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,0BAA0B,IAAI;IAC1D;IAEA;;GAEC,GACD,MAAM,qBAAqB,OAA4B,EAA+B;QACpF,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACpC,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B;QAEA,IAAI;YACF,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC;YAExC,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;oBACP,QAAQ;oBACR,GAAG;oBACH,MAAM,QAAQ,IAAI,IAAI;oBACtB,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC/E;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,iBAAiB,KAAK,GAAG,KAAK;YAEpC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;gBACrC,OAAO;oBACL,SAAS;oBACT,UAAU,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG;oBAC1B,QAAQ;oBACR;gBACF;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,iCAAiC;YACjC,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,AAAQ,oBAAoB,OAA4B,EAAU;QAChE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,MAAM,EAAE,GAAG;QAE1D,6CAA6C;QAC7C,MAAM,YAAY,MAAM,IAAI,CAAC,WAAW;QACxC,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC;QAElC,gCAAgC;QAChC,IAAI,SAAS;QAEb,4CAA4C;QAC5C,OAAQ;YACN,KAAK;gBACH,SAAS,CAAC,6DAA6D,EAAE,UAAU,IAAI,EAAE,YAAY,GAAG,CAAC;gBACzG,UAAU,CAAC,mFAAmF,CAAC;gBAC/F;YACF,KAAK;gBACH,SAAS,CAAC,sCAAsC,EAAE,UAAU,IAAI,EAAE,YAAY,4CAA4C,CAAC;gBAC3H;YACF,KAAK;gBACH,SAAS,CAAC,8DAA8D,EAAE,UAAU,IAAI,EAAE,YAAY,GAAG,CAAC;gBAC1G,UAAU,CAAC,0DAA0D,CAAC;gBACtE;YACF,KAAK;gBACH,SAAS,CAAC,gDAAgD,EAAE,UAAU,IAAI,EAAE,YAAY,oCAAoC,CAAC;gBAC7H;YACF,KAAK;gBACH,SAAS,CAAC,iDAAiD,EAAE,UAAU,IAAI,EAAE,YAAY,2CAA2C,CAAC;gBACrI;QACJ;QAEA,2DAA2D;QAC3D,MAAM,eAAe,IAAI,CAAC,sBAAsB,CAAC,MAAM,IAAI;QAC3D,IAAI,cAAc;YAChB,UAAU,CAAC,sDAAsD,EAAE,aAAa,GAAG,CAAC;YACpF,UAAU,CAAC,kEAAkE,CAAC;QAChF;QAEA,gDAAgD;QAChD,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC,MAAM,IAAI;QACxD,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,UAAU,CAAC,8BAA8B,EAAE,aAAa,IAAI,CAAC,MAAM,EAAE,CAAC;QACxE;QAEA,kDAAkD;QAClD,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,UAAU,CAAC,+BAA+B,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;QACnE;QAEA,2CAA2C;QAC3C,UAAU,CAAC,qCAAqC,CAAC;QACjD,UAAU,CAAC,uCAAuC,CAAC;QACnD,UAAU,CAAC,mCAAmC,CAAC;QAC/C,UAAU,CAAC,iCAAiC,CAAC;QAC7C,UAAU,CAAC,0BAA0B,CAAC;QACtC,UAAU,CAAC,0CAA0C,CAAC;QACtD,UAAU,CAAC,YAAY,EAAE,YAAY,mBAAmB,CAAC;QAEzD,6BAA6B;QAC7B,OAAQ;YACN,KAAK;gBACH,UAAU,CAAC,+FAA+F,CAAC;gBAC3G;YACF,KAAK;gBACH,UAAU,CAAC,gGAAgG,CAAC;gBAC5G;YACF,KAAK;gBACH,UAAU,CAAC,oFAAoF,CAAC;gBAChG;YACF,KAAK;gBACH,UAAU,CAAC,8EAA8E,CAAC;gBAC1F;YACF,KAAK;gBACH,UAAU,CAAC,kFAAkF,CAAC;gBAC9F;QACJ;QAEA,UAAU,CAAC,oEAAoE,CAAC;QAChF,UAAU,CAAC,yEAAyE,CAAC;QAErF,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,oBAAoB,IAAY,EAAY;QAClD,MAAM,WAAqB,EAAE;QAE7B,sCAAsC;QACtC,MAAM,eAAe;YACnB,OAAO;YACP,YAAY;YACZ,iBAAiB;YACjB,oBAAoB;YACpB,aAAa;YACb,eAAe;YACf,iBAAiB;YACjB,aAAa;YACb,eAAe;YACf,iBAAiB;YACjB,OAAO;YACP,OAAO;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,mBAAmB;YACnB,mBAAmB;YACnB,gBAAgB;YAChB,aAAa;YACb,QAAQ;YACR,QAAQ;YACR,UAAU;QACZ;QAEA,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,cAAe;YAC7D,IAAI,QAAQ,IAAI,CAAC,OAAO;gBACtB,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,OAAO,SAAS,KAAK,CAAC,GAAG,IAAI,0BAA0B;IACzD;IAEA;;GAEC,GACD,AAAQ,cAAc,IAAY,EAAY;QAC5C,MAAM,SAAmB,EAAE;QAE3B,2BAA2B;QAC3B,MAAM,gBAAgB;YACpB,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,cAAc;YACd,QAAQ;YACR,iBAAiB;YACjB,iBAAiB;YACjB,YAAY;YACZ,QAAQ;YACR,gBAAgB;YAChB,YAAY;YACZ,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QAEA,KAAK,MAAM,CAAC,OAAO,QAAQ,IAAI,OAAO,OAAO,CAAC,eAAgB;YAC5D,IAAI,QAAQ,IAAI,CAAC,OAAO;gBACtB,OAAO,IAAI,CAAC;YACd;QACF;QAEA,OAAO,OAAO,KAAK,CAAC,GAAG,IAAI,wBAAwB;IACrD;IAEA;;GAEC,GACD,AAAQ,uBAAuB,IAAY,EAAU;QACnD,sCAAsC;QACtC,IAAI,YAAY,KACb,OAAO,CAAC,sBAAsB,IAC9B,OAAO,CAAC,SAAS,IACjB,OAAO,CAAC,SAAS,IACjB,OAAO,CAAC,QAAQ,KAChB,IAAI;QAEP,4BAA4B;QAC5B,IAAI,UAAU,MAAM,GAAG,KAAK;YAC1B,YAAY,UAAU,SAAS,CAAC,GAAG,OAAO;QAC5C;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,eAAe,OAA4B,EAA+B;QAChF,OAAO,IAAI,QAAQ,CAAC;YAClB,qBAAqB;YACrB,WAAW;gBACT,MAAM,aAAa;oBACjB;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,MAAM,cAAc,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;gBAC7E,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC;gBAExC,QAAQ;oBACN,SAAS;oBACT,UAAU;oBACV,QAAQ;oBACR,gBAAgB,OAAO,KAAK,MAAM,KAAK,KAAK,cAAc;gBAC5D;YACF,GAAG,OAAO,KAAK,MAAM,KAAK,OAAO,mBAAmB;QACtD;IACF;IAEA;;GAEC,GACD,MAAM,0BACJ,OAA4B,EAC5B,QAAgB,CAAC,EACc;QAC/B,MAAM,SAA8C;YAAC;YAAQ;YAAY;SAAU;QACnF,MAAM,WAAW,OAAO,KAAK,CAAC,GAAG,OAAO,GAAG,CAAC,CAAA,QAC1C,IAAI,CAAC,oBAAoB,CAAC;gBAAE,GAAG,OAAO;gBAAE;YAAM;QAGhD,IAAI;YACF,OAAO,MAAM,QAAQ,GAAG,CAAC;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,GAAW,EAAoB;QACpD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAAE,QAAQ;YAAO;YACnD,OAAO,SAAS,EAAE;QACpB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA;;GAEC,GACD,kBAAkB,OAAe,WAAW,EAAU;QACpD,uCAAuC;QACvC,MAAM,QAAQ;YACZ,WAAW;YACX,WAAW;YACX,aAAa;QACf;QACA,OAAO,KAAK,CAAC,KAA2B,IAAI;IAC9C;IAEA;;GAEC,GACD,eAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW;IAC3C;IAEA;;GAEC,GACD,YAAyE;QACvE,OAAO;YACL,YAAY,IAAI,CAAC,YAAY;YAC7B,UAAU,IAAI,CAAC,WAAW;YAC1B,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM;QACvB;IACF;AACF;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 2527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/tweet/TokenLaunchModal.tsx"], "sourcesContent": ["/**\n * Token Launch Modal Component\n * Modal for launching tokens from tweets\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { EnrichedTweet, TweetToTokenParams, TokenLaunchResult } from '@/types/twitter';\nimport { aiImageService, TokenArtworkRequest, TokenArtworkResult } from '@/services/aiImageService';\n\ninterface TokenLaunchModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  tweet: EnrichedTweet | null;\n  onLaunch: (params: TweetToTokenParams) => Promise<TokenLaunchResult>;\n}\n\nexport function TokenLaunchModal({ isOpen, onClose, tweet, onLaunch }: TokenLaunchModalProps) {\n  const [tokenName, setTokenName] = useState('');\n  const [tokenSymbol, setTokenSymbol] = useState('');\n  const [tokenDescription, setTokenDescription] = useState('');\n  const [initialLiquidity, setInitialLiquidity] = useState(0.2);\n  const [totalSupply, setTotalSupply] = useState('1000000000');\n  const [decimals, setDecimals] = useState(9);\n  const [isLaunching, setIsLaunching] = useState(false);\n  const [launchResult, setLaunchResult] = useState<TokenLaunchResult | null>(null);\n  const [step, setStep] = useState<'form' | 'generating' | 'launching' | 'success' | 'error'>('form');\n  const [generatedArtwork, setGeneratedArtwork] = useState<TokenArtworkResult | null>(null);\n  const [isGeneratingArt, setIsGeneratingArt] = useState(false);\n  const [artworkStyle, setArtworkStyle] = useState<'meme' | 'abstract' | 'cartoon' | 'minimalist' | 'cyberpunk'>('meme');\n\n  // Reset form when tweet changes\n  useEffect(() => {\n    if (tweet && isOpen) {\n      setTokenName(tweet.token_potential?.name_suggestion || '');\n      setTokenSymbol(tweet.token_potential?.symbol_suggestion || '');\n      setTokenDescription(tweet.token_potential?.description_suggestion || tweet.text.substring(0, 100));\n      setStep('form');\n      setLaunchResult(null);\n      setGeneratedArtwork(null);\n      setIsGeneratingArt(false);\n    }\n  }, [tweet, isOpen]);\n\n  const generateArtwork = async () => {\n    if (!tweet) return;\n\n    setIsGeneratingArt(true);\n    setStep('generating');\n\n    try {\n      const artworkRequest: TokenArtworkRequest = {\n        tweet,\n        tokenName,\n        tokenSymbol,\n        style: artworkStyle,\n        size: '1024x1024'\n      };\n\n      const result = await aiImageService.generateTokenArtwork(artworkRequest);\n      setGeneratedArtwork(result);\n\n      if (result.success) {\n        setStep('form');\n      } else {\n        setStep('error');\n      }\n    } catch (error) {\n      console.error('Artwork generation failed:', error);\n      setStep('error');\n    } finally {\n      setIsGeneratingArt(false);\n    }\n  };\n\n  const handleLaunch = async () => {\n    if (!tweet) return;\n\n    setIsLaunching(true);\n    setStep('launching');\n\n    try {\n      const params: TweetToTokenParams = {\n        tweet,\n        token_name: tokenName,\n        token_symbol: tokenSymbol,\n        token_description: tokenDescription,\n        initial_liquidity: initialLiquidity,\n        total_supply: totalSupply,\n        decimals,\n        creator_wallet: 'placeholder' // This would come from wallet connection\n      };\n\n      const result = await onLaunch(params);\n      setLaunchResult(result);\n      \n      if (result.success) {\n        setStep('success');\n      } else {\n        setStep('error');\n      }\n    } catch (error) {\n      console.error('Launch error:', error);\n      setLaunchResult({\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error occurred'\n      });\n      setStep('error');\n    } finally {\n      setIsLaunching(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isLaunching) {\n      onClose();\n      setTimeout(() => {\n        setStep('form');\n        setLaunchResult(null);\n      }, 300);\n    }\n  };\n\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text);\n    // You could add a toast notification here\n  };\n\n  if (!isOpen || !tweet) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n      <div className=\"bg-gray-900 border border-gray-700 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-700\">\n          <h2 className=\"text-xl font-bold text-white\">\n            {step === 'form' && 'Launch Token from Tweet'}\n            {step === 'generating' && 'Generating AI Artwork...'}\n            {step === 'launching' && 'Launching Token...'}\n            {step === 'success' && 'Token Launched Successfully! 🎉'}\n            {step === 'error' && 'Launch Failed'}\n          </h2>\n          <button\n            onClick={handleClose}\n            disabled={isLaunching}\n            className=\"text-gray-400 hover:text-white transition-colors disabled:opacity-50\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Tweet Preview */}\n          <div className=\"bg-gray-800/50 border border-gray-600/50 rounded-xl p-4 mb-6\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white font-bold\">\n                  {tweet.author.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n              <div>\n                <p className=\"text-white font-semibold\">{tweet.author.name}</p>\n                <p className=\"text-gray-400 text-sm\">@{tweet.author.username}</p>\n              </div>\n            </div>\n            <p className=\"text-gray-200 text-sm leading-relaxed\">{tweet.text}</p>\n          </div>\n\n          {/* Form Step */}\n          {step === 'form' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Token Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={tokenName}\n                    onChange={(e) => setTokenName(e.target.value)}\n                    placeholder=\"e.g., Bonk Inu\"\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Token Symbol\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={tokenSymbol}\n                    onChange={(e) => setTokenSymbol(e.target.value.toUpperCase())}\n                    placeholder=\"e.g., BONK\"\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Token Description\n                </label>\n                <textarea\n                  value={tokenDescription}\n                  onChange={(e) => setTokenDescription(e.target.value)}\n                  placeholder=\"Describe your token...\"\n                  rows={3}\n                  className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300 resize-none\"\n                />\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Initial Liquidity (SOL)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={initialLiquidity}\n                    onChange={(e) => setInitialLiquidity(parseFloat(e.target.value) || 0)}\n                    min=\"0.1\"\n                    step=\"0.1\"\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                  />\n                  <p className=\"text-gray-500 text-xs mt-1\">Minimum 0.2 SOL recommended</p>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Total Supply\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={totalSupply}\n                    onChange={(e) => setTotalSupply(e.target.value)}\n                    placeholder=\"1000000000\"\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Decimals\n                  </label>\n                  <select\n                    value={decimals}\n                    onChange={(e) => setDecimals(parseInt(e.target.value))}\n                    className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white transition-all duration-300\"\n                  >\n                    <option value={6}>6</option>\n                    <option value={9}>9</option>\n                  </select>\n                </div>\n              </div>\n\n              {/* AI Artwork Generation */}\n              <div className=\"bg-pink-900/20 border border-pink-500/30 rounded-xl p-4\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h4 className=\"text-pink-400 font-semibold\">AI Token Artwork</h4>\n                  <div className=\"flex items-center space-x-2\">\n                    <select\n                      value={artworkStyle}\n                      onChange={(e) => setArtworkStyle(e.target.value as any)}\n                      className=\"bg-gray-800 border border-gray-600 rounded-lg px-2 py-1 text-white text-xs\"\n                    >\n                      <option value=\"meme\">Meme Style</option>\n                      <option value=\"abstract\">Abstract</option>\n                      <option value=\"cartoon\">Cartoon</option>\n                      <option value=\"minimalist\">Minimalist</option>\n                      <option value=\"cyberpunk\">Cyberpunk</option>\n                    </select>\n                    <button\n                      type=\"button\"\n                      onClick={generateArtwork}\n                      disabled={isGeneratingArt || !tokenName || !tokenSymbol}\n                      className=\"bg-pink-600 hover:bg-pink-700 disabled:bg-gray-600 text-white text-xs font-medium py-1 px-3 rounded-lg transition-colors disabled:cursor-not-allowed\"\n                    >\n                      {isGeneratingArt ? 'Generating...' : 'Generate'}\n                    </button>\n                  </div>\n                </div>\n\n                {generatedArtwork && generatedArtwork.success ? (\n                  <div className=\"space-y-2\">\n                    <div className=\"relative\">\n                      <img\n                        src={generatedArtwork.imageUrl}\n                        alt={`${tokenName} token artwork`}\n                        className=\"w-full h-32 object-cover rounded-lg border border-gray-600\"\n                      />\n                      <div className=\"absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded\">\n                        AI Generated\n                      </div>\n                    </div>\n                    <p className=\"text-gray-400 text-xs\">\n                      Generated in {generatedArtwork.generationTime ? Math.round(generatedArtwork.generationTime / 1000) : 0}s\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"text-center py-4 border-2 border-dashed border-gray-600 rounded-lg\">\n                    <svg className=\"w-8 h-8 text-gray-500 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                    </svg>\n                    <p className=\"text-gray-500 text-xs\">Click Generate to create AI artwork</p>\n                  </div>\n                )}\n              </div>\n\n              {/* Cost Estimation */}\n              <div className=\"bg-purple-900/20 border border-purple-500/30 rounded-xl p-4\">\n                <h4 className=\"text-purple-400 font-semibold mb-2\">Estimated Costs</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-gray-400\">Token Creation:</span>\n                    <span className=\"text-white ml-2\">~0.01 SOL</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-400\">Pool Creation:</span>\n                    <span className=\"text-white ml-2\">~0.2 SOL</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-400\">Initial Liquidity:</span>\n                    <span className=\"text-white ml-2\">{initialLiquidity} SOL</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-400\">AI Artwork:</span>\n                    <span className=\"text-white ml-2\">$0.02</span>\n                  </div>\n                  <div className=\"col-span-2\">\n                    <span className=\"text-gray-400\">Total Cost:</span>\n                    <span className=\"text-cyan-400 ml-2 font-semibold\">~{(initialLiquidity + 0.21).toFixed(2)} SOL + $0.02</span>\n                  </div>\n                </div>\n              </div>\n\n              <button\n                onClick={handleLaunch}\n                disabled={!tokenName || !tokenSymbol || initialLiquidity < 0.1}\n                className=\"w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 disabled:from-gray-600 disabled:to-gray-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 disabled:cursor-not-allowed border border-orange-400/30 hover:border-orange-300/50 disabled:border-gray-600/30\"\n              >\n                Launch Token on Raydium 🚀\n              </button>\n            </div>\n          )}\n\n          {/* Generating Step */}\n          {step === 'generating' && (\n            <div className=\"text-center py-12\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"animate-spin w-8 h-8 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold text-white mb-2\">Generating AI Artwork...</h3>\n              <p className=\"text-gray-400 mb-4\">Creating unique token artwork based on the tweet content</p>\n              <div className=\"bg-gray-800/50 rounded-lg p-4 max-w-md mx-auto\">\n                <p className=\"text-sm text-gray-300 mb-2\">Style: <span className=\"text-pink-400 capitalize\">{artworkStyle}</span></p>\n                <p className=\"text-sm text-gray-300\">Token: <span className=\"text-cyan-400\">${tokenSymbol}</span></p>\n              </div>\n            </div>\n          )}\n\n          {/* Launching Step */}\n          {step === 'launching' && (\n            <div className=\"text-center py-12\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"animate-spin w-8 h-8 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold text-white mb-2\">Launching Your Token...</h3>\n              <p className=\"text-gray-400 mb-4\">This may take a few moments. Please don't close this window.</p>\n              {generatedArtwork && generatedArtwork.success && (\n                <div className=\"bg-gray-800/50 rounded-lg p-4 max-w-md mx-auto\">\n                  <img\n                    src={generatedArtwork.imageUrl}\n                    alt={`${tokenName} artwork`}\n                    className=\"w-16 h-16 object-cover rounded-lg mx-auto mb-2\"\n                  />\n                  <p className=\"text-sm text-gray-300\">Including AI-generated artwork</p>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Success Step */}\n          {step === 'success' && launchResult && (\n            <div className=\"text-center py-8\">\n              <div className=\"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold text-white mb-4\">Token Launched Successfully!</h3>\n\n              {/* Show generated artwork */}\n              {generatedArtwork && generatedArtwork.success && (\n                <div className=\"mb-6\">\n                  <img\n                    src={generatedArtwork.imageUrl}\n                    alt={`${tokenName} token artwork`}\n                    className=\"w-32 h-32 object-cover rounded-xl mx-auto border-2 border-green-500/30\"\n                  />\n                  <p className=\"text-gray-400 text-sm mt-2\">AI-Generated Token Artwork</p>\n                </div>\n              )}\n              \n              <div className=\"space-y-4 mb-6\">\n                {launchResult.token_address && (\n                  <div className=\"bg-gray-800/50 border border-gray-600/50 rounded-xl p-4\">\n                    <p className=\"text-gray-400 text-sm mb-1\">Token Address</p>\n                    <div className=\"flex items-center space-x-2\">\n                      <code className=\"text-cyan-400 text-sm font-mono\">{launchResult.token_address}</code>\n                      <button\n                        onClick={() => copyToClipboard(launchResult.token_address!)}\n                        className=\"text-gray-400 hover:text-white\"\n                      >\n                        <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n                )}\n                \n                {launchResult.solscan_url && (\n                  <a\n                    href={launchResult.solscan_url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors\"\n                  >\n                    View on Solscan\n                  </a>\n                )}\n              </div>\n\n              <div className=\"space-y-3\">\n                <button\n                  onClick={() => copyToClipboard(`Buy $${tokenSymbol} now! ${launchResult.solscan_url || ''}`)}\n                  className=\"w-full bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300\"\n                >\n                  Copy Share Message\n                </button>\n                <button\n                  onClick={handleClose}\n                  className=\"w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors\"\n                >\n                  Close\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Error Step */}\n          {step === 'error' && launchResult && (\n            <div className=\"text-center py-8\">\n              <div className=\"w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-bold text-white mb-4\">Launch Failed</h3>\n              <p className=\"text-gray-400 mb-6\">{launchResult.error || 'An unknown error occurred'}</p>\n              \n              <div className=\"space-y-3\">\n                <button\n                  onClick={() => setStep('form')}\n                  className=\"w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors\"\n                >\n                  Try Again\n                </button>\n                <button\n                  onClick={handleClose}\n                  className=\"w-full bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300\"\n                >\n                  Close\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAEA;AAJA;;;;AAaO,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAyB;IAC1F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC3E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6D;IAC5F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACpF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgE;IAE/G,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,QAAQ;YACnB,aAAa,MAAM,eAAe,EAAE,mBAAmB;YACvD,eAAe,MAAM,eAAe,EAAE,qBAAqB;YAC3D,oBAAoB,MAAM,eAAe,EAAE,0BAA0B,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG;YAC7F,QAAQ;YACR,gBAAgB;YAChB,oBAAoB;YACpB,mBAAmB;QACrB;IACF,GAAG;QAAC;QAAO;KAAO;IAElB,MAAM,kBAAkB;QACtB,IAAI,CAAC,OAAO;QAEZ,mBAAmB;QACnB,QAAQ;QAER,IAAI;YACF,MAAM,iBAAsC;gBAC1C;gBACA;gBACA;gBACA,OAAO;gBACP,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,iIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC;YACzD,oBAAoB;YAEpB,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ;YACV,OAAO;gBACL,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,QAAQ;QACV,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO;QAEZ,eAAe;QACf,QAAQ;QAER,IAAI;YACF,MAAM,SAA6B;gBACjC;gBACA,YAAY;gBACZ,cAAc;gBACd,mBAAmB;gBACnB,mBAAmB;gBACnB,cAAc;gBACd;gBACA,gBAAgB,cAAc,yCAAyC;YACzE;YAEA,MAAM,SAAS,MAAM,SAAS;YAC9B,gBAAgB;YAEhB,IAAI,OAAO,OAAO,EAAE;gBAClB,QAAQ;YACV,OAAO;gBACL,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,gBAAgB;gBACd,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;YACA,QAAQ;QACV,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa;YAChB;YACA,WAAW;gBACT,QAAQ;gBACR,gBAAgB;YAClB,GAAG;QACL;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC;IAC9B,0CAA0C;IAC5C;IAEA,IAAI,CAAC,UAAU,CAAC,OAAO,OAAO;IAE9B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCACX,SAAS,UAAU;gCACnB,SAAS,gBAAgB;gCACzB,SAAS,eAAe;gCACxB,SAAS,aAAa;gCACtB,SAAS,WAAW;;;;;;;sCAEvB,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;sDAG5C,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA4B,MAAM,MAAM,CAAC,IAAI;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;;wDAAwB;wDAAE,MAAM,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;8CAGhE,8OAAC;oCAAE,WAAU;8CAAyC,MAAM,IAAI;;;;;;;;;;;;wBAIjE,SAAS,wBACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC5C,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;oDAC1D,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,aAAY;4CACZ,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACnE,KAAI;oDACJ,MAAK;oDACL,WAAU;;;;;;8DAEZ,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;oDACpD,WAAU;;sEAEV,8OAAC;4DAAO,OAAO;sEAAG;;;;;;sEAClB,8OAAC;4DAAO,OAAO;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;8CAMxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAO;;;;;;8EACrB,8OAAC;oEAAO,OAAM;8EAAW;;;;;;8EACzB,8OAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,8OAAC;oEAAO,OAAM;8EAAa;;;;;;8EAC3B,8OAAC;oEAAO,OAAM;8EAAY;;;;;;;;;;;;sEAE5B,8OAAC;4DACC,MAAK;4DACL,SAAS;4DACT,UAAU,mBAAmB,CAAC,aAAa,CAAC;4DAC5C,WAAU;sEAET,kBAAkB,kBAAkB;;;;;;;;;;;;;;;;;;wCAK1C,oBAAoB,iBAAiB,OAAO,iBAC3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,KAAK,iBAAiB,QAAQ;4DAC9B,KAAK,GAAG,UAAU,cAAc,CAAC;4DACjC,WAAU;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;sEAA0E;;;;;;;;;;;;8DAI3F,8OAAC;oDAAE,WAAU;;wDAAwB;wDACrB,iBAAiB,cAAc,GAAG,KAAK,KAAK,CAAC,iBAAiB,cAAc,GAAG,QAAQ;wDAAE;;;;;;;;;;;;iEAI3G,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAqC,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5F,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAM3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAkB;;;;;;;;;;;;8DAEpC,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAkB;;;;;;;;;;;;8DAEpC,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAmB;gEAAiB;;;;;;;;;;;;;8DAEtD,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAkB;;;;;;;;;;;;8DAEpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAmC;gEAAE,CAAC,mBAAmB,IAAI,EAAE,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;8CAKhG,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,aAAa,CAAC,eAAe,mBAAmB;oCAC3D,WAAU;8CACX;;;;;;;;;;;;wBAOJ,SAAS,8BACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAkC,MAAK;wCAAO,SAAQ;;0DACnE,8OAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,8OAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;;;;;;8CAGvD,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;gDAA6B;8DAAO,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;sDAC7F,8OAAC;4CAAE,WAAU;;gDAAwB;8DAAO,8OAAC;oDAAK,WAAU;;wDAAgB;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;;wBAMnF,SAAS,6BACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAkC,MAAK;wCAAO,SAAQ;;0DACnE,8OAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,8OAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;;;;;;8CAGvD,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;gCACjC,oBAAoB,iBAAiB,OAAO,kBAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK,iBAAiB,QAAQ;4CAC9B,KAAK,GAAG,UAAU,QAAQ,CAAC;4CAC3B,WAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;wBAO5C,SAAS,aAAa,8BACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAe,SAAQ;kDAC9D,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAqH,UAAS;;;;;;;;;;;;;;;;8CAG7J,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;gCAGjD,oBAAoB,iBAAiB,OAAO,kBAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK,iBAAiB,QAAQ;4CAC9B,KAAK,GAAG,UAAU,cAAc,CAAC;4CACjC,WAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAI9C,8OAAC;oCAAI,WAAU;;wCACZ,aAAa,aAAa,kBACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAmC,aAAa,aAAa;;;;;;sEAC7E,8OAAC;4DACC,SAAS,IAAM,gBAAgB,aAAa,aAAa;4DACzD,WAAU;sEAEV,cAAA,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACjE,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAO9E,aAAa,WAAW,kBACvB,8OAAC;4CACC,MAAM,aAAa,WAAW;4CAC9B,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;8CAML,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,gBAAgB,CAAC,KAAK,EAAE,YAAY,MAAM,EAAE,aAAa,WAAW,IAAI,IAAI;4CAC3F,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;wBAQN,SAAS,WAAW,8BACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAsB,aAAa,KAAK,IAAI;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,QAAQ;4CACvB,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 3809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Header } from \"@/components/Header\";\nimport { TweetFeed } from \"@/components/tweet/TweetFeed\";\nimport { TokenLaunchModal } from \"@/components/tweet/TokenLaunchModal\";\nimport { EnrichedTweet, TweetToTokenParams, TokenLaunchResult } from \"@/types/twitter\";\n\nexport default function Home() {\n  const [selectedTweet, setSelectedTweet] = useState<EnrichedTweet | null>(null);\n  const [isLaunchModalOpen, setIsLaunchModalOpen] = useState(false);\n\n  const handleTweetLaunch = async (tweet: EnrichedTweet) => {\n    setSelectedTweet(tweet);\n    setIsLaunchModalOpen(true);\n  };\n\n  const handleTokenLaunch = async (params: TweetToTokenParams): Promise<TokenLaunchResult> => {\n    // Mock implementation - replace with actual Raydium token creation logic\n    console.log('Launching token with params:', params);\n\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 3000));\n\n    // Mock success response\n    return {\n      success: true,\n      token_address: 'So11111111111111111111111111111111111111112',\n      transaction_signature: 'mock_signature_123',\n      pool_address: 'mock_pool_address_456',\n      solscan_url: 'https://solscan.io/token/So11111111111111111111111111111111111111112',\n      estimated_cost: 0.21\n    };\n  };\n\n  const handleTweetClick = (tweet: EnrichedTweet) => {\n    // Optional: Handle tweet detail view\n    console.log('Tweet clicked:', tweet);\n  };\n\n  return (\n    <>\n      <Header />\n\n      {/* Main Content - Tweet Feed Focus */}\n      <main className=\"relative overflow-hidden bg-black min-h-screen\">\n        {/* Animated background elements */}\n        <div className=\"absolute inset-0\">\n          {/* Grid pattern overlay */}\n          <div className=\"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]\"></div>\n\n          {/* Gradient orbs */}\n          <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl animate-pulse\"></div>\n          <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-orange-500/5 to-red-500/5 rounded-full blur-3xl\"></div>\n        </div>\n\n        {/* Content */}\n        <div className=\"relative z-10 container mx-auto px-4 py-8\">\n          {/* Compact Header */}\n          <div className=\"text-center mb-8\">\n            <div className=\"inline-flex items-center bg-gray-900/50 border border-orange-500/30 rounded-full px-6 py-3 mb-4\">\n              <span className=\"w-2 h-2 bg-red-400 rounded-full animate-pulse mr-3\"></span>\n              <span className=\"text-gray-300 text-sm font-medium\">🔥 Live Viral Feed • AI Token Generation</span>\n            </div>\n\n            <h1 className=\"text-3xl lg:text-5xl font-bold text-white mb-4 leading-tight\">\n              <span className=\"bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 bg-clip-text text-transparent\">\n                Viral Tweets\n              </span>\n              {\" \"}→ Instant Tokens\n            </h1>\n\n            <p className=\"text-base lg:text-lg text-gray-400 mb-6 max-w-2xl mx-auto\">\n              Click any tweet to launch a meme coin with <span className=\"text-orange-400 font-semibold\">AI-generated artwork</span> on Raydium\n            </p>\n          </div>\n\n          {/* Live Tweet Feed - Full Focus */}\n          <div className=\"max-w-5xl mx-auto\">\n            <TweetFeed\n              onTweetLaunch={handleTweetLaunch}\n              onTweetClick={handleTweetClick}\n            />\n          </div>\n\n          {/* Minimal Footer */}\n          <div className=\"mt-12 pt-6 border-t border-gray-800/30 text-center\">\n            <div className=\"flex items-center justify-center space-x-6 text-gray-500 text-sm\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full\"></div>\n                <span>Solana</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full\"></div>\n                <span>Raydium</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full\"></div>\n                <span>Twitter API</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full\"></div>\n                <span>GPT-4 Vision</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Token Launch Modal */}\n      <TokenLaunchModal\n        isOpen={isLaunchModalOpen}\n        onClose={() => setIsLaunchModalOpen(false)}\n        tweet={selectedTweet}\n        onLaunch={handleTokenLaunch}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,oBAAoB,OAAO;QAC/B,iBAAiB;QACjB,qBAAqB;IACvB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,yEAAyE;QACzE,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,0BAA0B;QAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,wBAAwB;QACxB,OAAO;YACL,SAAS;YACT,eAAe;YACf,uBAAuB;YACvB,cAAc;YACd,aAAa;YACb,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,qCAAqC;QACrC,QAAQ,GAAG,CAAC,kBAAkB;IAChC;IAEA,qBACE;;0BACE,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAGP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAGtD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAyF;;;;;;4CAGxG;4CAAI;;;;;;;kDAGP,8OAAC;wCAAE,WAAU;;4CAA4D;0DAC5B,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;4CAA2B;;;;;;;;;;;;;0CAK1H,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wIAAA,CAAA,YAAS;oCACR,eAAe;oCACf,cAAc;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC,+IAAA,CAAA,mBAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,OAAO;gBACP,UAAU;;;;;;;;AAIlB", "debugId": null}}]}