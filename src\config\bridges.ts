/**
 * Cross-Chain Bridge Configuration
 * Defines bridge protocols and their configurations
 */

import { SupportedChain } from './chains';

export enum BridgeProtocol {
  WORMHOLE = 'wormhole',
  AXELAR = 'axelar',
  SINGULARITY = 'singularity',
  XCM = 'xcm',
}

export interface BridgeConfig {
  protocol: BridgeProtocol;
  name: string;
  supportedChains: SupportedChain[];
  contractAddresses: Record<string, string>;
  fees: {
    base: number;
    percentage: number;
  };
  estimatedTime: string;
  features: string[];
}

export const BRIDGE_CONFIGS: Record<BridgeProtocol, BridgeConfig> = {
  [BridgeProtocol.WORMHOLE]: {
    protocol: BridgeProtocol.WORMHOLE,
    name: 'Wormhole',
    supportedChains: [
      SupportedChain.SOLANA,
      SupportedChain.BNB_CHAIN,
      SupportedChain.AVALANCHE,
    ],
    contractAddresses: {
      [SupportedChain.SOLANA]: 'wormDTUJ6AWPNvk59vGQbDvGJmqbDTdgWgAqcLBCgUb2',
      [SupportedChain.BNB_CHAIN]: '0x98f3c9e6E3fAce36bAAd05FE09d375Ef1464288B',
      [SupportedChain.AVALANCHE]: '0x54a8e5f9c4CbA08F9943965859F6c34eAF03E26c',
    },
    fees: {
      base: 0.001,
      percentage: 0.1,
    },
    estimatedTime: '5-15 minutes',
    features: ['token-attestation', 'message-passing', 'nft-bridge'],
  },
  [BridgeProtocol.AXELAR]: {
    protocol: BridgeProtocol.AXELAR,
    name: 'Axelar',
    supportedChains: [
      SupportedChain.BNB_CHAIN,
      SupportedChain.AVALANCHE,
    ],
    contractAddresses: {
      [SupportedChain.BNB_CHAIN]: '0x4D147dCb984e6affEEC47e44293DA442580A3Ec0',
      [SupportedChain.AVALANCHE]: '0x5029C0EFf6C34351a0CEc334542cDb22c7928f78',
    },
    fees: {
      base: 0.002,
      percentage: 0.15,
    },
    estimatedTime: '2-10 minutes',
    features: ['general-message-passing', 'token-transfer', 'smart-contracts'],
  },
  [BridgeProtocol.SINGULARITY]: {
    protocol: BridgeProtocol.SINGULARITY,
    name: 'Singularity Protocol',
    supportedChains: [
      SupportedChain.SOLANA,
      SupportedChain.BNB_CHAIN,
      SupportedChain.AVALANCHE,
      SupportedChain.POLKADOT,
    ],
    contractAddresses: {
      // Placeholder addresses - to be updated with actual Singularity contracts
      [SupportedChain.SOLANA]: 'SingularityProtocolSolana...',
      [SupportedChain.BNB_CHAIN]: '0x...',
      [SupportedChain.AVALANCHE]: '0x...',
      [SupportedChain.POLKADOT]: 'polkadot-address...',
    },
    fees: {
      base: 0.0005,
      percentage: 0.05,
    },
    estimatedTime: '1-5 minutes',
    features: ['bridging-free-swaps', 'cross-chain-amm', 'invariant-pricing'],
  },
  [BridgeProtocol.XCM]: {
    protocol: BridgeProtocol.XCM,
    name: 'Cross-Consensus Messaging (XCM)',
    supportedChains: [SupportedChain.POLKADOT],
    contractAddresses: {
      [SupportedChain.POLKADOT]: 'native-xcm',
    },
    fees: {
      base: 0.001,
      percentage: 0.02,
    },
    estimatedTime: '1-3 minutes',
    features: ['parachain-communication', 'native-interoperability'],
  },
};

export interface BridgeRoute {
  from: SupportedChain;
  to: SupportedChain;
  protocol: BridgeProtocol;
  estimatedFee: number;
  estimatedTime: string;
}

export function getBridgeConfig(protocol: BridgeProtocol): BridgeConfig {
  return BRIDGE_CONFIGS[protocol];
}

export function getSupportedBridges(chain: SupportedChain): BridgeConfig[] {
  return Object.values(BRIDGE_CONFIGS).filter(bridge =>
    bridge.supportedChains.includes(chain)
  );
}

export function findBridgeRoutes(from: SupportedChain, to: SupportedChain): BridgeRoute[] {
  const routes: BridgeRoute[] = [];
  
  Object.values(BRIDGE_CONFIGS).forEach(bridge => {
    if (bridge.supportedChains.includes(from) && bridge.supportedChains.includes(to)) {
      routes.push({
        from,
        to,
        protocol: bridge.protocol,
        estimatedFee: bridge.fees.base,
        estimatedTime: bridge.estimatedTime,
      });
    }
  });
  
  return routes.sort((a, b) => a.estimatedFee - b.estimatedFee);
}

export function getOptimalBridgeRoute(from: SupportedChain, to: SupportedChain): BridgeRoute | null {
  const routes = findBridgeRoutes(from, to);
  return routes.length > 0 ? routes[0] : null;
}
