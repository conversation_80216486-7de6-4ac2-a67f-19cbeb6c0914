(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@solflare-wallet/metamask-sdk/lib/esm/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@solflare-wallet_metamask-sdk_388f5620._.js",
  "static/chunks/node_modules_@solflare-wallet_metamask-sdk_lib_esm_index_0e110b8b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@solflare-wallet/metamask-sdk/lib/esm/index.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@solflare-wallet/sdk/lib/esm/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@solflare-wallet_sdk_6e79e8f3._.js",
  "static/chunks/node_modules_@solflare-wallet_sdk_lib_esm_index_da0fd85d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@solflare-wallet/sdk/lib/esm/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);