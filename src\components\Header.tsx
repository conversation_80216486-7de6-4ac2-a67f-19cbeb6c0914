'use client';

import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { useWallet } from '@solana/wallet-adapter-react';
import { SOLANA_NETWORK } from '@/lib/constants';
// import { ChainSelector, ConnectionStatus } from '@/components/cross-chain/MultiChainWalletProvider';

export function Header() {
  const { connected } = useWallet();

  return (
    <header className="bg-black/95 backdrop-blur-sm border-b border-gray-800/50 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3">
              {/* Logo */}
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  TokenLaunch
                </h1>
                <p className="text-xs text-gray-400">Cross-Chain Token Creator</p>
              </div>
            </div>

            {/* Multi-Chain Status */}
            <div className="hidden lg:flex items-center space-x-4">
              <div className="flex items-center space-x-2 bg-gray-800/50 border border-gray-700/30 rounded-xl px-3 py-1">
                <span className="text-cyan-400 text-sm">🌐</span>
                <span className="text-sm text-gray-400 font-medium">Cross-Chain Ready</span>
              </div>
              <div className="w-px h-6 bg-gray-700"></div>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  SOLANA_NETWORK === 'mainnet-beta' ? 'bg-green-400' : 'bg-yellow-400'
                } animate-pulse`}></div>
                <span className="text-sm text-gray-400 font-medium">
                  Solana {SOLANA_NETWORK === 'mainnet-beta' ? 'Mainnet' : 'Devnet'}
                </span>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#features" className="text-gray-400 hover:text-purple-400 font-medium transition-colors">
              Cross-Chain
            </a>
            <a href="#launch" className="text-gray-400 hover:text-purple-400 font-medium transition-colors">
              Launch
            </a>
            <a href="#" className="text-gray-400 hover:text-purple-400 font-medium transition-colors">
              Docs
            </a>
          </nav>

          <div className="flex items-center space-x-4">
            {/* Chain Indicators */}
            <div className="hidden xl:flex items-center gap-2 p-2 bg-gray-800/50 rounded-xl border border-gray-700/50">
              <span className="text-gray-400 text-sm font-medium mr-2">Chains:</span>
              <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg" title="Solana">
                <span className="text-sm">◎</span>
              </div>
              <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 text-gray-400" title="BNB Chain">
                <span className="text-sm">🟡</span>
              </div>
              <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 text-gray-400" title="Avalanche">
                <span className="text-sm">🔺</span>
              </div>
              <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-700 text-gray-400" title="Polkadot">
                <span className="text-sm">⚫</span>
              </div>
            </div>

            {/* Solana Connection Status */}
            {connected && (
              <div className="hidden sm:flex items-center space-x-2 bg-green-900/30 border border-green-500/30 px-3 py-1 rounded-full">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-400 font-medium">Solana Connected</span>
              </div>
            )}

            {/* Primary Solana Wallet Button */}
            <WalletMultiButton className="!bg-gradient-to-r !from-purple-500 !to-pink-500 hover:!from-purple-600 hover:!to-pink-600 !text-white !rounded-xl !px-6 !py-2 !text-sm !font-semibold !transition-all !duration-300 !border !border-purple-400/30 hover:!border-purple-300/50 hover:!scale-105" />
          </div>
        </div>
      </div>
    </header>
  );
}
