/**
 * Multi-Chain Configuration
 * Defines supported blockchains and their parameters
 */

export enum SupportedChain {
  SOLANA = 'solana',
  BNB_CHAIN = 'bnb',
  AVALANCHE = 'avalanche',
  POLKADOT = 'polkadot'
}

export interface ChainConfig {
  id: string;
  name: string;
  displayName: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  rpcUrls: {
    default: string;
    testnet?: string;
  };
  blockExplorers: {
    default: {
      name: string;
      url: string;
    };
  };
  testnet?: boolean;
  chainType: 'evm' | 'solana' | 'substrate';
  features: {
    tokenStandard: string;
    dexIntegration: string[];
    bridgeSupport: string[];
  };
}

export const CHAIN_CONFIGS: Record<SupportedChain, ChainConfig> = {
  [SupportedChain.SOLANA]: {
    id: 'solana',
    name: 'solana',
    displayName: 'Solana',
    nativeCurrency: {
      name: '<PERSON><PERSON>',
      symbol: 'SOL',
      decimals: 9,
    },
    rpcUrls: {
      default: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
      testnet: 'https://api.devnet.solana.com',
    },
    blockExplorers: {
      default: {
        name: 'Solscan',
        url: 'https://solscan.io',
      },
    },
    chainType: 'solana',
    features: {
      tokenStandard: 'SPL',
      dexIntegration: ['raydium', 'orca'],
      bridgeSupport: ['wormhole', 'allbridge'],
    },
  },
  [SupportedChain.BNB_CHAIN]: {
    id: '56',
    name: 'bnb-smart-chain',
    displayName: 'BNB Chain',
    nativeCurrency: {
      name: 'BNB',
      symbol: 'BNB',
      decimals: 18,
    },
    rpcUrls: {
      default: 'https://bsc-dataseed1.binance.org',
      testnet: 'https://data-seed-prebsc-1-s1.binance.org:8545',
    },
    blockExplorers: {
      default: {
        name: 'BscScan',
        url: 'https://bscscan.com',
      },
    },
    chainType: 'evm',
    features: {
      tokenStandard: 'BEP-20',
      dexIntegration: ['pancakeswap', 'biswap'],
      bridgeSupport: ['wormhole', 'axelar', 'multichain'],
    },
  },
  [SupportedChain.AVALANCHE]: {
    id: '43114',
    name: 'avalanche',
    displayName: 'Avalanche',
    nativeCurrency: {
      name: 'Avalanche',
      symbol: 'AVAX',
      decimals: 18,
    },
    rpcUrls: {
      default: 'https://api.avax.network/ext/bc/C/rpc',
      testnet: 'https://api.avax-test.network/ext/bc/C/rpc',
    },
    blockExplorers: {
      default: {
        name: 'SnowTrace',
        url: 'https://snowtrace.io',
      },
    },
    chainType: 'evm',
    features: {
      tokenStandard: 'ERC-20',
      dexIntegration: ['traderjoe', 'pangolin'],
      bridgeSupport: ['wormhole', 'axelar', 'avalanche-bridge'],
    },
  },
  [SupportedChain.POLKADOT]: {
    id: 'polkadot',
    name: 'polkadot',
    displayName: 'Polkadot',
    nativeCurrency: {
      name: 'Polkadot',
      symbol: 'DOT',
      decimals: 10,
    },
    rpcUrls: {
      default: 'wss://rpc.polkadot.io',
      testnet: 'wss://westend-rpc.polkadot.io',
    },
    blockExplorers: {
      default: {
        name: 'Polkascan',
        url: 'https://polkascan.io/polkadot',
      },
    },
    chainType: 'substrate',
    features: {
      tokenStandard: 'PSP-22',
      dexIntegration: ['hydradx', 'basilisk'],
      bridgeSupport: ['xcm', 'wormhole'],
    },
  },
};

export const DEFAULT_CHAINS = [
  SupportedChain.SOLANA,
  SupportedChain.BNB_CHAIN,
  SupportedChain.AVALANCHE,
  SupportedChain.POLKADOT,
];

export function getChainConfig(chain: SupportedChain): ChainConfig {
  return CHAIN_CONFIGS[chain];
}

export function getAllChainConfigs(): ChainConfig[] {
  return Object.values(CHAIN_CONFIGS);
}

export function isEVMChain(chain: SupportedChain): boolean {
  return CHAIN_CONFIGS[chain].chainType === 'evm';
}

export function isSolanaChain(chain: SupportedChain): boolean {
  return CHAIN_CONFIGS[chain].chainType === 'solana';
}

export function isSubstrateChain(chain: SupportedChain): boolean {
  return CHAIN_CONFIGS[chain].chainType === 'substrate';
}
