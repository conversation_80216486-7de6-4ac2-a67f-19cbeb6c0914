(()=>{var e={};e.id=492,e.ids=[492],e.modules={755:e=>{"use strict";e.exports=require("node:url")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},1997:e=>{"use strict";e.exports=require("punycode")},2412:e=>{"use strict";e.exports=require("assert")},2749:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3136:(e,r,t)=>{Promise.resolve().then(t.bind(t,7590)),Promise.resolve().then(t.bind(t,6028))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3408:(e,r,t)=>{Promise.resolve().then(t.bind(t,5625)),Promise.resolve().then(t.bind(t,7270))},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u,metadata:()=>d});var s=t(7413),n=t(5091),o=t.n(n);t(1135),t(9249);var i=t(7270),a=t(5625);let d={title:"TokenLaunch - Create and Launch Solana Tokens",description:"Create, launch, and add liquidity to Solana tokens with ease. Built with Next.js and Raydium SDK.",keywords:["Solana","Token","Launch","DeFi","Cryptocurrency","Raydium"],authors:[{name:"TokenLaunch Team"}],viewport:"width=device-width, initial-scale=1"};function u({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${o().variable} font-sans antialiased bg-gray-50 min-h-screen`,children:(0,s.jsxs)(i.WalletContextProvider,{children:[(0,s.jsx)("div",{className:"min-h-screen flex flex-col",children:e}),(0,s.jsx)(a.Toaster,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#10B981",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#EF4444",secondary:"#fff"}}}})]})})})}},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6028:(e,r,t)=>{"use strict";t.d(r,{WalletContextProvider:()=>h});var s=t(687),n=t(3210),o=t(2767),i=t(8911),a=t(6141),d=t(3588),u=t(3233),l=t(4690),c=t(2263),p=t(6420);function h({children:e}){let r="mainnet-beta"===p.kD?a.B.Mainnet:a.B.Devnet,t=(0,n.useMemo)(()=>p.hR?p.hR:(0,c.Kw)(r),[r]),h=(0,n.useMemo)(()=>[new u.c,new l.d({network:r})],[r]);return(0,s.jsx)(o.S,{endpoint:t,children:(0,s.jsx)(i.r,{wallets:h,autoConnect:!0,children:(0,s.jsx)(d.I,{children:e})})})}},6420:(e,r,t)=>{"use strict";t.d(r,{hR:()=>o,kD:()=>n});var s=t(2263);let n="devnet",o="https://api.devnet.solana.com";new s.J3("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"),new s.J3("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"),new s.J3("9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin")},7270:(e,r,t)=>{"use strict";t.d(r,{WalletContextProvider:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call WalletContextProvider() from the server but WalletContextProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\TokenLaunch\\src\\components\\WalletProvider.tsx","WalletContextProvider")},7598:e=>{"use strict";e.exports=require("node:crypto")},7910:e=>{"use strict";e.exports=require("stream")},8013:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},8172:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>u});var s=t(5239),n=t(8088),o=t(8170),i=t.n(o),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let u={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\TokenLaunch\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=[],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,534],()=>t(8172));module.exports=s})();