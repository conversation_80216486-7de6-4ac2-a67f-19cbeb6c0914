(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1950:(e,t,r)=>{"use strict";r.d(t,{hR:()=>l,kD:()=>a});var s=r(3570);let a="devnet",l="https://api.devnet.solana.com";new s.J3("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"),new s.J3("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"),new s.J3("9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin")},3401:(e,t,r)=>{Promise.resolve().then(r.bind(r,7754))},7754:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(5155),a=r(2115),l=r(1392),n=r(7615),i=r(3347);function o({walletIcon:e,walletName:t,...r}){return a.createElement(n.$,{...r,className:"wallet-adapter-button-trigger",startIcon:e&&t?a.createElement(i.l,{wallet:{adapter:{icon:e,name:t}}}):void 0})}var d=r(840);function c({children:e,labels:t,...r}){let{setVisible:s}=(0,d.o)(),{buttonState:n,onConnect:i,onDisconnect:c,publicKey:m,walletIcon:x,walletName:u}=function({onSelectWallet:e}){let t,{connect:r,connected:s,connecting:n,disconnect:i,disconnecting:o,publicKey:d,select:c,wallet:m,wallets:x}=(0,l.v)();t=n?"connecting":s?"connected":o?"disconnecting":m?"has-wallet":"no-wallet";let u=(0,a.useCallback)(()=>{r().catch(()=>{})},[r]),p=(0,a.useCallback)(()=>{i().catch(()=>{})},[i]);return{buttonState:t,onConnect:"has-wallet"===t?u:void 0,onDisconnect:"disconnecting"!==t&&"no-wallet"!==t?p:void 0,onSelectWallet:(0,a.useCallback)(()=>{e({onSelectWallet:c,wallets:x})},[e,c,x]),publicKey:d??void 0,walletIcon:m?.adapter.icon,walletName:m?.adapter.name}}({onSelectWallet(){s(!0)}}),[p,h]=(0,a.useState)(!1),[g,b]=(0,a.useState)(!1),j=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=e=>{let t=j.current;!t||t.contains(e.target)||b(!1)};return document.addEventListener("mousedown",e),document.addEventListener("touchstart",e),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("touchstart",e)}},[]);let y=(0,a.useMemo)(()=>{if(e)return e;if(m){let e=m.toBase58();return e.slice(0,4)+".."+e.slice(-4)}return"connecting"===n||"has-wallet"===n?t[n]:t["no-wallet"]},[n,e,t,m]);return a.createElement("div",{className:"wallet-adapter-dropdown"},a.createElement(o,{...r,"aria-expanded":g,style:{pointerEvents:g?"none":"auto",...r.style},onClick:()=>{switch(n){case"no-wallet":s(!0);break;case"has-wallet":i&&i();break;case"connected":b(!0)}},walletIcon:x,walletName:u},y),a.createElement("ul",{"aria-label":"dropdown-list",className:`wallet-adapter-dropdown-list ${g&&"wallet-adapter-dropdown-list-active"}`,ref:j,role:"menu"},m?a.createElement("li",{className:"wallet-adapter-dropdown-list-item",onClick:async()=>{await navigator.clipboard.writeText(m.toBase58()),h(!0),setTimeout(()=>h(!1),400)},role:"menuitem"},p?t.copied:t["copy-address"]):null,a.createElement("li",{className:"wallet-adapter-dropdown-list-item",onClick:()=>{s(!0),b(!1)},role:"menuitem"},t["change-wallet"]),c?a.createElement("li",{className:"wallet-adapter-dropdown-list-item",onClick:()=>{c(),b(!1)},role:"menuitem"},t.disconnect):null))}let m={"change-wallet":"Change wallet",connecting:"Connecting ...","copy-address":"Copy address",copied:"Copied",disconnect:"Disconnect","has-wallet":"Connect","no-wallet":"Select Wallet"};function x(e){return a.createElement(c,{...e,labels:m})}var u=r(1950);function p(){let{connected:e}=(0,l.v)();return(0,s.jsx)("header",{className:"bg-black/95 backdrop-blur-sm border-b border-gray-800/50 sticky top-0 z-50",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,s.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent",children:"TokenLaunch"})]}),(0,s.jsxs)("div",{className:"hidden sm:flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat("mainnet-beta"===u.kD?"bg-green-400":"bg-yellow-400"," animate-pulse")}),(0,s.jsx)("span",{className:"text-sm text-gray-400 font-medium",children:"mainnet-beta"===u.kD?"Mainnet":"Devnet"})]})]}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,s.jsx)("a",{href:"#features",className:"text-gray-400 hover:text-purple-400 font-medium transition-colors",children:"Features"}),(0,s.jsx)("a",{href:"#launch",className:"text-gray-400 hover:text-purple-400 font-medium transition-colors",children:"Launch"}),(0,s.jsx)("a",{href:"#",className:"text-gray-400 hover:text-purple-400 font-medium transition-colors",children:"Docs"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,s.jsxs)("div",{className:"hidden sm:flex items-center space-x-2 bg-green-900/30 border border-green-500/30 px-3 py-1 rounded-full",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-sm text-green-400 font-medium",children:"Connected"})]}),(0,s.jsx)(x,{className:"!bg-gradient-to-r !from-purple-500 !to-pink-500 hover:!from-purple-600 hover:!to-pink-600 !text-white !rounded-xl !px-6 !py-2 !text-sm !font-semibold !transition-all !duration-300 !border !border-purple-400/30 hover:!border-purple-300/50 hover:!scale-105"})]})]})})})}function h(e){let{isOpen:t,onClose:r}=e,{connected:n,publicKey:i}=(0,l.v)(),[o,d]=(0,a.useState)("basics"),[c,m]=(0,a.useState)({name:"",symbol:"",description:"",image:null,supply:"1000000",decimals:9,website:"",twitter:"",telegram:"",revokeMint:!0,revokeFreeze:!0}),[u,p]=(0,a.useState)({initialSolAmount:"0.2"}),[h,g]=(0,a.useState)({tokenCreation:.002,metadata:.001,poolCreation:.152,total:.155}),[b,j]=(0,a.useState)(!1),[y,f]=(0,a.useState)(null);(0,a.useEffect)(()=>{let e=5e-4*!!c.image;g({tokenCreation:.002,metadata:.001+e,poolCreation:.152,total:.003+e+.152})},[c.image]);let v=(e,t)=>{m(r=>({...r,[e]:t}))},N=(e,t)=>{p(r=>({...r,[e]:t}))},w=()=>{"basics"===o?d("liquidity"):"liquidity"===o&&d("review")},k=()=>{"liquidity"===o?d("basics"):"review"===o&&d("liquidity")},C=async()=>{if(n&&i){d("creating"),j(!0);try{console.log("Creating token with data:",{tokenBasics:c,liquidityData:u}),await new Promise(e=>setTimeout(e,5e3)),r()}catch(e){console.error("Token creation failed:",e)}finally{j(!1)}}},L=c.name&&c.symbol&&c.supply&&n,S=u.initialSolAmount&&parseFloat(u.initialSolAmount)>0;return t?(0,s.jsx)("div",{className:"w-full max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl w-full shadow-2xl",children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 p-6 rounded-t-2xl border-b border-gray-700/50",children:(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center shadow-lg",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white",children:(()=>{switch(o){case"basics":return"Token Basics";case"liquidity":return"Liquidity & Market";case"review":return"Review & Launch";case"creating":return"Creating Token";default:return"Create Token"}})()}),(0,s.jsx)("p",{className:"text-white/80 text-sm",children:(()=>{switch(o){case"basics":return"Start with your token's core identity";case"liquidity":return"Set up CPMM pool and initial liquidity";case"review":return"Review all details before launch";case"creating":return"Please wait while we create your token";default:return""}})()})]})]})})}),(0,s.jsx)("div",{className:"p-6",children:n?(0,s.jsxs)(s.Fragment,{children:["basics"===o&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Token Logo (Optional)"}),(0,s.jsx)("div",{className:"border-2 border-dashed border-gray-600 rounded-xl p-8 text-center hover:border-purple-400 transition-colors bg-gray-800/30",children:y?(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("img",{src:y,alt:"Token",className:"w-24 h-24 rounded-xl object-cover mb-4 border border-gray-600"}),(0,s.jsx)("button",{type:"button",onClick:()=>{f(null),m(e=>({...e,image:null}))},className:"text-sm text-red-400 hover:text-red-300 transition-colors",children:"Remove Image"})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("svg",{className:"w-12 h-12 text-gray-500 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),(0,s.jsx)("p",{className:"text-gray-400 mb-2",children:"Click to upload or drag and drop"}),(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"PNG or JPG up to 2MB. Recommended size: 512\xd7512px for best quality."}),(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){m(e=>({...e,image:r}));let e=new FileReader;e.onload=e=>{var t;f(null==(t=e.target)?void 0:t.result)},e.readAsDataURL(r)}},className:"hidden",id:"token-image"}),(0,s.jsx)("label",{htmlFor:"token-image",className:"mt-4 inline-block bg-purple-900/50 hover:bg-purple-800/50 border border-purple-500/30 hover:border-purple-400/50 text-purple-300 px-4 py-2 rounded-lg cursor-pointer transition-all duration-300",children:"Choose Image"})]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Token Name *"}),(0,s.jsx)("input",{type:"text",value:c.name,onChange:e=>v("name",e.target.value),placeholder:"Solana Gaming Token",className:"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"The full name that will appear everywhere"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Symbol *"}),(0,s.jsx)("input",{type:"text",value:c.symbol,onChange:e=>v("symbol",e.target.value.toUpperCase()),placeholder:"SGT",maxLength:10,className:"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"3-10 characters, like BTC or SOL"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (Optional)"}),(0,s.jsx)("textarea",{value:c.description,onChange:e=>v("description",e.target.value),placeholder:"Describe your token's purpose and utility...",rows:3,className:"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 resize-none transition-all duration-300"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Brief description of your token"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Total Supply *"}),(0,s.jsx)("input",{type:"number",value:c.supply,onChange:e=>v("supply",e.target.value),placeholder:"1000000",min:"1",className:"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Total number of tokens to create"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Decimals"}),(0,s.jsx)("select",{value:c.decimals,onChange:e=>v("decimals",parseInt(e.target.value)),className:"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white transition-all duration-300","aria-label":"Token decimals",children:[0,2,4,6,8,9].map(e=>(0,s.jsxs)("option",{value:e,className:"bg-gray-800 text-white",children:[e," decimals"]},e))}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Number of decimal places (9 is standard)"})]})]}),(0,s.jsx)("div",{className:"flex justify-end pt-6",children:(0,s.jsxs)("button",{type:"button",onClick:w,disabled:!L,className:"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30",children:["Continue",(0,s.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]}),"liquidity"===o&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-900/30 border border-blue-500/30 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-blue-400 mt-0.5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-blue-300",children:"Raydium CPMM Pool"}),(0,s.jsx)("p",{className:"text-sm text-blue-200 mt-1",children:"Create a Constant Product Market Maker (CPMM) pool on Raydium. No OpenBook Market required, supports Token-2022."})]})]})}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-purple-900/30 to-cyan-900/30 border border-purple-500/30 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-500 to-cyan-400 rounded-xl flex items-center justify-center mr-3",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-white",children:"Standard AMM (CPMM)"}),(0,s.jsx)("p",{className:"text-sm text-gray-300",children:"Latest Raydium pool type • No OpenBook Market • Token-2022 Support"})]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Initial SOL Liquidity *"}),(0,s.jsx)("input",{type:"number",value:u.initialSolAmount,onChange:e=>N("initialSolAmount",e.target.value),placeholder:"0.2",min:"0.01",step:"0.01",className:"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"No minimum required, but 0.2+ SOL recommended for healthy trading"})]}),(0,s.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,s.jsxs)("button",{type:"button",onClick:k,className:"bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 flex items-center",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back"]}),(0,s.jsxs)("button",{type:"button",onClick:w,disabled:!S,className:"bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30",children:["Continue",(0,s.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]}),"review"===o&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-green-900/30 to-blue-900/30 border border-green-500/30 rounded-xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Review Your Token"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-white mb-3",children:"Token Details"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Name:"}),(0,s.jsx)("span",{className:"font-medium text-gray-200",children:c.name||"Not set"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Symbol:"}),(0,s.jsx)("span",{className:"font-medium text-gray-200",children:c.symbol||"Not set"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Supply:"}),(0,s.jsx)("span",{className:"font-medium text-gray-200",children:c.supply||"Not set"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-white mb-3",children:"Pool Setup"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Initial SOL:"}),(0,s.jsxs)("span",{className:"font-medium text-gray-200",children:[u.initialSolAmount," SOL"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Pool Type:"}),(0,s.jsx)("span",{className:"font-medium text-gray-200",children:"Standard AMM (CPMM)"})]})]})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Total Cost Breakdown"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Token Creation:"}),(0,s.jsxs)("span",{className:"font-medium text-gray-200",children:[h.tokenCreation," SOL"]})]}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Metadata & Image:"}),(0,s.jsxs)("span",{className:"font-medium text-gray-200",children:[h.metadata," SOL"]})]}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"CPMM Pool Creation:"}),(0,s.jsxs)("span",{className:"font-medium text-gray-200",children:[h.poolCreation," SOL"]})]}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Initial Liquidity:"}),(0,s.jsxs)("span",{className:"font-medium text-gray-200",children:[u.initialSolAmount," SOL"]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-600 pt-3 flex justify-between",children:[(0,s.jsx)("span",{className:"font-semibold text-white",children:"Total Cost:"}),(0,s.jsxs)("span",{className:"font-bold text-purple-400 text-lg",children:[(h.total+parseFloat(u.initialSolAmount||"0")).toFixed(3)," SOL"]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between pt-6",children:[(0,s.jsxs)("button",{type:"button",onClick:k,className:"bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 flex items-center",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back"]}),(0,s.jsxs)("button",{type:"button",onClick:C,className:"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 flex items-center border border-green-400/30 hover:border-green-300/50",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})}),"Launch Token"]})]})]}),"creating"===o&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-gradient-to-r from-purple-500 to-cyan-400 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-white"})}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Creating Your Token"}),(0,s.jsx)("p",{className:"text-gray-400 mb-8 max-w-md mx-auto",children:"Please wait while we create your token and set up the CPMM liquidity pool on Raydium. This may take a few minutes."}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Creating SPL Token..."]}),(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse"}),"Preparing CPMM Pool Creation..."]}),(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-500 rounded-full mr-2"}),"Initializing Raydium Pool..."]})]})]})]}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-purple-900/30 border border-purple-500/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Connect Your Wallet"}),(0,s.jsx)("p",{className:"text-gray-400 mb-6",children:"Please connect your Solana wallet to create a token."}),(0,s.jsx)(x,{className:"!bg-gradient-to-r !from-purple-500 !to-pink-500 hover:!from-purple-600 hover:!to-pink-600 !text-white !rounded-xl !px-6 !py-3 !font-semibold !border !border-purple-400/30 hover:!border-purple-300/50"})]})})]})}):null}function g(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p,{}),(0,s.jsxs)("main",{className:"relative overflow-hidden bg-black min-h-screen",children:[(0,s.jsxs)("div",{className:"absolute inset-0",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"}),(0,s.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse"}),(0,s.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-r from-cyan-500/20 to-green-500/20 rounded-full blur-3xl animate-pulse delay-1000"}),(0,s.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse delay-500"}),(0,s.jsx)("div",{className:"absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-full blur-2xl animate-pulse delay-2000"}),(0,s.jsx)("div",{className:"absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-full blur-2xl animate-pulse delay-3000"})]}),(0,s.jsxs)("div",{className:"relative container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsxs)("div",{className:"inline-flex items-center bg-gray-900/50 border border-purple-500/30 rounded-full px-6 py-3 mb-6",children:[(0,s.jsx)("span",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse mr-3"}),(0,s.jsx)("span",{className:"text-gray-300 text-sm font-medium",children:"Live on Solana Mainnet"})]}),(0,s.jsxs)("h1",{className:"text-4xl lg:text-6xl font-black text-white mb-4 leading-tight tracking-tight",children:["Launch Your",(0,s.jsx)("span",{className:"block bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent",children:"Solana Token"})]}),(0,s.jsxs)("p",{className:"text-lg lg:text-xl text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed",children:["Create, deploy, and add liquidity to your Solana token in minutes with our",(0,s.jsx)("span",{className:"text-purple-400 font-semibold",children:" professional-grade platform"}),"."]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-3xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-white",children:"~0.15 SOL"}),(0,s.jsx)("div",{className:"text-gray-400 text-sm",children:"Total Cost"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-white",children:"< 2 min"}),(0,s.jsx)("div",{className:"text-gray-400 text-sm",children:"Deploy Time"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-white",children:"3 Steps"}),(0,s.jsx)("div",{className:"text-gray-400 text-sm",children:"Simple Process"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-lg font-bold text-white",children:"DEX Ready"}),(0,s.jsx)("div",{className:"text-gray-400 text-sm",children:"Instant Trading"})]})]}),(0,s.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,s.jsx)(h,{isOpen:!0,onClose:()=>{}})}),(0,s.jsxs)("div",{className:"mt-16 pt-8 border-t border-gray-800/30 text-center",children:[(0,s.jsx)("p",{className:"text-gray-500 text-sm mb-6",children:"Built with industry-leading protocols"}),(0,s.jsxs)("div",{className:"flex flex-wrap justify-center items-center gap-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg"}),(0,s.jsx)("span",{className:"text-gray-300 font-medium",children:"Solana"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg"}),(0,s.jsx)("span",{className:"text-gray-300 font-medium",children:"Raydium"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg"}),(0,s.jsx)("span",{className:"text-gray-300 font-medium",children:"Metaplex"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg"}),(0,s.jsx)("span",{className:"text-gray-300 font-medium",children:"SPL Token"})]})]})]})]})]})]})}},7790:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[956,372,441,684,358],()=>t(3401)),_N_E=e.O()}]);