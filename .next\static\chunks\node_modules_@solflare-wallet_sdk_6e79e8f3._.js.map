{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/lib/esm/adapters/base.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport EventEmitter from 'eventemitter3';\nvar WalletAdapter = /** @class */ (function (_super) {\n    __extends(WalletAdapter, _super);\n    function WalletAdapter() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return WalletAdapter;\n}(EventEmitter));\nexport default WalletAdapter;\n"], "names": [], "mappings": ";;;AAeA;AAAA;AAfA,IAAI,YAAY,AAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QACpG,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;QAC7D,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;;AAEA,IAAI,gBAA+B,SAAU,MAAM;IAC/C,UAAU,eAAe;IACzB,SAAS;QACL,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACnE;IACA,OAAO;AACX,EAAE,0JAAA,CAAA,UAAY;uCACC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/lib/esm/adapters/WalletProvider.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nimport EventEmitter from 'eventemitter3';\nimport { PublicKey } from '@solana/web3.js';\nimport bs58 from 'bs58';\nvar Wallet = /** @class */ (function (_super) {\n    __extends(Wallet, _super);\n    function Wallet(provider, network) {\n        var _this = _super.call(this) || this;\n        _this._handleMessage = function (e) {\n            if ((_this._injectedProvider && e.source === window) ||\n                (e.origin === _this._providerUrl.origin && e.source === _this._popup)) {\n                if (e.data.method === 'connected') {\n                    var newPublicKey = new PublicKey(e.data.params.publicKey);\n                    if (!_this._publicKey || !_this._publicKey.equals(newPublicKey)) {\n                        if (_this._publicKey && !_this._publicKey.equals(newPublicKey)) {\n                            _this._handleDisconnect();\n                        }\n                        _this._publicKey = newPublicKey;\n                        _this._autoApprove = !!e.data.params.autoApprove;\n                        _this.emit('connect', _this._publicKey);\n                    }\n                }\n                else if (e.data.method === 'disconnected') {\n                    _this._handleDisconnect();\n                }\n                else if (e.data.result || e.data.error) {\n                    if (_this._responsePromises.has(e.data.id)) {\n                        var _a = __read(_this._responsePromises.get(e.data.id), 2), resolve = _a[0], reject = _a[1];\n                        if (e.data.result) {\n                            resolve(e.data.result);\n                        }\n                        else {\n                            reject(new Error(e.data.error));\n                        }\n                    }\n                }\n            }\n        };\n        _this._handleConnect = function () {\n            if (!_this._handlerAdded) {\n                _this._handlerAdded = true;\n                window.addEventListener('message', _this._handleMessage);\n                window.addEventListener('beforeunload', _this.disconnect);\n            }\n            if (_this._injectedProvider) {\n                return new Promise(function (resolve) {\n                    _this._sendRequest('connect', {});\n                    resolve();\n                });\n            }\n            else {\n                window.name = 'parent';\n                _this._popup = window.open(_this._providerUrl.toString(), '_blank', 'location,resizable,width=460,height=675');\n                return new Promise(function (resolve) {\n                    _this.once('connect', resolve);\n                });\n            }\n        };\n        _this._handleDisconnect = function () {\n            if (_this._handlerAdded) {\n                _this._handlerAdded = false;\n                window.removeEventListener('message', _this._handleMessage);\n                window.removeEventListener('beforeunload', _this.disconnect);\n            }\n            if (_this._publicKey) {\n                _this._publicKey = null;\n                _this.emit('disconnect');\n            }\n            _this._responsePromises.forEach(function (_a, id) {\n                var _b = __read(_a, 2), resolve = _b[0], reject = _b[1];\n                _this._responsePromises.delete(id);\n                reject('Wallet disconnected');\n            });\n        };\n        _this._sendRequest = function (method, params) { return __awaiter(_this, void 0, void 0, function () {\n            var requestId;\n            var _this = this;\n            return __generator(this, function (_a) {\n                if (method !== 'connect' && !this.connected) {\n                    throw new Error('Wallet not connected');\n                }\n                requestId = this._nextRequestId;\n                ++this._nextRequestId;\n                return [2 /*return*/, new Promise(function (resolve, reject) {\n                        _this._responsePromises.set(requestId, [resolve, reject]);\n                        if (_this._injectedProvider) {\n                            _this._injectedProvider.postMessage({\n                                jsonrpc: '2.0',\n                                id: requestId,\n                                method: method,\n                                params: __assign({ network: _this._network }, params),\n                            });\n                        }\n                        else {\n                            _this._popup.postMessage({\n                                jsonrpc: '2.0',\n                                id: requestId,\n                                method: method,\n                                params: params,\n                            }, _this._providerUrl.origin);\n                            if (!_this.autoApprove) {\n                                _this._popup.focus();\n                            }\n                        }\n                    })];\n            });\n        }); };\n        _this.connect = function () {\n            if (_this._popup) {\n                _this._popup.close();\n            }\n            return _this._handleConnect();\n        };\n        _this.disconnect = function () { return __awaiter(_this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this._injectedProvider) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this._sendRequest('disconnect', {})];\n                    case 1:\n                        _a.sent();\n                        _a.label = 2;\n                    case 2:\n                        if (this._popup) {\n                            this._popup.close();\n                        }\n                        this._handleDisconnect();\n                        return [2 /*return*/];\n                }\n            });\n        }); };\n        _this.sign = function (data, display) { return __awaiter(_this, void 0, void 0, function () {\n            var response, signature, publicKey;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!(data instanceof Uint8Array)) {\n                            throw new Error('Data must be an instance of Uint8Array');\n                        }\n                        return [4 /*yield*/, this._sendRequest('sign', {\n                                data: data,\n                                display: display,\n                            })];\n                    case 1:\n                        response = _a.sent();\n                        signature = bs58.decode(response.signature);\n                        publicKey = new PublicKey(response.publicKey);\n                        return [2 /*return*/, {\n                                signature: signature,\n                                publicKey: publicKey,\n                            }];\n                }\n            });\n        }); };\n        if (isInjectedProvider(provider)) {\n            _this._injectedProvider = provider;\n        }\n        else if (isString(provider)) {\n            _this._providerUrl = new URL(provider);\n            _this._providerUrl.hash = new URLSearchParams({\n                origin: window.location.origin,\n                network: network,\n            }).toString();\n        }\n        else {\n            throw new Error('provider parameter must be an injected provider or a URL string.');\n        }\n        _this._network = network;\n        _this._publicKey = null;\n        _this._autoApprove = false;\n        _this._popup = null;\n        _this._handlerAdded = false;\n        _this._nextRequestId = 1;\n        _this._responsePromises = new Map();\n        return _this;\n    }\n    Object.defineProperty(Wallet.prototype, \"publicKey\", {\n        get: function () {\n            return this._publicKey;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Wallet.prototype, \"connected\", {\n        get: function () {\n            return this._publicKey !== null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Wallet.prototype, \"autoApprove\", {\n        get: function () {\n            return this._autoApprove;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Wallet;\n}(EventEmitter));\nexport default Wallet;\nfunction isString(a) {\n    return typeof a === 'string';\n}\nfunction isInjectedProvider(a) {\n    return isObject(a) && isFunction(a.postMessage);\n}\nfunction isObject(a) {\n    return typeof a === 'object' && a !== null;\n}\nfunction isFunction(a) {\n    return typeof a === 'function';\n}\n"], "names": [], "mappings": ";;;AA8EA;AAAA;AACA;AACA;AAhFA,IAAI,YAAY,AAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QACpG,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;QAC7D,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,AAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAK;IACtC,WAAW,OAAO,MAAM,IAAI,SAAS,CAAC;QAClC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IACzD,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACnB;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAChC;AACA,IAAI,YAAY,AAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACnF,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AACA,IAAI,cAAc,AAAC,IAAI,IAAI,IAAI,CAAC,WAAW,IAAK,SAAU,OAAO,EAAE,IAAI;IACnE,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG;IAC/G,OAAO,IAAI;QAAE,MAAM,KAAK;QAAI,SAAS,KAAK;QAAI,UAAU,KAAK;IAAG,GAAG,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IACvJ,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACJ;AACA,IAAI,SAAS,AAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAK,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC1D,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;IAC/B,IAAI;QACA,MAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAE,GAAG,IAAI,CAAC,EAAE,KAAK;IAC7E,EACA,OAAO,OAAO;QAAE,IAAI;YAAE,OAAO;QAAM;IAAG,SAC9B;QACJ,IAAI;YACA,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC;QAClD,SACQ;YAAE,IAAI,GAAG,MAAM,EAAE,KAAK;QAAE;IACpC;IACA,OAAO;AACX;;;;AAIA,IAAI,SAAwB,SAAU,MAAM;IACxC,UAAU,QAAQ;IAClB,SAAS,OAAO,QAAQ,EAAE,OAAO;QAC7B,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,cAAc,GAAG,SAAU,CAAC;YAC9B,IAAI,AAAC,MAAM,iBAAiB,IAAI,EAAE,MAAM,KAAK,UACxC,EAAE,MAAM,KAAK,MAAM,YAAY,CAAC,MAAM,IAAI,EAAE,MAAM,KAAK,MAAM,MAAM,EAAG;gBACvE,IAAI,EAAE,IAAI,CAAC,MAAM,KAAK,aAAa;oBAC/B,IAAI,eAAe,IAAI,2KAAA,CAAA,YAAS,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;oBACxD,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,CAAC,eAAe;wBAC7D,IAAI,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,CAAC,eAAe;4BAC5D,MAAM,iBAAiB;wBAC3B;wBACA,MAAM,UAAU,GAAG;wBACnB,MAAM,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;wBAChD,MAAM,IAAI,CAAC,WAAW,MAAM,UAAU;oBAC1C;gBACJ,OACK,IAAI,EAAE,IAAI,CAAC,MAAM,KAAK,gBAAgB;oBACvC,MAAM,iBAAiB;gBAC3B,OACK,IAAI,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE;oBACpC,IAAI,MAAM,iBAAiB,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG;wBACxC,IAAI,KAAK,OAAO,MAAM,iBAAiB,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;wBAC3F,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE;4BACf,QAAQ,EAAE,IAAI,CAAC,MAAM;wBACzB,OACK;4BACD,OAAO,IAAI,MAAM,EAAE,IAAI,CAAC,KAAK;wBACjC;oBACJ;gBACJ;YACJ;QACJ;QACA,MAAM,cAAc,GAAG;YACnB,IAAI,CAAC,MAAM,aAAa,EAAE;gBACtB,MAAM,aAAa,GAAG;gBACtB,OAAO,gBAAgB,CAAC,WAAW,MAAM,cAAc;gBACvD,OAAO,gBAAgB,CAAC,gBAAgB,MAAM,UAAU;YAC5D;YACA,IAAI,MAAM,iBAAiB,EAAE;gBACzB,OAAO,IAAI,QAAQ,SAAU,OAAO;oBAChC,MAAM,YAAY,CAAC,WAAW,CAAC;oBAC/B;gBACJ;YACJ,OACK;gBACD,OAAO,IAAI,GAAG;gBACd,MAAM,MAAM,GAAG,OAAO,IAAI,CAAC,MAAM,YAAY,CAAC,QAAQ,IAAI,UAAU;gBACpE,OAAO,IAAI,QAAQ,SAAU,OAAO;oBAChC,MAAM,IAAI,CAAC,WAAW;gBAC1B;YACJ;QACJ;QACA,MAAM,iBAAiB,GAAG;YACtB,IAAI,MAAM,aAAa,EAAE;gBACrB,MAAM,aAAa,GAAG;gBACtB,OAAO,mBAAmB,CAAC,WAAW,MAAM,cAAc;gBAC1D,OAAO,mBAAmB,CAAC,gBAAgB,MAAM,UAAU;YAC/D;YACA,IAAI,MAAM,UAAU,EAAE;gBAClB,MAAM,UAAU,GAAG;gBACnB,MAAM,IAAI,CAAC;YACf;YACA,MAAM,iBAAiB,CAAC,OAAO,CAAC,SAAU,EAAE,EAAE,EAAE;gBAC5C,IAAI,KAAK,OAAO,IAAI,IAAI,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;gBACvD,MAAM,iBAAiB,CAAC,MAAM,CAAC;gBAC/B,OAAO;YACX;QACJ;QACA,MAAM,YAAY,GAAG,SAAU,MAAM,EAAE,MAAM;YAAI,OAAO,UAAU,OAAO,KAAK,GAAG,KAAK,GAAG;gBACrF,IAAI;gBACJ,IAAI,QAAQ,IAAI;gBAChB,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;oBACjC,IAAI,WAAW,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE;wBACzC,MAAM,IAAI,MAAM;oBACpB;oBACA,YAAY,IAAI,CAAC,cAAc;oBAC/B,EAAE,IAAI,CAAC,cAAc;oBACrB,OAAO;wBAAC,EAAE,QAAQ;wBAAI,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;4BACnD,MAAM,iBAAiB,CAAC,GAAG,CAAC,WAAW;gCAAC;gCAAS;6BAAO;4BACxD,IAAI,MAAM,iBAAiB,EAAE;gCACzB,MAAM,iBAAiB,CAAC,WAAW,CAAC;oCAChC,SAAS;oCACT,IAAI;oCACJ,QAAQ;oCACR,QAAQ,SAAS;wCAAE,SAAS,MAAM,QAAQ;oCAAC,GAAG;gCAClD;4BACJ,OACK;gCACD,MAAM,MAAM,CAAC,WAAW,CAAC;oCACrB,SAAS;oCACT,IAAI;oCACJ,QAAQ;oCACR,QAAQ;gCACZ,GAAG,MAAM,YAAY,CAAC,MAAM;gCAC5B,IAAI,CAAC,MAAM,WAAW,EAAE;oCACpB,MAAM,MAAM,CAAC,KAAK;gCACtB;4BACJ;wBACJ;qBAAG;gBACX;YACJ;QAAI;QACJ,MAAM,OAAO,GAAG;YACZ,IAAI,MAAM,MAAM,EAAE;gBACd,MAAM,MAAM,CAAC,KAAK;YACtB;YACA,OAAO,MAAM,cAAc;QAC/B;QACA,MAAM,UAAU,GAAG;YAAc,OAAO,UAAU,OAAO,KAAK,GAAG,KAAK,GAAG;gBACrE,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;oBACjC,OAAQ,GAAG,KAAK;wBACZ,KAAK;4BACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO;gCAAC,EAAE,OAAO;gCAAI;6BAAE;4BACpD,OAAO;gCAAC,EAAE,OAAO;gCAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;6BAAG;wBAC7D,KAAK;4BACD,GAAG,IAAI;4BACP,GAAG,KAAK,GAAG;wBACf,KAAK;4BACD,IAAI,IAAI,CAAC,MAAM,EAAE;gCACb,IAAI,CAAC,MAAM,CAAC,KAAK;4BACrB;4BACA,IAAI,CAAC,iBAAiB;4BACtB,OAAO;gCAAC,EAAE,QAAQ;6BAAG;oBAC7B;gBACJ;YACJ;QAAI;QACJ,MAAM,IAAI,GAAG,SAAU,IAAI,EAAE,OAAO;YAAI,OAAO,UAAU,OAAO,KAAK,GAAG,KAAK,GAAG;gBAC5E,IAAI,UAAU,WAAW;gBACzB,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;oBACjC,OAAQ,GAAG,KAAK;wBACZ,KAAK;4BACD,IAAI,CAAC,CAAC,gBAAgB,UAAU,GAAG;gCAC/B,MAAM,IAAI,MAAM;4BACpB;4BACA,OAAO;gCAAC,EAAE,OAAO;gCAAI,IAAI,CAAC,YAAY,CAAC,QAAQ;oCACvC,MAAM;oCACN,SAAS;gCACb;6BAAG;wBACX,KAAK;4BACD,WAAW,GAAG,IAAI;4BAClB,YAAY,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC,SAAS,SAAS;4BAC1C,YAAY,IAAI,2KAAA,CAAA,YAAS,CAAC,SAAS,SAAS;4BAC5C,OAAO;gCAAC,EAAE,QAAQ;gCAAI;oCACd,WAAW;oCACX,WAAW;gCACf;6BAAE;oBACd;gBACJ;YACJ;QAAI;QACJ,IAAI,mBAAmB,WAAW;YAC9B,MAAM,iBAAiB,GAAG;QAC9B,OACK,IAAI,SAAS,WAAW;YACzB,MAAM,YAAY,GAAG,IAAI,IAAI;YAC7B,MAAM,YAAY,CAAC,IAAI,GAAG,IAAI,gBAAgB;gBAC1C,QAAQ,OAAO,QAAQ,CAAC,MAAM;gBAC9B,SAAS;YACb,GAAG,QAAQ;QACf,OACK;YACD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,QAAQ,GAAG;QACjB,MAAM,UAAU,GAAG;QACnB,MAAM,YAAY,GAAG;QACrB,MAAM,MAAM,GAAG;QACf,MAAM,aAAa,GAAG;QACtB,MAAM,cAAc,GAAG;QACvB,MAAM,iBAAiB,GAAG,IAAI;QAC9B,OAAO;IACX;IACA,OAAO,cAAc,CAAC,OAAO,SAAS,EAAE,aAAa;QACjD,KAAK;YACD,OAAO,IAAI,CAAC,UAAU;QAC1B;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,OAAO,SAAS,EAAE,aAAa;QACjD,KAAK;YACD,OAAO,IAAI,CAAC,UAAU,KAAK;QAC/B;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,OAAO,SAAS,EAAE,eAAe;QACnD,KAAK;YACD,OAAO,IAAI,CAAC,YAAY;QAC5B;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO;AACX,EAAE,0JAAA,CAAA,UAAY;uCACC;AACf,SAAS,SAAS,CAAC;IACf,OAAO,OAAO,MAAM;AACxB;AACA,SAAS,mBAAmB,CAAC;IACzB,OAAO,SAAS,MAAM,WAAW,EAAE,WAAW;AAClD;AACA,SAAS,SAAS,CAAC;IACf,OAAO,OAAO,MAAM,YAAY,MAAM;AAC1C;AACA,SAAS,WAAW,CAAC;IACjB,OAAO,OAAO,MAAM;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/lib/esm/adapters/web.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport WalletAdapter from './base';\nimport Wallet from './WalletProvider';\nimport bs58 from 'bs58';\nvar WebAdapter = /** @class */ (function (_super) {\n    __extends(WebAdapter, _super);\n    // @ts-ignore\n    function WebAdapter(iframe, network, provider) {\n        var _this = _super.call(this) || this;\n        _this._instance = null;\n        // @ts-ignore\n        _this.handleMessage = function (data) {\n            // nothing to do here\n        };\n        _this._sendRequest = function (method, params) { return __awaiter(_this, void 0, void 0, function () {\n            var _a, _b;\n            return __generator(this, function (_c) {\n                switch (_c.label) {\n                    case 0:\n                        if (!((_a = this._instance) === null || _a === void 0 ? void 0 : _a.sendRequest)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, this._instance.sendRequest(method, params)];\n                    case 1: return [2 /*return*/, _c.sent()];\n                    case 2:\n                        if (!((_b = this._instance) === null || _b === void 0 ? void 0 : _b._sendRequest)) return [3 /*break*/, 4];\n                        return [4 /*yield*/, this._instance._sendRequest(method, params)];\n                    case 3: return [2 /*return*/, _c.sent()];\n                    case 4: throw new Error('Unsupported version of `@project-serum/sol-wallet-adapter`');\n                }\n            });\n        }); };\n        _this._handleConnect = function () {\n            _this.emit('connect');\n        };\n        _this._handleDisconnect = function () {\n            window.clearInterval(_this._pollTimer);\n            _this.emit('disconnect');\n        };\n        _this._network = network;\n        _this._provider = provider;\n        return _this;\n    }\n    Object.defineProperty(WebAdapter.prototype, \"publicKey\", {\n        get: function () {\n            return this._instance.publicKey || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(WebAdapter.prototype, \"connected\", {\n        get: function () {\n            return this._instance.connected || false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WebAdapter.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this._instance = new Wallet(this._provider, this._network);\n                        this._instance.on('connect', this._handleConnect);\n                        this._instance.on('disconnect', this._handleDisconnect);\n                        this._pollTimer = window.setInterval(function () {\n                            var _a, _b;\n                            // @ts-ignore\n                            if (((_b = (_a = _this._instance) === null || _a === void 0 ? void 0 : _a._popup) === null || _b === void 0 ? void 0 : _b.closed) !== false) {\n                                _this._handleDisconnect();\n                            }\n                        }, 200);\n                        return [4 /*yield*/, this._instance.connect()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        this._instance.removeAllListeners('connect');\n                        this._instance.removeAllListeners('disconnect');\n                        return [4 /*yield*/, this._instance.disconnect()];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signTransaction = function (transaction) {\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransaction;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._sendRequest('signTransactionV2', {\n                                transaction: bs58.encode(transaction)\n                            })];\n                    case 1:\n                        signedTransaction = (_a.sent()).transaction;\n                        return [2 /*return*/, bs58.decode(signedTransaction)];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signAllTransactions = function (transactions) {\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransactions;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._sendRequest('signAllTransactionsV2', {\n                                transactions: transactions.map(function (transaction) { return bs58.encode(transaction); })\n                            })];\n                    case 1:\n                        signedTransactions = (_a.sent()).transactions;\n                        return [2 /*return*/, signedTransactions.map(function (transaction) { return bs58.decode(transaction); })];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signAndSendTransaction = function (transaction, options) {\n        return __awaiter(this, void 0, void 0, function () {\n            var response;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._sendRequest('signAndSendTransaction', {\n                                transaction: bs58.encode(transaction),\n                                options: options\n                            })];\n                    case 1:\n                        response = (_a.sent());\n                        return [2 /*return*/, response.signature];\n                }\n            });\n        });\n    };\n    WebAdapter.prototype.signMessage = function (data, display) {\n        if (display === void 0) { display = 'hex'; }\n        return __awaiter(this, void 0, void 0, function () {\n            var signature;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._instance.sign(data, display)];\n                    case 1:\n                        signature = (_a.sent()).signature;\n                        return [2 /*return*/, Uint8Array.from(signature)];\n                }\n            });\n        });\n    };\n    return WebAdapter;\n}(WalletAdapter));\nexport default WebAdapter;\n"], "names": [], "mappings": ";;;AAmDA;AACA;AACA;AArDA,IAAI,YAAY,AAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QACpG,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;QAC7D,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,YAAY,AAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACnF,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AACA,IAAI,cAAc,AAAC,IAAI,IAAI,IAAI,CAAC,WAAW,IAAK,SAAU,OAAO,EAAE,IAAI;IACnE,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG;IAC/G,OAAO,IAAI;QAAE,MAAM,KAAK;QAAI,SAAS,KAAK;QAAI,UAAU,KAAK;IAAG,GAAG,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IACvJ,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACJ;;;;AAIA,IAAI,aAA4B,SAAU,MAAM;IAC5C,UAAU,YAAY;IACtB,aAAa;IACb,SAAS,WAAW,MAAM,EAAE,OAAO,EAAE,QAAQ;QACzC,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,SAAS,GAAG;QAClB,aAAa;QACb,MAAM,aAAa,GAAG,SAAU,IAAI;QAChC,qBAAqB;QACzB;QACA,MAAM,YAAY,GAAG,SAAU,MAAM,EAAE,MAAM;YAAI,OAAO,UAAU,OAAO,KAAK,GAAG,KAAK,GAAG;gBACrF,IAAI,IAAI;gBACR,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;oBACjC,OAAQ,GAAG,KAAK;wBACZ,KAAK;4BACD,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,GAAG,OAAO;gCAAC,EAAE,OAAO;gCAAI;6BAAE;4BACzG,OAAO;gCAAC,EAAE,OAAO;gCAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ;6BAAQ;wBACpE,KAAK;4BAAG,OAAO;gCAAC,EAAE,QAAQ;gCAAI,GAAG,IAAI;6BAAG;wBACxC,KAAK;4BACD,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,GAAG,OAAO;gCAAC,EAAE,OAAO;gCAAI;6BAAE;4BAC1G,OAAO;gCAAC,EAAE,OAAO;gCAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ;6BAAQ;wBACrE,KAAK;4BAAG,OAAO;gCAAC,EAAE,QAAQ;gCAAI,GAAG,IAAI;6BAAG;wBACxC,KAAK;4BAAG,MAAM,IAAI,MAAM;oBAC5B;gBACJ;YACJ;QAAI;QACJ,MAAM,cAAc,GAAG;YACnB,MAAM,IAAI,CAAC;QACf;QACA,MAAM,iBAAiB,GAAG;YACtB,OAAO,aAAa,CAAC,MAAM,UAAU;YACrC,MAAM,IAAI,CAAC;QACf;QACA,MAAM,QAAQ,GAAG;QACjB,MAAM,SAAS,GAAG;QAClB,OAAO;IACX;IACA,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,aAAa;QACrD,KAAK;YACD,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI;QACvC;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,WAAW,SAAS,EAAE,aAAa;QACrD,KAAK;YACD,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI;QACvC;QACA,YAAY;QACZ,cAAc;IAClB;IACA,WAAW,SAAS,CAAC,OAAO,GAAG;QAC3B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,QAAQ,IAAI;YAChB,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,SAAS,GAAG,IAAI,0LAAA,CAAA,UAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ;wBACzD,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,cAAc;wBAChD,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,cAAc,IAAI,CAAC,iBAAiB;wBACtD,IAAI,CAAC,UAAU,GAAG,OAAO,WAAW,CAAC;4BACjC,IAAI,IAAI;4BACR,aAAa;4BACb,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,OAAO;gCACzI,MAAM,iBAAiB;4BAC3B;wBACJ,GAAG;wBACH,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,SAAS,CAAC,OAAO;yBAAG;oBAClD,KAAK;wBACD,GAAG,IAAI;wBACP,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBAC7B;YACJ;QACJ;IACJ;IACA,WAAW,SAAS,CAAC,UAAU,GAAG;QAC9B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;wBAClC,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;wBAClC,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,SAAS,CAAC,UAAU;yBAAG;oBACrD,KAAK;wBACD,GAAG,IAAI;wBACP,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBAC7B;YACJ;QACJ;IACJ;IACA,WAAW,SAAS,CAAC,eAAe,GAAG,SAAU,WAAW;QACxD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,YAAY,CAAC,qBAAqB;gCACpD,aAAa,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;4BAC7B;yBAAG;oBACX,KAAK;wBACD,oBAAoB,AAAC,GAAG,IAAI,GAAI,WAAW;wBAC3C,OAAO;4BAAC,EAAE,QAAQ;4BAAI,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;yBAAmB;gBAC7D;YACJ;QACJ;IACJ;IACA,WAAW,SAAS,CAAC,mBAAmB,GAAG,SAAU,YAAY;QAC7D,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,YAAY,CAAC,yBAAyB;gCACxD,cAAc,aAAa,GAAG,CAAC,SAAU,WAAW;oCAAI,OAAO,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;gCAAc;4BAC7F;yBAAG;oBACX,KAAK;wBACD,qBAAqB,AAAC,GAAG,IAAI,GAAI,YAAY;wBAC7C,OAAO;4BAAC,EAAE,QAAQ;4BAAI,mBAAmB,GAAG,CAAC,SAAU,WAAW;gCAAI,OAAO,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;4BAAc;yBAAG;gBAClH;YACJ;QACJ;IACJ;IACA,WAAW,SAAS,CAAC,sBAAsB,GAAG,SAAU,WAAW,EAAE,OAAO;QACxE,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,YAAY,CAAC,0BAA0B;gCACzD,aAAa,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;gCACzB,SAAS;4BACb;yBAAG;oBACX,KAAK;wBACD,WAAY,GAAG,IAAI;wBACnB,OAAO;4BAAC,EAAE,QAAQ;4BAAI,SAAS,SAAS;yBAAC;gBACjD;YACJ;QACJ;IACJ;IACA,WAAW,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI,EAAE,OAAO;QACtD,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAO;QAC3C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM;yBAAS;oBAC5D,KAAK;wBACD,YAAY,AAAC,GAAG,IAAI,GAAI,SAAS;wBACjC,OAAO;4BAAC,EAAE,QAAQ;4BAAI,WAAW,IAAI,CAAC;yBAAW;gBACzD;YACJ;QACJ;IACJ;IACA,OAAO;AACX,EAAE,gLAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/native.js"], "sourcesContent": ["const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa,OAAO,WAAW,eAAe,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,IAAI,CAAC;uCACjF;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/rng.js"], "sourcesContent": ["// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}"], "names": [], "mappings": "AAAA,6FAA6F;AAC7F,6FAA6F;AAC7F,mCAAmC;;;;AACnC,IAAI;AACJ,MAAM,QAAQ,IAAI,WAAW;AACd,SAAS;IACtB,8EAA8E;IAC9E,IAAI,CAAC,iBAAiB;QACpB,4FAA4F;QAC5F,kBAAkB,OAAO,WAAW,eAAe,OAAO,eAAe,IAAI,OAAO,eAAe,CAAC,IAAI,CAAC;QAEzG,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO,gBAAgB;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;"], "names": [], "mappings": ";;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\n\nexport default validate;"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,SAAS,IAAI;IACpB,OAAO,OAAO,SAAS,YAAY,yMAAA,CAAA,UAAK,CAAC,IAAI,CAAC;AAChD;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 919, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/stringify.js"], "sourcesContent": ["import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;"], "names": [], "mappings": ";;;;AAAA;;AACA;;;CAGC,GAED,MAAM,YAAY,EAAE;AAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC5B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAChD;AAEO,SAAS,gBAAgB,GAAG,EAAE,SAAS,CAAC;IAC7C,uEAAuE;IACvE,oFAAoF;IACpF,OAAO,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC;AACpf;AAEA,SAAS,UAAU,GAAG,EAAE,SAAS,CAAC;IAChC,MAAM,OAAO,gBAAgB,KAAK,SAAS,4EAA4E;IACvH,oBAAoB;IACpB,wEAAwE;IACxE,2BAA2B;IAC3B,mEAAmE;IAEnE,IAAI,CAAC,CAAA,GAAA,4MAAA,CAAA,UAAQ,AAAD,EAAE,OAAO;QACnB,MAAM,UAAU;IAClB;IAEA,OAAO;AACT;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/node_modules/uuid/dist/esm-browser/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC9B,IAAI,0MAAA,CAAA,UAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACzC,OAAO,0MAAA,CAAA,UAAM,CAAC,UAAU;IAC1B;IAEA,UAAU,WAAW,CAAC;IACtB,MAAM,OAAO,QAAQ,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,uMAAA,CAAA,UAAG,KAAK,gEAAgE;IAEvH,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,OAAO;IAC3B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,OAAO,MAAM,oCAAoC;IAErE,IAAI,KAAK;QACP,SAAS,UAAU;QAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YAC3B,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC3B;QAEA,OAAO;IACT;IAEA,OAAO,CAAA,GAAA,6MAAA,CAAA,kBAAe,AAAD,EAAE;AACzB;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/lib/esm/adapters/iframe.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport { PublicKey } from '@solana/web3.js';\nimport WalletAdapter from './base';\nimport { v4 as uuidv4 } from 'uuid';\nimport bs58 from 'bs58';\nvar IframeAdapter = /** @class */ (function (_super) {\n    __extends(IframeAdapter, _super);\n    function IframeAdapter(iframe, publicKey) {\n        var _this = this;\n        var _a;\n        _this = _super.call(this) || this;\n        _this._publicKey = null;\n        _this._messageHandlers = {};\n        _this.handleMessage = function (data) {\n            if (_this._messageHandlers[data.id]) {\n                var _a = _this._messageHandlers[data.id], resolve = _a.resolve, reject = _a.reject;\n                delete _this._messageHandlers[data.id];\n                if (data.error) {\n                    reject(data.error);\n                }\n                else {\n                    resolve(data.result);\n                }\n            }\n        };\n        _this._sendMessage = function (data) {\n            if (!_this.connected) {\n                throw new Error('Wallet not connected');\n            }\n            return new Promise(function (resolve, reject) {\n                var _a, _b;\n                var messageId = uuidv4();\n                _this._messageHandlers[messageId] = { resolve: resolve, reject: reject };\n                (_b = (_a = _this._iframe) === null || _a === void 0 ? void 0 : _a.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage({\n                    channel: 'solflareWalletAdapterToIframe',\n                    data: __assign({ id: messageId }, data)\n                }, '*');\n            });\n        };\n        _this._iframe = iframe;\n        _this._publicKey = new PublicKey((_a = publicKey === null || publicKey === void 0 ? void 0 : publicKey.toString) === null || _a === void 0 ? void 0 : _a.call(publicKey));\n        return _this;\n    }\n    Object.defineProperty(IframeAdapter.prototype, \"publicKey\", {\n        get: function () {\n            return this._publicKey || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(IframeAdapter.prototype, \"connected\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IframeAdapter.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                return [2 /*return*/];\n            });\n        });\n    };\n    IframeAdapter.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this._sendMessage({\n                            method: 'disconnect'\n                        })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signTransaction = function (transaction) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransaction, e_1;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signTransaction',\n                                params: {\n                                    transaction: bs58.encode(transaction)\n                                }\n                            })];\n                    case 2:\n                        signedTransaction = _b.sent();\n                        return [2 /*return*/, bs58.decode(signedTransaction)];\n                    case 3:\n                        e_1 = _b.sent();\n                        throw new Error(((_a = e_1 === null || e_1 === void 0 ? void 0 : e_1.toString) === null || _a === void 0 ? void 0 : _a.call(e_1)) || 'Failed to sign transaction');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signAllTransactions = function (transactions) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var signedTransactions, e_2;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signAllTransactions',\n                                params: {\n                                    transactions: transactions.map(function (transaction) { return bs58.encode(transaction); })\n                                }\n                            })];\n                    case 2:\n                        signedTransactions = _b.sent();\n                        return [2 /*return*/, signedTransactions.map(function (transaction) { return bs58.decode(transaction); })];\n                    case 3:\n                        e_2 = _b.sent();\n                        throw new Error(((_a = e_2 === null || e_2 === void 0 ? void 0 : e_2.toString) === null || _a === void 0 ? void 0 : _a.call(e_2)) || 'Failed to sign transactions');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signAndSendTransaction = function (transaction, options) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function () {\n            var result, e_3;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signAndSendTransaction',\n                                params: {\n                                    transaction: bs58.encode(transaction),\n                                    options: options\n                                }\n                            })];\n                    case 2:\n                        result = _b.sent();\n                        return [2 /*return*/, result];\n                    case 3:\n                        e_3 = _b.sent();\n                        throw new Error(((_a = e_3 === null || e_3 === void 0 ? void 0 : e_3.toString) === null || _a === void 0 ? void 0 : _a.call(e_3)) || 'Failed to sign and send transaction');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    IframeAdapter.prototype.signMessage = function (data, display) {\n        var _a;\n        if (display === void 0) { display = 'hex'; }\n        return __awaiter(this, void 0, void 0, function () {\n            var result, e_4;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._sendMessage({\n                                method: 'signMessage',\n                                params: {\n                                    data: data,\n                                    display: display\n                                }\n                            })];\n                    case 2:\n                        result = _b.sent();\n                        return [2 /*return*/, Uint8Array.from(bs58.decode(result))];\n                    case 3:\n                        e_4 = _b.sent();\n                        throw new Error(((_a = e_4 === null || e_4 === void 0 ? void 0 : e_4.toString) === null || _a === void 0 ? void 0 : _a.call(e_4)) || 'Failed to sign message');\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    return IframeAdapter;\n}(WalletAdapter));\nexport default IframeAdapter;\n"], "names": [], "mappings": ";;;AA8DA;AACA;AACA;AACA;AAjEA,IAAI,YAAY,AAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QACpG,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;QAC7D,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,AAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAK;IACtC,WAAW,OAAO,MAAM,IAAI,SAAS,CAAC;QAClC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IACzD,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACnB;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAChC;AACA,IAAI,YAAY,AAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACnF,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AACA,IAAI,cAAc,AAAC,IAAI,IAAI,IAAI,CAAC,WAAW,IAAK,SAAU,OAAO,EAAE,IAAI;IACnE,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG;IAC/G,OAAO,IAAI;QAAE,MAAM,KAAK;QAAI,SAAS,KAAK;QAAI,UAAU,KAAK;IAAG,GAAG,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IACvJ,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACJ;;;;;AAKA,IAAI,gBAA+B,SAAU,MAAM;IAC/C,UAAU,eAAe;IACzB,SAAS,cAAc,MAAM,EAAE,SAAS;QACpC,IAAI,QAAQ,IAAI;QAChB,IAAI;QACJ,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACjC,MAAM,UAAU,GAAG;QACnB,MAAM,gBAAgB,GAAG,CAAC;QAC1B,MAAM,aAAa,GAAG,SAAU,IAAI;YAChC,IAAI,MAAM,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE;gBACjC,IAAI,KAAK,MAAM,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,GAAG,OAAO,EAAE,SAAS,GAAG,MAAM;gBAClF,OAAO,MAAM,gBAAgB,CAAC,KAAK,EAAE,CAAC;gBACtC,IAAI,KAAK,KAAK,EAAE;oBACZ,OAAO,KAAK,KAAK;gBACrB,OACK;oBACD,QAAQ,KAAK,MAAM;gBACvB;YACJ;QACJ;QACA,MAAM,YAAY,GAAG,SAAU,IAAI;YAC/B,IAAI,CAAC,MAAM,SAAS,EAAE;gBAClB,MAAM,IAAI,MAAM;YACpB;YACA,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBACxC,IAAI,IAAI;gBACR,IAAI,YAAY,CAAA,GAAA,uOAAA,CAAA,KAAM,AAAD;gBACrB,MAAM,gBAAgB,CAAC,UAAU,GAAG;oBAAE,SAAS;oBAAS,QAAQ;gBAAO;gBACvE,CAAC,KAAK,CAAC,KAAK,MAAM,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,CAAC;oBAClI,SAAS;oBACT,MAAM,SAAS;wBAAE,IAAI;oBAAU,GAAG;gBACtC,GAAG;YACP;QACJ;QACA,MAAM,OAAO,GAAG;QAChB,MAAM,UAAU,GAAG,IAAI,2KAAA,CAAA,YAAS,CAAC,CAAC,KAAK,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;QAC9J,OAAO;IACX;IACA,OAAO,cAAc,CAAC,cAAc,SAAS,EAAE,aAAa;QACxD,KAAK;YACD,OAAO,IAAI,CAAC,UAAU,IAAI;QAC9B;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,cAAc,SAAS,EAAE,aAAa;QACxD,KAAK;YACD,OAAO;QACX;QACA,YAAY;QACZ,cAAc;IAClB;IACA,cAAc,SAAS,CAAC,OAAO,GAAG;QAC9B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAO;oBAAC,EAAE,QAAQ;iBAAG;YACzB;QACJ;IACJ;IACA,cAAc,SAAS,CAAC,UAAU,GAAG;QACjC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBAAG,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,YAAY,CAAC;gCACvC,QAAQ;4BACZ;yBAAG;oBACP,KAAK;wBACD,GAAG,IAAI;wBACP,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBAC7B;YACJ;QACJ;IACJ;IACA,cAAc,SAAS,CAAC,eAAe,GAAG,SAAU,WAAW;QAC3D,IAAI;QACJ,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,mBAAmB;YACvB,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,GAAG,KAAK,GAAG;oBACf,KAAK;wBACD,GAAG,IAAI,CAAC,IAAI,CAAC;4BAAC;4BAAG;;4BAAK;yBAAE;wBACxB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,YAAY,CAAC;gCAC/B,QAAQ;gCACR,QAAQ;oCACJ,aAAa,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;gCAC7B;4BACJ;yBAAG;oBACX,KAAK;wBACD,oBAAoB,GAAG,IAAI;wBAC3B,OAAO;4BAAC,EAAE,QAAQ;4BAAI,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;yBAAmB;oBACzD,KAAK;wBACD,MAAM,GAAG,IAAI;wBACb,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK;oBACzI,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBACjC;YACJ;QACJ;IACJ;IACA,cAAc,SAAS,CAAC,mBAAmB,GAAG,SAAU,YAAY;QAChE,IAAI;QACJ,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,oBAAoB;YACxB,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,GAAG,KAAK,GAAG;oBACf,KAAK;wBACD,GAAG,IAAI,CAAC,IAAI,CAAC;4BAAC;4BAAG;;4BAAK;yBAAE;wBACxB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,YAAY,CAAC;gCAC/B,QAAQ;gCACR,QAAQ;oCACJ,cAAc,aAAa,GAAG,CAAC,SAAU,WAAW;wCAAI,OAAO,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;oCAAc;gCAC7F;4BACJ;yBAAG;oBACX,KAAK;wBACD,qBAAqB,GAAG,IAAI;wBAC5B,OAAO;4BAAC,EAAE,QAAQ;4BAAI,mBAAmB,GAAG,CAAC,SAAU,WAAW;gCAAI,OAAO,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;4BAAc;yBAAG;oBAC9G,KAAK;wBACD,MAAM,GAAG,IAAI;wBACb,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK;oBACzI,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBACjC;YACJ;QACJ;IACJ;IACA,cAAc,SAAS,CAAC,sBAAsB,GAAG,SAAU,WAAW,EAAE,OAAO;QAC3E,IAAI;QACJ,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,QAAQ;YACZ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,GAAG,KAAK,GAAG;oBACf,KAAK;wBACD,GAAG,IAAI,CAAC,IAAI,CAAC;4BAAC;4BAAG;;4BAAK;yBAAE;wBACxB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,YAAY,CAAC;gCAC/B,QAAQ;gCACR,QAAQ;oCACJ,aAAa,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;oCACzB,SAAS;gCACb;4BACJ;yBAAG;oBACX,KAAK;wBACD,SAAS,GAAG,IAAI;wBAChB,OAAO;4BAAC,EAAE,QAAQ;4BAAI;yBAAO;oBACjC,KAAK;wBACD,MAAM,GAAG,IAAI;wBACb,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK;oBACzI,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBACjC;YACJ;QACJ;IACJ;IACA,cAAc,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI,EAAE,OAAO;QACzD,IAAI;QACJ,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAO;QAC3C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,QAAQ;YACZ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,GAAG,KAAK,GAAG;oBACf,KAAK;wBACD,GAAG,IAAI,CAAC,IAAI,CAAC;4BAAC;4BAAG;;4BAAK;yBAAE;wBACxB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,YAAY,CAAC;gCAC/B,QAAQ;gCACR,QAAQ;oCACJ,MAAM;oCACN,SAAS;gCACb;4BACJ;yBAAG;oBACX,KAAK;wBACD,SAAS,GAAG,IAAI;wBAChB,OAAO;4BAAC,EAAE,QAAQ;4BAAI,WAAW,IAAI,CAAC,gIAAA,CAAA,UAAI,CAAC,MAAM,CAAC;yBAAS;oBAC/D,KAAK;wBACD,MAAM,GAAG,IAAI;wBACb,MAAM,IAAI,MAAM,CAAC,CAAC,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK;oBACzI,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBACjC;YACJ;QACJ;IACJ;IACA,OAAO;AACX,EAAE,gLAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/lib/esm/utils.js"], "sourcesContent": ["export function isLegacyTransactionInstance(transaction) {\n    return transaction.version === undefined;\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,4BAA4B,WAAW;IACnD,OAAO,YAAY,OAAO,KAAK;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/lib/esm/version.js"], "sourcesContent": ["export var VERSION = \"1.4.2\";\n"], "names": [], "mappings": ";;;AAAO,IAAI,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/node_modules/%40solflare-wallet/sdk/lib/esm/index.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport { Transaction, VersionedTransaction } from '@solana/web3.js';\nimport EventEmitter from 'eventemitter3';\nimport WebAdapter from './adapters/web';\nimport IframeAdapter from './adapters/iframe';\nimport { isLegacyTransactionInstance } from './utils';\nimport { VERSION } from './version';\nvar Solflare = /** @class */ (function (_super) {\n    __extends(Solflare, _super);\n    function Solflare(config) {\n        var _this = _super.call(this) || this;\n        _this._network = 'mainnet-beta';\n        _this._provider = null;\n        _this._iframeParams = {};\n        _this._adapterInstance = null;\n        _this._element = null;\n        _this._iframe = null;\n        _this._connectHandler = null;\n        _this._flutterHandlerInterval = null;\n        _this._handleEvent = function (event) {\n            var _a, _b, _c, _d;\n            switch (event.type) {\n                case 'connect_native_web': {\n                    _this._collapseIframe();\n                    _this._adapterInstance = new WebAdapter(_this._iframe, _this._network, ((_a = event.data) === null || _a === void 0 ? void 0 : _a.provider) || _this._provider || 'https://solflare.com/provider');\n                    _this._adapterInstance.on('connect', _this._webConnected);\n                    _this._adapterInstance.on('disconnect', _this._webDisconnected);\n                    _this._adapterInstance.connect();\n                    _this._setPreferredAdapter('native_web');\n                    return;\n                }\n                case 'connect': {\n                    _this._collapseIframe();\n                    _this._adapterInstance = new IframeAdapter(_this._iframe, ((_b = event.data) === null || _b === void 0 ? void 0 : _b.publicKey) || '');\n                    _this._adapterInstance.connect();\n                    _this._setPreferredAdapter((_c = event.data) === null || _c === void 0 ? void 0 : _c.adapter);\n                    if (_this._connectHandler) {\n                        _this._connectHandler.resolve();\n                        _this._connectHandler = null;\n                    }\n                    _this.emit('connect', _this.publicKey);\n                    return;\n                }\n                case 'disconnect': {\n                    if (_this._connectHandler) {\n                        _this._connectHandler.reject();\n                        _this._connectHandler = null;\n                    }\n                    _this._disconnected();\n                    _this.emit('disconnect');\n                    return;\n                }\n                case 'accountChanged': {\n                    if ((_d = event.data) === null || _d === void 0 ? void 0 : _d.publicKey) {\n                        _this._adapterInstance = new IframeAdapter(_this._iframe, event.data.publicKey);\n                        _this._adapterInstance.connect();\n                        _this.emit('accountChanged', _this.publicKey);\n                    }\n                    else {\n                        _this.emit('accountChanged', undefined);\n                    }\n                    return;\n                }\n                // legacy event, use resize message type instead\n                case 'collapse': {\n                    _this._collapseIframe();\n                    return;\n                }\n                default: {\n                    return;\n                }\n            }\n        };\n        _this._handleResize = function (data) {\n            if (data.resizeMode === 'full') {\n                if (data.params.mode === 'fullscreen') {\n                    _this._expandIframe();\n                }\n                else if (data.params.mode === 'hide') {\n                    _this._collapseIframe();\n                }\n            }\n            else if (data.resizeMode === 'coordinates') {\n                if (_this._iframe) {\n                    _this._iframe.style.top = isFinite(data.params.top) ? \"\".concat(data.params.top, \"px\") : '';\n                    _this._iframe.style.bottom = isFinite(data.params.bottom) ? \"\".concat(data.params.bottom, \"px\") : '';\n                    _this._iframe.style.left = isFinite(data.params.left) ? \"\".concat(data.params.left, \"px\") : '';\n                    _this._iframe.style.right = isFinite(data.params.right) ? \"\".concat(data.params.right, \"px\") : '';\n                    _this._iframe.style.width = isFinite(data.params.width) ? \"\".concat(data.params.width, \"px\") : data.params.width;\n                    _this._iframe.style.height = isFinite(data.params.height) ? \"\".concat(data.params.height, \"px\") : data.params.height;\n                }\n            }\n        };\n        _this._handleMessage = function (event) {\n            var _a;\n            if (((_a = event.data) === null || _a === void 0 ? void 0 : _a.channel) !== 'solflareIframeToWalletAdapter') {\n                return;\n            }\n            var data = event.data.data || {};\n            if (data.type === 'event') {\n                _this._handleEvent(data.event);\n            }\n            else if (data.type === 'resize') {\n                _this._handleResize(data);\n            }\n            else if (data.type === 'response') {\n                if (_this._adapterInstance) {\n                    _this._adapterInstance.handleMessage(data);\n                }\n            }\n        };\n        _this._removeElement = function () {\n            if (_this._flutterHandlerInterval !== null) {\n                clearInterval(_this._flutterHandlerInterval);\n                _this._flutterHandlerInterval = null;\n            }\n            if (_this._element) {\n                _this._element.remove();\n                _this._element = null;\n            }\n        };\n        _this._removeDanglingElements = function () {\n            var e_1, _a;\n            var elements = document.getElementsByClassName('solflare-wallet-adapter-iframe');\n            try {\n                for (var elements_1 = __values(elements), elements_1_1 = elements_1.next(); !elements_1_1.done; elements_1_1 = elements_1.next()) {\n                    var element = elements_1_1.value;\n                    if (element.parentElement) {\n                        element.remove();\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (elements_1_1 && !elements_1_1.done && (_a = elements_1.return)) _a.call(elements_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        };\n        _this._injectElement = function () {\n            _this._removeElement();\n            _this._removeDanglingElements();\n            var params = __assign(__assign({}, _this._iframeParams), { cluster: _this._network || 'mainnet-beta', origin: window.location.origin || '', title: document.title || '', version: 1, sdkVersion: VERSION || 'unknown' });\n            var preferredAdapter = _this._getPreferredAdapter();\n            if (preferredAdapter) {\n                params.adapter = preferredAdapter;\n            }\n            if (_this._provider) {\n                params.provider = _this._provider;\n            }\n            var queryString = Object.keys(params)\n                .map(function (key) { return \"\".concat(key, \"=\").concat(encodeURIComponent(params[key])); })\n                .join('&');\n            var iframeUrl = \"\".concat(Solflare.IFRAME_URL, \"?\").concat(queryString);\n            _this._element = document.createElement('div');\n            _this._element.className = 'solflare-wallet-adapter-iframe';\n            _this._element.innerHTML = \"\\n      <iframe src='\".concat(iframeUrl, \"' referrerPolicy='strict-origin-when-cross-origin' style='position: fixed; top: 0; bottom: 0; left: 0; right: 0; width: 100%; height: 100%; border: none; border-radius: 0; z-index: 99999; color-scheme: auto;' allowtransparency='true'></iframe>\\n    \");\n            document.body.appendChild(_this._element);\n            _this._iframe = _this._element.querySelector('iframe');\n            // @ts-ignore\n            window.fromFlutter = _this._handleMobileMessage;\n            _this._flutterHandlerInterval = setInterval(function () {\n                // @ts-ignore\n                window.fromFlutter = _this._handleMobileMessage;\n            }, 100);\n            window.addEventListener('message', _this._handleMessage, false);\n        };\n        _this._collapseIframe = function () {\n            if (_this._iframe) {\n                _this._iframe.style.top = '';\n                _this._iframe.style.right = '';\n                _this._iframe.style.height = '2px';\n                _this._iframe.style.width = '2px';\n            }\n        };\n        _this._expandIframe = function () {\n            if (_this._iframe) {\n                _this._iframe.style.top = '0px';\n                _this._iframe.style.bottom = '0px';\n                _this._iframe.style.left = '0px';\n                _this._iframe.style.right = '0px';\n                _this._iframe.style.width = '100%';\n                _this._iframe.style.height = '100%';\n            }\n        };\n        _this._getPreferredAdapter = function () {\n            if (localStorage) {\n                return localStorage.getItem('solflarePreferredWalletAdapter') || null;\n            }\n            return null;\n        };\n        _this._setPreferredAdapter = function (adapter) {\n            if (localStorage && adapter) {\n                localStorage.setItem('solflarePreferredWalletAdapter', adapter);\n            }\n        };\n        _this._clearPreferredAdapter = function () {\n            if (localStorage) {\n                localStorage.removeItem('solflarePreferredWalletAdapter');\n            }\n        };\n        _this._webConnected = function () {\n            if (_this._connectHandler) {\n                _this._connectHandler.resolve();\n                _this._connectHandler = null;\n            }\n            _this.emit('connect', _this.publicKey);\n        };\n        _this._webDisconnected = function () {\n            if (_this._connectHandler) {\n                _this._connectHandler.reject();\n                _this._connectHandler = null;\n            }\n            _this._disconnected();\n            _this.emit('disconnect');\n        };\n        _this._disconnected = function () {\n            window.removeEventListener('message', _this._handleMessage, false);\n            _this._removeElement();\n            _this._clearPreferredAdapter();\n            _this._adapterInstance = null;\n        };\n        _this._handleMobileMessage = function (data) {\n            var _a, _b;\n            (_b = (_a = _this._iframe) === null || _a === void 0 ? void 0 : _a.contentWindow) === null || _b === void 0 ? void 0 : _b.postMessage({\n                channel: 'solflareMobileToIframe',\n                data: data\n            }, '*');\n        };\n        if (config === null || config === void 0 ? void 0 : config.network) {\n            _this._network = config === null || config === void 0 ? void 0 : config.network;\n        }\n        if (config === null || config === void 0 ? void 0 : config.provider) {\n            _this._provider = config === null || config === void 0 ? void 0 : config.provider;\n        }\n        if (config === null || config === void 0 ? void 0 : config.params) {\n            _this._iframeParams = __assign({}, config === null || config === void 0 ? void 0 : config.params);\n        }\n        return _this;\n    }\n    Object.defineProperty(Solflare.prototype, \"publicKey\", {\n        get: function () {\n            var _a;\n            return ((_a = this._adapterInstance) === null || _a === void 0 ? void 0 : _a.publicKey) || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Solflare.prototype, \"isConnected\", {\n        get: function () {\n            var _a;\n            return !!((_a = this._adapterInstance) === null || _a === void 0 ? void 0 : _a.connected);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Solflare.prototype, \"connected\", {\n        get: function () {\n            return this.isConnected;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Solflare.prototype, \"autoApprove\", {\n        get: function () {\n            return false;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Solflare.prototype.connect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (this.connected) {\n                            return [2 /*return*/];\n                        }\n                        this._injectElement();\n                        return [4 /*yield*/, new Promise(function (resolve, reject) {\n                                _this._connectHandler = { resolve: resolve, reject: reject };\n                            })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Solflare.prototype.disconnect = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this._adapterInstance) {\n                            return [2 /*return*/];\n                        }\n                        return [4 /*yield*/, this._adapterInstance.disconnect()];\n                    case 1:\n                        _a.sent();\n                        this._disconnected();\n                        this.emit('disconnect');\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signTransaction = function (transaction) {\n        return __awaiter(this, void 0, void 0, function () {\n            var serializedTransaction, signedTransaction;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        serializedTransaction = isLegacyTransactionInstance(transaction) ?\n                            Uint8Array.from(transaction.serialize({ verifySignatures: false, requireAllSignatures: false })) :\n                            transaction.serialize();\n                        return [4 /*yield*/, this._adapterInstance.signTransaction(serializedTransaction)];\n                    case 1:\n                        signedTransaction = _a.sent();\n                        return [2 /*return*/, isLegacyTransactionInstance(transaction) ? Transaction.from(signedTransaction) : VersionedTransaction.deserialize(signedTransaction)];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signAllTransactions = function (transactions) {\n        return __awaiter(this, void 0, void 0, function () {\n            var serializedTransactions, signedTransactions;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        serializedTransactions = transactions.map(function (transaction) {\n                            return isLegacyTransactionInstance(transaction) ?\n                                Uint8Array.from(transaction.serialize({ verifySignatures: false, requireAllSignatures: false })) :\n                                transaction.serialize();\n                        });\n                        return [4 /*yield*/, this._adapterInstance.signAllTransactions(serializedTransactions)];\n                    case 1:\n                        signedTransactions = _a.sent();\n                        if (signedTransactions.length !== transactions.length) {\n                            throw new Error('Failed to sign all transactions');\n                        }\n                        return [2 /*return*/, signedTransactions.map(function (signedTransaction, index) {\n                                return isLegacyTransactionInstance(transactions[index]) ? Transaction.from(signedTransaction) : VersionedTransaction.deserialize(signedTransaction);\n                            })];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signAndSendTransaction = function (transaction, options) {\n        return __awaiter(this, void 0, void 0, function () {\n            var serializedTransaction;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        serializedTransaction = isLegacyTransactionInstance(transaction) ? transaction.serialize({ verifySignatures: false, requireAllSignatures: false }) : transaction.serialize();\n                        return [4 /*yield*/, this._adapterInstance.signAndSendTransaction(serializedTransaction, options)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Solflare.prototype.signMessage = function (data, display) {\n        if (display === void 0) { display = 'utf8'; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!this.connected) {\n                            throw new Error('Wallet not connected');\n                        }\n                        return [4 /*yield*/, this._adapterInstance.signMessage(data, display)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Solflare.prototype.sign = function (data, display) {\n        if (display === void 0) { display = 'utf8'; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.signMessage(data, display)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    Solflare.prototype.detectWallet = function (timeout) {\n        var _a;\n        if (timeout === void 0) { timeout = 10; }\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_b) {\n                if (window.SolflareApp || ((_a = window.solflare) === null || _a === void 0 ? void 0 : _a.isSolflare)) {\n                    return [2 /*return*/, true];\n                }\n                return [2 /*return*/, new Promise(function (resolve) {\n                        var pollInterval, pollTimeout;\n                        pollInterval = setInterval(function () {\n                            var _a;\n                            if (window.SolflareApp || ((_a = window.solflare) === null || _a === void 0 ? void 0 : _a.isSolflare)) {\n                                clearInterval(pollInterval);\n                                clearTimeout(pollTimeout);\n                                resolve(true);\n                            }\n                        }, 500);\n                        pollTimeout = setTimeout(function () {\n                            clearInterval(pollInterval);\n                            resolve(false);\n                        }, timeout * 1000);\n                    })];\n            });\n        });\n    };\n    Solflare.IFRAME_URL = 'https://connect.solflare.com/';\n    return Solflare;\n}(EventEmitter));\nexport default Solflare;\n"], "names": [], "mappings": ";;;AAyEA;AACA;AAAA;AACA;AACA;AACA;AACA;AA9EA,IAAI,YAAY,AAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAK,AAAC;IACzC,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;QAC9B,gBAAgB,OAAO,cAAc,IAChC,CAAA;YAAE,WAAW,EAAE;QAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,EAAE,SAAS,GAAG;QAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;YAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAAE;QACpG,OAAO,cAAc,GAAG;IAC5B;IACA,OAAO,SAAU,CAAC,EAAE,CAAC;QACjB,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;QAC7D,cAAc,GAAG;QACjB,SAAS;YAAO,IAAI,CAAC,WAAW,GAAG;QAAG;QACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;IACvF;AACJ;AACA,IAAI,WAAW,AAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAK;IACtC,WAAW,OAAO,MAAM,IAAI,SAAS,CAAC;QAClC,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IACzD,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACnB;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAChC;AACA,IAAI,YAAY,AAAC,IAAI,IAAI,IAAI,CAAC,SAAS,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACnF,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AACA,IAAI,cAAc,AAAC,IAAI,IAAI,IAAI,CAAC,WAAW,IAAK,SAAU,OAAO,EAAE,IAAI;IACnE,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG;IAC/G,OAAO,IAAI;QAAE,MAAM,KAAK;QAAI,SAAS,KAAK;QAAI,UAAU,KAAK;IAAG,GAAG,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IACvJ,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACJ;AACA,IAAI,WAAW,AAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAK,SAAS,CAAC;IAChD,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACxD;;;;;;;AAOA,IAAI,WAA0B,SAAU,MAAM;IAC1C,UAAU,UAAU;IACpB,SAAS,SAAS,MAAM;QACpB,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,QAAQ,GAAG;QACjB,MAAM,SAAS,GAAG;QAClB,MAAM,aAAa,GAAG,CAAC;QACvB,MAAM,gBAAgB,GAAG;QACzB,MAAM,QAAQ,GAAG;QACjB,MAAM,OAAO,GAAG;QAChB,MAAM,eAAe,GAAG;QACxB,MAAM,uBAAuB,GAAG;QAChC,MAAM,YAAY,GAAG,SAAU,KAAK;YAChC,IAAI,IAAI,IAAI,IAAI;YAChB,OAAQ,MAAM,IAAI;gBACd,KAAK;oBAAsB;wBACvB,MAAM,eAAe;wBACrB,MAAM,gBAAgB,GAAG,IAAI,+KAAA,CAAA,UAAU,CAAC,MAAM,OAAO,EAAE,MAAM,QAAQ,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,KAAK,MAAM,SAAS,IAAI;wBAClK,MAAM,gBAAgB,CAAC,EAAE,CAAC,WAAW,MAAM,aAAa;wBACxD,MAAM,gBAAgB,CAAC,EAAE,CAAC,cAAc,MAAM,gBAAgB;wBAC9D,MAAM,gBAAgB,CAAC,OAAO;wBAC9B,MAAM,oBAAoB,CAAC;wBAC3B;oBACJ;gBACA,KAAK;oBAAW;wBACZ,MAAM,eAAe;wBACrB,MAAM,gBAAgB,GAAG,IAAI,kLAAA,CAAA,UAAa,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,KAAK;wBACnI,MAAM,gBAAgB,CAAC,OAAO;wBAC9B,MAAM,oBAAoB,CAAC,CAAC,KAAK,MAAM,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO;wBAC5F,IAAI,MAAM,eAAe,EAAE;4BACvB,MAAM,eAAe,CAAC,OAAO;4BAC7B,MAAM,eAAe,GAAG;wBAC5B;wBACA,MAAM,IAAI,CAAC,WAAW,MAAM,SAAS;wBACrC;oBACJ;gBACA,KAAK;oBAAc;wBACf,IAAI,MAAM,eAAe,EAAE;4BACvB,MAAM,eAAe,CAAC,MAAM;4BAC5B,MAAM,eAAe,GAAG;wBAC5B;wBACA,MAAM,aAAa;wBACnB,MAAM,IAAI,CAAC;wBACX;oBACJ;gBACA,KAAK;oBAAkB;wBACnB,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,EAAE;4BACrE,MAAM,gBAAgB,GAAG,IAAI,kLAAA,CAAA,UAAa,CAAC,MAAM,OAAO,EAAE,MAAM,IAAI,CAAC,SAAS;4BAC9E,MAAM,gBAAgB,CAAC,OAAO;4BAC9B,MAAM,IAAI,CAAC,kBAAkB,MAAM,SAAS;wBAChD,OACK;4BACD,MAAM,IAAI,CAAC,kBAAkB;wBACjC;wBACA;oBACJ;gBACA,gDAAgD;gBAChD,KAAK;oBAAY;wBACb,MAAM,eAAe;wBACrB;oBACJ;gBACA;oBAAS;wBACL;oBACJ;YACJ;QACJ;QACA,MAAM,aAAa,GAAG,SAAU,IAAI;YAChC,IAAI,KAAK,UAAU,KAAK,QAAQ;gBAC5B,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,cAAc;oBACnC,MAAM,aAAa;gBACvB,OACK,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,QAAQ;oBAClC,MAAM,eAAe;gBACzB;YACJ,OACK,IAAI,KAAK,UAAU,KAAK,eAAe;gBACxC,IAAI,MAAM,OAAO,EAAE;oBACf,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,SAAS,KAAK,MAAM,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ;oBACzF,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,KAAK,MAAM,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ;oBAClG,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,KAAK,MAAM,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ;oBAC5F,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,KAAK,MAAM,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,EAAE,QAAQ;oBAC/F,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,KAAK,MAAM,CAAC,KAAK,IAAI,GAAG,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,EAAE,QAAQ,KAAK,MAAM,CAAC,KAAK;oBAChH,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,KAAK,MAAM,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,KAAK,MAAM,CAAC,MAAM;gBACxH;YACJ;QACJ;QACA,MAAM,cAAc,GAAG,SAAU,KAAK;YAClC,IAAI;YACJ,IAAI,CAAC,CAAC,KAAK,MAAM,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,iCAAiC;gBACzG;YACJ;YACA,IAAI,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC;YAC/B,IAAI,KAAK,IAAI,KAAK,SAAS;gBACvB,MAAM,YAAY,CAAC,KAAK,KAAK;YACjC,OACK,IAAI,KAAK,IAAI,KAAK,UAAU;gBAC7B,MAAM,aAAa,CAAC;YACxB,OACK,IAAI,KAAK,IAAI,KAAK,YAAY;gBAC/B,IAAI,MAAM,gBAAgB,EAAE;oBACxB,MAAM,gBAAgB,CAAC,aAAa,CAAC;gBACzC;YACJ;QACJ;QACA,MAAM,cAAc,GAAG;YACnB,IAAI,MAAM,uBAAuB,KAAK,MAAM;gBACxC,cAAc,MAAM,uBAAuB;gBAC3C,MAAM,uBAAuB,GAAG;YACpC;YACA,IAAI,MAAM,QAAQ,EAAE;gBAChB,MAAM,QAAQ,CAAC,MAAM;gBACrB,MAAM,QAAQ,GAAG;YACrB;QACJ;QACA,MAAM,uBAAuB,GAAG;YAC5B,IAAI,KAAK;YACT,IAAI,WAAW,SAAS,sBAAsB,CAAC;YAC/C,IAAI;gBACA,IAAK,IAAI,aAAa,SAAS,WAAW,eAAe,WAAW,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,eAAe,WAAW,IAAI,GAAI;oBAC9H,IAAI,UAAU,aAAa,KAAK;oBAChC,IAAI,QAAQ,aAAa,EAAE;wBACvB,QAAQ,MAAM;oBAClB;gBACJ;YACJ,EACA,OAAO,OAAO;gBAAE,MAAM;oBAAE,OAAO;gBAAM;YAAG,SAChC;gBACJ,IAAI;oBACA,IAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,WAAW,MAAM,GAAG,GAAG,IAAI,CAAC;gBAChF,SACQ;oBAAE,IAAI,KAAK,MAAM,IAAI,KAAK;gBAAE;YACxC;QACJ;QACA,MAAM,cAAc,GAAG;YACnB,MAAM,cAAc;YACpB,MAAM,uBAAuB;YAC7B,IAAI,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,aAAa,GAAG;gBAAE,SAAS,MAAM,QAAQ,IAAI;gBAAgB,QAAQ,OAAO,QAAQ,CAAC,MAAM,IAAI;gBAAI,OAAO,SAAS,KAAK,IAAI;gBAAI,SAAS;gBAAG,YAAY,uKAAA,CAAA,UAAO,IAAI;YAAU;YACtN,IAAI,mBAAmB,MAAM,oBAAoB;YACjD,IAAI,kBAAkB;gBAClB,OAAO,OAAO,GAAG;YACrB;YACA,IAAI,MAAM,SAAS,EAAE;gBACjB,OAAO,QAAQ,GAAG,MAAM,SAAS;YACrC;YACA,IAAI,cAAc,OAAO,IAAI,CAAC,QACzB,GAAG,CAAC,SAAU,GAAG;gBAAI,OAAO,GAAG,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,mBAAmB,MAAM,CAAC,IAAI;YAAI,GACzF,IAAI,CAAC;YACV,IAAI,YAAY,GAAG,MAAM,CAAC,SAAS,UAAU,EAAE,KAAK,MAAM,CAAC;YAC3D,MAAM,QAAQ,GAAG,SAAS,aAAa,CAAC;YACxC,MAAM,QAAQ,CAAC,SAAS,GAAG;YAC3B,MAAM,QAAQ,CAAC,SAAS,GAAG,wBAAwB,MAAM,CAAC,WAAW;YACrE,SAAS,IAAI,CAAC,WAAW,CAAC,MAAM,QAAQ;YACxC,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,aAAa,CAAC;YAC7C,aAAa;YACb,OAAO,WAAW,GAAG,MAAM,oBAAoB;YAC/C,MAAM,uBAAuB,GAAG,YAAY;gBACxC,aAAa;gBACb,OAAO,WAAW,GAAG,MAAM,oBAAoB;YACnD,GAAG;YACH,OAAO,gBAAgB,CAAC,WAAW,MAAM,cAAc,EAAE;QAC7D;QACA,MAAM,eAAe,GAAG;YACpB,IAAI,MAAM,OAAO,EAAE;gBACf,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG;gBAC1B,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG;gBAC5B,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC7B,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG;YAChC;QACJ;QACA,MAAM,aAAa,GAAG;YAClB,IAAI,MAAM,OAAO,EAAE;gBACf,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG;gBAC1B,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC7B,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG;gBAC3B,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG;gBAC5B,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG;gBAC5B,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACjC;QACJ;QACA,MAAM,oBAAoB,GAAG;YACzB,IAAI,cAAc;gBACd,OAAO,aAAa,OAAO,CAAC,qCAAqC;YACrE;YACA,OAAO;QACX;QACA,MAAM,oBAAoB,GAAG,SAAU,OAAO;YAC1C,IAAI,gBAAgB,SAAS;gBACzB,aAAa,OAAO,CAAC,kCAAkC;YAC3D;QACJ;QACA,MAAM,sBAAsB,GAAG;YAC3B,IAAI,cAAc;gBACd,aAAa,UAAU,CAAC;YAC5B;QACJ;QACA,MAAM,aAAa,GAAG;YAClB,IAAI,MAAM,eAAe,EAAE;gBACvB,MAAM,eAAe,CAAC,OAAO;gBAC7B,MAAM,eAAe,GAAG;YAC5B;YACA,MAAM,IAAI,CAAC,WAAW,MAAM,SAAS;QACzC;QACA,MAAM,gBAAgB,GAAG;YACrB,IAAI,MAAM,eAAe,EAAE;gBACvB,MAAM,eAAe,CAAC,MAAM;gBAC5B,MAAM,eAAe,GAAG;YAC5B;YACA,MAAM,aAAa;YACnB,MAAM,IAAI,CAAC;QACf;QACA,MAAM,aAAa,GAAG;YAClB,OAAO,mBAAmB,CAAC,WAAW,MAAM,cAAc,EAAE;YAC5D,MAAM,cAAc;YACpB,MAAM,sBAAsB;YAC5B,MAAM,gBAAgB,GAAG;QAC7B;QACA,MAAM,oBAAoB,GAAG,SAAU,IAAI;YACvC,IAAI,IAAI;YACR,CAAC,KAAK,CAAC,KAAK,MAAM,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,CAAC;gBAClI,SAAS;gBACT,MAAM;YACV,GAAG;QACP;QACA,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,OAAO,EAAE;YAChE,MAAM,QAAQ,GAAG,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,OAAO;QACnF;QACA,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,EAAE;YACjE,MAAM,SAAS,GAAG,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ;QACrF;QACA,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM,EAAE;YAC/D,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM;QACpG;QACA,OAAO;IACX;IACA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,aAAa;QACnD,KAAK;YACD,IAAI;YACJ,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,gBAAgB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,KAAK;QAC/F;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,eAAe;QACrD,KAAK;YACD,IAAI;YACJ,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,gBAAgB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS;QAC5F;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,aAAa;QACnD,KAAK;YACD,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,YAAY;QACZ,cAAc;IAClB;IACA,OAAO,cAAc,CAAC,SAAS,SAAS,EAAE,eAAe;QACrD,KAAK;YACD,OAAO;QACX;QACA,YAAY;QACZ,cAAc;IAClB;IACA,SAAS,SAAS,CAAC,OAAO,GAAG;QACzB,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,QAAQ,IAAI;YAChB,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,IAAI,CAAC,SAAS,EAAE;4BAChB,OAAO;gCAAC,EAAE,QAAQ;6BAAG;wBACzB;wBACA,IAAI,CAAC,cAAc;wBACnB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gCAClD,MAAM,eAAe,GAAG;oCAAE,SAAS;oCAAS,QAAQ;gCAAO;4BAC/D;yBAAG;oBACX,KAAK;wBACD,GAAG,IAAI;wBACP,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBAC7B;YACJ;QACJ;IACJ;IACA,SAAS,SAAS,CAAC,UAAU,GAAG;QAC5B,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;4BACxB,OAAO;gCAAC,EAAE,QAAQ;6BAAG;wBACzB;wBACA,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU;yBAAG;oBAC5D,KAAK;wBACD,GAAG,IAAI;wBACP,IAAI,CAAC,aAAa;wBAClB,IAAI,CAAC,IAAI,CAAC;wBACV,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBAC7B;YACJ;QACJ;IACJ;IACA,SAAS,SAAS,CAAC,eAAe,GAAG,SAAU,WAAW;QACtD,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,uBAAuB;YAC3B,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,wBAAwB,CAAA,GAAA,qKAAA,CAAA,8BAA2B,AAAD,EAAE,eAChD,WAAW,IAAI,CAAC,YAAY,SAAS,CAAC;4BAAE,kBAAkB;4BAAO,sBAAsB;wBAAM,MAC7F,YAAY,SAAS;wBACzB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;yBAAuB;oBACtF,KAAK;wBACD,oBAAoB,GAAG,IAAI;wBAC3B,OAAO;4BAAC,EAAE,QAAQ;4BAAI,CAAA,GAAA,qKAAA,CAAA,8BAA2B,AAAD,EAAE,eAAe,2KAAA,CAAA,cAAW,CAAC,IAAI,CAAC,qBAAqB,2KAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC;yBAAmB;gBACnK;YACJ;QACJ;IACJ;IACA,SAAS,SAAS,CAAC,mBAAmB,GAAG,SAAU,YAAY;QAC3D,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI,wBAAwB;YAC5B,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,yBAAyB,aAAa,GAAG,CAAC,SAAU,WAAW;4BAC3D,OAAO,CAAA,GAAA,qKAAA,CAAA,8BAA2B,AAAD,EAAE,eAC/B,WAAW,IAAI,CAAC,YAAY,SAAS,CAAC;gCAAE,kBAAkB;gCAAO,sBAAsB;4BAAM,MAC7F,YAAY,SAAS;wBAC7B;wBACA,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;yBAAwB;oBAC3F,KAAK;wBACD,qBAAqB,GAAG,IAAI;wBAC5B,IAAI,mBAAmB,MAAM,KAAK,aAAa,MAAM,EAAE;4BACnD,MAAM,IAAI,MAAM;wBACpB;wBACA,OAAO;4BAAC,EAAE,QAAQ;4BAAI,mBAAmB,GAAG,CAAC,SAAU,iBAAiB,EAAE,KAAK;gCACvE,OAAO,CAAA,GAAA,qKAAA,CAAA,8BAA2B,AAAD,EAAE,YAAY,CAAC,MAAM,IAAI,2KAAA,CAAA,cAAW,CAAC,IAAI,CAAC,qBAAqB,2KAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC;4BACrI;yBAAG;gBACf;YACJ;QACJ;IACJ;IACA,SAAS,SAAS,CAAC,sBAAsB,GAAG,SAAU,WAAW,EAAE,OAAO;QACtE,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,wBAAwB,CAAA,GAAA,qKAAA,CAAA,8BAA2B,AAAD,EAAE,eAAe,YAAY,SAAS,CAAC;4BAAE,kBAAkB;4BAAO,sBAAsB;wBAAM,KAAK,YAAY,SAAS;wBAC1K,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,uBAAuB;yBAAS;oBACtG,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA,SAAS,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI,EAAE,OAAO;QACpD,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAQ;QAC5C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;4BACjB,MAAM,IAAI,MAAM;wBACpB;wBACA,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,MAAM;yBAAS;oBAC1E,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,OAAO;QAC7C,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAQ;QAC5C,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBAAG,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,CAAC,WAAW,CAAC,MAAM;yBAAS;oBAC7D,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;gBAC5C;YACJ;QACJ;IACJ;IACA,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,OAAO;QAC/C,IAAI;QACJ,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU;QAAI;QACxC,OAAO,UAAU,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;YACnC,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,IAAI,OAAO,WAAW,IAAI,CAAC,CAAC,KAAK,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG;oBACnG,OAAO;wBAAC,EAAE,QAAQ;wBAAI;qBAAK;gBAC/B;gBACA,OAAO;oBAAC,EAAE,QAAQ;oBAAI,IAAI,QAAQ,SAAU,OAAO;wBAC3C,IAAI,cAAc;wBAClB,eAAe,YAAY;4BACvB,IAAI;4BACJ,IAAI,OAAO,WAAW,IAAI,CAAC,CAAC,KAAK,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG;gCACnG,cAAc;gCACd,aAAa;gCACb,QAAQ;4BACZ;wBACJ,GAAG;wBACH,cAAc,WAAW;4BACrB,cAAc;4BACd,QAAQ;wBACZ,GAAG,UAAU;oBACjB;iBAAG;YACX;QACJ;IACJ;IACA,SAAS,UAAU,GAAG;IACtB,OAAO;AACX,EAAE,0JAAA,CAAA,UAAY;uCACC", "ignoreList": [0], "debugId": null}}]}