@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for wallet adapter */
.wallet-adapter-button {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

.wallet-adapter-button-trigger {
  @apply bg-blue-600 hover:bg-blue-700;
}

.wallet-adapter-modal-wrapper {
  @apply backdrop-blur-sm;
}

.wallet-adapter-modal {
  @apply bg-white rounded-lg shadow-xl border border-gray-200;
}

.wallet-adapter-modal-title {
  @apply text-xl font-semibold text-gray-900;
}

.wallet-adapter-modal-list {
  @apply space-y-2;
}

.wallet-adapter-modal-list-item {
  @apply p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full hover:bg-gray-400;
}

/* Custom animations for Tailwind CSS v3 */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  will-change: opacity;
}

/* Custom focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2;
}

/* Solana-inspired gradients */
.gradient-purple-pink {
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
}

.gradient-blue-cyan {
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
}

.gradient-green-emerald {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Custom backdrop blur for better browser support */
.backdrop-blur-custom {
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
}

/* Enhanced button hover effects */
.btn-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-success {
  background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
}

.gradient-bg-error {
  background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
}

/* Enhanced animations for homepage */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-blob {
  animation: blob 7s infinite;
}

.delay-1000 {
  animation-delay: 1s;
}

.delay-2000 {
  animation-delay: 2s;
}

.delay-3000 {
  animation-delay: 3s;
}
