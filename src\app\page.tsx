'use client';

import { useState } from 'react';
import { Header } from "@/components/Header";
import { TweetFeed } from "@/components/tweet/TweetFeed";
import { TokenLaunchModal } from "@/components/tweet/TokenLaunchModal";
import { EnrichedTweet, TweetToTokenParams, TokenLaunchResult } from "@/types/twitter";

export default function Home() {
  const [selectedTweet, setSelectedTweet] = useState<EnrichedTweet | null>(null);
  const [isLaunchModalOpen, setIsLaunchModalOpen] = useState(false);

  const handleTweetLaunch = async (tweet: EnrichedTweet) => {
    setSelectedTweet(tweet);
    setIsLaunchModalOpen(true);
  };

  const handleTokenLaunch = async (params: TweetToTokenParams): Promise<TokenLaunchResult> => {
    // Mock implementation - replace with actual Raydium token creation logic
    console.log('Launching token with params:', params);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Mock success response
    return {
      success: true,
      token_address: 'So11111111111111111111111111111111111111112',
      transaction_signature: 'mock_signature_123',
      pool_address: 'mock_pool_address_456',
      solscan_url: 'https://solscan.io/token/So11111111111111111111111111111111111111112',
      estimated_cost: 0.21
    };
  };

  const handleTweetClick = (tweet: EnrichedTweet) => {
    // Optional: Handle tweet detail view
    console.log('Tweet clicked:', tweet);
  };

  return (
    <>
      <Header />

      {/* Main Content - Tweet Feed Focus */}
      <main className="relative overflow-hidden bg-black min-h-screen">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          {/* Grid pattern overlay */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

          {/* Gradient orbs */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-orange-500/5 to-red-500/5 rounded-full blur-3xl"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 container mx-auto px-4 py-8">
          {/* Compact Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center bg-gray-900/50 border border-orange-500/30 rounded-full px-6 py-3 mb-4">
              <span className="w-2 h-2 bg-red-400 rounded-full animate-pulse mr-3"></span>
              <span className="text-gray-300 text-sm font-medium">🔥 Live Viral Feed • AI Token Generation</span>
            </div>

            <h1 className="text-3xl lg:text-5xl font-bold text-white mb-4 leading-tight">
              <span className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 bg-clip-text text-transparent">
                Viral Tweets
              </span>
              {" "}→ Instant Tokens
            </h1>

            <p className="text-base lg:text-lg text-gray-400 mb-6 max-w-2xl mx-auto">
              Click any tweet to launch a meme coin with <span className="text-orange-400 font-semibold">AI-generated artwork</span> on Raydium
            </p>
          </div>

          {/* Live Tweet Feed - Full Focus */}
          <div className="max-w-5xl mx-auto">
            <TweetFeed
              onTweetLaunch={handleTweetLaunch}
              onTweetClick={handleTweetClick}
            />
          </div>

          {/* Minimal Footer */}
          <div className="mt-12 pt-6 border-t border-gray-800/30 text-center">
            <div className="flex items-center justify-center space-x-6 text-gray-500 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"></div>
                <span>Solana</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                <span>Raydium</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                <span>Twitter API</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full"></div>
                <span>GPT-4 Vision</span>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Token Launch Modal */}
      <TokenLaunchModal
        isOpen={isLaunchModalOpen}
        onClose={() => setIsLaunchModalOpen(false)}
        tweet={selectedTweet}
        onLaunch={handleTokenLaunch}
      />
    </>
  );
}
