'use client';

import { Head<PERSON> } from "@/components/Header";
import { TokenLaunchModal } from "@/components/TokenLaunchModal";

export default function Home() {
  return (
    <>
      <Header />

      {/* Main Content - Token Creation Focus */}
      <main className="relative overflow-hidden bg-black min-h-screen">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          {/* Grid pattern overlay */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

          {/* Floating orbs */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-r from-cyan-500/20 to-green-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse delay-500"></div>

          {/* Additional floating elements */}
          <div className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-full blur-2xl animate-pulse delay-2000"></div>
          <div className="absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-full blur-2xl animate-pulse delay-3000"></div>
        </div>

        <div className="relative container mx-auto px-4 py-8">
          {/* Simplified header section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center bg-gray-900/50 border border-purple-500/30 rounded-full px-6 py-3 mb-6">
              <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-3"></span>
              <span className="text-gray-300 text-sm font-medium">Live on Solana Mainnet</span>
            </div>

            <h1 className="text-4xl lg:text-6xl font-black text-white mb-4 leading-tight tracking-tight">
              Launch Your
              <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                Solana Token
              </span>
            </h1>

            <p className="text-lg lg:text-xl text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed">
              Create, deploy, and add liquidity to your Solana token in minutes with our
              <span className="text-purple-400 font-semibold"> professional-grade platform</span>.
            </p>
          </div>

          {/* Quick feature highlights */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-3xl mx-auto">
            <div className="text-center">
              <div className="text-lg font-bold text-white">~0.15 SOL</div>
              <div className="text-gray-400 text-sm">Total Cost</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-white">&lt; 2 min</div>
              <div className="text-gray-400 text-sm">Deploy Time</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-white">3 Steps</div>
              <div className="text-gray-400 text-sm">Simple Process</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-white">DEX Ready</div>
              <div className="text-gray-400 text-sm">Instant Trading</div>
            </div>
          </div>

          {/* Main Token Creation Form */}
          <div className="max-w-5xl mx-auto">
            <TokenLaunchModal
              isOpen={true}
              onClose={() => {}}
            />
          </div>

          {/* Trust indicators */}
          <div className="mt-16 pt-8 border-t border-gray-800/30 text-center">
            <p className="text-gray-500 text-sm mb-6">Built with industry-leading protocols</p>
            <div className="flex flex-wrap justify-center items-center gap-6">
              <div className="flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2">
                <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium">Solana</span>
              </div>
              <div className="flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2">
                <div className="w-6 h-6 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg"></div>
                <span className="text-gray-300 font-medium">Raydium</span>
              </div>
              <div className="flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2">
                <div className="w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium">Metaplex</span>
              </div>
              <div className="flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2">
                <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium">SPL Token</span>
              </div>
            </div>
          </div>
        </div>
      </main>

    </>
  );
}
