'use client';

import { useState } from 'react';
import { Header } from "@/components/Header";
import { TokenLaunchModal } from "@/components/TokenLaunchModal";
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';

export default function Home() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { connected } = useWallet();

  return (
    <>
      <Header />

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-black min-h-screen flex items-center">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          {/* Grid pattern overlay */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

          {/* Floating orbs */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-r from-cyan-500/20 to-green-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse delay-500"></div>

          {/* Additional floating elements */}
          <div className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-full blur-2xl animate-pulse delay-2000"></div>
          <div className="absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-full blur-2xl animate-pulse delay-3000"></div>
        </div>

        <div className="relative container mx-auto px-4 text-center">
          {/* Main heading with enhanced styling */}
          <div className="mb-8">
            <div className="inline-flex items-center bg-gray-900/50 border border-purple-500/30 rounded-full px-6 py-3 mb-8">
              <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-3"></span>
              <span className="text-gray-300 text-sm font-medium">Live on Solana Mainnet</span>
            </div>

            <h1 className="text-6xl lg:text-8xl font-black text-white mb-6 leading-tight tracking-tight">
              Launch Your
              <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent animate-pulse">
                Solana Token
              </span>
            </h1>

            <p className="text-xl lg:text-2xl text-gray-400 mb-12 max-w-4xl mx-auto leading-relaxed">
              Create, deploy, and add liquidity to your Solana token in minutes with our
              <span className="text-purple-400 font-semibold"> professional-grade platform</span>.
              Built with Raydium CPMM for seamless DEX integration.
            </p>
          </div>

          {/* Enhanced feature badges */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-16 max-w-4xl mx-auto">
            <div className="group bg-gray-900/60 backdrop-blur-sm border border-purple-500/30 rounded-2xl p-6 hover:border-purple-400/50 hover:bg-gray-800/60 transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-white font-semibold mb-2">Lightning Fast</h3>
              <p className="text-gray-400 text-sm">Deploy in seconds on Solana</p>
            </div>

            <div className="group bg-gray-900/60 backdrop-blur-sm border border-cyan-500/30 rounded-2xl p-6 hover:border-cyan-400/50 hover:bg-gray-800/60 transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-white font-semibold mb-2">Secure</h3>
              <p className="text-gray-400 text-sm">Audited smart contracts</p>
            </div>

            <div className="group bg-gray-900/60 backdrop-blur-sm border border-green-500/30 rounded-2xl p-6 hover:border-green-400/50 hover:bg-gray-800/60 transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-white font-semibold mb-2">Low Cost</h3>
              <p className="text-gray-400 text-sm">~0.15 SOL total fees</p>
            </div>

            <div className="group bg-gray-900/60 backdrop-blur-sm border border-pink-500/30 rounded-2xl p-6 hover:border-pink-400/50 hover:bg-gray-800/60 transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>
              <h3 className="text-white font-semibold mb-2">DEX Ready</h3>
              <p className="text-gray-400 text-sm">Instant Raydium listing</p>
            </div>
          </div>

          {/* Enhanced CTA Section */}
          <div className="space-y-8">
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              {connected ? (
                <button
                  type="button"
                  onClick={() => setIsModalOpen(true)}
                  className="group relative bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold px-12 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-purple-500/25 border border-purple-400/30 hover:border-purple-300/50 text-lg"
                >
                  <span className="relative z-10 flex items-center gap-3">
                    Create Your Token
                    <svg className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                </button>
              ) : (
                <div className="flex flex-col items-center gap-4">
                  <WalletMultiButton className="!bg-gradient-to-r !from-purple-500 !to-pink-500 hover:!from-purple-600 hover:!to-pink-600 !text-white !font-bold !px-12 !py-5 !rounded-2xl !transition-all !duration-300 !transform hover:!scale-105 !shadow-2xl hover:!shadow-purple-500/25 !border !border-purple-400/30 hover:!border-purple-300/50 !text-lg" />
                  <div className="flex items-center gap-2 text-gray-400 text-sm">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    Connect wallet to get started
                  </div>
                </div>
              )}

              <button type="button" className="group bg-gray-900/60 backdrop-blur-sm border border-gray-600/30 hover:border-gray-500/50 hover:bg-gray-800/60 text-white font-semibold px-8 py-5 rounded-2xl transition-all duration-300 hover:scale-105">
                <span className="flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Learn More
                </span>
              </button>
            </div>

            {/* Quick stats */}
            <div className="flex flex-wrap justify-center gap-8 text-center">
              <div className="group">
                <div className="text-2xl font-bold text-white group-hover:text-purple-400 transition-colors duration-300">~0.15 SOL</div>
                <div className="text-gray-400 text-sm">Total Cost</div>
              </div>
              <div className="group">
                <div className="text-2xl font-bold text-white group-hover:text-cyan-400 transition-colors duration-300">&lt; 2 min</div>
                <div className="text-gray-400 text-sm">Deploy Time</div>
              </div>
              <div className="group">
                <div className="text-2xl font-bold text-white group-hover:text-green-400 transition-colors duration-300">3 Steps</div>
                <div className="text-gray-400 text-sm">Simple Process</div>
              </div>
            </div>
          </div>

          {/* Enhanced trust indicators */}
          <div className="mt-20 pt-8 border-t border-gray-800/30">
            <p className="text-gray-500 text-sm mb-6">Built with industry-leading protocols</p>
            <div className="flex flex-wrap justify-center items-center gap-8">
              <div className="group flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2 hover:border-gray-600/50 transition-all duration-300">
                <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium group-hover:text-white transition-colors duration-300">Solana</span>
              </div>
              <div className="group flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2 hover:border-gray-600/50 transition-all duration-300">
                <div className="w-6 h-6 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg"></div>
                <span className="text-gray-300 font-medium group-hover:text-white transition-colors duration-300">Raydium</span>
              </div>
              <div className="group flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2 hover:border-gray-600/50 transition-all duration-300">
                <div className="w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium group-hover:text-white transition-colors duration-300">Metaplex</span>
              </div>
              <div className="group flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2 hover:border-gray-600/50 transition-all duration-300">
                <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium group-hover:text-white transition-colors duration-300">SPL Token</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Token Creation Section - appears below hero when modal is opened */}
      {isModalOpen && (
        <section className="bg-gray-950 border-t border-gray-800/50">
          <div className="container mx-auto px-4 py-16">
            <TokenLaunchModal
              isOpen={isModalOpen}
              onClose={() => setIsModalOpen(false)}
            />
          </div>
        </section>
      )}

      {/* Stats Section */}
      <section className="bg-gray-950 py-16 lg:py-20 border-t border-gray-800/50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                Trusted by Builders
              </h2>
              <p className="text-lg text-gray-400 max-w-2xl mx-auto">
                Join thousands of developers who have launched their tokens on Solana
              </p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
              <div className="text-center group">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2 group-hover:from-purple-300 group-hover:to-pink-300 transition-all duration-300">10,000+</div>
                <div className="text-gray-400 font-medium group-hover:text-gray-300 transition-colors duration-300">Tokens Created</div>
              </div>
              <div className="text-center group">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-cyan-400 to-green-400 bg-clip-text text-transparent mb-2 group-hover:from-cyan-300 group-hover:to-green-300 transition-all duration-300">$50M+</div>
                <div className="text-gray-400 font-medium group-hover:text-gray-300 transition-colors duration-300">Total Volume</div>
              </div>
              <div className="text-center group">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent mb-2 group-hover:from-green-300 group-hover:to-emerald-300 transition-all duration-300">99.9%</div>
                <div className="text-gray-400 font-medium group-hover:text-gray-300 transition-colors duration-300">Uptime</div>
              </div>
              <div className="text-center group">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-2 group-hover:from-orange-300 group-hover:to-red-300 transition-all duration-300">&lt;1s</div>
                <div className="text-gray-400 font-medium group-hover:text-gray-300 transition-colors duration-300">Deploy Time</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-gray-900 py-20 lg:py-28">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
                Everything You Need to
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  {" "}Launch Successfully
                </span>
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
                From token creation to DEX listing, we provide all the tools you need
                to launch your project on Solana with confidence.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: "Simple Token Creation",
                  description: "Create your SPL token with just a few clicks. Set name, symbol, supply, and metadata without writing any code.",
                  gradient: "bg-gradient-to-br from-purple-500 to-pink-500"
                },
                {
                  title: "Instant Deployment",
                  description: "Deploy your token to Solana mainnet or devnet instantly. Leverage Solana's speed for immediate availability.",
                  gradient: "bg-gradient-to-br from-blue-500 to-cyan-500"
                },
                {
                  title: "Raydium Integration",
                  description: "Seamlessly add liquidity to Raydium DEX. Create trading pairs and enable immediate token trading.",
                  gradient: "bg-gradient-to-br from-green-500 to-emerald-500"
                },
                {
                  title: "Secure & Audited",
                  description: "Built with security-first principles using audited smart contracts and best practices for token creation.",
                  gradient: "bg-gradient-to-br from-orange-500 to-red-500"
                },
                {
                  title: "Real-time Analytics",
                  description: "Track your token's performance with built-in analytics. Monitor supply, holders, and trading activity.",
                  gradient: "bg-gradient-to-br from-indigo-500 to-purple-500"
                },
                {
                  title: "Cost Effective",
                  description: "Launch tokens for pennies, not dollars. Solana's low fees make token creation accessible to everyone.",
                  gradient: "bg-gradient-to-br from-teal-500 to-green-500"
                }
              ].map((feature, index) => (
                <div key={index} className="group relative bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 hover:border-gray-600/50 transition-all duration-300 transform hover:-translate-y-1 hover:bg-gray-800/70">
                  <div className={`w-16 h-16 ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4 group-hover:text-gray-100 transition-colors duration-300">{feature.title}</h3>
                  <p className="text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors duration-300">{feature.description}</p>
                </div>
              ))}
            </div>

            <div className="mt-20 text-center">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl p-12 text-white border border-purple-400/30 shadow-2xl">
                <h3 className="text-3xl font-bold mb-4">Ready to Build the Future?</h3>
                <p className="text-xl mb-8 opacity-90">
                  Join the Solana ecosystem and launch your token today
                </p>
                <button
                  type="button"
                  onClick={() => setIsModalOpen(true)}
                  className="inline-block bg-white text-purple-600 font-semibold px-8 py-4 rounded-xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  Get Started Now →
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>


    </>
  );
}
