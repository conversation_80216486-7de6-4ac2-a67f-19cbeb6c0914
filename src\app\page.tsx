'use client';

import { useState } from 'react';
import { Header } from "@/components/Header";
import { TweetFeed } from "@/components/tweet/TweetFeed";
import { TokenLaunchModal } from "@/components/tweet/TokenLaunchModal";
import { EnrichedTweet, TweetToTokenParams, TokenLaunchResult } from "@/types/twitter";

export default function Home() {
  const [selectedTweet, setSelectedTweet] = useState<EnrichedTweet | null>(null);
  const [isLaunchModalOpen, setIsLaunchModalOpen] = useState(false);

  const handleTweetLaunch = async (tweet: EnrichedTweet) => {
    setSelectedTweet(tweet);
    setIsLaunchModalOpen(true);
  };

  const handleTokenLaunch = async (params: TweetToTokenParams): Promise<TokenLaunchResult> => {
    // Mock implementation - replace with actual Raydium token creation logic
    console.log('Launching token with params:', params);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Mock success response
    return {
      success: true,
      token_address: 'So11111111111111111111111111111111111111112',
      transaction_signature: 'mock_signature_123',
      pool_address: 'mock_pool_address_456',
      solscan_url: 'https://solscan.io/token/So11111111111111111111111111111111111111112',
      estimated_cost: 0.21
    };
  };

  const handleTweetClick = (tweet: EnrichedTweet) => {
    // Optional: Handle tweet detail view
    console.log('Tweet clicked:', tweet);
  };

  return (
    <>
      <Header />

      {/* Main Content - Tweet Feed Focus */}
      <main className="relative overflow-hidden bg-black min-h-screen">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          {/* Grid pattern overlay */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

          {/* Gradient orbs */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-orange-500/5 to-red-500/5 rounded-full blur-3xl"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Turn Viral{" "}
              <span className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 bg-clip-text text-transparent">
                Tweets
              </span>
              {" "}Into Tradeable Tokens
            </h1>

            <p className="text-lg lg:text-xl text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed">
              Discover the most viral tweets and turn them into meme coins on <span className="text-orange-400 font-semibold">Raydium</span> with one click.
              <span className="block mt-2 text-red-400 font-semibold">From viral moment to tradeable token in seconds.</span>
            </p>
          </div>

          {/* Quick feature highlights */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-lg font-bold text-white">Viral Content</div>
              <div className="text-gray-400 text-sm">Most Popular Tweets</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-white">One Click</div>
              <div className="text-gray-400 text-sm">Token Launch</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-white">Raydium</div>
              <div className="text-gray-400 text-sm">Instant Trading</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-white">Meme Power</div>
              <div className="text-gray-400 text-sm">Viral to Value</div>
            </div>
          </div>

          {/* Main Tweet Feed */}
          <div className="max-w-4xl mx-auto">
            <TweetFeed
              onTweetLaunch={handleTweetLaunch}
              onTweetClick={handleTweetClick}
            />
          </div>

          {/* Trust indicators */}
          <div className="mt-16 pt-8 border-t border-gray-800/30 text-center">
            <p className="text-gray-500 text-sm mb-6">Powered by industry-leading platforms</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {/* Platforms */}
              <div className="flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2">
                <div className="w-5 h-5 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium text-sm">Solana</span>
              </div>
              <div className="flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2">
                <div className="w-5 h-5 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium text-sm">Raydium</span>
              </div>
              <div className="flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2">
                <div className="w-5 h-5 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium text-sm">Twitter</span>
              </div>
              <div className="flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-3 py-2">
                <div className="w-5 h-5 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg"></div>
                <span className="text-gray-300 font-medium text-sm">Tweet-Pow</span>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Token Launch Modal */}
      <TokenLaunchModal
        isOpen={isLaunchModalOpen}
        onClose={() => setIsLaunchModalOpen(false)}
        tweet={selectedTweet}
        onLaunch={handleTokenLaunch}
      />
    </>
  );
}
