(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{61:(e,t,n)=>{let i=n(1640);function r(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}r.prototype.initialize=function(e){this.degree=e,this.genPoly=i.generateECPolynomial(this.degree)},r.prototype.encode=function(e){if(!this.genPoly)throw Error("Encoder not initialized");let t=new Uint8Array(e.length+this.degree);t.set(e);let n=i.mod(t,this.genPoly),r=this.degree-n.length;if(r>0){let e=new Uint8Array(this.degree);return e.set(n,r),e}return n},e.exports=r},519:(e,t,n)=>{let i=n(8976),r=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],a=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];t.getBlocksCount=function(e,t){switch(t){case i.L:return r[(e-1)*4+0];case i.M:return r[(e-1)*4+1];case i.Q:return r[(e-1)*4+2];case i.H:return r[(e-1)*4+3];default:return}},t.getTotalCodewordsCount=function(e,t){switch(t){case i.L:return a[(e-1)*4+0];case i.M:return a[(e-1)*4+1];case i.Q:return a[(e-1)*4+2];case i.H:return a[(e-1)*4+3];default:return}}},645:(e,t,n)=>{let i=n(6087).getSymbolSize;t.getPositions=function(e){let t=i(e);return[[0,0],[t-7,0],[0,t-7]]}},763:(e,t,n)=>{"use strict";n.d(t,{u:()=>i});let i="standard:connect"},908:(e,t)=>{function n(e){if("number"==typeof e&&(e=e.toString()),"string"!=typeof e)throw Error("Color should be defined as hex string");let t=e.slice().replace("#","").split("");if(t.length<3||5===t.length||t.length>8)throw Error("Invalid hex color: "+e);(3===t.length||4===t.length)&&(t=Array.prototype.concat.apply([],t.map(function(e){return[e,e]}))),6===t.length&&t.push("F","F");let n=parseInt(t.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+t.slice(0,6).join("")}}t.getOptions=function(e){e||(e={}),e.color||(e.color={});let t=void 0===e.margin||null===e.margin||e.margin<0?4:e.margin,i=e.width&&e.width>=21?e.width:void 0,r=e.scale||4;return{width:i,scale:i?4:r,margin:t,color:{dark:n(e.color.dark||"#000000ff"),light:n(e.color.light||"#ffffffff")},type:e.type,rendererOpts:e.rendererOpts||{}}},t.getScale=function(e,t){return t.width&&t.width>=e+2*t.margin?t.width/(e+2*t.margin):t.scale},t.getImageWidth=function(e,n){let i=t.getScale(e,n);return Math.floor((e+2*n.margin)*i)},t.qrToImageData=function(e,n,i){let r=n.modules.size,a=n.modules.data,o=t.getScale(r,i),s=Math.floor((r+2*i.margin)*o),l=i.margin*o,c=[i.color.light,i.color.dark];for(let t=0;t<s;t++)for(let n=0;n<s;n++){let d=(t*s+n)*4,u=i.color.light;t>=l&&n>=l&&t<s-l&&n<s-l&&(u=c[+!!a[Math.floor((t-l)/o)*r+Math.floor((n-l)/o)]]),e[d++]=u.r,e[d++]=u.g,e[d++]=u.b,e[d]=u.a}}},1640:(e,t,n)=>{let i=n(7354);t.mul=function(e,t){let n=new Uint8Array(e.length+t.length-1);for(let r=0;r<e.length;r++)for(let a=0;a<t.length;a++)n[r+a]^=i.mul(e[r],t[a]);return n},t.mod=function(e,t){let n=new Uint8Array(e);for(;n.length-t.length>=0;){let e=n[0];for(let r=0;r<t.length;r++)n[r]^=i.mul(t[r],e);let r=0;for(;r<n.length&&0===n[r];)r++;n=n.slice(r)}return n},t.generateECPolynomial=function(e){let n=new Uint8Array([1]);for(let r=0;r<e;r++)n=t.mul(n,new Uint8Array([1,i.exp(r)]));return n}},1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},1685:(e,t)=>{let n="[0-9]+",i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",r="(?:(?![A-Z0-9 $%*+\\-./:]|"+(i=i.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";t.KANJI=RegExp(i,"g"),t.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),t.BYTE=RegExp(r,"g"),t.NUMERIC=RegExp(n,"g"),t.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let a=RegExp("^"+i+"$"),o=RegExp("^"+n+"$"),s=RegExp("^[A-Z0-9 $%*+\\-./:]+$");t.testKanji=function(e){return a.test(e)},t.testNumeric=function(e){return o.test(e)},t.testAlphanumeric=function(e){return s.test(e)}},2013:(e,t,n)=>{let i=n(908);function r(e,t){let n=e.a/255,i=t+'="'+e.hex+'"';return n<1?i+" "+t+'-opacity="'+n.toFixed(2).slice(1)+'"':i}function a(e,t,n){let i=e+t;return void 0!==n&&(i+=" "+n),i}t.render=function(e,t,n){let o=i.getOptions(t),s=e.modules.size,l=e.modules.data,c=s+2*o.margin,d=o.color.light.a?"<path "+r(o.color.light,"fill")+' d="M0 0h'+c+"v"+c+'H0z"/>':"",u="<path "+r(o.color.dark,"stroke")+' d="'+function(e,t,n){let i="",r=0,o=!1,s=0;for(let l=0;l<e.length;l++){let c=Math.floor(l%t),d=Math.floor(l/t);c||o||(o=!0),e[l]?(s++,l>0&&c>0&&e[l-1]||(i+=o?a("M",c+n,.5+d+n):a("m",r,0),r=0,o=!1),c+1<t&&e[l+1]||(i+=a("h",s),s=0)):r++}return i}(l,s,o.margin)+'"/>',h='<svg xmlns="http://www.w3.org/2000/svg" '+(o.width?'width="'+o.width+'" height="'+o.width+'" ':"")+('viewBox="0 0 '+c+" ")+c+'" shape-rendering="crispEdges">'+d+u+"</svg>\n";return"function"==typeof n&&n(null,h),h}},2131:(e,t,n)=>{"use strict";n.d(t,{AE:()=>r,Ez:()=>o,K3:()=>p,Kd:()=>u,PQ:()=>l,Sz:()=>a,UF:()=>f,Y6:()=>s,Y8:()=>c,fk:()=>d,kW:()=>h,m7:()=>i,o7:()=>m,z4:()=>g});class i extends Error{constructor(e,t){super(e),this.error=t}}class r extends i{constructor(){super(...arguments),this.name="WalletNotReadyError"}}class a extends i{constructor(){super(...arguments),this.name="WalletLoadError"}}class o extends i{constructor(){super(...arguments),this.name="WalletConfigError"}}class s extends i{constructor(){super(...arguments),this.name="WalletConnectionError"}}class l extends i{constructor(){super(...arguments),this.name="WalletDisconnectedError"}}class c extends i{constructor(){super(...arguments),this.name="WalletDisconnectionError"}}class d extends i{constructor(){super(...arguments),this.name="WalletAccountError"}}class u extends i{constructor(){super(...arguments),this.name="WalletPublicKeyError"}}class h extends i{constructor(){super(...arguments),this.name="WalletNotConnectedError"}}class f extends i{constructor(){super(...arguments),this.name="WalletSendTransactionError"}}class g extends i{constructor(){super(...arguments),this.name="WalletSignTransactionError"}}class p extends i{constructor(){super(...arguments),this.name="WalletSignMessageError"}}class m extends i{constructor(){super(...arguments),this.name="WalletSignInError"}}},2202:(e,t,n)=>{let i=n(6087),r=n(519),a=n(8976),o=n(3585),s=n(9343),l=i.getBCHDigit(7973);function c(e,t){return o.getCharCountIndicator(e,t)+4}t.from=function(e,t){return s.isValid(e)?parseInt(e,10):t},t.getCapacity=function(e,t,n){if(!s.isValid(e))throw Error("Invalid QR Code version");void 0===n&&(n=o.BYTE);let a=(i.getSymbolTotalCodewords(e)-r.getTotalCodewordsCount(e,t))*8;if(n===o.MIXED)return a;let l=a-c(n,e);switch(n){case o.NUMERIC:return Math.floor(l/10*3);case o.ALPHANUMERIC:return Math.floor(l/11*2);case o.KANJI:return Math.floor(l/13);case o.BYTE:default:return Math.floor(l/8)}},t.getBestVersionForData=function(e,n){let i,r=a.from(n,a.M);if(Array.isArray(e)){if(e.length>1){for(let n=1;n<=40;n++)if(function(e,t){let n=0;return e.forEach(function(e){let i=c(e.mode,t);n+=i+e.getBitsLength()}),n}(e,n)<=t.getCapacity(n,r,o.MIXED))return n;return}if(0===e.length)return 1;i=e[0]}else i=e;return function(e,n,i){for(let r=1;r<=40;r++)if(n<=t.getCapacity(r,i,e))return r}(i.mode,i.getLength(),r)},t.getEncodedBits=function(e){if(!s.isValid(e)||e<7)throw Error("Invalid QR Code version");let t=e<<12;for(;i.getBCHDigit(t)-l>=0;)t^=7973<<i.getBCHDigit(t)-l;return e<<12|t}},2361:(e,t,n)=>{"use strict";n.d(t,{E:()=>r,w:()=>a});var i=n(2115);let r=(0,i.createContext)({});function a(){return(0,i.useContext)(r)}},2920:(e,t,n)=>{let i=n(3585),r=n(6444),a=n(9184),o=n(7487),s=n(5580),l=n(1685),c=n(6087),d=n(8521);function u(e){return unescape(encodeURIComponent(e)).length}function h(e,t,n){let i,r=[];for(;null!==(i=e.exec(n));)r.push({data:i[0],index:i.index,mode:t,length:i[0].length});return r}function f(e){let t,n,r=h(l.NUMERIC,i.NUMERIC,e),a=h(l.ALPHANUMERIC,i.ALPHANUMERIC,e);return c.isKanjiModeEnabled()?(t=h(l.BYTE,i.BYTE,e),n=h(l.KANJI,i.KANJI,e)):(t=h(l.BYTE_KANJI,i.BYTE,e),n=[]),r.concat(a,t,n).sort(function(e,t){return e.index-t.index}).map(function(e){return{data:e.data,mode:e.mode,length:e.length}})}function g(e,t){switch(t){case i.NUMERIC:return r.getBitsLength(e);case i.ALPHANUMERIC:return a.getBitsLength(e);case i.KANJI:return s.getBitsLength(e);case i.BYTE:return o.getBitsLength(e)}}function p(e,t){let n,l=i.getBestModeForData(e);if((n=i.from(t,l))!==i.BYTE&&n.bit<l.bit)throw Error('"'+e+'" cannot be encoded with mode '+i.toString(n)+".\n Suggested mode is: "+i.toString(l));switch(n===i.KANJI&&!c.isKanjiModeEnabled()&&(n=i.BYTE),n){case i.NUMERIC:return new r(e);case i.ALPHANUMERIC:return new a(e);case i.KANJI:return new s(e);case i.BYTE:return new o(e)}}t.fromArray=function(e){return e.reduce(function(e,t){return"string"==typeof t?e.push(p(t,null)):t.data&&e.push(p(t.data,t.mode)),e},[])},t.fromString=function(e,n){let r=function(e,t){let n={},r={start:{}},a=["start"];for(let o=0;o<e.length;o++){let s=e[o],l=[];for(let e=0;e<s.length;e++){let c=s[e],d=""+o+e;l.push(d),n[d]={node:c,lastCount:0},r[d]={};for(let e=0;e<a.length;e++){let o=a[e];n[o]&&n[o].node.mode===c.mode?(r[o][d]=g(n[o].lastCount+c.length,c.mode)-g(n[o].lastCount,c.mode),n[o].lastCount+=c.length):(n[o]&&(n[o].lastCount=c.length),r[o][d]=g(c.length,c.mode)+4+i.getCharCountIndicator(c.mode,t))}}a=l}for(let e=0;e<a.length;e++)r[a[e]].end=0;return{map:r,table:n}}(function(e){let t=[];for(let n=0;n<e.length;n++){let r=e[n];switch(r.mode){case i.NUMERIC:t.push([r,{data:r.data,mode:i.ALPHANUMERIC,length:r.length},{data:r.data,mode:i.BYTE,length:r.length}]);break;case i.ALPHANUMERIC:t.push([r,{data:r.data,mode:i.BYTE,length:r.length}]);break;case i.KANJI:t.push([r,{data:r.data,mode:i.BYTE,length:u(r.data)}]);break;case i.BYTE:t.push([{data:r.data,mode:i.BYTE,length:u(r.data)}])}}return t}(f(e,c.isKanjiModeEnabled())),n),a=d.find_path(r.map,"start","end"),o=[];for(let e=1;e<a.length-1;e++)o.push(r.table[a[e]].node);return t.fromArray(o.reduce(function(e,t){let n=e.length-1>=0?e[e.length-1]:null;return n&&n.mode===t.mode?e[e.length-1].data+=t.data:e.push(t),e},[]))},t.rawSplit=function(e){return t.fromArray(f(e,c.isKanjiModeEnabled()))}},3465:(e,t,n)=>{"use strict";n.d(t,{w:()=>i});let i="standard:disconnect"},3568:(e,t,n)=>{"use strict";function i(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}n.d(t,{Toaster:()=>ej});var r,a=n(2115);let o={data:""},s=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,l=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,d=/\n+/g,u=(e,t)=>{let n="",i="",r="";for(let a in e){let o=e[a];"@"==a[0]?"i"==a[1]?n=a+" "+o+";":i+="f"==a[1]?u(o,a):a+"{"+u(o,"k"==a[1]?"":t)+"}":"object"==typeof o?i+=u(o,t?t.replace(/([^,])+/g,e=>a.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):a):null!=o&&(a=/^--/.test(a)?a:a.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=u.p?u.p(a,o):a+":"+o+";")}return n+(t&&r?t+"{"+r+"}":r)+i},h={},f=e=>{if("object"==typeof e){let t="";for(let n in e)t+=n+f(e[n]);return t}return e},g=(e,t,n,i,r)=>{let a=f(e),o=h[a]||(h[a]=(e=>{let t=0,n=11;for(;t<e.length;)n=101*n+e.charCodeAt(t++)>>>0;return"go"+n})(a));if(!h[o]){let t=a!==e?e:(e=>{let t,n,i=[{}];for(;t=l.exec(e.replace(c,""));)t[4]?i.shift():t[3]?(n=t[3].replace(d," ").trim(),i.unshift(i[0][n]=i[0][n]||{})):i[0][t[1]]=t[2].replace(d," ").trim();return i[0]})(e);h[o]=u(r?{["@keyframes "+o]:t}:t,n?"":"."+o)}let s=n&&h.g?h.g:null;return n&&(h.g=h[o]),((e,t,n,i)=>{i?t.data=t.data.replace(i,e):-1===t.data.indexOf(e)&&(t.data=n?e+t.data:t.data+e)})(h[o],t,i,s),o},p=(e,t,n)=>e.reduce((e,i,r)=>{let a=t[r];if(a&&a.call){let e=a(n),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;a=t?"."+t:e&&"object"==typeof e?e.props?"":u(e,""):!1===e?"":e}return e+i+(null==a?"":a)},"");function m(e){let t=this||{},n=e.call?e(t.p):e;return g(n.unshift?n.raw?p(n,[].slice.call(arguments,1),t.p):n.reduce((e,n)=>Object.assign(e,n&&n.call?n(t.p):n),{}):n,s(t.target),t.g,t.o,t.k)}m.bind({g:1});let w,y,M,v=m.bind({k:1});function b(e,t){let n=this||{};return function(){let i=arguments;function r(a,o){let s=Object.assign({},a),l=s.className||r.className;n.p=Object.assign({theme:y&&y()},s),n.o=/ *go\d+/.test(l),s.className=m.apply(n,i)+(l?" "+l:""),t&&(s.ref=o);let c=e;return e[0]&&(c=s.as||e,delete s.as),M&&c[0]&&M(s),w(c,s)}return t?t(r):r}}function E(){let e=i(["\nfrom {\n  transform: scale(0) rotate(45deg);\n	opacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}"]);return E=function(){return e},e}function N(){let e=i(["\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]);return N=function(){return e},e}function I(){let e=i(["\nfrom {\n  transform: scale(0) rotate(90deg);\n	opacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n	opacity: 1;\n}"]);return I=function(){return e},e}function j(){let e=i(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ",";\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n"]);return j=function(){return e},e}function L(){let e=i(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"]);return L=function(){return e},e}function T(){let e=i(["\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ",";\n  border-right-color: ",";\n  animation: "," 1s linear infinite;\n"]);return T=function(){return e},e}function A(){let e=i(["\nfrom {\n  transform: scale(0) rotate(45deg);\n	opacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n	opacity: 1;\n}"]);return A=function(){return e},e}function S(){let e=i(["\n0% {\n	height: 0;\n	width: 0;\n	opacity: 0;\n}\n40% {\n  height: 0;\n	width: 6px;\n	opacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}"]);return S=function(){return e},e}function D(){let e=i(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: "," 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ",";\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n"]);return D=function(){return e},e}function x(){let e=i(["\n  position: absolute;\n"]);return x=function(){return e},e}function z(){let e=i(["\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n"]);return z=function(){return e},e}function O(){let e=i(["\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]);return O=function(){return e},e}function C(){let e=i(["\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: "," 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n"]);return C=function(){return e},e}function k(){let e=i(["\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n"]);return k=function(){return e},e}function _(){let e=i(["\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n"]);return _=function(){return e},e}function U(){let e=i(["\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n"]);return U=function(){return e},e}var R=e=>"function"==typeof e,W=(e,t)=>R(e)?e(t):e,P=(()=>{let e=0;return()=>(++e).toString()})(),B=(()=>{let e;return()=>{if(void 0===e&&"u">typeof window){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),Y=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:n}=t;return Y(e,{type:+!!e.toasts.find(e=>e.id===n.id),toast:n});case 3:let{toastId:i}=t;return{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let r=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+r}))}}},Q=[],F={toasts:[],pausedAt:void 0},Z=e=>{F=Y(F,e),Q.forEach(e=>{e(F)})},H={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},K=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,n]=(0,a.useState)(F),i=(0,a.useRef)(F);(0,a.useEffect)(()=>(i.current!==F&&n(F),Q.push(n),()=>{let e=Q.indexOf(n);e>-1&&Q.splice(e,1)}),[]);let r=t.toasts.map(t=>{var n,i,r;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(n=e[t.type])?void 0:n.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(i=e[t.type])?void 0:i.duration)||(null==e?void 0:e.duration)||H[t.type],style:{...e.style,...null==(r=e[t.type])?void 0:r.style,...t.style}}});return{...t,toasts:r}},G=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"blank",n=arguments.length>2?arguments[2]:void 0;return{createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...n,id:(null==n?void 0:n.id)||P()}},V=e=>(t,n)=>{let i=G(t,e,n);return Z({type:2,toast:i}),i.id},q=(e,t)=>V("blank")(e,t);q.error=V("error"),q.success=V("success"),q.loading=V("loading"),q.custom=V("custom"),q.dismiss=e=>{Z({type:3,toastId:e})},q.remove=e=>Z({type:4,toastId:e}),q.promise=(e,t,n)=>{let i=q.loading(t.loading,{...n,...null==n?void 0:n.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let r=t.success?W(t.success,e):void 0;return r?q.success(r,{id:i,...n,...null==n?void 0:n.success}):q.dismiss(i),e}).catch(e=>{let r=t.error?W(t.error,e):void 0;r?q.error(r,{id:i,...n,...null==n?void 0:n.error}):q.dismiss(i)}),e};var J=(e,t)=>{Z({type:1,toast:{id:e,height:t}})},$=()=>{Z({type:5,time:Date.now()})},X=new Map,ee=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;if(X.has(e))return;let n=setTimeout(()=>{X.delete(e),Z({type:4,toastId:e})},t);X.set(e,n)},et=e=>{let{toasts:t,pausedAt:n}=K(e);(0,a.useEffect)(()=>{if(n)return;let e=Date.now(),i=t.map(t=>{if(t.duration===1/0)return;let n=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(n<0){t.visible&&q.dismiss(t.id);return}return setTimeout(()=>q.dismiss(t.id),n)});return()=>{i.forEach(e=>e&&clearTimeout(e))}},[t,n]);let i=(0,a.useCallback)(()=>{n&&Z({type:6,time:Date.now()})},[n]),r=(0,a.useCallback)((e,n)=>{let{reverseOrder:i=!1,gutter:r=8,defaultPosition:a}=n||{},o=t.filter(t=>(t.position||a)===(e.position||a)&&t.height),s=o.findIndex(t=>t.id===e.id),l=o.filter((e,t)=>t<s&&e.visible).length;return o.filter(e=>e.visible).slice(...i?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+r,0)},[t]);return(0,a.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)ee(e.id,e.removeDelay);else{let t=X.get(e.id);t&&(clearTimeout(t),X.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:J,startPause:$,endPause:i,calculateOffset:r}}},en=v(E()),ei=v(N()),er=v(I()),ea=b("div")(j(),e=>e.primary||"#ff4b4b",en,ei,e=>e.secondary||"#fff",er),eo=v(L()),es=b("div")(T(),e=>e.secondary||"#e0e0e0",e=>e.primary||"#616161",eo),el=v(A()),ec=v(S()),ed=b("div")(D(),e=>e.primary||"#61d345",el,ec,e=>e.secondary||"#fff"),eu=b("div")(x()),eh=b("div")(z()),ef=v(O()),eg=b("div")(C(),ef),ep=e=>{let{toast:t}=e,{icon:n,type:i,iconTheme:r}=t;return void 0!==n?"string"==typeof n?a.createElement(eg,null,n):n:"blank"===i?null:a.createElement(eh,null,a.createElement(es,{...r}),"loading"!==i&&a.createElement(eu,null,"error"===i?a.createElement(ea,{...r}):a.createElement(ed,{...r})))},em=e=>"\n0% {transform: translate3d(0,".concat(-200*e,"%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n"),ew=e=>"\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,".concat(-150*e,"%,-1px) scale(.6); opacity:0;}\n"),ey=b("div")(k()),eM=b("div")(_()),ev=(e,t)=>{let n=e.includes("top")?1:-1,[i,r]=B()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[em(n),ew(n)];return{animation:t?"".concat(v(i)," 0.35s cubic-bezier(.21,1.02,.73,1) forwards"):"".concat(v(r)," 0.4s forwards cubic-bezier(.06,.71,.55,1)")}},eb=a.memo(e=>{let{toast:t,position:n,style:i,children:r}=e,o=t.height?ev(t.position||n||"top-center",t.visible):{opacity:0},s=a.createElement(ep,{toast:t}),l=a.createElement(eM,{...t.ariaProps},W(t.message,t));return a.createElement(ey,{className:t.className,style:{...o,...i,...t.style}},"function"==typeof r?r({icon:s,message:l}):a.createElement(a.Fragment,null,s,l))});r=a.createElement,u.p=void 0,w=r,y=void 0,M=void 0;var eE=e=>{let{id:t,className:n,style:i,onHeightUpdate:r,children:o}=e,s=a.useCallback(e=>{if(e){let n=()=>{r(t,e.getBoundingClientRect().height)};n(),new MutationObserver(n).observe(e,{subtree:!0,childList:!0,characterData:!0})}},[t,r]);return a.createElement("div",{ref:s,className:n,style:i},o)},eN=(e,t)=>{let n=e.includes("top"),i=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:B()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:"translateY(".concat(t*(n?1:-1),"px)"),...n?{top:0}:{bottom:0},...i}},eI=m(U()),ej=e=>{let{reverseOrder:t,position:n="top-center",toastOptions:i,gutter:r,children:o,containerStyle:s,containerClassName:l}=e,{toasts:c,handlers:d}=et(i);return a.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...s},className:l,onMouseEnter:d.startPause,onMouseLeave:d.endPause},c.map(e=>{let i=e.position||n,s=eN(i,d.calculateOffset(e,{reverseOrder:t,gutter:r,defaultPosition:n}));return a.createElement(eE,{id:e.id,key:e.id,onHeightUpdate:d.updateHeight,className:e.visible?eI:"",style:s},"custom"===e.type?W(e.message,e):o?o(e):a.createElement(eb,{toast:e,position:i}))}))}},3585:(e,t,n)=>{let i=n(9343),r=n(1685);t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(e,t){if(!e.ccBits)throw Error("Invalid mode: "+e);if(!i.isValid(t))throw Error("Invalid version: "+t);return t>=1&&t<10?e.ccBits[0]:t<27?e.ccBits[1]:e.ccBits[2]},t.getBestModeForData=function(e){return r.testNumeric(e)?t.NUMERIC:r.testAlphanumeric(e)?t.ALPHANUMERIC:r.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(e){if(e&&e.id)return e.id;throw Error("Invalid mode")},t.isValid=function(e){return e&&e.bit&&e.ccBits},t.from=function(e,n){if(t.isValid(e))return e;try{if("string"!=typeof e)throw Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw Error("Unknown mode: "+e)}}catch(e){return n}}},3669:()=>{},4119:(e,t,n)=>{"use strict";n.d(t,{S:()=>o});var i=n(3570),r=n(2115),a=n(2361);let o=({children:e,endpoint:t,config:n={commitment:"confirmed"}})=>{let o=(0,r.useMemo)(()=>new i.Ng(t,n),[t,n]);return r.createElement(a.E.Provider,{value:{connection:o}},e)}},4122:e=>{function t(){this.buffer=[],this.length=0}t.prototype={get:function(e){let t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)==1},put:function(e,t){for(let n=0;n<t;n++)this.putBit((e>>>t-n-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(e){let t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},e.exports=t},4676:e=>{e.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},5580:(e,t,n)=>{let i=n(3585),r=n(6087);function a(e){this.mode=i.KANJI,this.data=e}a.getBitsLength=function(e){return 13*e},a.prototype.getLength=function(){return this.data.length},a.prototype.getBitsLength=function(){return a.getBitsLength(this.data.length)},a.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let n=r.toSJIS(this.data[t]);if(n>=33088&&n<=40956)n-=33088;else if(n>=57408&&n<=60351)n-=49472;else throw Error("Invalid SJIS character: "+this.data[t]+"\nMake sure your charset is UTF-8");n=(n>>>8&255)*192+(255&n),e.put(n,13)}},e.exports=a},5908:(e,t,n)=>{let i=n(6087),r=n(8976),a=n(4122),o=n(9621),s=n(7788),l=n(645),c=n(6517),d=n(519),u=n(61),h=n(2202),f=n(8252),g=n(3585),p=n(2920);function m(e,t,n){let i,r,a=e.size,o=f.getEncodedBits(t,n);for(i=0;i<15;i++)r=(o>>i&1)==1,i<6?e.set(i,8,r,!0):i<8?e.set(i+1,8,r,!0):e.set(a-15+i,8,r,!0),i<8?e.set(8,a-i-1,r,!0):i<9?e.set(8,15-i-1+1,r,!0):e.set(8,15-i-1,r,!0);e.set(a-8,8,1,!0)}t.create=function(e,t){let n,f;if(void 0===e||""===e)throw Error("No input text");let w=r.M;return void 0!==t&&(w=r.from(t.errorCorrectionLevel,r.M),n=h.from(t.version),f=c.from(t.maskPattern),t.toSJISFunc&&i.setToSJISFunction(t.toSJISFunc)),function(e,t,n,r){let f;if(Array.isArray(e))f=p.fromArray(e);else if("string"==typeof e){let i=t;if(!i){let t=p.rawSplit(e);i=h.getBestVersionForData(t,n)}f=p.fromString(e,i||40)}else throw Error("Invalid data");let w=h.getBestVersionForData(f,n);if(!w)throw Error("The amount of data is too big to be stored in a QR Code");if(t){if(t<w)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+w+".\n")}else t=w;let y=function(e,t,n){let r=new a;n.forEach(function(t){r.put(t.mode.bit,4),r.put(t.getLength(),g.getCharCountIndicator(t.mode,e)),t.write(r)});let o=(i.getSymbolTotalCodewords(e)-d.getTotalCodewordsCount(e,t))*8;for(r.getLengthInBits()+4<=o&&r.put(0,4);r.getLengthInBits()%8!=0;)r.putBit(0);let s=(o-r.getLengthInBits())/8;for(let e=0;e<s;e++)r.put(e%2?17:236,8);return function(e,t,n){let r,a,o=i.getSymbolTotalCodewords(t),s=o-d.getTotalCodewordsCount(t,n),l=d.getBlocksCount(t,n),c=o%l,h=l-c,f=Math.floor(o/l),g=Math.floor(s/l),p=g+1,m=f-g,w=new u(m),y=0,M=Array(l),v=Array(l),b=0,E=new Uint8Array(e.buffer);for(let e=0;e<l;e++){let t=e<h?g:p;M[e]=E.slice(y,y+t),v[e]=w.encode(M[e]),y+=t,b=Math.max(b,t)}let N=new Uint8Array(o),I=0;for(r=0;r<b;r++)for(a=0;a<l;a++)r<M[a].length&&(N[I++]=M[a][r]);for(r=0;r<m;r++)for(a=0;a<l;a++)N[I++]=v[a][r];return N}(r,e,t)}(t,n,f),M=new o(i.getSymbolSize(t));!function(e,t){let n=e.size,i=l.getPositions(t);for(let t=0;t<i.length;t++){let r=i[t][0],a=i[t][1];for(let t=-1;t<=7;t++)if(!(r+t<=-1)&&!(n<=r+t))for(let i=-1;i<=7;i++)a+i<=-1||n<=a+i||(t>=0&&t<=6&&(0===i||6===i)||i>=0&&i<=6&&(0===t||6===t)||t>=2&&t<=4&&i>=2&&i<=4?e.set(r+t,a+i,!0,!0):e.set(r+t,a+i,!1,!0))}}(M,t);let v=M.size;for(let e=8;e<v-8;e++){let t=e%2==0;M.set(e,6,t,!0),M.set(6,e,t,!0)}return!function(e,t){let n=s.getPositions(t);for(let t=0;t<n.length;t++){let i=n[t][0],r=n[t][1];for(let t=-2;t<=2;t++)for(let n=-2;n<=2;n++)-2===t||2===t||-2===n||2===n||0===t&&0===n?e.set(i+t,r+n,!0,!0):e.set(i+t,r+n,!1,!0)}}(M,t),m(M,n,0),t>=7&&function(e,t){let n,i,r,a=e.size,o=h.getEncodedBits(t);for(let t=0;t<18;t++)n=Math.floor(t/3),i=t%3+a-8-3,r=(o>>t&1)==1,e.set(n,i,r,!0),e.set(i,n,r,!0)}(M,t),!function(e,t){let n=e.size,i=-1,r=n-1,a=7,o=0;for(let s=n-1;s>0;s-=2)for(6===s&&s--;;){for(let n=0;n<2;n++)if(!e.isReserved(r,s-n)){let i=!1;o<t.length&&(i=(t[o]>>>a&1)==1),e.set(r,s-n,i),-1==--a&&(o++,a=7)}if((r+=i)<0||n<=r){r-=i,i=-i;break}}}(M,y),isNaN(r)&&(r=c.getBestMask(M,m.bind(null,M,n))),c.applyMask(r,M),m(M,n,r),{modules:M,version:t,errorCorrectionLevel:n,maskPattern:r,segments:f}}(e,n,w,f)}},5936:(e,t,n)=>{"use strict";n.d(t,{F:()=>i});let i="solana:signMessage"},6068:(e,t,n)=>{"use strict";function i(e){return"version"in e}n.d(t,{Y:()=>i})},6087:(e,t)=>{let n,i=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];t.getSymbolSize=function(e){if(!e)throw Error('"version" cannot be null or undefined');if(e<1||e>40)throw Error('"version" should be in range from 1 to 40');return 4*e+17},t.getSymbolTotalCodewords=function(e){return i[e]},t.getBCHDigit=function(e){let t=0;for(;0!==e;)t++,e>>>=1;return t},t.setToSJISFunction=function(e){if("function"!=typeof e)throw Error('"toSJISFunc" is not a valid function.');n=e},t.isKanjiModeEnabled=function(){return void 0!==n},t.toSJIS=function(e){return n(e)}},6329:(e,t,n)=>{"use strict";n.d(t,{I:()=>g});var i=n(2115),r=n(840),a=n(7399),o=n(1392),s=n(7650);let l=({id:e,children:t,expanded:n=!1})=>{let r=(0,i.useRef)(null),a=(0,i.useRef)(!0),o=()=>{let e=r.current;e&&requestAnimationFrame(()=>{e.style.height=e.scrollHeight+"px"})},s=()=>{let e=r.current;e&&requestAnimationFrame(()=>{e.style.height=e.offsetHeight+"px",e.style.overflow="hidden",requestAnimationFrame(()=>{e.style.height="0"})})};return(0,i.useLayoutEffect)(()=>{n?o():s()},[n]),(0,i.useLayoutEffect)(()=>{let e=r.current;if(e)return a.current&&(t(),a.current=!1),e.addEventListener("transitionend",i),()=>e.removeEventListener("transitionend",i);function t(){e&&(e.style.overflow=n?"initial":"hidden",n&&(e.style.height="auto"))}function i(n){e&&n.target===e&&"height"===n.propertyName&&t()}},[n]),i.createElement("div",{className:"wallet-adapter-collapse",id:e,ref:r,role:"region",style:{height:0,transition:a.current?void 0:"height 250ms ease-out"}},t)};var c=n(7615),d=n(3347);let u=({handleClick:e,tabIndex:t,wallet:n})=>i.createElement("li",null,i.createElement(c.$,{onClick:e,startIcon:i.createElement(d.l,{wallet:n}),tabIndex:t},n.adapter.name,n.readyState===a.Ok.Installed&&i.createElement("span",null,"Detected"))),h=()=>i.createElement("svg",{width:"97",height:"96",viewBox:"0 0 97 96",fill:"none",xmlns:"http://www.w3.org/2000/svg"},i.createElement("circle",{cx:"48.5",cy:"48",r:"48",fill:"url(#paint0_linear_880_5115)",fillOpacity:"0.1"}),i.createElement("circle",{cx:"48.5",cy:"48",r:"47",stroke:"url(#paint1_linear_880_5115)",strokeOpacity:"0.4",strokeWidth:"2"}),i.createElement("g",{clipPath:"url(#clip0_880_5115)"},i.createElement("path",{d:"M65.5769 28.1523H31.4231C27.6057 28.1523 24.5 31.258 24.5 35.0754V60.9215C24.5 64.7389 27.6057 67.8446 31.4231 67.8446H65.5769C69.3943 67.8446 72.5 64.7389 72.5 60.9215V35.0754C72.5 31.258 69.3943 28.1523 65.5769 28.1523ZM69.7308 52.1523H59.5769C57.2865 52.1523 55.4231 50.289 55.4231 47.9985C55.4231 45.708 57.2864 43.8446 59.5769 43.8446H69.7308V52.1523ZM69.7308 41.0754H59.5769C55.7595 41.0754 52.6539 44.1811 52.6539 47.9985C52.6539 51.8159 55.7595 54.9215 59.5769 54.9215H69.7308V60.9215C69.7308 63.2119 67.8674 65.0754 65.5769 65.0754H31.4231C29.1327 65.0754 27.2692 63.212 27.2692 60.9215V35.0754C27.2692 32.785 29.1326 30.9215 31.4231 30.9215H65.5769C67.8673 30.9215 69.7308 32.7849 69.7308 35.0754V41.0754Z",fill:"url(#paint2_linear_880_5115)"}),i.createElement("path",{d:"M61.4231 46.6172H59.577C58.8123 46.6172 58.1924 47.2371 58.1924 48.0018C58.1924 48.7665 58.8123 49.3863 59.577 49.3863H61.4231C62.1878 49.3863 62.8077 48.7664 62.8077 48.0018C62.8077 47.2371 62.1878 46.6172 61.4231 46.6172Z",fill:"url(#paint3_linear_880_5115)"})),i.createElement("defs",null,i.createElement("linearGradient",{id:"paint0_linear_880_5115",x1:"3.41664",y1:"98.0933",x2:"103.05",y2:"8.42498",gradientUnits:"userSpaceOnUse"},i.createElement("stop",{stopColor:"#9945FF"}),i.createElement("stop",{offset:"0.14",stopColor:"#8A53F4"}),i.createElement("stop",{offset:"0.42",stopColor:"#6377D6"}),i.createElement("stop",{offset:"0.79",stopColor:"#24B0A7"}),i.createElement("stop",{offset:"0.99",stopColor:"#00D18C"}),i.createElement("stop",{offset:"1",stopColor:"#00D18C"})),i.createElement("linearGradient",{id:"paint1_linear_880_5115",x1:"3.41664",y1:"98.0933",x2:"103.05",y2:"8.42498",gradientUnits:"userSpaceOnUse"},i.createElement("stop",{stopColor:"#9945FF"}),i.createElement("stop",{offset:"0.14",stopColor:"#8A53F4"}),i.createElement("stop",{offset:"0.42",stopColor:"#6377D6"}),i.createElement("stop",{offset:"0.79",stopColor:"#24B0A7"}),i.createElement("stop",{offset:"0.99",stopColor:"#00D18C"}),i.createElement("stop",{offset:"1",stopColor:"#00D18C"})),i.createElement("linearGradient",{id:"paint2_linear_880_5115",x1:"25.9583",y1:"68.7101",x2:"67.2337",y2:"23.7879",gradientUnits:"userSpaceOnUse"},i.createElement("stop",{stopColor:"#9945FF"}),i.createElement("stop",{offset:"0.14",stopColor:"#8A53F4"}),i.createElement("stop",{offset:"0.42",stopColor:"#6377D6"}),i.createElement("stop",{offset:"0.79",stopColor:"#24B0A7"}),i.createElement("stop",{offset:"0.99",stopColor:"#00D18C"}),i.createElement("stop",{offset:"1",stopColor:"#00D18C"})),i.createElement("linearGradient",{id:"paint3_linear_880_5115",x1:"58.3326",y1:"49.4467",x2:"61.0002",y2:"45.4453",gradientUnits:"userSpaceOnUse"},i.createElement("stop",{stopColor:"#9945FF"}),i.createElement("stop",{offset:"0.14",stopColor:"#8A53F4"}),i.createElement("stop",{offset:"0.42",stopColor:"#6377D6"}),i.createElement("stop",{offset:"0.79",stopColor:"#24B0A7"}),i.createElement("stop",{offset:"0.99",stopColor:"#00D18C"}),i.createElement("stop",{offset:"1",stopColor:"#00D18C"})),i.createElement("clipPath",{id:"clip0_880_5115"},i.createElement("rect",{width:"48",height:"48",fill:"white",transform:"translate(24.5 24)"})))),f=({className:e="",container:t="body"})=>{let n=(0,i.useRef)(null),{wallets:c,select:d}=(0,o.v)(),{setVisible:f}=(0,r.o)(),[g,p]=(0,i.useState)(!1),[m,w]=(0,i.useState)(!1),[y,M]=(0,i.useState)(null),[v,b]=(0,i.useMemo)(()=>{let e=[],t=[];for(let n of c)n.readyState===a.Ok.Installed?e.push(n):t.push(n);return e.length?[e,t]:[t,[]]},[c]),E=(0,i.useCallback)(()=>{w(!1),setTimeout(()=>f(!1),150)},[f]),N=(0,i.useCallback)(e=>{e.preventDefault(),E()},[E]),I=(0,i.useCallback)((e,t)=>{d(t),N(e)},[d,N]),j=(0,i.useCallback)(()=>p(!g),[g]),L=(0,i.useCallback)(e=>{let t=n.current;if(!t)return;let i=t.querySelectorAll("button"),r=i[0],a=i[i.length-1];e.shiftKey?document.activeElement===r&&(a.focus(),e.preventDefault()):document.activeElement===a&&(r.focus(),e.preventDefault())},[n]);return(0,i.useLayoutEffect)(()=>{let e=e=>{"Escape"===e.key?E():"Tab"===e.key&&L(e)},{overflow:t}=window.getComputedStyle(document.body);return setTimeout(()=>w(!0),0),document.body.style.overflow="hidden",window.addEventListener("keydown",e,!1),()=>{document.body.style.overflow=t,window.removeEventListener("keydown",e,!1)}},[E,L]),(0,i.useLayoutEffect)(()=>M(document.querySelector(t)),[t]),y&&(0,s.createPortal)(i.createElement("div",{"aria-labelledby":"wallet-adapter-modal-title","aria-modal":"true",className:`wallet-adapter-modal ${m&&"wallet-adapter-modal-fade-in"} ${e}`,ref:n,role:"dialog"},i.createElement("div",{className:"wallet-adapter-modal-container"},i.createElement("div",{className:"wallet-adapter-modal-wrapper"},i.createElement("button",{onClick:N,className:"wallet-adapter-modal-button-close"},i.createElement("svg",{width:"14",height:"14"},i.createElement("path",{d:"M14 12.461 8.3 6.772l5.234-5.233L12.006 0 6.772 5.234 1.54 0 0 1.539l5.234 5.233L0 12.006l1.539 1.528L6.772 8.3l5.69 5.7L14 12.461z"}))),v.length?i.createElement(i.Fragment,null,i.createElement("h1",{className:"wallet-adapter-modal-title"},"Connect a wallet on Solana to continue"),i.createElement("ul",{className:"wallet-adapter-modal-list"},v.map(e=>i.createElement(u,{key:e.adapter.name,handleClick:t=>I(t,e.adapter.name),wallet:e})),b.length?i.createElement(l,{expanded:g,id:"wallet-adapter-modal-collapse"},b.map(e=>i.createElement(u,{key:e.adapter.name,handleClick:t=>I(t,e.adapter.name),tabIndex:g?0:-1,wallet:e}))):null),b.length?i.createElement("button",{className:"wallet-adapter-modal-list-more",onClick:j,tabIndex:0},i.createElement("span",null,g?"Less ":"More ","options"),i.createElement("svg",{width:"13",height:"7",viewBox:"0 0 13 7",xmlns:"http://www.w3.org/2000/svg",className:`${g?"wallet-adapter-modal-list-more-icon-rotate":""}`},i.createElement("path",{d:"M0.71418 1.626L5.83323 6.26188C5.91574 6.33657 6.0181 6.39652 6.13327 6.43762C6.24844 6.47872 6.37371 6.5 6.50048 6.5C6.62725 6.5 6.75252 6.47872 6.8677 6.43762C6.98287 6.39652 7.08523 6.33657 7.16774 6.26188L12.2868 1.626C12.7753 1.1835 12.3703 0.5 11.6195 0.5H1.37997C0.629216 0.5 0.224175 1.1835 0.71418 1.626Z"}))):null):i.createElement(i.Fragment,null,i.createElement("h1",{className:"wallet-adapter-modal-title"},"You'll need a wallet on Solana to continue"),i.createElement("div",{className:"wallet-adapter-modal-middle"},i.createElement(h,null)),b.length?i.createElement(i.Fragment,null,i.createElement("button",{className:"wallet-adapter-modal-list-more",onClick:j,tabIndex:0},i.createElement("span",null,g?"Hide ":"Already have a wallet? View ","options"),i.createElement("svg",{width:"13",height:"7",viewBox:"0 0 13 7",xmlns:"http://www.w3.org/2000/svg",className:`${g?"wallet-adapter-modal-list-more-icon-rotate":""}`},i.createElement("path",{d:"M0.71418 1.626L5.83323 6.26188C5.91574 6.33657 6.0181 6.39652 6.13327 6.43762C6.24844 6.47872 6.37371 6.5 6.50048 6.5C6.62725 6.5 6.75252 6.47872 6.8677 6.43762C6.98287 6.39652 7.08523 6.33657 7.16774 6.26188L12.2868 1.626C12.7753 1.1835 12.3703 0.5 11.6195 0.5H1.37997C0.629216 0.5 0.224175 1.1835 0.71418 1.626Z"}))),i.createElement(l,{expanded:g,id:"wallet-adapter-modal-collapse"},i.createElement("ul",{className:"wallet-adapter-modal-list"},b.map(e=>i.createElement(u,{key:e.adapter.name,handleClick:t=>I(t,e.adapter.name),tabIndex:g?0:-1,wallet:e}))))):null))),i.createElement("div",{className:"wallet-adapter-modal-overlay",onMouseDown:N})),y)},g=({children:e,...t})=>{let[n,a]=(0,i.useState)(!1);return i.createElement(r.w.Provider,{value:{visible:n,setVisible:a}},e,n&&i.createElement(f,{...t}))}},6444:(e,t,n)=>{let i=n(3585);function r(e){this.mode=i.NUMERIC,this.data=e.toString()}r.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},r.prototype.getLength=function(){return this.data.length},r.prototype.getBitsLength=function(){return r.getBitsLength(this.data.length)},r.prototype.write=function(e){let t,n,i;for(t=0;t+3<=this.data.length;t+=3)i=parseInt(this.data.substr(t,3),10),e.put(i,10);let r=this.data.length-t;r>0&&(i=parseInt(this.data.substr(t),10),e.put(i,3*r+1))},e.exports=r},6501:(e,t,n)=>{"use strict";n.d(t,{CE:()=>i,g4:()=>o,re:()=>a,sE:()=>r});let i="solana:mainnet",r="solana:devnet",a="solana:testnet",o="solana:localnet"},6514:(e,t,n)=>{let i=n(908);t.render=function(e,t,n){var r;let a=n,o=t;void 0!==a||t&&t.getContext||(a=t,t=void 0),t||(o=function(){try{return document.createElement("canvas")}catch(e){throw Error("You need to specify a canvas element")}}()),a=i.getOptions(a);let s=i.getImageWidth(e.modules.size,a),l=o.getContext("2d"),c=l.createImageData(s,s);return i.qrToImageData(c.data,e,a),r=o,l.clearRect(0,0,r.width,r.height),r.style||(r.style={}),r.height=s,r.width=s,r.style.height=s+"px",r.style.width=s+"px",l.putImageData(c,0,0),o},t.renderToDataURL=function(e,n,i){let r=i;void 0!==r||n&&n.getContext||(r=n,n=void 0),r||(r={});let a=t.render(e,n,r),o=r.type||"image/png",s=r.rendererOpts||{};return a.toDataURL(o,s.quality)}},6517:(e,t)=>{t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let n={N1:3,N2:3,N3:40,N4:10};t.isValid=function(e){return null!=e&&""!==e&&!isNaN(e)&&e>=0&&e<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(e){let t=e.size,i=0,r=0,a=0,o=null,s=null;for(let l=0;l<t;l++){r=a=0,o=s=null;for(let c=0;c<t;c++){let t=e.get(l,c);t===o?r++:(r>=5&&(i+=n.N1+(r-5)),o=t,r=1),(t=e.get(c,l))===s?a++:(a>=5&&(i+=n.N1+(a-5)),s=t,a=1)}r>=5&&(i+=n.N1+(r-5)),a>=5&&(i+=n.N1+(a-5))}return i},t.getPenaltyN2=function(e){let t=e.size,i=0;for(let n=0;n<t-1;n++)for(let r=0;r<t-1;r++){let t=e.get(n,r)+e.get(n,r+1)+e.get(n+1,r)+e.get(n+1,r+1);(4===t||0===t)&&i++}return i*n.N2},t.getPenaltyN3=function(e){let t=e.size,i=0,r=0,a=0;for(let n=0;n<t;n++){r=a=0;for(let o=0;o<t;o++)r=r<<1&2047|e.get(n,o),o>=10&&(1488===r||93===r)&&i++,a=a<<1&2047|e.get(o,n),o>=10&&(1488===a||93===a)&&i++}return i*n.N3},t.getPenaltyN4=function(e){let t=0,i=e.data.length;for(let n=0;n<i;n++)t+=e.data[n];return Math.abs(Math.ceil(100*t/i/5)-10)*n.N4},t.applyMask=function(e,n){let i=n.size;for(let r=0;r<i;r++)for(let a=0;a<i;a++)n.isReserved(a,r)||n.xor(a,r,function(e,n,i){switch(e){case t.Patterns.PATTERN000:return(n+i)%2==0;case t.Patterns.PATTERN001:return n%2==0;case t.Patterns.PATTERN010:return i%3==0;case t.Patterns.PATTERN011:return(n+i)%3==0;case t.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(i/3))%2==0;case t.Patterns.PATTERN101:return n*i%2+n*i%3==0;case t.Patterns.PATTERN110:return(n*i%2+n*i%3)%2==0;case t.Patterns.PATTERN111:return(n*i%3+(n+i)%2)%2==0;default:throw Error("bad maskPattern:"+e)}}(e,a,r))},t.getBestMask=function(e,n){let i=Object.keys(t.Patterns).length,r=0,a=1/0;for(let o=0;o<i;o++){n(o),t.applyMask(o,e);let i=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(o,e),i<a&&(a=i,r=o)}return r}},6803:(e,t,n)=>{"use strict";n.d(t,{q:()=>i});let i="solana:signTransaction"},6862:(e,t,n)=>{let i=n(4676),r=n(5908),a=n(6514),o=n(2013);function s(e,t,n,a,o){let s=[].slice.call(arguments,1),l=s.length,c="function"==typeof s[l-1];if(!c&&!i())throw Error("Callback required as last argument");if(c){if(l<2)throw Error("Too few arguments provided");2===l?(o=n,n=t,t=a=void 0):3===l&&(t.getContext&&void 0===o?(o=a,a=void 0):(o=a,a=n,n=t,t=void 0))}else{if(l<1)throw Error("Too few arguments provided");return 1===l?(n=t,t=a=void 0):2!==l||t.getContext||(a=n,n=t,t=void 0),new Promise(function(i,o){try{let o=r.create(n,a);i(e(o,t,a))}catch(e){o(e)}})}try{let i=r.create(n,a);o(null,e(i,t,a))}catch(e){o(e)}}t.create=r.create,t.toCanvas=s.bind(null,a.render),t.toDataURL=s.bind(null,a.renderToDataURL),t.toString=s.bind(null,function(e,t,n){return o.render(e,n)})},6922:(e,t,n)=>{e.exports=n(8837)("**********************************************************")},6994:(e,t,n)=>{"use strict";n.d(t,{d:()=>R});var i,r,a,o,s,l,c,d,u,h,f,g,p,m,w,y=n(8670),M=n(7399),v=n(2131),b=n(6068),E=n(3570),N=function(e,t,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(e):i?i.value:t.get(e)},I=function(e,t,n,i,r){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(e,n):r?r.value=n:t.set(e,n),n};class j extends Event{get detail(){return N(this,i,"f")}get type(){return"wallet-standard:register-wallet"}constructor(e){super("wallet-standard:register-wallet",{bubbles:!1,cancelable:!1,composed:!1}),i.set(this,void 0),I(this,i,e,"f")}preventDefault(){throw Error("preventDefault cannot be called")}stopImmediatePropagation(){throw Error("stopImmediatePropagation cannot be called")}stopPropagation(){throw Error("stopPropagation cannot be called")}}i=new WeakMap;var L=n(6501),T=n(7308),A=n(6803),S=n(5936),D=n(763),x=n(3465),z=n(9698),O=function(e,t,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(e):i?i.value:t.get(e)},C=function(e,t,n,i,r){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(e,n):r?r.value=n:t.set(e,n),n};class k{constructor(){r.add(this),a.set(this,{}),o.set(this,"1.0.0"),s.set(this,"MetaMask"),l.set(this,"data:image/svg+xml;base64,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"),c.set(this,null),d.set(this,(e,t)=>(O(this,a,"f")[e]?.push(t)||(O(this,a,"f")[e]=[t]),()=>O(this,r,"m",h).call(this,e,t))),f.set(this,async()=>{if(!O(this,c,"f")){let e;try{e=(await n.e(878).then(n.bind(n,4878))).default}catch(e){throw Error("Unable to load Solflare MetaMask SDK")}C(this,c,new e,"f"),O(this,c,"f").on("standard_change",e=>O(this,r,"m",u).call(this,"change",e))}return this.accounts.length||await O(this,c,"f").connect(),{accounts:this.accounts}}),g.set(this,async()=>{O(this,c,"f")&&await O(this,c,"f").disconnect()}),p.set(this,async(...e)=>{if(!O(this,c,"f"))throw new v.kW;return await O(this,c,"f").standardSignAndSendTransaction(...e)}),m.set(this,async(...e)=>{if(!O(this,c,"f"))throw new v.kW;return await O(this,c,"f").standardSignTransaction(...e)}),w.set(this,async(...e)=>{if(!O(this,c,"f"))throw new v.kW;return await O(this,c,"f").standardSignMessage(...e)})}get version(){return O(this,o,"f")}get name(){return O(this,s,"f")}get icon(){return O(this,l,"f")}get chains(){return[L.CE,L.sE,L.re]}get features(){return{[D.u]:{version:"1.0.0",connect:O(this,f,"f")},[x.w]:{version:"1.0.0",disconnect:O(this,g,"f")},[z.j]:{version:"1.0.0",on:O(this,d,"f")},[T.R]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signAndSendTransaction:O(this,p,"f")},[A.q]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signTransaction:O(this,m,"f")},[S.F]:{version:"1.0.0",signMessage:O(this,w,"f")}}}get accounts(){return O(this,c,"f")?O(this,c,"f").standardAccounts:[]}}a=new WeakMap,o=new WeakMap,s=new WeakMap,l=new WeakMap,c=new WeakMap,d=new WeakMap,f=new WeakMap,g=new WeakMap,p=new WeakMap,m=new WeakMap,w=new WeakMap,r=new WeakSet,u=function(e,...t){O(this,a,"f")[e]?.forEach(e=>e.apply(null,t))},h=function(e,t){O(this,a,"f")[e]=O(this,a,"f")[e]?.filter(e=>t!==e)};let _=!1;async function U(){let e="solflare-detect-metamask";function t(){window.postMessage({target:"metamask-contentscript",data:{name:"metamask-provider",data:{id:e,jsonrpc:"2.0",method:"wallet_getSnaps"}}},window.location.origin)}function n(i){let r=i.data;r?.target==="metamask-inpage"&&r.data?.name==="metamask-provider"&&(r.data.data?.id===e?(window.removeEventListener("message",n),!r.data.data.error&&(_||(function(e){let t=({register:t})=>t(e);try{window.dispatchEvent(new j(t))}catch(e){console.error("wallet-standard:register-wallet event could not be dispatched\n",e)}try{window.addEventListener("wallet-standard:app-ready",({detail:e})=>t(e))}catch(e){console.error("wallet-standard:app-ready event listener could not be added\n",e)}}(new k),_=!0))):t())}window.addEventListener("message",n),window.setTimeout(()=>window.removeEventListener("message",n),5e3),t()}class R extends y.DE{constructor(e={}){super(),this.name="Solflare",this.url="https://solflare.com",this.icon="data:image/svg+xml;base64,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",this.supportedTransactionVersions=new Set(["legacy",0]),this._readyState="undefined"==typeof window||"undefined"==typeof document?M.Ok.Unsupported:M.Ok.Loadable,this._disconnected=()=>{let e=this._wallet;e&&(e.off("disconnect",this._disconnected),this._wallet=null,this._publicKey=null,this.emit("error",new v.PQ),this.emit("disconnect"))},this._accountChanged=e=>{if(!e)return;let t=this._publicKey;if(t){try{e=new E.J3(e.toBytes())}catch(e){this.emit("error",new v.Kd(e?.message,e));return}t.equals(e)||(this._publicKey=e,this.emit("connect",e))}},this._connecting=!1,this._publicKey=null,this._wallet=null,this._config=e,this._readyState!==M.Ok.Unsupported&&((0,M.qG)(()=>(!!window.solflare?.isSolflare||!!window.SolflareApp)&&(this._readyState=M.Ok.Installed,this.emit("readyStateChange",this._readyState),!0)),U())}get publicKey(){return this._publicKey}get connecting(){return this._connecting}get connected(){return!!this._wallet?.connected}get readyState(){return this._readyState}async autoConnect(){this.readyState===M.Ok.Loadable&&(0,M.Br)()||await this.connect()}async connect(){try{let e,t,i;if(this.connected||this.connecting)return;if(this._readyState!==M.Ok.Loadable&&this._readyState!==M.Ok.Installed)throw new v.AE;if(this.readyState===M.Ok.Loadable&&(0,M.Br)()){let e=encodeURIComponent(window.location.href),t=encodeURIComponent(window.location.origin);window.location.href=`https://solflare.com/ul/v1/browse/${e}?ref=${t}`;return}try{e=(await n.e(3).then(n.bind(n,2003))).default}catch(e){throw new v.Sz(e?.message,e)}try{t=new e({network:this._config.network})}catch(e){throw new v.Ez(e?.message,e)}if(this._connecting=!0,!t.connected)try{await t.connect()}catch(e){throw new v.Y6(e?.message,e)}if(!t.publicKey)throw new v.Y6;try{i=new E.J3(t.publicKey.toBytes())}catch(e){throw new v.Kd(e?.message,e)}t.on("disconnect",this._disconnected),t.on("accountChanged",this._accountChanged),this._wallet=t,this._publicKey=i,this.emit("connect",i)}catch(e){throw this.emit("error",e),e}finally{this._connecting=!1}}async disconnect(){let e=this._wallet;if(e){e.off("disconnect",this._disconnected),e.off("accountChanged",this._accountChanged),this._wallet=null,this._publicKey=null;try{await e.disconnect()}catch(e){this.emit("error",new v.Y8(e?.message,e))}}this.emit("disconnect")}async sendTransaction(e,t,n={}){try{let i=this._wallet;if(!i)throw new v.kW;try{let{signers:r,...a}=n;return(0,b.Y)(e)?r?.length&&e.sign(r):(e=await this.prepareTransaction(e,t,a),r?.length&&e.partialSign(...r)),a.preflightCommitment=a.preflightCommitment||t.commitment,await i.signAndSendTransaction(e,a)}catch(e){if(e instanceof v.m7)throw e;throw new v.UF(e?.message,e)}}catch(e){throw this.emit("error",e),e}}async signTransaction(e){try{let t=this._wallet;if(!t)throw new v.kW;try{return await t.signTransaction(e)||e}catch(e){throw new v.z4(e?.message,e)}}catch(e){throw this.emit("error",e),e}}async signAllTransactions(e){try{let t=this._wallet;if(!t)throw new v.kW;try{return await t.signAllTransactions(e)||e}catch(e){throw new v.z4(e?.message,e)}}catch(e){throw this.emit("error",e),e}}async signMessage(e){try{let t=this._wallet;if(!t)throw new v.kW;try{return await t.signMessage(e,"utf8")}catch(e){throw new v.K3(e?.message,e)}}catch(e){throw this.emit("error",e),e}}}},7308:(e,t,n)=>{"use strict";n.d(t,{R:()=>i});let i="solana:signAndSendTransaction"},7324:(e,t,n)=>{"use strict";let i,r,a,o;n.d(t,{r:()=>nK});var s,l,c,d,u,h,f,g,p,m,w,y,M,v,b,E,N,I,j,L,T,A,S,D,x,z,O,C,k,_,U,R,W,P,B,Y,Q,F,Z,H,K,G,V,q,J,$,X,ee,et,en,ei,er,ea,eo,es,el,ec,ed,eu,eh,ef,eg,ep,em,ew,ey,eM,ev,eb,eE,eN,eI,ej,eL,eT,eA,eS,eD,ex,ez,eO,eC,ek,e_,eU,eR,eW,eP,eB,eY,eQ,eF,eZ,eH,eK,eG,eV,eq,eJ,e$,eX,e0,e1,e4,e2,e5,e3,e6,e8,e7,e9,te,tt=n(8670),tn=n(7399),ti=n(2131),tr=n(3570);let ta="solana:signIn";var to=n(5936),ts=n(7308),tl=n(6803),tc=n(6862),td=n(763),tu=n(3465),th=n(9698),tf=n(6501);let tg=`(?:\\nURI: (?<uri>[^\\n]+))?(?:\\nVersion: (?<version>[^\\n]+))?(?:\\nChain ID: (?<chainId>[^\\n]+))?(?:\\nNonce: (?<nonce>[^\\n]+))?(?:\\nIssued At: (?<issuedAt>[^\\n]+))?(?:\\nExpiration Time: (?<expirationTime>[^\\n]+))?(?:\\nNot Before: (?<notBefore>[^\\n]+))?(?:\\nRequest ID: (?<requestId>[^\\n]+))?(?:\\nResources:(?<resources>(?:\\n- [^\\n]+)*))?`;RegExp(`^(?<domain>[^\\n]+?) wants you to sign in with your Solana account:\\n(?<address>[^\\n]+)(?:\\n|$)(?:\\n(?<statement>[\\S\\s]*?)(?:\\n|$))??${tg}\\n*$`);let tp={ERROR_ASSOCIATION_PORT_OUT_OF_RANGE:"ERROR_ASSOCIATION_PORT_OUT_OF_RANGE",ERROR_FORBIDDEN_WALLET_BASE_URL:"ERROR_FORBIDDEN_WALLET_BASE_URL",ERROR_SECURE_CONTEXT_REQUIRED:"ERROR_SECURE_CONTEXT_REQUIRED",ERROR_SESSION_CLOSED:"ERROR_SESSION_CLOSED",ERROR_SESSION_TIMEOUT:"ERROR_SESSION_TIMEOUT",ERROR_WALLET_NOT_FOUND:"ERROR_WALLET_NOT_FOUND",ERROR_INVALID_PROTOCOL_VERSION:"ERROR_INVALID_PROTOCOL_VERSION"};class tm extends Error{constructor(...e){let[t,n,i]=e;super(n),this.code=t,this.data=i,this.name="SolanaMobileWalletAdapterError"}}class tw extends Error{constructor(...e){let[t,n,i,r]=e;super(i),this.code=n,this.data=r,this.jsonRpcMessageId=t,this.name="SolanaMobileWalletAdapterProtocolError"}}function ty(e,t,n,i){return new(n||(n=Promise))(function(r,a){function o(e){try{l(i.next(e))}catch(e){a(e)}}function s(e){try{l(i.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(o,s)}l((i=i.apply(e,t||[])).next())})}function tM(e,t){let n=window.btoa(String.fromCharCode.call(null,...e));return t?n.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):n}function tv(e,t){return ty(this,void 0,void 0,function*(){let n=yield crypto.subtle.exportKey("raw",e),i=yield crypto.subtle.sign({hash:"SHA-256",name:"ECDSA"},t,n),r=new Uint8Array(n.byteLength+i.byteLength);return r.set(new Uint8Array(n),0),r.set(new Uint8Array(i),n.byteLength),r})}let tb="solana:cloneAuthorization";function tE(e,t){return new Proxy({},{get:(n,i)=>"then"===i?null:(null==n[i]&&(n[i]=function(n){return ty(this,void 0,void 0,function*(){let{method:r,params:a}=function(e,t,n){let i=t,r=e.toString().replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`).toLowerCase();switch(e){case"authorize":{let{chain:e}=i;if("legacy"===n){switch(e){case"solana:testnet":e="testnet";break;case"solana:devnet":e="devnet";break;case"solana:mainnet":e="mainnet-beta";break;default:e=i.cluster}i.cluster=e}else{switch(e){case"testnet":case"devnet":e=`solana:${e}`;break;case"mainnet-beta":e="solana:mainnet"}i.chain=e}}case"reauthorize":{let{auth_token:e,identity:t}=i;e&&("legacy"===n?(r="reauthorize",i={auth_token:e,identity:t}):r="authorize")}}return{method:r,params:i}}(i,n,e),o=yield t(r,a);return"authorize"===r&&a.sign_in_payload&&!o.sign_in_result&&(o.sign_in_result=yield function(e,t,n){var i;return ty(this,void 0,void 0,function*(){var r;let a=null!=(i=e.domain)?i:window.location.host,o=t.accounts[0].address,s=(r=function(e){let t=`${e.domain} wants you to sign in with your Solana account:
`;t+=`${e.address}`,e.statement&&(t+=`

${e.statement}`);let n=[];if(e.uri&&n.push(`URI: ${e.uri}`),e.version&&n.push(`Version: ${e.version}`),e.chainId&&n.push(`Chain ID: ${e.chainId}`),e.nonce&&n.push(`Nonce: ${e.nonce}`),e.issuedAt&&n.push(`Issued At: ${e.issuedAt}`),e.expirationTime&&n.push(`Expiration Time: ${e.expirationTime}`),e.notBefore&&n.push(`Not Before: ${e.notBefore}`),e.requestId&&n.push(`Request ID: ${e.requestId}`),e.resources)for(let t of(n.push("Resources:"),e.resources))n.push(`- ${t}`);return n.length&&(t+=`

${n.join("\n")}`),t}(Object.assign(Object.assign({},e),{domain:a,address:o})),window.btoa(r)),l=yield n("sign_messages",{addresses:[o],payloads:[s]});return{address:o,signed_message:s,signature:l.signed_payloads[0].slice(s.length)}})}(a.sign_in_payload,o,t)),function(e,t,n){if("getCapabilities"===e)switch(n){case"legacy":{let e=["solana:signTransactions"];return!0===t.supports_clone_authorization&&e.push(tb),Object.assign(Object.assign({},t),{features:e})}case"v1":return Object.assign(Object.assign({},t),{supports_sign_and_send_transactions:!0,supports_clone_authorization:t.features.includes(tb)})}return t}(i,o,e)})}),n[i]),defineProperty:()=>!1,deleteProperty:()=>!1})}function tN(e,t){return ty(this,void 0,void 0,function*(){let n=e.slice(0,4),r=e.slice(4,16),a=e.slice(16),o=yield crypto.subtle.decrypt(tI(n,r),t,a);return(void 0===i&&(i=new TextDecoder("utf-8")),i).decode(o)})}function tI(e,t){return{additionalData:e,iv:t,name:"AES-GCM",tagLength:128}}function tj(){return ty(this,void 0,void 0,function*(){return yield crypto.subtle.generateKey({name:"ECDSA",namedCurve:"P-256"},!1,["sign"])})}function tL(){return ty(this,void 0,void 0,function*(){return yield crypto.subtle.generateKey({name:"ECDH",namedCurve:"P-256"},!1,["deriveKey","deriveBits"])})}function tT(e){let t="",n=new Uint8Array(e),i=n.byteLength;for(let e=0;e<i;e++)t+=String.fromCharCode(n[e]);return window.btoa(t)}function tA(e){if(e<49152||e>65535)throw new tm(tp.ERROR_ASSOCIATION_PORT_OUT_OF_RANGE,`Association port number must be between 49152 and 65535. ${e} given.`,{port:e});return e}function tS(e){return e.replace(/[/+=]/g,e=>({"/":"_","+":"-","=":"."})[e])}function tD(e){return e.replace(/(^\/+|\/+$)/g,"").split("/")}function tx(e,t){let n=null;if(t){try{n=new URL(t)}catch(e){}if((null==n?void 0:n.protocol)!=="https:")throw new tm(tp.ERROR_FORBIDDEN_WALLET_BASE_URL,"Base URLs supplied by wallets must be valid `https` URLs")}return n||(n=new URL("solana-wallet:/")),new URL(e.startsWith("/")?e:[...tD(n.pathname),...tD(e)].join("/"),n)}function tz(e,t){return ty(this,void 0,void 0,function*(){return function(e,t,n){return ty(this,void 0,void 0,function*(){let i=function(e){if(e>=0x100000000)throw Error("Outbound sequence number overflow. The maximum sequence number is 32-bytes.");let t=new ArrayBuffer(4);return new DataView(t).setUint32(0,e,!1),new Uint8Array(t)}(t),r=new Uint8Array(12);crypto.getRandomValues(r);let a=yield crypto.subtle.encrypt(tI(i,r),n,new TextEncoder().encode(e)),o=new Uint8Array(i.byteLength+r.byteLength+a.byteLength);return o.set(new Uint8Array(i),0),o.set(new Uint8Array(r),i.byteLength),o.set(new Uint8Array(a),i.byteLength+r.byteLength),o})}(JSON.stringify(e),e.id,t)})}function tO(e,t){return ty(this,void 0,void 0,function*(){let n=JSON.parse((yield tN(e,t)));if(Object.hasOwnProperty.call(n,"error"))throw new tw(n.id,n.error.code,n.error.message);return n})}function tC(e,t,n){return ty(this,void 0,void 0,function*(){let[i,r]=yield Promise.all([crypto.subtle.exportKey("raw",t),crypto.subtle.importKey("raw",e.slice(0,65),{name:"ECDH",namedCurve:"P-256"},!1,[])]),a=yield crypto.subtle.deriveBits({name:"ECDH",public:r},n,256),o=yield crypto.subtle.importKey("raw",a,"HKDF",!1,["deriveKey"]);return yield crypto.subtle.deriveKey({name:"HKDF",hash:"SHA-256",salt:new Uint8Array(i),info:new Uint8Array},o,{name:"AES-GCM",length:128},!1,["encrypt","decrypt"])})}function tk(e,t){return ty(this,void 0,void 0,function*(){let n=JSON.parse((yield tN(e,t))),i="legacy";if(Object.hasOwnProperty.call(n,"v"))switch(n.v){case 1:case"1":case"v1":i="v1";break;case"legacy":i="legacy";break;default:throw new tm(tp.ERROR_INVALID_PROTOCOL_VERSION,`Unknown/unsupported protocol version: ${n.v}`)}return{protocol_version:i}})}let t_={Firefox:0,Other:1},tU=null,tR={retryDelayScheduleMs:[150,150,200,500,500,750,750,1e3],timeoutMs:3e4},tW="com.solana.mobilewalletadapter.v1",tP="com.solana.mobilewalletadapter.v1.base64";function tB(){if("undefined"==typeof window||!0!==window.isSecureContext)throw new tm(tp.ERROR_SECURE_CONTEXT_REQUIRED,"The mobile wallet adapter protocol must be used in a secure context (`https`).")}function tY(e){let t;try{t=new URL(e)}catch(e){throw new tm(tp.ERROR_FORBIDDEN_WALLET_BASE_URL,"Invalid base URL supplied by wallet")}if("https:"!==t.protocol)throw new tm(tp.ERROR_FORBIDDEN_WALLET_BASE_URL,"Base URLs supplied by wallets must be valid `https` URLs")}function tQ(e){return new DataView(e).getUint32(0,!1)}var tF=n(6922);function tZ(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)0>t.indexOf(i[r])&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]]);return n}function tH(e,t,n,i){return new(n||(n=Promise))(function(r,a){function o(e){try{l(i.next(e))}catch(e){a(e)}}function s(e){try{l(i.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(o,s)}l((i=i.apply(e,t||[])).next())})}function tK(e){return window.btoa(String.fromCharCode.call(null,...e))}function tG(e){return new Uint8Array(window.atob(e).split("").map(e=>e.charCodeAt(0)))}function tV(e){return tK("version"in e?e.serialize():e.serialize({requireAllSignatures:!1,verifySignatures:!1}))}function tq(e){let t=e[0]*tr.P3+1;return"legacy"===tr.B2.deserializeMessageVersion(e.slice(t,e.length))?tr.ZX.from(e):tr.Kt.deserialize(e)}function tJ(e){return new Proxy({},{get(t,n){if(null==t[n])switch(n){case"signAndSendTransactions":t[n]=function(t){var{minContextSlot:n,commitment:i,skipPreflight:r,maxRetries:a,waitForCommitmentToSendNextTransaction:o,transactions:s}=t,l=tZ(t,["minContextSlot","commitment","skipPreflight","maxRetries","waitForCommitmentToSendNextTransaction","transactions"]);return tH(this,void 0,void 0,function*(){let t=s.map(tV),c={min_context_slot:n,commitment:i,skip_preflight:r,max_retries:a,wait_for_commitment_to_send_next_transaction:o},{signatures:d}=yield e.signAndSendTransactions(Object.assign(Object.assign(Object.assign({},l),Object.values(c).some(e=>null!=e)?{options:c}:null),{payloads:t}));return d.map(tG).map(tF.encode)})};break;case"signMessages":t[n]=function(t){var{payloads:n}=t,i=tZ(t,["payloads"]);return tH(this,void 0,void 0,function*(){let t=n.map(tK),{signed_payloads:r}=yield e.signMessages(Object.assign(Object.assign({},i),{payloads:t}));return r.map(tG)})};break;case"signTransactions":t[n]=function(t){var{transactions:n}=t,i=tZ(t,["transactions"]);return tH(this,void 0,void 0,function*(){let t=n.map(tV),{signed_payloads:r}=yield e.signTransactions(Object.assign(Object.assign({},i),{payloads:t}));return r.map(tG).map(tq)})};break;default:t[n]=e[n]}return t[n]},defineProperty:()=>!1,deleteProperty:()=>!1})}function t$(e,t,n,i){return new(n||(n=Promise))(function(r,a){function o(e){try{l(i.next(e))}catch(e){a(e)}}function s(e){try{l(i.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(o,s)}l((i=i.apply(e,t||[])).next())})}function tX(e,t,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(e):i?i.value:t.get(e)}function t0(e,t,n,i,r){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(e,n):r?r.value=n:t.set(e,n),n}let t1=`
<div class="mobile-wallet-adapter-embedded-modal-container" role="dialog" aria-modal="true" aria-labelledby="modal-title">
    <div data-modal-close style="position: absolute; width: 100%; height: 100%;"></div>
	<div class="mobile-wallet-adapter-embedded-modal-card">
		<div>
			<button data-modal-close class="mobile-wallet-adapter-embedded-modal-close">
				<svg width="14" height="14">
					<path d="M 6.7125,8.3036995 1.9082,13.108199 c -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 C 0.1056,12.896899 0,12.631699 0,12.312499 c 0,-0.3192 0.1056,-0.5844 0.3167,-0.7958 L 5.1212,6.7124995 0.3167,1.9082 C 0.1056,1.6969 0,1.4317 0,1.1125 0,0.7933 0.1056,0.5281 0.3167,0.3167 0.5281,0.1056 0.7933,0 1.1125,0 1.4317,0 1.6969,0.1056 1.9082,0.3167 L 6.7125,5.1212 11.5167,0.3167 C 11.7281,0.1056 11.9933,0 12.3125,0 c 0.3192,0 0.5844,0.1056 0.7957,0.3167 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 L 8.3037001,6.7124995 13.1082,11.516699 c 0.2112,0.2114 0.3168,0.4766 0.3168,0.7958 0,0.3192 -0.1056,0.5844 -0.3168,0.7957 -0.2113,0.2112 -0.4765,0.3168 -0.7957,0.3168 -0.3192,0 -0.5844,-0.1056 -0.7958,-0.3168 z" />
				</svg>
			</button>
		</div>
		<div class="mobile-wallet-adapter-embedded-modal-content"></div>
	</div>
</div>
`,t4=`
.mobile-wallet-adapter-embedded-modal-container {
    display: flex; /* Use flexbox to center content */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
    overflow-y: auto; /* enable scrolling */
}

.mobile-wallet-adapter-embedded-modal-card {
    display: flex;
    flex-direction: column;
    margin: auto 20px;
    max-width: 780px;
    padding: 20px;
    border-radius: 24px;
    background: #ffffff;
    font-family: "Inter Tight", "PT Sans", Calibri, sans-serif;
    transform: translateY(-200%);
    animation: slide-in 0.5s forwards;
}

@keyframes slide-in {
    100% { transform: translateY(0%); }
}

.mobile-wallet-adapter-embedded-modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    background: #e4e9e9;
    border: none;
    border-radius: 50%;
}

.mobile-wallet-adapter-embedded-modal-close:focus-visible {
    outline-color: red;
}

.mobile-wallet-adapter-embedded-modal-close svg {
    fill: #546266;
    transition: fill 200ms ease 0s;
}

.mobile-wallet-adapter-embedded-modal-close:hover svg {
    fill: #fff;
}
`,t2=`
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter+Tight:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
`;class t5{constructor(){s.add(this),l.set(this,null),c.set(this,{}),d.set(this,!1),this.dom=null,this.open=()=>{console.debug("Modal open"),tX(this,s,"m",h).call(this),tX(this,l,"f")&&(tX(this,l,"f").style.display="flex")},this.close=e=>{var t;console.debug("Modal close"),tX(this,s,"m",f).call(this),tX(this,l,"f")&&(tX(this,l,"f").style.display="none"),null==(t=tX(this,c,"f").close)||t.forEach(t=>t(e))},g.set(this,e=>{"Escape"===e.key&&this.close(e)}),this.init=this.init.bind(this),t0(this,l,document.getElementById("mobile-wallet-adapter-embedded-root-ui"),"f")}init(){return t$(this,void 0,void 0,function*(){console.log("Injecting modal"),tX(this,s,"m",u).call(this)})}addEventListener(e,t){var n;return(null==(n=tX(this,c,"f")[e])?void 0:n.push(t))||(tX(this,c,"f")[e]=[t]),()=>this.removeEventListener(e,t)}removeEventListener(e,t){var n;tX(this,c,"f")[e]=null==(n=tX(this,c,"f")[e])?void 0:n.filter(e=>t!==e)}}l=new WeakMap,c=new WeakMap,d=new WeakMap,g=new WeakMap,s=new WeakSet,u=function(){if(document.getElementById("mobile-wallet-adapter-embedded-root-ui")){tX(this,l,"f")||t0(this,l,document.getElementById("mobile-wallet-adapter-embedded-root-ui"),"f");return}t0(this,l,document.createElement("div"),"f"),tX(this,l,"f").id="mobile-wallet-adapter-embedded-root-ui",tX(this,l,"f").innerHTML=t1,tX(this,l,"f").style.display="none";let e=tX(this,l,"f").querySelector(".mobile-wallet-adapter-embedded-modal-content");e&&(e.innerHTML=this.contentHtml);let t=document.createElement("style");t.id="mobile-wallet-adapter-embedded-modal-styles",t.textContent=t4+this.contentStyles;let n=document.createElement("div");n.innerHTML=t2,this.dom=n.attachShadow({mode:"closed"}),this.dom.appendChild(t),this.dom.appendChild(tX(this,l,"f")),document.body.appendChild(n)},h=function(){!tX(this,l,"f")||tX(this,d,"f")||([...tX(this,l,"f").querySelectorAll("[data-modal-close]")].forEach(e=>null==e?void 0:e.addEventListener("click",this.close)),window.addEventListener("load",this.close),document.addEventListener("keydown",tX(this,g,"f")),t0(this,d,!0,"f"))},f=function(){if(tX(this,d,"f"))window.removeEventListener("load",this.close),document.removeEventListener("keydown",tX(this,g,"f")),tX(this,l,"f")&&([...tX(this,l,"f").querySelectorAll("[data-modal-close]")].forEach(e=>null==e?void 0:e.removeEventListener("click",this.close)),t0(this,d,!1,"f"))};class t3 extends t5{constructor(){super(...arguments),this.contentStyles=t8,this.contentHtml=t6}initWithQR(e){let t=Object.create(null,{init:{get:()=>super.init}});return t$(this,void 0,void 0,function*(){t.init.call(this),this.populateQRCode(e)})}populateQRCode(e){var t;return t$(this,void 0,void 0,function*(){let n=null==(t=this.dom)?void 0:t.getElementById("mobile-wallet-adapter-embedded-modal-qr-code-container");if(n){let t=yield tc.toCanvas(e,{width:200,margin:0});null!==n.firstElementChild?n.replaceChild(t,n.firstElementChild):n.appendChild(t)}else console.error("QRCode Container not found")})}}let t6=`
<div class="mobile-wallet-adapter-embedded-modal-qr-content">
    <div>
        <svg class="mobile-wallet-adapter-embedded-modal-icon" width="100%" height="100%">
            <circle r="52" cx="53" cy="53" fill="#99b3be" stroke="#000000" stroke-width="2"/>
            <path d="m 53,82.7305 c -3.3116,0 -6.1361,-1.169 -8.4735,-3.507 -2.338,-2.338 -3.507,-5.1625 -3.507,-8.4735 0,-3.3116 1.169,-6.1364 3.507,-8.4744 2.3374,-2.338 5.1619,-3.507 8.4735,-3.507 3.3116,0 6.1361,1.169 8.4735,3.507 2.338,2.338 3.507,5.1628 3.507,8.4744 0,3.311 -1.169,6.1355 -3.507,8.4735 -2.3374,2.338 -5.1619,3.507 -8.4735,3.507 z m 0.007,-5.25 c 1.8532,0 3.437,-0.6598 4.7512,-1.9793 1.3149,-1.3195 1.9723,-2.9058 1.9723,-4.7591 0,-1.8526 -0.6598,-3.4364 -1.9793,-4.7512 -1.3195,-1.3149 -2.9055,-1.9723 -4.7582,-1.9723 -1.8533,0 -3.437,0.6598 -4.7513,1.9793 -1.3148,1.3195 -1.9722,2.9058 -1.9722,4.7591 0,1.8527 0.6597,3.4364 1.9792,4.7512 1.3195,1.3149 2.9056,1.9723 4.7583,1.9723 z m -28,-33.5729 -3.85,-3.6347 c 4.1195,-4.025 8.8792,-7.1984 14.2791,-9.52 5.4005,-2.3223 11.2551,-3.4834 17.5639,-3.4834 6.3087,0 12.1634,1.1611 17.5639,3.4834 5.3999,2.3216 10.1596,5.495 14.2791,9.52 l -3.85,3.6347 C 77.2999,40.358 73.0684,37.5726 68.2985,35.5514 63.5292,33.5301 58.4296,32.5195 53,32.5195 c -5.4297,0 -10.5292,1.0106 -15.2985,3.0319 -4.7699,2.0212 -9.0014,4.8066 -12.6945,8.3562 z m 44.625,10.8771 c -2.2709,-2.1046 -4.7962,-3.7167 -7.5758,-4.8361 -2.7795,-1.12 -5.7983,-1.68 -9.0562,-1.68 -3.2579,0 -6.2621,0.56 -9.0125,1.68 -2.7504,1.1194 -5.2903,2.7315 -7.6195,4.8361 L 32.5189,51.15 c 2.8355,-2.6028 5.9777,-4.6086 9.4263,-6.0174 3.4481,-1.4087 7.133,-2.1131 11.0548,-2.1131 3.9217,0 7.5979,0.7044 11.0285,2.1131 3.43,1.4088 6.5631,3.4146 9.3992,6.0174 z"/>
        </svg>
        <div class="mobile-wallet-adapter-embedded-modal-title">Remote Mobile Wallet Adapter</div>
    </div>
    <div>
        <div>
            <h4 class="mobile-wallet-adapter-embedded-modal-qr-label">
                Open your wallet and scan this code
            </h4>
        </div>
        <div id="mobile-wallet-adapter-embedded-modal-qr-code-container" class="mobile-wallet-adapter-embedded-modal-qr-code-container"></div>
    </div>
</div>
<div class="mobile-wallet-adapter-embedded-modal-divider"><hr></div>
<div class="mobile-wallet-adapter-embedded-modal-footer">
    <div class="mobile-wallet-adapter-embedded-modal-subtitle">
        Follow the instructions on your device. When you're finished, this screen will update.
    </div>
    <div class="mobile-wallet-adapter-embedded-modal-progress-badge">
        <div>
            <div class="spinner">
                <div class="leftWrapper">
                    <div class="left">
                        <div class="circle"></div>
                    </div>
                </div>
                <div class="rightWrapper">
                    <div class="right">
                        <div class="circle"></div>
                    </div>
                </div>
            </div>
        </div>
        <div>Waiting for scan</div>
    </div>
</div>
`,t8=`
.mobile-wallet-adapter-embedded-modal-qr-content {
    display: flex; 
    margin-top: 10px;
    padding: 10px;
}

.mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {
    display: flex;
    flex-direction: column;
    flex: 2;
    margin-top: auto;
    margin-right: 30px;
}

.mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-left: auto;
}

.mobile-wallet-adapter-embedded-modal-footer {
    display: flex;
    padding: 10px;
}

.mobile-wallet-adapter-embedded-modal-icon {}

.mobile-wallet-adapter-embedded-modal-title {
    color: #000000;
    font-size: 2.5em;
    font-weight: 600;
}

.mobile-wallet-adapter-embedded-modal-qr-label {
    text-align: right;
    color: #000000;
}

.mobile-wallet-adapter-embedded-modal-qr-code-container {
    margin-left: auto;
}

.mobile-wallet-adapter-embedded-modal-divider {
    margin-top: 20px;
    padding-left: 10px;
    padding-right: 10px;
}

.mobile-wallet-adapter-embedded-modal-divider hr {
    border-top: 1px solid #D9DEDE;
}

.mobile-wallet-adapter-embedded-modal-subtitle {
    margin: auto;
    margin-right: 60px;
    padding: 20px;
    color: #6E8286;
}

.mobile-wallet-adapter-embedded-modal-progress-badge {
    display: flex;
    background: #F7F8F8;
    height: 56px;
    min-width: 200px;
    margin: auto;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 18px;
    color: #A8B6B8;
    align-items: center;
}

.mobile-wallet-adapter-embedded-modal-progress-badge > div:first-child {
    margin-left: auto;
    margin-right: 20px;
}

.mobile-wallet-adapter-embedded-modal-progress-badge > div:nth-child(2) {
    margin-right: auto;
}

/* Smaller screens */
@media all and (max-width: 600px) {
    .mobile-wallet-adapter-embedded-modal-card {
        text-align: center;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content {
        flex-direction: column;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content > div:first-child {
        margin: auto;
    }
    .mobile-wallet-adapter-embedded-modal-qr-content > div:nth-child(2) {
        margin: auto;
        flex: 2 auto;
    }
    .mobile-wallet-adapter-embedded-modal-footer {
        flex-direction: column;
    }
    .mobile-wallet-adapter-embedded-modal-icon {
        display: none;
    }
    .mobile-wallet-adapter-embedded-modal-title {
        font-size: 1.5em;
    }
    .mobile-wallet-adapter-embedded-modal-subtitle {
        margin-right: unset;
    }
    .mobile-wallet-adapter-embedded-modal-qr-label {
        text-align: center;
    }
    .mobile-wallet-adapter-embedded-modal-qr-code-container {
        margin: auto;
    }
}

/* Spinner */
@keyframes spinLeft {
    0% {
        transform: rotate(20deg);
    }
    50% {
        transform: rotate(160deg);
    }
    100% {
        transform: rotate(20deg);
    }
}
@keyframes spinRight {
    0% {
        transform: rotate(160deg);
    }
    50% {
        transform: rotate(20deg);
    }
    100% {
        transform: rotate(160deg);
    }
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(2520deg);
    }
}

.spinner {
    position: relative;
    width: 1.5em;
    height: 1.5em;
    margin: auto;
    animation: spin 10s linear infinite;
}
.spinner::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
.right, .rightWrapper, .left, .leftWrapper {
    position: absolute;
    top: 0;
    overflow: hidden;
    width: .75em;
    height: 1.5em;
}
.left, .leftWrapper {
    left: 0;
}
.right {
    left: -12px;
}
.rightWrapper {
    right: 0;
}
.circle {
    border: .125em solid #A8B6B8;
    width: 1.25em; /* 1.5em - 2*0.125em border */
    height: 1.25em; /* 1.5em - 2*0.125em border */
    border-radius: 0.75em; /* 0.5*1.5em spinner size 8 */
}
.left {
    transform-origin: 100% 50%;
    animation: spinLeft 2.5s cubic-bezier(.2,0,.8,1) infinite;
}
.right {
    transform-origin: 100% 50%;
    animation: spinRight 2.5s cubic-bezier(.2,0,.8,1) infinite;
}
`,t7="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03IDIuNUgxN0MxNy44Mjg0IDIuNSAxOC41IDMuMTcxNTcgMTguNSA0VjIwQzE4LjUgMjAuODI4NCAxNy44Mjg0IDIxLjUgMTcgMjEuNUg3QzYuMTcxNTcgMjEuNSA1LjUgMjAuODI4NCA1LjUgMjBWNEM1LjUgMy4xNzE1NyA2LjE3MTU3IDIuNSA3IDIuNVpNMyA0QzMgMS43OTA4NiA0Ljc5MDg2IDAgNyAwSDE3QzE5LjIwOTEgMCAyMSAxLjc5MDg2IDIxIDRWMjBDMjEgMjIuMjA5MSAxOS4yMDkxIDI0IDE3IDI0SDdDNC43OTA4NiAyNCAzIDIyLjIwOTEgMyAyMFY0Wk0xMSA0LjYxNTM4QzEwLjQ0NzcgNC42MTUzOCAxMCA1LjA2MzEgMTAgNS42MTUzOFY2LjM4NDYyQzEwIDYuOTM2OSAxMC40NDc3IDcuMzg0NjIgMTEgNy4zODQ2MkgxM0MxMy41NTIzIDcuMzg0NjIgMTQgNi45MzY5IDE0IDYuMzg0NjJWNS42MTUzOEMxNCA1LjA2MzEgMTMuNTUyMyA0LjYxNTM4IDEzIDQuNjE1MzhIMTFaIiBmaWxsPSIjRENCOEZGIi8+Cjwvc3ZnPgo=";function t9(e){return"version"in e}function ne(e){return window.btoa(String.fromCharCode.call(null,...e))}function nt(e){return new Uint8Array(window.atob(e).split("").map(e=>e.charCodeAt(0)))}let nn="Mobile Wallet Adapter",ni=[ts.R,tl.q,to.F,ta];class nr{constructor(e){p.add(this),m.set(this,{}),w.set(this,"1.0.0"),y.set(this,nn),M.set(this,"https://solanamobile.com/wallets"),v.set(this,t7),b.set(this,void 0),E.set(this,void 0),N.set(this,void 0),I.set(this,!1),j.set(this,0),L.set(this,[]),T.set(this,void 0),A.set(this,void 0),S.set(this,void 0),D.set(this,(e,t)=>{var n;return(null==(n=tX(this,m,"f")[e])?void 0:n.push(t))||(tX(this,m,"f")[e]=[t]),()=>tX(this,p,"m",z).call(this,e,t)}),O.set(this,({silent:e}={})=>t$(this,void 0,void 0,function*(){if(tX(this,I,"f")||this.connected)return{accounts:this.accounts};t0(this,I,!0,"f");try{if(e){let e=yield tX(this,N,"f").get();if(!e)return{accounts:this.accounts};yield tX(this,k,"f").call(this,e)}else yield tX(this,C,"f").call(this)}catch(e){throw Error(e instanceof Error&&e.message||"Unknown error")}finally{t0(this,I,!1,"f")}return{accounts:this.accounts}})),C.set(this,e=>t$(this,void 0,void 0,function*(){try{let t=yield tX(this,N,"f").get();if(t)return tX(this,k,"f").call(this,t),t;let n=yield tX(this,T,"f").select(tX(this,L,"f"));return yield tX(this,W,"f").call(this,t=>t$(this,void 0,void 0,function*(){let[i,r]=yield Promise.all([t.getCapabilities(),t.authorize({chain:n,identity:tX(this,b,"f"),sign_in_payload:e})]),a=tX(this,B,"f").call(this,r.accounts),o=Object.assign(Object.assign({},r),{accounts:a,chain:n});return Promise.all([tX(this,_,"f").call(this,i),tX(this,N,"f").set(o),tX(this,k,"f").call(this,o)]),o}))}catch(e){throw Error(e instanceof Error&&e.message||"Unknown error")}})),k.set(this,e=>t$(this,void 0,void 0,function*(){var t;let n=null==tX(this,E,"f")||(null==(t=tX(this,E,"f"))?void 0:t.accounts.length)!==e.accounts.length||tX(this,E,"f").accounts.some((t,n)=>t.address!==e.accounts[n].address);t0(this,E,e,"f"),n&&tX(this,p,"m",x).call(this,"change",{accounts:this.accounts})})),_.set(this,e=>t$(this,void 0,void 0,function*(){let t=e.features.includes("solana:signTransactions"),n=e.supports_sign_and_send_transactions,i=ts.R in this.features!==n||tl.q in this.features!==t;t0(this,A,Object.assign(Object.assign({},(n||!n&&!t)&&{[ts.R]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signAndSendTransaction:tX(this,F,"f")}}),t&&{[tl.q]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signTransaction:tX(this,Z,"f")}}),"f"),i&&tX(this,p,"m",x).call(this,"change",{features:this.features})})),U.set(this,(e,t,n)=>t$(this,void 0,void 0,function*(){try{let i=yield e.authorize({auth_token:t,identity:tX(this,b,"f"),chain:n}),r=tX(this,B,"f").call(this,i.accounts),a=Object.assign(Object.assign({},i),{accounts:r,chain:n});Promise.all([tX(this,N,"f").set(a),tX(this,k,"f").call(this,a)])}catch(e){throw tX(this,R,"f").call(this),Error(e instanceof Error&&e.message||"Unknown error")}})),R.set(this,()=>t$(this,void 0,void 0,function*(){var e;tX(this,N,"f").clear(),t0(this,I,!1,"f"),t0(this,j,(e=tX(this,j,"f"),++e),"f"),t0(this,E,void 0,"f"),tX(this,p,"m",x).call(this,"change",{accounts:this.accounts})})),W.set(this,e=>t$(this,void 0,void 0,function*(){var t;let n=null==(t=tX(this,E,"f"))?void 0:t.wallet_uri_base,i=tX(this,j,"f");try{return yield function(e,t){return tH(this,void 0,void 0,function*(){return yield function(e,t){return ty(this,void 0,void 0,function*(){let n;tB();let i=yield tj(),r=yield function(e,t){return ty(this,void 0,void 0,function*(){let n=tA(49152+Math.floor(16384*Math.random())),i=yield function(e,t,n,i=["v1"]){return ty(this,void 0,void 0,function*(){let r=tA(t),a=tT((yield crypto.subtle.exportKey("raw",e))),o=tx("v1/associate/local",n);return o.searchParams.set("association",tS(a)),o.searchParams.set("port",`${r}`),i.forEach(e=>{o.searchParams.set("v",e)}),o})}(e,n,t);return yield function(e){return ty(this,void 0,void 0,function*(){if("https:"===e.protocol)window.location.assign(e);else try{switch(-1!==navigator.userAgent.indexOf("Firefox/")?t_.Firefox:t_.Other){case t_.Firefox:null==tU&&((tU=document.createElement("iframe")).style.display="none",document.body.appendChild(tU)),tU.contentWindow.location.href=e.toString();break;case t_.Other:{let t=new Promise((e,t)=>{function n(){clearTimeout(r),window.removeEventListener("blur",i)}function i(){n(),e()}window.addEventListener("blur",i);let r=setTimeout(()=>{n(),t()},3e3)});window.location.assign(e),yield t}}}catch(e){throw new tm(tp.ERROR_WALLET_NOT_FOUND,"Found no installed wallet that supports the mobile wallet protocol.")}})}(i),n})}(i.publicKey,null==t?void 0:t.baseUri),a=`ws://localhost:${r}/solana-wallet`,o=(()=>{let e=[...tR.retryDelayScheduleMs];return()=>e.length>1?e.shift():e[0]})(),s=1,l=0,c={__type:"disconnected"};return new Promise((t,r)=>{let d,u,h,f={},g=()=>ty(this,void 0,void 0,function*(){if("connecting"!==c.__type)return void console.warn(`Expected adapter state to be \`connecting\` at the moment the websocket opens. Got \`${c.__type}\`.`);d.removeEventListener("open",g);let{associationKeypair:e}=c,t=yield tL();d.send((yield tv(t.publicKey,e.privateKey))),c={__type:"hello_req_sent",associationPublicKey:e.publicKey,ecdhPrivateKey:t.privateKey}}),p=e=>{e.wasClean?c={__type:"disconnected"}:r(new tm(tp.ERROR_SESSION_CLOSED,`The wallet session dropped unexpectedly (${e.code}: ${e.reason}).`,{closeEvent:e})),u()},m=e=>ty(this,void 0,void 0,function*(){u(),Date.now()-n>=tR.timeoutMs?r(new tm(tp.ERROR_SESSION_TIMEOUT,`Failed to connect to the wallet websocket at ${a}.`)):(yield new Promise(e=>{let t=o();h=window.setTimeout(e,t)}),y())}),w=n=>ty(this,void 0,void 0,function*(){let a=yield n.data.arrayBuffer();switch(c.__type){case"connecting":if(0!==a.byteLength)throw Error("Encountered unexpected message while connecting");let o=yield tL();d.send((yield tv(o.publicKey,i.privateKey))),c={__type:"hello_req_sent",associationPublicKey:i.publicKey,ecdhPrivateKey:o.privateKey};break;case"connected":try{let e=a.slice(0,4),t=tQ(e);if(t!==l+1)throw Error("Encrypted message has invalid sequence number");l=t;let n=yield tO(a,c.sharedSecret),i=f[n.id];delete f[n.id],i.resolve(n.result)}catch(e){if(e instanceof tw){let t=f[e.jsonRpcMessageId];delete f[e.jsonRpcMessageId],t.reject(e)}else throw e}break;case"hello_req_sent":{if(0===a.byteLength){let e=yield tL();d.send((yield tv(e.publicKey,i.privateKey))),c={__type:"hello_req_sent",associationPublicKey:i.publicKey,ecdhPrivateKey:e.privateKey};break}let n=yield tC(a,c.associationPublicKey,c.ecdhPrivateKey),o=a.slice(65),h=0!==o.byteLength?yield ty(this,void 0,void 0,function*(){let e=tQ(o.slice(0,4));if(e!==l+1)throw Error("Encrypted message has invalid sequence number");return l=e,tk(o,n)}):{protocol_version:"legacy"};c={__type:"connected",sharedSecret:n,sessionProperties:h};let g=tE(h.protocol_version,(e,t)=>ty(this,void 0,void 0,function*(){let i=s++;return d.send((yield tz({id:i,jsonrpc:"2.0",method:e,params:null!=t?t:{}},n))),new Promise((t,n)=>{f[i]={resolve(i){switch(e){case"authorize":case"reauthorize":{let{wallet_uri_base:e}=i;if(null!=e)try{tY(e)}catch(e){n(e);return}}}t(i)},reject:n}})}));try{t((yield e(g)))}catch(e){r(e)}finally{u(),d.close()}}}}),y=()=>{u&&u(),c={__type:"connecting",associationKeypair:i},void 0===n&&(n=Date.now()),(d=new WebSocket(a,[tW])).addEventListener("open",g),d.addEventListener("close",p),d.addEventListener("error",m),d.addEventListener("message",w),u=()=>{window.clearTimeout(h),d.removeEventListener("open",g),d.removeEventListener("close",p),d.removeEventListener("error",m),d.removeEventListener("message",w)}};y()})})}(t=>e(tJ(t)),t)})}(e,n?{baseUri:n}:void 0)}catch(e){throw tX(this,j,"f")!==i&&(yield new Promise(()=>{})),e instanceof Error&&"SolanaMobileWalletAdapterError"===e.name&&"ERROR_WALLET_NOT_FOUND"===e.code&&(yield tX(this,S,"f").call(this,this)),e}})),P.set(this,()=>{if(!tX(this,E,"f"))throw Error("Wallet not connected");return{authToken:tX(this,E,"f").auth_token,chain:tX(this,E,"f").chain}}),B.set(this,e=>e.map(e=>{var t,n;let i=nt(e.address);return{address:tF.encode(i),publicKey:i,label:e.label,icon:e.icon,chains:null!=(t=e.chains)?t:tX(this,L,"f"),features:null!=(n=e.features)?n:ni}})),Y.set(this,e=>t$(this,void 0,void 0,function*(){let{authToken:t,chain:n}=tX(this,P,"f").call(this);try{return yield tX(this,W,"f").call(this,i=>t$(this,void 0,void 0,function*(){return yield tX(this,U,"f").call(this,i,t,n),yield i.signTransactions({transactions:e})}))}catch(e){throw Error(e instanceof Error&&e.message||"Unknown error")}})),Q.set(this,(e,t)=>t$(this,void 0,void 0,function*(){let{authToken:n,chain:i}=tX(this,P,"f").call(this);try{return yield tX(this,W,"f").call(this,r=>t$(this,void 0,void 0,function*(){let[a,o]=yield Promise.all([r.getCapabilities(),tX(this,U,"f").call(this,r,n,i)]);if(a.supports_sign_and_send_transactions)return(yield r.signAndSendTransactions(Object.assign(Object.assign({},t),{transactions:[e]})))[0];throw Error("connected wallet does not support signAndSendTransaction")}))}catch(e){throw Error(e instanceof Error&&e.message||"Unknown error")}})),F.set(this,(...e)=>t$(this,void 0,void 0,function*(){let t=[];for(let n of e){let e=tr.Kt.deserialize(n.transaction),i=yield tX(this,Q,"f").call(this,e,n.options);t.push({signature:tF.decode(i)})}return t})),Z.set(this,(...e)=>t$(this,void 0,void 0,function*(){let t=e.map(({transaction:e})=>tr.Kt.deserialize(e));return(yield tX(this,Y,"f").call(this,t)).map(e=>({signedTransaction:t9(e)?e.serialize():new Uint8Array(e.serialize({requireAllSignatures:!1,verifySignatures:!1}))}))})),H.set(this,(...e)=>t$(this,void 0,void 0,function*(){let{authToken:t,chain:n}=tX(this,P,"f").call(this),i=e.map(({account:e})=>ne(e.publicKey)),r=e.map(({message:e})=>e);try{return yield tX(this,W,"f").call(this,e=>t$(this,void 0,void 0,function*(){return yield tX(this,U,"f").call(this,e,t,n),(yield e.signMessages({addresses:i,payloads:r})).map(e=>({signedMessage:e,signature:e.slice(-64)}))}))}catch(e){throw Error(e instanceof Error&&e.message||"Unknown error")}})),K.set(this,(...e)=>t$(this,void 0,void 0,function*(){let t=[];if(!(e.length>1))return[(yield tX(this,G,"f").call(this,e[0]))];for(let n of e)t.push((yield tX(this,G,"f").call(this,n)));return t})),G.set(this,e=>t$(this,void 0,void 0,function*(){var t,n;t0(this,I,!0,"f");try{let i=yield tX(this,C,"f").call(this,Object.assign(Object.assign({},e),{domain:null!=(t=null==e?void 0:e.domain)?t:window.location.host}));if(!i.sign_in_result)throw Error("Sign in failed, no sign in result returned by wallet");let r=i.sign_in_result.address;return{account:Object.assign(Object.assign({},null!=(n=i.accounts.find(e=>e.address==r))?n:{address:r}),{publicKey:nt(r)}),signedMessage:nt(i.sign_in_result.signed_message),signature:nt(i.sign_in_result.signature)}}catch(e){throw Error(e instanceof Error&&e.message||"Unknown error")}finally{t0(this,I,!1,"f")}})),t0(this,N,e.authorizationCache,"f"),t0(this,b,e.appIdentity,"f"),t0(this,L,e.chains,"f"),t0(this,T,e.chainSelector,"f"),t0(this,S,e.onWalletNotFound,"f"),t0(this,A,{[ts.R]:{version:"1.0.0",supportedTransactionVersions:["legacy",0],signAndSendTransaction:tX(this,F,"f")}},"f")}get version(){return tX(this,w,"f")}get name(){return tX(this,y,"f")}get url(){return tX(this,M,"f")}get icon(){return tX(this,v,"f")}get chains(){return tX(this,L,"f")}get features(){return Object.assign({[td.u]:{version:"1.0.0",connect:tX(this,O,"f")},[tu.w]:{version:"1.0.0",disconnect:tX(this,R,"f")},[th.j]:{version:"1.0.0",on:tX(this,D,"f")},[to.F]:{version:"1.0.0",signMessage:tX(this,H,"f")},[ta]:{version:"1.0.0",signIn:tX(this,K,"f")}},tX(this,A,"f"))}get accounts(){var e,t;return null!=(t=null==(e=tX(this,E,"f"))?void 0:e.accounts)?t:[]}get connected(){return!!tX(this,E,"f")}get isAuthorized(){return!!tX(this,E,"f")}get currentAuthorization(){return tX(this,E,"f")}get cachedAuthorizationResult(){return tX(this,N,"f").get()}}m=new WeakMap,w=new WeakMap,y=new WeakMap,M=new WeakMap,v=new WeakMap,b=new WeakMap,E=new WeakMap,N=new WeakMap,I=new WeakMap,j=new WeakMap,L=new WeakMap,T=new WeakMap,A=new WeakMap,S=new WeakMap,D=new WeakMap,O=new WeakMap,C=new WeakMap,k=new WeakMap,_=new WeakMap,U=new WeakMap,R=new WeakMap,W=new WeakMap,P=new WeakMap,B=new WeakMap,Y=new WeakMap,Q=new WeakMap,F=new WeakMap,Z=new WeakMap,H=new WeakMap,K=new WeakMap,G=new WeakMap,p=new WeakSet,x=function(e,...t){var n;null==(n=tX(this,m,"f")[e])||n.forEach(e=>e.apply(null,t))},z=function(e,t){var n;tX(this,m,"f")[e]=null==(n=tX(this,m,"f")[e])?void 0:n.filter(e=>t!==e)};q=new WeakMap,J=new WeakMap,$=new WeakMap,X=new WeakMap,ee=new WeakMap,et=new WeakMap,en=new WeakMap,ei=new WeakMap,er=new WeakMap,ea=new WeakMap,eo=new WeakMap,es=new WeakMap,el=new WeakMap,ec=new WeakMap,ed=new WeakMap,eu=new WeakMap,eh=new WeakMap,ep=new WeakMap,em=new WeakMap,ew=new WeakMap,ey=new WeakMap,eM=new WeakMap,ev=new WeakMap,eb=new WeakMap,eE=new WeakMap,eN=new WeakMap,eI=new WeakMap,ej=new WeakMap,eL=new WeakMap,eT=new WeakMap,eA=new WeakMap,eS=new WeakMap,eD=new WeakMap,V=new WeakSet,ef=function(e,...t){var n;null==(n=tX(this,q,"f")[e])||n.forEach(e=>e.apply(null,t))},eg=function(e,t){var n;tX(this,q,"f")[e]=null==(n=tX(this,q,"f")[e])?void 0:n.filter(e=>t!==e)};var na=function(e,t,n,i,r){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(e,n):r?r.value=n:t.set(e,n),n},no=function(e,t,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(e):i?i.value:t.get(e)};class ns extends Event{constructor(e){super("wallet-standard:register-wallet",{bubbles:!1,cancelable:!1,composed:!1}),ex.set(this,void 0),na(this,ex,e,"f")}get detail(){return no(this,ex,"f")}get type(){return"wallet-standard:register-wallet"}preventDefault(){throw Error("preventDefault cannot be called")}stopImmediatePropagation(){throw Error("stopImmediatePropagation cannot be called")}stopPropagation(){throw Error("stopPropagation cannot be called")}}ex=new WeakMap;class nl extends t5{constructor(){super(...arguments),this.contentStyles=nd,this.contentHtml=nc}initWithError(e){super.init(),this.populateError(e)}populateError(e){var t,n;let i=null==(t=this.dom)?void 0:t.getElementById("mobile-wallet-adapter-error-message"),r=null==(n=this.dom)?void 0:n.getElementById("mobile-wallet-adapter-error-action");if(i){if("SolanaMobileWalletAdapterError"===e.name)switch(e.code){case"ERROR_WALLET_NOT_FOUND":i.innerHTML="To use mobile wallet adapter, you must have a compatible mobile wallet application installed on your device.",r&&r.addEventListener("click",()=>{window.location.href="https://solanamobile.com/wallets"});return;case"ERROR_BROWSER_NOT_SUPPORTED":i.innerHTML="This browser appears to be incompatible with mobile wallet adapter. Open this page in a compatible mobile browser app and try again.",r&&(r.style.display="none");return}i.innerHTML=`An unexpected error occurred: ${e.message}`}else console.log("Failed to locate error dialog element")}}let nc=`
<svg class="mobile-wallet-adapter-embedded-modal-error-icon" xmlns="http://www.w3.org/2000/svg" height="50px" viewBox="0 -960 960 960" width="50px" fill="#000000"><path d="M 280,-80 Q 197,-80 138.5,-138.5 80,-197 80,-280 80,-363 138.5,-421.5 197,-480 280,-480 q 83,0 141.5,58.5 58.5,58.5 58.5,141.5 0,83 -58.5,141.5 Q 363,-80 280,-80 Z M 824,-120 568,-376 Q 556,-389 542.5,-402.5 529,-416 516,-428 q 38,-24 61,-64 23,-40 23,-88 0,-75 -52.5,-127.5 Q 495,-760 420,-760 345,-760 292.5,-707.5 240,-655 240,-580 q 0,6 0.5,11.5 0.5,5.5 1.5,11.5 -18,2 -39.5,8 -21.5,6 -38.5,14 -2,-11 -3,-22 -1,-11 -1,-23 0,-109 75.5,-184.5 Q 311,-840 420,-840 q 109,0 184.5,75.5 75.5,75.5 75.5,184.5 0,43 -13.5,81.5 Q 653,-460 629,-428 l 251,252 z m -615,-61 71,-71 70,71 29,-28 -71,-71 71,-71 -28,-28 -71,71 -71,-71 -28,28 71,71 -71,71 z"/></svg>
<div class="mobile-wallet-adapter-embedded-modal-title">We can't find a wallet.</div>
<div id="mobile-wallet-adapter-error-message" class="mobile-wallet-adapter-embedded-modal-subtitle"></div>
<div>
    <button data-error-action id="mobile-wallet-adapter-error-action" class="mobile-wallet-adapter-embedded-modal-error-action">
        Find a wallet
    </button>
</div>
`,nd=`
.mobile-wallet-adapter-embedded-modal-content {
    text-align: center;
}

.mobile-wallet-adapter-embedded-modal-error-icon {
    margin-top: 24px;
}

.mobile-wallet-adapter-embedded-modal-title {
    margin: 18px 100px auto 100px;
    color: #000000;
    font-size: 2.75em;
    font-weight: 600;
}

.mobile-wallet-adapter-embedded-modal-subtitle {
    margin: 30px 60px 40px 60px;
    color: #000000;
    font-size: 1.25em;
    font-weight: 400;
}

.mobile-wallet-adapter-embedded-modal-error-action {
    display: block;
    width: 100%;
    height: 56px;
    /*margin-top: 40px;*/
    font-size: 1.25em;
    /*line-height: 24px;*/
    /*letter-spacing: -1%;*/
    background: #000000;
    color: #FFFFFF;
    border-radius: 18px;
}

/* Smaller screens */
@media all and (max-width: 600px) {
    .mobile-wallet-adapter-embedded-modal-title {
        font-size: 1.5em;
        margin-right: 12px;
        margin-left: 12px;
    }
    .mobile-wallet-adapter-embedded-modal-subtitle {
        margin-right: 12px;
        margin-left: 12px;
    }
}
`,nu="SolanaMobileWalletAdapterDefaultAuthorizationCache";function nh(e,t,n,i){return new(n||(n=Promise))(function(r,a){function o(e){try{l(i.next(e))}catch(e){a(e)}}function s(e){try{l(i.throw(e))}catch(e){a(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(o,s)}l((i=i.apply(e,t||[])).next())})}function nf(e,t,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(e):i?i.value:t.get(e)}function ng(e,t,n,i,r){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(e,n):r?r.value=n:t.set(e,n),n}let np="standard:connect";function nm(e){return window.btoa(String.fromCharCode.call(null,...e))}let nw="Mobile Wallet Adapter";function ny(e){switch(e){case"mainnet-beta":return"solana:mainnet";case"testnet":return"solana:testnet";case"devnet":return"solana:devnet";default:return e}}class nM extends tt.Xl{constructor(e,t){super(),ez.add(this),this.supportedTransactionVersions=new Set(["legacy",0]),eO.set(this,void 0),eC.set(this,!1),ek.set(this,"undefined"!=typeof window&&window.isSecureContext&&"undefined"!=typeof document&&/android/i.test(navigator.userAgent)?tn.Ok.Loadable:tn.Ok.Unsupported),e_.set(this,void 0),eU.set(this,void 0),eR.set(this,void 0),eW.set(this,e=>nh(this,void 0,void 0,function*(){if(e.accounts&&e.accounts.length>0){nf(this,ez,"m",eB).call(this);let t=yield nf(this,e_,"f").call(this,e.accounts);t!==nf(this,eU,"f")&&(ng(this,eU,t,"f"),ng(this,eR,void 0,"f"),this.emit("connect",this.publicKey))}})),ng(this,e_,e=>nh(this,void 0,void 0,function*(){var n;let i=yield t.addressSelector.select(e.map(({publicKey:e})=>nm(e)));return null!=(n=e.find(({publicKey:e})=>nm(e)===i))?n:e[0]}),"f"),ng(this,eO,e,"f"),nf(this,eO,"f").features["standard:events"].on("change",nf(this,eW,"f")),this.name=nf(this,eO,"f").name,this.icon=nf(this,eO,"f").icon,this.url=nf(this,eO,"f").url}get publicKey(){var e;if(!nf(this,eR,"f")&&nf(this,eU,"f"))try{ng(this,eR,new tr.J3(nf(this,eU,"f").publicKey),"f")}catch(e){throw new ti.Kd(e instanceof Error&&(null==e?void 0:e.message)||"Unknown error",e)}return null!=(e=nf(this,eR,"f"))?e:null}get connected(){return nf(this,eO,"f").connected}get connecting(){return nf(this,eC,"f")}get readyState(){return nf(this,ek,"f")}autoConnect_DO_NOT_USE_OR_YOU_WILL_BE_FIRED(){return nh(this,void 0,void 0,function*(){return yield this.autoConnect()})}autoConnect(){return nh(this,void 0,void 0,function*(){nf(this,ez,"m",eP).call(this,!0)})}connect(){return nh(this,void 0,void 0,function*(){nf(this,ez,"m",eP).call(this)})}performAuthorization(e){return nh(this,void 0,void 0,function*(){try{let t=yield nf(this,eO,"f").cachedAuthorizationResult;if(t)return yield nf(this,eO,"f").features[np].connect({silent:!0}),t;return e?yield nf(this,eO,"f").features[ta].signIn(e):yield nf(this,eO,"f").features[np].connect(),yield yield nf(this,eO,"f").cachedAuthorizationResult}catch(e){throw new ti.Y6(e instanceof Error&&e.message||"Unknown error",e)}})}disconnect(){return nh(this,void 0,void 0,function*(){return yield nf(this,ez,"m",eF).call(this,()=>nh(this,void 0,void 0,function*(){ng(this,eC,!1,"f"),ng(this,eR,void 0,"f"),ng(this,eU,void 0,"f"),yield nf(this,eO,"f").features["standard:disconnect"].disconnect(),this.emit("disconnect")}))})}signIn(e){return nh(this,void 0,void 0,function*(){return nf(this,ez,"m",eF).call(this,()=>nh(this,void 0,void 0,function*(){var t;if(nf(this,ek,"f")!==tn.Ok.Installed&&nf(this,ek,"f")!==tn.Ok.Loadable)throw new ti.AE;ng(this,eC,!0,"f");try{let n=yield nf(this,eO,"f").features[ta].signIn(Object.assign(Object.assign({},e),{domain:null!=(t=null==e?void 0:e.domain)?t:window.location.host}));if(n.length>0)return n[0];throw Error("Sign in failed, no sign in result returned by wallet")}catch(e){throw new ti.Y6(e instanceof Error&&e.message||"Unknown error",e)}finally{ng(this,eC,!1,"f")}}))})}signMessage(e){return nh(this,void 0,void 0,function*(){return yield nf(this,ez,"m",eF).call(this,()=>nh(this,void 0,void 0,function*(){let t=nf(this,ez,"m",eY).call(this);try{return(yield nf(this,eO,"f").features[to.F].signMessage({account:t,message:e}))[0].signature}catch(e){throw new ti.K3(null==e?void 0:e.message,e)}}))})}sendTransaction(e,t,n){return nh(this,void 0,void 0,function*(){return yield nf(this,ez,"m",eF).call(this,()=>nh(this,void 0,void 0,function*(){let i=nf(this,ez,"m",eY).call(this);try{if(ts.R in nf(this,eO,"f").features){let t=ny(nf(this,eO,"f").currentAuthorization.chain),[r]=(yield nf(this,eO,"f").features[ts.R].signAndSendTransaction({account:i,transaction:e.serialize(),chain:t,options:n?{skipPreflight:n.skipPreflight,maxRetries:n.maxRetries}:void 0})).map(e=>nm(e.signature));return r}{let[i]=yield nf(this,ez,"m",eQ).call(this,[e]);if("version"in i)return yield t.sendTransaction(i);{let e=i.serialize();return yield t.sendRawTransaction(e,Object.assign(Object.assign({},n),{preflightCommitment:function(){let e,i;switch(t.commitment){case"confirmed":case"finalized":case"processed":e=t.commitment;break;default:e="finalized"}switch(null==n?void 0:n.preflightCommitment){case"confirmed":case"finalized":case"processed":i=n.preflightCommitment;break;case void 0:i=e;break;default:i="finalized"}let r="finalized"===i?2:+("confirmed"===i),a="finalized"===e?2:+("confirmed"===e);return r<a?i:e}()}))}}}catch(e){throw new ti.UF(null==e?void 0:e.message,e)}}))})}signTransaction(e){return nh(this,void 0,void 0,function*(){return yield nf(this,ez,"m",eF).call(this,()=>nh(this,void 0,void 0,function*(){let[t]=yield nf(this,ez,"m",eQ).call(this,[e]);return t}))})}signAllTransactions(e){return nh(this,void 0,void 0,function*(){return yield nf(this,ez,"m",eF).call(this,()=>nh(this,void 0,void 0,function*(){return yield nf(this,ez,"m",eQ).call(this,e)}))})}}eO=new WeakMap,eC=new WeakMap,ek=new WeakMap,e_=new WeakMap,eU=new WeakMap,eR=new WeakMap,eW=new WeakMap,ez=new WeakSet,eP=function(e=!1){return nh(this,void 0,void 0,function*(){if(!this.connecting&&!this.connected)return yield nf(this,ez,"m",eF).call(this,()=>nh(this,void 0,void 0,function*(){if(nf(this,ek,"f")!==tn.Ok.Installed&&nf(this,ek,"f")!==tn.Ok.Loadable)throw new ti.AE;ng(this,eC,!0,"f");try{yield nf(this,eO,"f").features[np].connect({silent:e})}catch(e){throw new ti.Y6(e instanceof Error&&e.message||"Unknown error",e)}finally{ng(this,eC,!1,"f")}}))})},eB=function(){nf(this,ek,"f")!==tn.Ok.Installed&&this.emit("readyStateChange",ng(this,ek,tn.Ok.Installed,"f"))},eY=function(){if(!nf(this,eO,"f").isAuthorized||!nf(this,eU,"f"))throw new ti.kW;return nf(this,eU,"f")},eQ=function(e){return nh(this,void 0,void 0,function*(){let t=nf(this,ez,"m",eY).call(this);try{if(tl.q in nf(this,eO,"f").features)return nf(this,eO,"f").features[tl.q].signTransaction(...e.map(e=>({account:t,transaction:e.serialize()}))).then(e=>e.map(e=>{let t=e.signedTransaction,n=t[0],i=tr.B2.deserializeMessageVersion(t.slice(64*n+1,t.length));return"legacy"===i?tr.ZX.from(t):tr.Kt.deserialize(t)}));throw Error("Connected wallet does not support signing transactions")}catch(e){throw new ti.z4(null==e?void 0:e.message,e)}})},eF=function(e){return nh(this,void 0,void 0,function*(){try{return yield e()}catch(e){throw this.emit("error",e),e}})};class nv extends nM{constructor(e){var t;let n=ny(null!=(t=e.chain)?t:e.cluster);super(new nr({appIdentity:e.appIdentity,authorizationCache:{set:e.authorizationResultCache.set,get:()=>nh(this,void 0,void 0,function*(){let t=yield e.authorizationResultCache.get();return t&&"chain"in t?t:t?Object.assign(Object.assign({},t),{chain:n}):void 0}),clear:e.authorizationResultCache.clear},chains:[n],chainSelector:{select(e){return t$(this,void 0,void 0,function*(){return 1===e.length?e[0]:e.includes(tf.CE)?tf.CE:e[0]})}},onWalletNotFound:()=>nh(this,void 0,void 0,function*(){e.onWalletNotFound(this)})}),{addressSelector:e.addressSelector,chain:n})}}class nb extends nv{}function nE(e){return nh(this,void 0,void 0,function*(){return function(){return t$(this,void 0,void 0,function*(){if("undefined"!=typeof window){let e=window.navigator.userAgent.toLowerCase(),t=new nl;e.includes("wv")?t.initWithError({name:"SolanaMobileWalletAdapterError",code:"ERROR_BROWSER_NOT_SUPPORTED",message:""}):t.initWithError({name:"SolanaMobileWalletAdapterError",code:"ERROR_WALLET_NOT_FOUND",message:""}),t.open()}})}()})}let nN=function(e){return td.u in e.features&&th.j in e.features&&(ts.R in e.features||tl.q in e.features)};var nI=n(6068);function nj(e){switch(e){case"processed":case"confirmed":case"finalized":case void 0:return e;case"recent":return"processed";case"single":case"singleGossip":return"confirmed";case"max":case"root":return"finalized";default:return}}new WeakMap,new WeakMap,new WeakMap,new WeakMap,new WeakMap,new WeakMap;let nL=function(e){if(e.length>=255)throw TypeError("Alphabet too long");let t=new Uint8Array(256);for(let e=0;e<t.length;e++)t[e]=255;for(let n=0;n<e.length;n++){let i=e.charAt(n),r=i.charCodeAt(0);if(255!==t[r])throw TypeError(i+" is ambiguous");t[r]=n}let n=e.length,i=e.charAt(0),r=Math.log(n)/Math.log(256),a=Math.log(256)/Math.log(n);function o(e){if("string"!=typeof e)throw TypeError("Expected String");if(0===e.length)return new Uint8Array;let a=0,o=0,s=0;for(;e[a]===i;)o++,a++;let l=(e.length-a)*r+1>>>0,c=new Uint8Array(l);for(;a<e.length;){let i=e.charCodeAt(a);if(i>255)return;let r=t[i];if(255===r)return;let o=0;for(let e=l-1;(0!==r||o<s)&&-1!==e;e--,o++)r+=n*c[e]>>>0,c[e]=r%256>>>0,r=r/256>>>0;if(0!==r)throw Error("Non-zero carry");s=o,a++}let d=l-s;for(;d!==l&&0===c[d];)d++;let u=new Uint8Array(o+(l-d)),h=o;for(;d!==l;)u[h++]=c[d++];return u}return{encode:function(t){if(t instanceof Uint8Array||(ArrayBuffer.isView(t)?t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength):Array.isArray(t)&&(t=Uint8Array.from(t))),!(t instanceof Uint8Array))throw TypeError("Expected Uint8Array");if(0===t.length)return"";let r=0,o=0,s=0,l=t.length;for(;s!==l&&0===t[s];)s++,r++;let c=(l-s)*a+1>>>0,d=new Uint8Array(c);for(;s!==l;){let e=t[s],i=0;for(let t=c-1;(0!==e||i<o)&&-1!==t;t--,i++)e+=256*d[t]>>>0,d[t]=e%n>>>0,e=e/n>>>0;if(0!==e)throw Error("Non-zero carry");o=i,s++}let u=c-o;for(;u!==c&&0===d[u];)u++;let h=i.repeat(r);for(;u<c;++u)h+=e.charAt(d[u]);return h},decodeUnsafe:o,decode:function(e){let t=o(e);if(t)return t;throw Error("Non-base"+n+" character")}}}("**********************************************************");var nT=function(e,t,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(e):i?i.value:t.get(e)},nA=function(e,t,n,i,r){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(e,n):r?r.value=n:t.set(e,n),n};class nS extends tn.Ce{get name(){return nT(this,e$,"f").name}get url(){return"https://github.com/solana-labs/wallet-standard"}get icon(){return nT(this,e$,"f").icon}get readyState(){return nT(this,eX,"f")}get publicKey(){return nT(this,eK,"f")}get connecting(){return nT(this,eG,"f")}get supportedTransactionVersions(){return nT(this,eJ,"f")}get wallet(){return nT(this,e$,"f")}get standard(){return!0}constructor({wallet:e}){super(),eZ.add(this),eH.set(this,void 0),eK.set(this,void 0),eG.set(this,void 0),eV.set(this,void 0),eq.set(this,void 0),eJ.set(this,void 0),e$.set(this,void 0),eX.set(this,"undefined"==typeof window||"undefined"==typeof document?tn.Ok.Unsupported:tn.Ok.Installed),e5.set(this,e=>{if("accounts"in e){let e=nT(this,e$,"f").accounts[0];nT(this,eH,"f")&&!nT(this,eV,"f")&&e!==nT(this,eH,"f")&&(e?nT(this,eZ,"m",e1).call(this,e):(this.emit("error",new ti.PQ),nT(this,eZ,"m",e4).call(this)))}"features"in e&&nT(this,eZ,"m",e2).call(this)}),nA(this,e$,e,"f"),nA(this,eH,null,"f"),nA(this,eK,null,"f"),nA(this,eG,!1,"f"),nA(this,eV,!1,"f"),nA(this,eq,nT(this,e$,"f").features[th.j].on("change",nT(this,e5,"f")),"f"),nT(this,eZ,"m",e2).call(this)}destroy(){nA(this,eH,null,"f"),nA(this,eK,null,"f"),nA(this,eG,!1,"f"),nA(this,eV,!1,"f");let e=nT(this,eq,"f");e&&(nA(this,eq,null,"f"),e())}async autoConnect(){return nT(this,eZ,"m",e0).call(this,{silent:!0})}async connect(){return nT(this,eZ,"m",e0).call(this)}async disconnect(){if(tu.w in nT(this,e$,"f").features)try{nA(this,eV,!0,"f"),await nT(this,e$,"f").features[tu.w].disconnect()}catch(e){this.emit("error",new ti.Y8(e?.message,e))}finally{nA(this,eV,!1,"f")}nT(this,eZ,"m",e4).call(this)}async sendTransaction(e,t,n={}){try{var i;let r,a=nT(this,eH,"f");if(!a)throw new ti.kW;if(ts.R in nT(this,e$,"f").features)if(a.features.includes(ts.R))r=ts.R;else if(tl.q in nT(this,e$,"f").features&&a.features.includes(tl.q))r=tl.q;else throw new ti.fk;else if(tl.q in nT(this,e$,"f").features){if(!a.features.includes(tl.q))throw new ti.fk;r=tl.q}else throw new ti.Ez;let o=(i=t.rpcEndpoint).includes("https://api.mainnet-beta.solana.com")?tf.CE:/\bdevnet\b/i.test(i)?tf.sE:/\btestnet\b/i.test(i)?tf.re:/\blocalhost\b/i.test(i)||/\b127\.0\.0\.1\b/.test(i)?tf.g4:tf.CE;if(!a.chains.includes(o))throw new ti.UF;try{let i,{signers:s,...l}=n;if((0,nI.Y)(e)?(s?.length&&e.sign(s),i=e.serialize()):(e=await this.prepareTransaction(e,t,l),s?.length&&e.partialSign(...s),i=new Uint8Array(e.serialize({requireAllSignatures:!1,verifySignatures:!1}))),r===ts.R){let[e]=await nT(this,e$,"f").features[ts.R].signAndSendTransaction({account:a,chain:o,transaction:i,options:{preflightCommitment:nj(l.preflightCommitment||t.commitment),skipPreflight:l.skipPreflight,maxRetries:l.maxRetries,minContextSlot:l.minContextSlot}});return nL.encode(e.signature)}{let[e]=await nT(this,e$,"f").features[tl.q].signTransaction({account:a,chain:o,transaction:i,options:{preflightCommitment:nj(l.preflightCommitment||t.commitment),minContextSlot:l.minContextSlot}});return await t.sendRawTransaction(e.signedTransaction,{...l,preflightCommitment:nj(l.preflightCommitment||t.commitment)})}}catch(e){if(e instanceof ti.m7)throw e;throw new ti.UF(e?.message,e)}}catch(e){throw this.emit("error",e),e}}}eH=new WeakMap,eK=new WeakMap,eG=new WeakMap,eV=new WeakMap,eq=new WeakMap,eJ=new WeakMap,e$=new WeakMap,eX=new WeakMap,e5=new WeakMap,eZ=new WeakSet,e0=async function(e){try{if(this.connected||this.connecting)return;if(nT(this,eX,"f")!==tn.Ok.Installed)throw new ti.AE;if(nA(this,eG,!0,"f"),!nT(this,e$,"f").accounts.length)try{await nT(this,e$,"f").features[td.u].connect(e)}catch(e){throw new ti.Y6(e?.message,e)}let t=nT(this,e$,"f").accounts[0];if(!t)throw new ti.fk;nT(this,eZ,"m",e1).call(this,t)}catch(e){throw this.emit("error",e),e}finally{nA(this,eG,!1,"f")}},e1=function(e){let t;try{t=new tr.J3(e.address)}catch(e){throw new ti.Kd(e?.message,e)}nA(this,eH,e,"f"),nA(this,eK,t,"f"),nT(this,eZ,"m",e2).call(this),this.emit("connect",t)},e4=function(){nA(this,eH,null,"f"),nA(this,eK,null,"f"),nT(this,eZ,"m",e2).call(this),this.emit("disconnect")},e2=function(){let e=ts.R in nT(this,e$,"f").features?nT(this,e$,"f").features[ts.R].supportedTransactionVersions:nT(this,e$,"f").features[tl.q].supportedTransactionVersions;nA(this,eJ,!function(e,t){if(e===t)return!0;let n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}(e,["legacy"])?new Set(e):null,"f"),tl.q in nT(this,e$,"f").features&&nT(this,eH,"f")?.features.includes(tl.q)?(this.signTransaction=nT(this,eZ,"m",e3),this.signAllTransactions=nT(this,eZ,"m",e6)):(delete this.signTransaction,delete this.signAllTransactions),to.F in nT(this,e$,"f").features&&nT(this,eH,"f")?.features.includes(to.F)?this.signMessage=nT(this,eZ,"m",e8):delete this.signMessage,ta in nT(this,e$,"f").features?this.signIn=nT(this,eZ,"m",e7):delete this.signIn},e3=async function(e){try{let t=nT(this,eH,"f");if(!t)throw new ti.kW;if(!(tl.q in nT(this,e$,"f").features))throw new ti.Ez;if(!t.features.includes(tl.q))throw new ti.fk;try{let n=(await nT(this,e$,"f").features[tl.q].signTransaction({account:t,transaction:(0,nI.Y)(e)?e.serialize():new Uint8Array(e.serialize({requireAllSignatures:!1,verifySignatures:!1}))}))[0].signedTransaction;return(0,nI.Y)(e)?tr.Kt.deserialize(n):tr.ZX.from(n)}catch(e){if(e instanceof ti.m7)throw e;throw new ti.z4(e?.message,e)}}catch(e){throw this.emit("error",e),e}},e6=async function(e){try{let t=nT(this,eH,"f");if(!t)throw new ti.kW;if(!(tl.q in nT(this,e$,"f").features))throw new ti.Ez;if(!t.features.includes(tl.q))throw new ti.fk;try{let n=await nT(this,e$,"f").features[tl.q].signTransaction(...e.map(e=>({account:t,transaction:(0,nI.Y)(e)?e.serialize():new Uint8Array(e.serialize({requireAllSignatures:!1,verifySignatures:!1}))})));return e.map((e,t)=>{let i=n[t].signedTransaction;return(0,nI.Y)(e)?tr.Kt.deserialize(i):tr.ZX.from(i)})}catch(e){throw new ti.z4(e?.message,e)}}catch(e){throw this.emit("error",e),e}},e8=async function(e){try{let t=nT(this,eH,"f");if(!t)throw new ti.kW;if(!(to.F in nT(this,e$,"f").features))throw new ti.Ez;if(!t.features.includes(to.F))throw new ti.fk;try{return(await nT(this,e$,"f").features[to.F].signMessage({account:t,message:e}))[0].signature}catch(e){throw new ti.K3(e?.message,e)}}catch(e){throw this.emit("error",e),e}},e7=async function(e={}){try{let t;if(!(ta in nT(this,e$,"f").features))throw new ti.Ez;try{[t]=await nT(this,e$,"f").features[ta].signIn(e)}catch(e){throw new ti.o7(e?.message,e)}if(!t)throw new ti.o7;return nT(this,eZ,"m",e1).call(this,t.account),t}catch(e){throw this.emit("error",e),e}};var nD=function(e,t,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(e):i?i.value:t.get(e)},nx=function(e,t,n,i,r){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(e,n):r?r.value=n:t.set(e,n),n};let nz=new Set,nO={};function nC(...e){return(e=e.filter(e=>!nz.has(e))).length?(e.forEach(e=>{r=void 0,nz.add(e)}),nO.register?.forEach(t=>nU(()=>t(...e))),function(){e.forEach(e=>{r=void 0,nz.delete(e)}),nO.unregister?.forEach(t=>nU(()=>t(...e)))}):()=>{}}function nk(){return r||(r=[...nz]),r}function n_(e,t){return nO[e]?.push(t)||(nO[e]=[t]),function(){nO[e]=nO[e]?.filter(e=>t!==e)}}function nU(e){try{e()}catch(e){console.error(e)}}class nR extends Event{get detail(){return nD(this,e9,"f")}get type(){return"wallet-standard:app-ready"}constructor(e){super("wallet-standard:app-ready",{bubbles:!1,cancelable:!1,composed:!1}),e9.set(this,void 0),nx(this,e9,e,"f")}preventDefault(){throw Error("preventDefault cannot be called")}stopImmediatePropagation(){throw Error("stopImmediatePropagation cannot be called")}stopPropagation(){throw Error("stopPropagation cannot be called")}}e9=new WeakMap;var nW=n(2115);function nP(e){let t=(0,nW.useRef)(void 0);return void 0===t.current&&(t.current={value:e()}),t.current.value}function nB(e){return e.filter(nN).map(e=>new nS({wallet:e}))}!function(e){e[e.DESKTOP_WEB=0]="DESKTOP_WEB",e[e.MOBILE_WEB=1]="MOBILE_WEB"}(te||(te={}));var nY=n(2361);class nQ extends ti.m7{constructor(){super(...arguments),this.name="WalletNotSelectedError"}}var nF=n(1392);function nZ({children:e,wallets:t,adapter:n,isUnloadingRef:i,onAutoConnectRequest:r,onConnectError:a,onError:o,onSelectWallet:s}){let l=(0,nW.useRef)(!1),[c,d]=(0,nW.useState)(!1),u=(0,nW.useRef)(!1),[h,f]=(0,nW.useState)(!1),[g,p]=(0,nW.useState)(()=>n?.publicKey??null),[m,w]=(0,nW.useState)(()=>n?.connected??!1),y=(0,nW.useRef)(o);(0,nW.useEffect)(()=>(y.current=o,()=>{y.current=void 0}),[o]);let M=(0,nW.useRef)((e,t)=>(!i.current&&(y.current?y.current(e,t):(console.error(e,t),e instanceof ti.AE&&"undefined"!=typeof window&&t&&window.open(t.url,"_blank"))),e)),[v,b]=(0,nW.useState)(()=>t.map(e=>({adapter:e,readyState:e.readyState})).filter(({readyState:e})=>e!==tn.Ok.Unsupported));(0,nW.useEffect)(()=>{function e(e){b(t=>{let n=t.findIndex(({adapter:e})=>e===this);if(-1===n)return t;let{adapter:i}=t[n];return[...t.slice(0,n),{adapter:i,readyState:e},...t.slice(n+1)].filter(({readyState:e})=>e!==tn.Ok.Unsupported)})}return b(e=>t.map((t,n)=>{let i=e[n];return i&&i.adapter===t&&i.readyState===t.readyState?i:{adapter:t,readyState:t.readyState}}).filter(({readyState:e})=>e!==tn.Ok.Unsupported)),t.forEach(t=>t.on("readyStateChange",e,t)),()=>{t.forEach(t=>t.off("readyStateChange",e,t))}},[n,t]);let E=(0,nW.useMemo)(()=>v.find(e=>e.adapter===n)??null,[n,v]);(0,nW.useEffect)(()=>{if(!n)return;let e=e=>{p(e),l.current=!1,d(!1),w(!0),u.current=!1,f(!1)},t=()=>{i.current||(p(null),l.current=!1,d(!1),w(!1),u.current=!1,f(!1))},r=e=>{M.current(e,n)};return n.on("connect",e),n.on("disconnect",t),n.on("error",r),()=>{n.off("connect",e),n.off("disconnect",t),n.off("error",r),t()}},[n,i]);let N=(0,nW.useRef)(!1);(0,nW.useEffect)(()=>()=>{N.current=!1},[n]),(0,nW.useEffect)(()=>{N.current||l.current||m||!r||E?.readyState!==tn.Ok.Installed&&E?.readyState!==tn.Ok.Loadable||(l.current=!0,d(!0),N.current=!0,async function(){try{await r()}catch{a()}finally{d(!1),l.current=!1}}())},[m,r,a,E]);let I=(0,nW.useCallback)(async(e,t,i)=>{if(!n)throw M.current(new nQ);if(!m)throw M.current(new ti.kW,n);return await n.sendTransaction(e,t,i)},[n,m]),j=(0,nW.useMemo)(()=>n&&"signTransaction"in n?async e=>{if(!m)throw M.current(new ti.kW,n);return await n.signTransaction(e)}:void 0,[n,m]),L=(0,nW.useMemo)(()=>n&&"signAllTransactions"in n?async e=>{if(!m)throw M.current(new ti.kW,n);return await n.signAllTransactions(e)}:void 0,[n,m]),T=(0,nW.useMemo)(()=>n&&"signMessage"in n?async e=>{if(!m)throw M.current(new ti.kW,n);return await n.signMessage(e)}:void 0,[n,m]),A=(0,nW.useMemo)(()=>n&&"signIn"in n?async e=>await n.signIn(e):void 0,[n]),S=(0,nW.useCallback)(async()=>{if(l.current||u.current||E?.adapter.connected)return;if(!E)throw M.current(new nQ);let{adapter:e,readyState:t}=E;if(t!==tn.Ok.Installed&&t!==tn.Ok.Loadable)throw M.current(new ti.AE,e);l.current=!0,d(!0);try{await e.connect()}catch(e){throw a(),e}finally{d(!1),l.current=!1}},[a,E]),D=(0,nW.useCallback)(async()=>{if(!u.current&&n){u.current=!0,f(!0);try{await n.disconnect()}finally{f(!1),u.current=!1}}},[n]);return nW.createElement(nF.b.Provider,{value:{autoConnect:!!r,wallets:v,wallet:E,publicKey:g,connected:m,connecting:c,disconnecting:h,select:s,connect:S,disconnect:D,sendTransaction:I,signTransaction:j,signAllTransactions:L,signMessage:T,signIn:A}},e)}function nH(e){return function({adapters:e,userAgentString:t}){return e.some(e=>e.name!==nw&&e.readyState===tn.Ok.Installed)?te.DESKTOP_WEB:t&&/android/i.test(t)&&!/(WebView|Version\/.+(Chrome)\/(\d+)\.(\d+)\.(\d+)\.(\d+)|; wv\).+(Chrome)\/(\d+)\.(\d+)\.(\d+)\.(\d+))/i.test(t)?te.MOBILE_WEB:te.DESKTOP_WEB}({adapters:e,userAgentString:(void 0===a&&(a=globalThis.navigator?.userAgent??null),a)})===te.MOBILE_WEB}function nK({children:e,wallets:t,autoConnect:n,localStorageKey:i="walletName",onError:r}){let{connection:a}=(0,nY.w)(),s=function(e){let t=nP(()=>new Set),{get:n,on:i}=nP(()=>(function(){if(o||(o=function(){if(o||(o=Object.freeze({register:nC,get:nk,on:n_}),"undefined"==typeof window))return o;let e=Object.freeze({register:nC});try{window.addEventListener("wallet-standard:register-wallet",({detail:t})=>t(e))}catch(e){console.error("wallet-standard:register-wallet event listener could not be added\n",e)}try{window.dispatchEvent(new nR(e))}catch(e){console.error("wallet-standard:app-ready event could not be dispatched\n",e)}return o}(),"undefined"==typeof window))return o;let e=window.navigator.wallets||[];if(!Array.isArray(e))return console.error("window.navigator.wallets is not an array"),o;let{register:t}=o,n=(...e)=>e.forEach(e=>nU(()=>e({register:t})));try{Object.defineProperty(window.navigator,"wallets",{value:Object.freeze({push:n})})}catch(e){return console.error("window.navigator.wallets could not be set"),o}return n(...e),o})()),[r,a]=(0,nW.useState)(()=>nB(n()));(0,nW.useEffect)(()=>{let e=[i("register",(...e)=>a(t=>[...t,...nB(e)])),i("unregister",(...e)=>a(t=>t.filter(t=>e.some(e=>e===t.wallet))))];return()=>e.forEach(e=>e())},[i]);let s=function(e){let t=(0,nW.useRef)(void 0);return(0,nW.useEffect)(()=>{t.current=e}),t.current}(r);return(0,nW.useEffect)(()=>{if(!s)return;let e=new Set(r);new Set(s.filter(t=>!e.has(t))).forEach(e=>e.destroy())},[s,r]),(0,nW.useEffect)(()=>()=>r.forEach(e=>e.destroy()),[]),(0,nW.useMemo)(()=>[...r,...e.filter(({name:e})=>!r.some(t=>t.name===e)||(t.has(e)||(t.add(e),console.warn(`${e} was registered as a Standard Wallet. The Wallet Adapter for ${e} can be removed from your app.`)),!1))],[r,e,t])}(t),l=(0,nW.useMemo)(()=>{var e;if(!nH(s))return null;let t=s.find(e=>e.name===nw);return t?t:new nb({addressSelector:{select(e){return nh(this,void 0,void 0,function*(){return e[0]})}},appIdentity:{uri:function(){let e=globalThis.location;if(e)return`${e.protocol}//${e.host}`}()},authorizationResultCache:function(){let e;try{e=window.localStorage}catch(e){}return{clear(){return t$(this,void 0,void 0,function*(){if(e)try{e.removeItem(nu)}catch(e){}})},get(){return t$(this,void 0,void 0,function*(){if(e)try{let t=JSON.parse(e.getItem(nu));if(!t||!t.accounts)return t||void 0;{let e=t.accounts.map(e=>Object.assign(Object.assign({},e),{publicKey:"publicKey"in e?new Uint8Array(Object.values(e.publicKey)):new tr.J3(e.address).toBytes()}));return Object.assign(Object.assign({},t),{accounts:e})}}catch(e){}})},set(t){return t$(this,void 0,void 0,function*(){if(e)try{e.setItem(nu,JSON.stringify(t))}catch(e){}})}}}(),cluster:(e=a?.rpcEndpoint)?/devnet/i.test(e)?"devnet":/testnet/i.test(e)?"testnet":"mainnet-beta":"mainnet-beta",onWalletNotFound:nE})},[s,a?.rpcEndpoint]),c=(0,nW.useMemo)(()=>null==l||-1!==s.indexOf(l)?s:[l,...s],[s,l]),[d,u]=function(e,t){let n=(0,nW.useState)(()=>{try{let t=localStorage.getItem(e);if(t)return JSON.parse(t)}catch(e){"undefined"!=typeof window&&console.error(e)}return null}),i=n[0],r=(0,nW.useRef)(!0);return(0,nW.useEffect)(()=>{if(r.current){r.current=!1;return}try{null===i?localStorage.removeItem(e):localStorage.setItem(e,JSON.stringify(i))}catch(e){"undefined"!=typeof window&&console.error(e)}},[i,e]),n}(i,0),h=(0,nW.useMemo)(()=>c.find(e=>e.name===d)??null,[c,d]),f=(0,nW.useCallback)(e=>{d!==e&&(h&&h.name!==nw&&h.disconnect(),u(e))},[h,u,d]);(0,nW.useEffect)(()=>{if(h)return h.on("disconnect",e),()=>{h.off("disconnect",e)};function e(){m.current||u(null)}},[h,s,u,d]);let g=(0,nW.useRef)(!1),p=(0,nW.useMemo)(()=>{if(n&&h)return async()=>{(!0===n||await n(h))&&(g.current?await h.connect():await h.autoConnect())}},[n,h]),m=(0,nW.useRef)(!1);(0,nW.useEffect)(()=>{if(d===nw&&nH(s)){m.current=!1;return}function e(){m.current=!0}return window.addEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}},[s,d]);let w=(0,nW.useCallback)(()=>{h&&f(null)},[h,f]),y=(0,nW.useCallback)(e=>{g.current=!0,f(e)},[f]);return nW.createElement(nZ,{wallets:c,adapter:h,isUnloadingRef:m,onAutoConnectRequest:p,onConnectError:w,onError:r,onSelectWallet:y},e)}},7354:(e,t)=>{let n=new Uint8Array(512),i=new Uint8Array(256);!function(){let e=1;for(let t=0;t<255;t++)n[t]=e,i[e]=t,256&(e<<=1)&&(e^=285);for(let e=255;e<512;e++)n[e]=n[e-255]}(),t.log=function(e){if(e<1)throw Error("log("+e+")");return i[e]},t.exp=function(e){return n[e]},t.mul=function(e,t){return 0===e||0===t?0:n[i[e]+i[t]]}},7399:(e,t,n)=>{"use strict";n.d(t,{Br:()=>l,Ce:()=>o,Ok:()=>i,qG:()=>s});var i,r=n(5484),a=n(2131);!function(e){e.Installed="Installed",e.NotDetected="NotDetected",e.Loadable="Loadable",e.Unsupported="Unsupported"}(i||(i={}));class o extends r.A{get connected(){return!!this.publicKey}async autoConnect(){await this.connect()}async prepareTransaction(e,t,n={}){let i=this.publicKey;if(!i)throw new a.kW;return e.feePayer=e.feePayer||i,e.recentBlockhash=e.recentBlockhash||(await t.getLatestBlockhash({commitment:n.preflightCommitment,minContextSlot:n.minContextSlot})).blockhash,e}}function s(e){if("undefined"==typeof window||"undefined"==typeof document)return;let t=[];function n(){if(e())for(let e of t)e()}let i=setInterval(n,1e3);t.push(()=>clearInterval(i)),"loading"===document.readyState&&(document.addEventListener("DOMContentLoaded",n,{once:!0}),t.push(()=>document.removeEventListener("DOMContentLoaded",n))),"complete"!==document.readyState&&(window.addEventListener("load",n,{once:!0}),t.push(()=>window.removeEventListener("load",n))),n()}function l(){if(!navigator)return!1;let e=navigator.userAgent.toLowerCase(),t=e.includes("iphone")||e.includes("ipad"),n=e.includes("safari");return t&&n}},7487:(e,t,n)=>{let i=n(3585);function r(e){this.mode=i.BYTE,"string"==typeof e?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}r.getBitsLength=function(e){return 8*e},r.prototype.getLength=function(){return this.data.length},r.prototype.getBitsLength=function(){return r.getBitsLength(this.data.length)},r.prototype.write=function(e){for(let t=0,n=this.data.length;t<n;t++)e.put(this.data[t],8)},e.exports=r},7788:(e,t,n)=>{let i=n(6087).getSymbolSize;t.getRowColCoords=function(e){if(1===e)return[];let t=Math.floor(e/7)+2,n=i(e),r=145===n?26:2*Math.ceil((n-13)/(2*t-2)),a=[n-7];for(let e=1;e<t-1;e++)a[e]=a[e-1]-r;return a.push(6),a.reverse()},t.getPositions=function(e){let n=[],i=t.getRowColCoords(e),r=i.length;for(let e=0;e<r;e++)for(let t=0;t<r;t++)(0!==e||0!==t)&&(0!==e||t!==r-1)&&(e!==r-1||0!==t)&&n.push([i[e],i[t]]);return n}},8087:(e,t,n)=>{"use strict";n.d(t,{c:()=>l});var i=n(8670),r=n(7399),a=n(2131),o=n(6068),s=n(3570);class l extends i.DE{constructor(e={}){super(),this.name="Phantom",this.url="https://phantom.app",this.icon="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDgiIGhlaWdodD0iMTA4IiB2aWV3Qm94PSIwIDAgMTA4IDEwOCIgZmlsbD0ibm9uZSI+CjxyZWN0IHdpZHRoPSIxMDgiIGhlaWdodD0iMTA4IiByeD0iMjYiIGZpbGw9IiNBQjlGRjIiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00Ni41MjY3IDY5LjkyMjlDNDIuMDA1NCA3Ni44NTA5IDM0LjQyOTIgODUuNjE4MiAyNC4zNDggODUuNjE4MkMxOS41ODI0IDg1LjYxODIgMTUgODMuNjU2MyAxNSA3NS4xMzQyQzE1IDUzLjQzMDUgNDQuNjMyNiAxOS44MzI3IDcyLjEyNjggMTkuODMyN0M4Ny43NjggMTkuODMyNyA5NCAzMC42ODQ2IDk0IDQzLjAwNzlDOTQgNTguODI1OCA4My43MzU1IDc2LjkxMjIgNzMuNTMyMSA3Ni45MTIyQzcwLjI5MzkgNzYuOTEyMiA2OC43MDUzIDc1LjEzNDIgNjguNzA1MyA3Mi4zMTRDNjguNzA1MyA3MS41NzgzIDY4LjgyNzUgNzAuNzgxMiA2OS4wNzE5IDY5LjkyMjlDNjUuNTg5MyA3NS44Njk5IDU4Ljg2ODUgODEuMzg3OCA1Mi41NzU0IDgxLjM4NzhDNDcuOTkzIDgxLjM4NzggNDUuNjcxMyA3OC41MDYzIDQ1LjY3MTMgNzQuNDU5OEM0NS42NzEzIDcyLjk4ODQgNDUuOTc2OCA3MS40NTU2IDQ2LjUyNjcgNjkuOTIyOVpNODMuNjc2MSA0Mi41Nzk0QzgzLjY3NjEgNDYuMTcwNCA4MS41NTc1IDQ3Ljk2NTggNzkuMTg3NSA0Ny45NjU4Qzc2Ljc4MTYgNDcuOTY1OCA3NC42OTg5IDQ2LjE3MDQgNzQuNjk4OSA0Mi41Nzk0Qzc0LjY5ODkgMzguOTg4NSA3Ni43ODE2IDM3LjE5MzEgNzkuMTg3NSAzNy4xOTMxQzgxLjU1NzUgMzcuMTkzMSA4My42NzYxIDM4Ljk4ODUgODMuNjc2MSA0Mi41Nzk0Wk03MC4yMTAzIDQyLjU3OTVDNzAuMjEwMyA0Ni4xNzA0IDY4LjA5MTYgNDcuOTY1OCA2NS43MjE2IDQ3Ljk2NThDNjMuMzE1NyA0Ny45NjU4IDYxLjIzMyA0Ni4xNzA0IDYxLjIzMyA0Mi41Nzk1QzYxLjIzMyAzOC45ODg1IDYzLjMxNTcgMzcuMTkzMSA2NS43MjE2IDM3LjE5MzFDNjguMDkxNiAzNy4xOTMxIDcwLjIxMDMgMzguOTg4NSA3MC4yMTAzIDQyLjU3OTVaIiBmaWxsPSIjRkZGREY4Ii8+Cjwvc3ZnPg==",this.supportedTransactionVersions=new Set(["legacy",0]),this._readyState="undefined"==typeof window||"undefined"==typeof document?r.Ok.Unsupported:r.Ok.NotDetected,this._disconnected=()=>{let e=this._wallet;e&&(e.off("disconnect",this._disconnected),e.off("accountChanged",this._accountChanged),this._wallet=null,this._publicKey=null,this.emit("error",new a.PQ),this.emit("disconnect"))},this._accountChanged=e=>{let t=this._publicKey;if(t){try{e=new s.J3(e.toBytes())}catch(e){this.emit("error",new a.Kd(e?.message,e));return}t.equals(e)||(this._publicKey=e,this.emit("connect",e))}},this._connecting=!1,this._wallet=null,this._publicKey=null,this._readyState!==r.Ok.Unsupported&&((0,r.Br)()?(this._readyState=r.Ok.Loadable,this.emit("readyStateChange",this._readyState)):(0,r.qG)(()=>!!(window.phantom?.solana?.isPhantom||window.solana?.isPhantom)&&(this._readyState=r.Ok.Installed,this.emit("readyStateChange",this._readyState),!0)))}get publicKey(){return this._publicKey}get connecting(){return this._connecting}get readyState(){return this._readyState}async autoConnect(){this.readyState===r.Ok.Installed&&await this.connect()}async connect(){try{let e;if(this.connected||this.connecting)return;if(this.readyState===r.Ok.Loadable){let e=encodeURIComponent(window.location.href),t=encodeURIComponent(window.location.origin);window.location.href=`https://phantom.app/ul/browse/${e}?ref=${t}`;return}if(this.readyState!==r.Ok.Installed)throw new a.AE;this._connecting=!0;let t=window.phantom?.solana||window.solana;if(!t.isConnected)try{await t.connect()}catch(e){throw new a.Y6(e?.message,e)}if(!t.publicKey)throw new a.fk;try{e=new s.J3(t.publicKey.toBytes())}catch(e){throw new a.Kd(e?.message,e)}t.on("disconnect",this._disconnected),t.on("accountChanged",this._accountChanged),this._wallet=t,this._publicKey=e,this.emit("connect",e)}catch(e){throw this.emit("error",e),e}finally{this._connecting=!1}}async disconnect(){let e=this._wallet;if(e){e.off("disconnect",this._disconnected),e.off("accountChanged",this._accountChanged),this._wallet=null,this._publicKey=null;try{await e.disconnect()}catch(e){this.emit("error",new a.Y8(e?.message,e))}}this.emit("disconnect")}async sendTransaction(e,t,n={}){try{let i=this._wallet;if(!i)throw new a.kW;try{let{signers:r,...a}=n;(0,o.Y)(e)?r?.length&&e.sign(r):(e=await this.prepareTransaction(e,t,a),r?.length&&e.partialSign(...r)),a.preflightCommitment=a.preflightCommitment||t.commitment;let{signature:s}=await i.signAndSendTransaction(e,a);return s}catch(e){if(e instanceof a.m7)throw e;throw new a.UF(e?.message,e)}}catch(e){throw this.emit("error",e),e}}async signTransaction(e){try{let t=this._wallet;if(!t)throw new a.kW;try{return await t.signTransaction(e)||e}catch(e){throw new a.z4(e?.message,e)}}catch(e){throw this.emit("error",e),e}}async signAllTransactions(e){try{let t=this._wallet;if(!t)throw new a.kW;try{return await t.signAllTransactions(e)||e}catch(e){throw new a.z4(e?.message,e)}}catch(e){throw this.emit("error",e),e}}async signMessage(e){try{let t=this._wallet;if(!t)throw new a.kW;try{let{signature:n}=await t.signMessage(e);return n}catch(e){throw new a.K3(e?.message,e)}}catch(e){throw this.emit("error",e),e}}}},8252:(e,t,n)=>{let i=n(6087),r=i.getBCHDigit(1335);t.getEncodedBits=function(e,t){let n=e.bit<<3|t,a=n<<10;for(;i.getBCHDigit(a)-r>=0;)a^=1335<<i.getBCHDigit(a)-r;return(n<<10|a)^21522}},8521:e=>{"use strict";var t={single_source_shortest_paths:function(e,n,i){var r,a,o,s,l,c,d,u={},h={};h[n]=0;var f=t.PriorityQueue.make();for(f.push(n,0);!f.empty();)for(o in a=(r=f.pop()).value,s=r.cost,l=e[a]||{})l.hasOwnProperty(o)&&(c=s+l[o],d=h[o],(void 0===h[o]||d>c)&&(h[o]=c,f.push(o,c),u[o]=a));if(void 0!==i&&void 0===h[i])throw Error(["Could not find a path from ",n," to ",i,"."].join(""));return u},extract_shortest_path_from_predecessor_list:function(e,t){for(var n=[],i=t;i;)n.push(i),e[i],i=e[i];return n.reverse(),n},find_path:function(e,n,i){var r=t.single_source_shortest_paths(e,n,i);return t.extract_shortest_path_from_predecessor_list(r,i)},PriorityQueue:{make:function(e){var n,i=t.PriorityQueue,r={};for(n in e=e||{},i)i.hasOwnProperty(n)&&(r[n]=i[n]);return r.queue=[],r.sorter=e.sorter||i.default_sorter,r},default_sorter:function(e,t){return e.cost-t.cost},push:function(e,t){this.queue.push({value:e,cost:t}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};e.exports=t},8670:(e,t,n)=>{"use strict";n.d(t,{DE:()=>s,Xl:()=>l});var i=n(7399),r=n(2131),a=n(6068);class o extends i.Ce{async sendTransaction(e,t,n={}){let i=!0;try{if((0,a.Y)(e)){if(!this.supportedTransactionVersions)throw new r.UF("Sending versioned transactions isn't supported by this wallet");if(!this.supportedTransactionVersions.has(e.version))throw new r.UF(`Sending transaction version ${e.version} isn't supported by this wallet`);try{let i=(e=await this.signTransaction(e)).serialize();return await t.sendRawTransaction(i,n)}catch(e){if(e instanceof r.z4)throw i=!1,e;throw new r.UF(e?.message,e)}}try{let{signers:i,...r}=n;e=await this.prepareTransaction(e,t,r),i?.length&&e.partialSign(...i);let a=(e=await this.signTransaction(e)).serialize();return await t.sendRawTransaction(a,r)}catch(e){if(e instanceof r.z4)throw i=!1,e;throw new r.UF(e?.message,e)}}catch(e){throw i&&this.emit("error",e),e}}async signAllTransactions(e){for(let t of e)if((0,a.Y)(t)){if(!this.supportedTransactionVersions)throw new r.z4("Signing versioned transactions isn't supported by this wallet");if(!this.supportedTransactionVersions.has(t.version))throw new r.z4(`Signing transaction version ${t.version} isn't supported by this wallet`)}let t=[];for(let n of e)t.push(await this.signTransaction(n));return t}}class s extends o{}class l extends s{}},8837:e=>{"use strict";e.exports=function(e){if(e.length>=255)throw TypeError("Alphabet too long");for(var t=new Uint8Array(256),n=0;n<t.length;n++)t[n]=255;for(var i=0;i<e.length;i++){var r=e.charAt(i),a=r.charCodeAt(0);if(255!==t[a])throw TypeError(r+" is ambiguous");t[a]=i}var o=e.length,s=e.charAt(0),l=Math.log(o)/Math.log(256),c=Math.log(256)/Math.log(o);function d(e){if("string"!=typeof e)throw TypeError("Expected String");if(0===e.length)return new Uint8Array;for(var n=0,i=0,r=0;e[n]===s;)i++,n++;for(var a=(e.length-n)*l+1>>>0,c=new Uint8Array(a);e[n];){var d=e.charCodeAt(n);if(d>255)return;var u=t[d];if(255===u)return;for(var h=0,f=a-1;(0!==u||h<r)&&-1!==f;f--,h++)u+=o*c[f]>>>0,c[f]=u%256>>>0,u=u/256>>>0;if(0!==u)throw Error("Non-zero carry");r=h,n++}for(var g=a-r;g!==a&&0===c[g];)g++;for(var p=new Uint8Array(i+(a-g)),m=i;g!==a;)p[m++]=c[g++];return p}return{encode:function(t){if(t instanceof Uint8Array||(ArrayBuffer.isView(t)?t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength):Array.isArray(t)&&(t=Uint8Array.from(t))),!(t instanceof Uint8Array))throw TypeError("Expected Uint8Array");if(0===t.length)return"";for(var n=0,i=0,r=0,a=t.length;r!==a&&0===t[r];)r++,n++;for(var l=(a-r)*c+1>>>0,d=new Uint8Array(l);r!==a;){for(var u=t[r],h=0,f=l-1;(0!==u||h<i)&&-1!==f;f--,h++)u+=256*d[f]>>>0,d[f]=u%o>>>0,u=u/o>>>0;if(0!==u)throw Error("Non-zero carry");i=h,r++}for(var g=l-i;g!==l&&0===d[g];)g++;for(var p=s.repeat(n);g<l;++g)p+=e.charAt(d[g]);return p},decodeUnsafe:d,decode:function(e){var t=d(e);if(t)return t;throw Error("Non-base"+o+" character")}}}},8976:(e,t)=>{t.L={bit:1},t.M={bit:0},t.Q={bit:3},t.H={bit:2},t.isValid=function(e){return e&&void 0!==e.bit&&e.bit>=0&&e.bit<4},t.from=function(e,n){if(t.isValid(e))return e;try{if("string"!=typeof e)throw Error("Param is not a string");switch(e.toLowerCase()){case"l":case"low":return t.L;case"m":case"medium":return t.M;case"q":case"quartile":return t.Q;case"h":case"high":return t.H;default:throw Error("Unknown EC Level: "+e)}}catch(e){return n}}},9021:(e,t,n)=>{"use strict";var i;n.d(t,{B:()=>i}),function(e){e.Mainnet="mainnet-beta",e.Testnet="testnet",e.Devnet="devnet"}(i||(i={}))},9184:(e,t,n)=>{let i=n(3585),r=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function a(e){this.mode=i.ALPHANUMERIC,this.data=e}a.getBitsLength=function(e){return 11*Math.floor(e/2)+e%2*6},a.prototype.getLength=function(){return this.data.length},a.prototype.getBitsLength=function(){return a.getBitsLength(this.data.length)},a.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let n=45*r.indexOf(this.data[t]);n+=r.indexOf(this.data[t+1]),e.put(n,11)}this.data.length%2&&e.put(r.indexOf(this.data[t]),6)},e.exports=a},9343:(e,t)=>{t.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40}},9621:e=>{function t(e){if(!e||e<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}t.prototype.set=function(e,t,n,i){let r=e*this.size+t;this.data[r]=n,i&&(this.reservedBit[r]=!0)},t.prototype.get=function(e,t){return this.data[e*this.size+t]},t.prototype.xor=function(e,t,n){this.data[e*this.size+t]^=n},t.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]},e.exports=t},9698:(e,t,n)=>{"use strict";n.d(t,{j:()=>i});let i="standard:events"}}]);