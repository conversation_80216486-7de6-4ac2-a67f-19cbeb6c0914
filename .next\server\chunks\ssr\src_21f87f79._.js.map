{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { WalletMultiButton } from '@solana/wallet-adapter-react-ui';\nimport { useWallet } from '@solana/wallet-adapter-react';\nimport { SOLANA_NETWORK } from '@/lib/constants';\nimport { ChainSelector, ConnectionStatus } from '@/components/cross-chain/MultiChainWalletProvider';\n\nexport function Header() {\n  const { connected } = useWallet();\n\n  return (\n    <header className=\"bg-black/95 backdrop-blur-sm border-b border-gray-800/50 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-3\">\n              {/* Logo */}\n              <div className=\"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg\">\n                <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">\n                TokenLaunch\n              </h1>\n            </div>\n\n            {/* Network indicator */}\n            <div className=\"hidden sm:flex items-center space-x-2\">\n              <div className={`w-2 h-2 rounded-full ${\n                SOLANA_NETWORK === 'mainnet-beta' ? 'bg-green-400' : 'bg-yellow-400'\n              } animate-pulse`}></div>\n              <span className=\"text-sm text-gray-400 font-medium\">\n                {SOLANA_NETWORK === 'mainnet-beta' ? 'Mainnet' : 'Devnet'}\n              </span>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a href=\"#features\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Features\n            </a>\n            <a href=\"#launch\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Launch\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 font-medium transition-colors\">\n              Docs\n            </a>\n          </nav>\n\n          <div className=\"flex items-center space-x-4\">\n            {connected && (\n              <div className=\"hidden sm:flex items-center space-x-2 bg-green-900/30 border border-green-500/30 px-3 py-1 rounded-full\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-green-400 font-medium\">Connected</span>\n              </div>\n            )}\n            <WalletMultiButton className=\"!bg-gradient-to-r !from-purple-500 !to-pink-500 hover:!from-purple-600 hover:!to-pink-600 !text-white !rounded-xl !px-6 !py-2 !text-sm !font-semibold !transition-all !duration-300 !border !border-purple-400/30 hover:!border-purple-300/50 hover:!scale-105\" />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOO,SAAS;IACd,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD;IAE9B,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC5E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAAgG;;;;;;;;;;;;0CAMhH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,qBAAqB,EACpC,uHAAA,CAAA,iBAAc,KAAK,iBAAiB,iBAAiB,gBACtD,cAAc,CAAC;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDACb,uHAAA,CAAA,iBAAc,KAAK,iBAAiB,YAAY;;;;;;;;;;;;;;;;;;kCAMvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAoE;;;;;;0CAGlG,8OAAC;gCAAE,MAAK;gCAAU,WAAU;0CAAoE;;;;;;0CAGhG,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CAAoE;;;;;;;;;;;;kCAK5F,8OAAC;wBAAI,WAAU;;4BACZ,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAqC;;;;;;;;;;;;0CAGzD,8OAAC,+LAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/components/TokenLaunchModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useWallet } from '@solana/wallet-adapter-react';\nimport { WalletMultiButton } from '@solana/wallet-adapter-react-ui';\n\ntype ModalStep = 'basics' | 'liquidity' | 'review' | 'creating';\n\ninterface TokenBasicsData {\n  name: string;\n  symbol: string;\n  description: string;\n  image: File | null;\n  supply: string;\n  decimals: number;\n  website: string;\n  twitter: string;\n  telegram: string;\n  revokeMint: boolean;\n  revokeFreeze: boolean;\n}\n\ninterface LiquidityData {\n  initialSolAmount: string;\n}\n\ninterface CostEstimate {\n  tokenCreation: number;\n  metadata: number;\n  poolCreation: number;\n  total: number;\n}\n\ninterface TokenLaunchModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function TokenLaunchModal({ isOpen, onClose }: TokenLaunchModalProps) {\n  const { connected, publicKey } = useWallet();\n  const [currentStep, setCurrentStep] = useState<ModalStep>('basics');\n\n  const [tokenBasics, setTokenBasics] = useState<TokenBasicsData>({\n    name: '',\n    symbol: '',\n    description: '',\n    image: null,\n    supply: '1000000',\n    decimals: 9,\n    website: '',\n    twitter: '',\n    telegram: '',\n    revokeMint: true,\n    revokeFreeze: true,\n  });\n\n  const [liquidityData, setLiquidityData] = useState<LiquidityData>({\n    initialSolAmount: '0.2',\n  });\n\n  const [costEstimate, setCostEstimate] = useState<CostEstimate>({\n    tokenCreation: 0.002,\n    metadata: 0.001,\n    poolCreation: 0.152, // 0.15 SOL protocol fee + 0.002 SOL network fees\n    total: 0.155,\n  });\n\n  const [isCreating, setIsCreating] = useState(false);\n  const [imagePreview, setImagePreview] = useState<string | null>(null);\n\n  // Calculate cost estimate based on form data\n  useEffect(() => {\n    const baseCost = 0.002; // Token creation\n    const metadataCost = 0.001; // Metadata upload\n    const imageCost = tokenBasics.image ? 0.0005 : 0; // Image upload\n\n    // Pool creation costs for CPMM (Standard AMM only)\n    const poolCreationCost = 0.152; // 0.15 SOL protocol fee + 0.002 SOL network fees\n\n    const total = baseCost + metadataCost + imageCost + poolCreationCost;\n\n    setCostEstimate({\n      tokenCreation: baseCost,\n      metadata: metadataCost + imageCost,\n      poolCreation: poolCreationCost,\n      total,\n    });\n  }, [tokenBasics.image]);\n\n  const handleBasicsChange = (field: keyof TokenBasicsData, value: any) => {\n    setTokenBasics(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleLiquidityChange = (field: keyof LiquidityData, value: any) => {\n    setLiquidityData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      setTokenBasics(prev => ({ ...prev, image: file }));\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setImagePreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const nextStep = () => {\n    if (currentStep === 'basics') setCurrentStep('liquidity');\n    else if (currentStep === 'liquidity') setCurrentStep('review');\n  };\n\n  const prevStep = () => {\n    if (currentStep === 'liquidity') setCurrentStep('basics');\n    else if (currentStep === 'review') setCurrentStep('liquidity');\n  };\n\n  const handleSubmit = async () => {\n    if (!connected || !publicKey) return;\n\n    setCurrentStep('creating');\n    setIsCreating(true);\n\n    try {\n      // TODO: Implement token creation logic\n      console.log('Creating token with data:', { tokenBasics, liquidityData });\n\n      // Simulate creation process\n      await new Promise(resolve => setTimeout(resolve, 5000));\n\n      // Close modal on success\n      onClose();\n    } catch (error) {\n      console.error('Token creation failed:', error);\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  const isBasicsValid = tokenBasics.name && tokenBasics.symbol && tokenBasics.supply && connected;\n  const isLiquidityValid = liquidityData.initialSolAmount && parseFloat(liquidityData.initialSolAmount) > 0;\n\n  if (!isOpen) return null;\n\n  const getStepTitle = () => {\n    switch (currentStep) {\n      case 'basics': return 'Token Basics';\n      case 'liquidity': return 'Liquidity & Market';\n      case 'review': return 'Review & Launch';\n      case 'creating': return 'Creating Token';\n      default: return 'Create Token';\n    }\n  };\n\n  const getStepSubtitle = () => {\n    switch (currentStep) {\n      case 'basics': return \"Start with your token's core identity\";\n      case 'liquidity': return 'Set up CPMM pool and initial liquidity';\n      case 'review': return 'Review all details before launch';\n      case 'creating': return 'Please wait while we create your token';\n      default: return '';\n    }\n  };\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n      <div className=\"bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl w-full shadow-2xl\">\n        {/* Header with gradient */}\n        <div className=\"bg-gradient-to-r from-purple-600 to-pink-600 p-6 rounded-t-2xl border-b border-gray-700/50\">\n          <div className=\"flex items-center justify-center\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center shadow-lg\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                </svg>\n              </div>\n              <div className=\"text-center\">\n                <h2 className=\"text-2xl font-bold text-white\">{getStepTitle()}</h2>\n                <p className=\"text-white/80 text-sm\">{getStepSubtitle()}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {!connected ? (\n            <div className=\"text-center py-8\">\n              <div className=\"w-16 h-16 bg-purple-900/30 border border-purple-500/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-white mb-2\">Connect Your Wallet</h3>\n              <p className=\"text-gray-400 mb-6\">Please connect your Solana wallet to create a token.</p>\n              <WalletMultiButton className=\"!bg-gradient-to-r !from-purple-500 !to-pink-500 hover:!from-purple-600 hover:!to-pink-600 !text-white !rounded-xl !px-6 !py-3 !font-semibold !border !border-purple-400/30 hover:!border-purple-300/50\" />\n            </div>\n          ) : (\n            <>\n              {/* Step 1: Token Basics */}\n              {currentStep === 'basics' && (\n                <div className=\"space-y-6\">\n                  {/* Token Image */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300 mb-3\">\n                      Token Logo (Optional)\n                    </label>\n                    <div className=\"border-2 border-dashed border-gray-600 rounded-xl p-8 text-center hover:border-purple-400 transition-colors bg-gray-800/30\">\n                      {imagePreview ? (\n                        <div className=\"flex flex-col items-center\">\n                          <img src={imagePreview} alt=\"Token\" className=\"w-24 h-24 rounded-xl object-cover mb-4 border border-gray-600\" />\n                          <button\n                            type=\"button\"\n                            onClick={() => {\n                              setImagePreview(null);\n                              setTokenBasics(prev => ({ ...prev, image: null }));\n                            }}\n                            className=\"text-sm text-red-400 hover:text-red-300 transition-colors\"\n                          >\n                            Remove Image\n                          </button>\n                        </div>\n                      ) : (\n                        <div>\n                          <svg className=\"w-12 h-12 text-gray-500 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n                          </svg>\n                          <p className=\"text-gray-400 mb-2\">Click to upload or drag and drop</p>\n                          <p className=\"text-gray-500 text-sm\">PNG or JPG up to 2MB. Recommended size: 512×512px for best quality.</p>\n                          <input\n                            type=\"file\"\n                            accept=\"image/*\"\n                            onChange={handleImageChange}\n                            className=\"hidden\"\n                            id=\"token-image\"\n                          />\n                          <label\n                            htmlFor=\"token-image\"\n                            className=\"mt-4 inline-block bg-purple-900/50 hover:bg-purple-800/50 border border-purple-500/30 hover:border-purple-400/50 text-purple-300 px-4 py-2 rounded-lg cursor-pointer transition-all duration-300\"\n                          >\n                            Choose Image\n                          </label>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Token Details */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                        Token Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={tokenBasics.name}\n                        onChange={(e) => handleBasicsChange('name', e.target.value)}\n                        placeholder=\"Solana Gaming Token\"\n                        className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                        required\n                      />\n                      <p className=\"text-xs text-gray-500 mt-1\">The full name that will appear everywhere</p>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                        Symbol *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={tokenBasics.symbol}\n                        onChange={(e) => handleBasicsChange('symbol', e.target.value.toUpperCase())}\n                        placeholder=\"SGT\"\n                        maxLength={10}\n                        className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                        required\n                      />\n                      <p className=\"text-xs text-gray-500 mt-1\">3-10 characters, like BTC or SOL</p>\n                    </div>\n                  </div>\n\n                  {/* Description */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                      Description (Optional)\n                    </label>\n                    <textarea\n                      value={tokenBasics.description}\n                      onChange={(e) => handleBasicsChange('description', e.target.value)}\n                      placeholder=\"Describe your token's purpose and utility...\"\n                      rows={3}\n                      className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 resize-none transition-all duration-300\"\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">Brief description of your token</p>\n                  </div>\n\n                  {/* Supply and Decimals */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                        Total Supply *\n                      </label>\n                      <input\n                        type=\"number\"\n                        value={tokenBasics.supply}\n                        onChange={(e) => handleBasicsChange('supply', e.target.value)}\n                        placeholder=\"1000000\"\n                        min=\"1\"\n                        className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                        required\n                      />\n                      <p className=\"text-xs text-gray-500 mt-1\">Total number of tokens to create</p>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                        Decimals\n                      </label>\n                      <select\n                        value={tokenBasics.decimals}\n                        onChange={(e) => handleBasicsChange('decimals', parseInt(e.target.value))}\n                        className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white transition-all duration-300\"\n                        aria-label=\"Token decimals\"\n                      >\n                        {[0, 2, 4, 6, 8, 9].map(num => (\n                          <option key={num} value={num} className=\"bg-gray-800 text-white\">{num} decimals</option>\n                        ))}\n                      </select>\n                      <p className=\"text-xs text-gray-500 mt-1\">Number of decimal places (9 is standard)</p>\n                    </div>\n                  </div>\n\n                  {/* Continue Button */}\n                  <div className=\"flex justify-end pt-6\">\n                    <button\n                      type=\"button\"\n                      onClick={nextStep}\n                      disabled={!isBasicsValid}\n                      className=\"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30\"\n                    >\n                      Continue\n                      <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n              )}\n\n              {/* Step 2: Liquidity & Market */}\n              {currentStep === 'liquidity' && (\n                <div className=\"space-y-6\">\n                  <div className=\"bg-blue-900/30 border border-blue-500/30 rounded-xl p-4\">\n                    <div className=\"flex items-start\">\n                      <svg className=\"w-5 h-5 text-blue-400 mt-0.5 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <div>\n                        <h4 className=\"text-sm font-medium text-blue-300\">Raydium CPMM Pool</h4>\n                        <p className=\"text-sm text-blue-200 mt-1\">\n                          Create a Constant Product Market Maker (CPMM) pool on Raydium. No OpenBook Market required, supports Token-2022.\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Pool Info */}\n                  <div className=\"bg-gradient-to-r from-purple-900/30 to-cyan-900/30 border border-purple-500/30 rounded-xl p-4\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-cyan-400 rounded-xl flex items-center justify-center mr-3\">\n                        <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                        </svg>\n                      </div>\n                      <div>\n                        <h4 className=\"font-medium text-white\">Standard AMM (CPMM)</h4>\n                        <p className=\"text-sm text-gray-300\">\n                          Latest Raydium pool type • No OpenBook Market • Token-2022 Support\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                      Initial SOL Liquidity *\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={liquidityData.initialSolAmount}\n                      onChange={(e) => handleLiquidityChange('initialSolAmount', e.target.value)}\n                      placeholder=\"0.2\"\n                      min=\"0.01\"\n                      step=\"0.01\"\n                      className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white placeholder-gray-400 transition-all duration-300\"\n                      required\n                    />\n                    <p className=\"text-xs text-gray-500 mt-1\">No minimum required, but 0.2+ SOL recommended for healthy trading</p>\n                  </div>\n\n\n\n                  {/* Navigation */}\n                  <div className=\"flex justify-between pt-6\">\n                    <button\n                      type=\"button\"\n                      onClick={prevStep}\n                      className=\"bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 flex items-center\"\n                    >\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                      </svg>\n                      Back\n                    </button>\n                    <button\n                      type=\"button\"\n                      onClick={nextStep}\n                      disabled={!isLiquidityValid}\n                      className=\"bg-gradient-to-r from-purple-500 to-cyan-400 hover:from-purple-600 hover:to-cyan-500 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 disabled:cursor-not-allowed flex items-center border border-purple-400/30 hover:border-purple-300/50 disabled:border-gray-600/30\"\n                    >\n                      Continue\n                      <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n              )}\n\n              {/* Step 3: Review & Launch */}\n              {currentStep === 'review' && (\n                <div className=\"space-y-6\">\n                  <div className=\"bg-gradient-to-r from-green-900/30 to-blue-900/30 border border-green-500/30 rounded-xl p-6\">\n                    <h3 className=\"text-lg font-semibold text-white mb-4\">Review Your Token</h3>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div>\n                        <h4 className=\"font-medium text-white mb-3\">Token Details</h4>\n                        <div className=\"space-y-2 text-sm\">\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Name:</span>\n                            <span className=\"font-medium text-gray-200\">{tokenBasics.name || 'Not set'}</span>\n                          </div>\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Symbol:</span>\n                            <span className=\"font-medium text-gray-200\">{tokenBasics.symbol || 'Not set'}</span>\n                          </div>\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Supply:</span>\n                            <span className=\"font-medium text-gray-200\">{tokenBasics.supply || 'Not set'}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div>\n                        <h4 className=\"font-medium text-white mb-3\">Pool Setup</h4>\n                        <div className=\"space-y-2 text-sm\">\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Initial SOL:</span>\n                            <span className=\"font-medium text-gray-200\">{liquidityData.initialSolAmount} SOL</span>\n                          </div>\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Pool Type:</span>\n                            <span className=\"font-medium text-gray-200\">Standard AMM (CPMM)</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Cost Breakdown */}\n                  <div className=\"bg-gray-800/50 border border-gray-700/50 rounded-xl p-6\">\n                    <h3 className=\"text-lg font-medium text-white mb-4\">Total Cost Breakdown</h3>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-400\">Token Creation:</span>\n                        <span className=\"font-medium text-gray-200\">{costEstimate.tokenCreation} SOL</span>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-400\">Metadata & Image:</span>\n                        <span className=\"font-medium text-gray-200\">{costEstimate.metadata} SOL</span>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-400\">CPMM Pool Creation:</span>\n                        <span className=\"font-medium text-gray-200\">{costEstimate.poolCreation} SOL</span>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-400\">Initial Liquidity:</span>\n                        <span className=\"font-medium text-gray-200\">{liquidityData.initialSolAmount} SOL</span>\n                      </div>\n                      <div className=\"border-t border-gray-600 pt-3 flex justify-between\">\n                        <span className=\"font-semibold text-white\">Total Cost:</span>\n                        <span className=\"font-bold text-purple-400 text-lg\">\n                          {(costEstimate.total + parseFloat(liquidityData.initialSolAmount || '0')).toFixed(3)} SOL\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Navigation */}\n                  <div className=\"flex justify-between pt-6\">\n                    <button\n                      type=\"button\"\n                      onClick={prevStep}\n                      className=\"bg-gray-800 hover:bg-gray-700 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 flex items-center\"\n                    >\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                      </svg>\n                      Back\n                    </button>\n                    <button\n                      type=\"button\"\n                      onClick={handleSubmit}\n                      className=\"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 flex items-center border border-green-400/30 hover:border-green-300/50\"\n                    >\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                      </svg>\n                      Launch Token\n                    </button>\n                  </div>\n                </div>\n              )}\n\n              {/* Step 4: Creating */}\n              {currentStep === 'creating' && (\n                <div className=\"text-center py-12\">\n                  <div className=\"w-20 h-20 bg-gradient-to-r from-purple-500 to-cyan-400 rounded-full flex items-center justify-center mx-auto mb-6\">\n                    <div className=\"animate-spin rounded-full h-10 w-10 border-b-2 border-white\"></div>\n                  </div>\n                  <h3 className=\"text-2xl font-bold text-white mb-4\">Creating Your Token</h3>\n                  <p className=\"text-gray-400 mb-8 max-w-md mx-auto\">\n                    Please wait while we create your token and set up the CPMM liquidity pool on Raydium. This may take a few minutes.\n                  </p>\n                  <div className=\"space-y-2 text-sm text-gray-400\">\n                    <div className=\"flex items-center justify-center\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full mr-2\"></div>\n                      Creating SPL Token...\n                    </div>\n                    <div className=\"flex items-center justify-center\">\n                      <div className=\"w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse\"></div>\n                      Preparing CPMM Pool Creation...\n                    </div>\n                    <div className=\"flex items-center justify-center\">\n                      <div className=\"w-2 h-2 bg-gray-500 rounded-full mr-2\"></div>\n                      Initializing Raydium Pool...\n                    </div>\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAsCO,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAyB;IACzE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IAE1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAC9D,MAAM;QACN,QAAQ;QACR,aAAa;QACb,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,SAAS;QACT,UAAU;QACV,YAAY;QACZ,cAAc;IAChB;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,kBAAkB;IACpB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,eAAe;QACf,UAAU;QACV,cAAc;QACd,OAAO;IACT;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,OAAO,iBAAiB;QACzC,MAAM,eAAe,OAAO,kBAAkB;QAC9C,MAAM,YAAY,YAAY,KAAK,GAAG,SAAS,GAAG,eAAe;QAEjE,mDAAmD;QACnD,MAAM,mBAAmB,OAAO,iDAAiD;QAEjF,MAAM,QAAQ,WAAW,eAAe,YAAY;QAEpD,gBAAgB;YACd,eAAe;YACf,UAAU,eAAe;YACzB,cAAc;YACd;QACF;IACF,GAAG;QAAC,YAAY,KAAK;KAAC;IAEtB,MAAM,qBAAqB,CAAC,OAA8B;QACxD,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACrD;IAEA,MAAM,wBAAwB,CAAC,OAA4B;QACzD,iBAAiB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACvD;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAK,CAAC;YAEhD,iBAAiB;YACjB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,gBAAgB,EAAE,MAAM,EAAE;YAC5B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,WAAW;QACf,IAAI,gBAAgB,UAAU,eAAe;aACxC,IAAI,gBAAgB,aAAa,eAAe;IACvD;IAEA,MAAM,WAAW;QACf,IAAI,gBAAgB,aAAa,eAAe;aAC3C,IAAI,gBAAgB,UAAU,eAAe;IACpD;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa,CAAC,WAAW;QAE9B,eAAe;QACf,cAAc;QAEd,IAAI;YACF,uCAAuC;YACvC,QAAQ,GAAG,CAAC,6BAA6B;gBAAE;gBAAa;YAAc;YAEtE,4BAA4B;YAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,yBAAyB;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,YAAY,IAAI,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,IAAI;IACtF,MAAM,mBAAmB,cAAc,gBAAgB,IAAI,WAAW,cAAc,gBAAgB,IAAI;IAExG,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO9C,8OAAC;oBAAI,WAAU;8BACZ,CAAC,0BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC,+LAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;;;;;;6CAG/B;;4BAEG,gBAAgB,0BACf,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;0DACZ,6BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,KAAK;4DAAc,KAAI;4DAAQ,WAAU;;;;;;sEAC9C,8OAAC;4DACC,MAAK;4DACL,SAAS;gEACP,gBAAgB;gEAChB,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,OAAO;oEAAK,CAAC;4DAClD;4DACA,WAAU;sEACX;;;;;;;;;;;yEAKH,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;4DAAuC,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9F,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,8OAAC;4DAAE,WAAU;sEAAqB;;;;;;sEAClC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DACC,MAAK;4DACL,QAAO;4DACP,UAAU;4DACV,WAAU;4DACV,IAAG;;;;;;sEAEL,8OAAC;4DACC,SAAQ;4DACR,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;kDAST,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO,YAAY,IAAI;wDACvB,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAC1D,aAAY;wDACZ,WAAU;wDACV,QAAQ;;;;;;kEAEV,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAG5C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO,YAAY,MAAM;wDACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;wDACxE,aAAY;wDACZ,WAAW;wDACX,WAAU;wDACV,QAAQ;;;;;;kEAEV,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;kDAK9C,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,YAAY,WAAW;gDAC9B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,KAAK;gDACjE,aAAY;gDACZ,MAAM;gDACN,WAAU;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAI5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,OAAO,YAAY,MAAM;wDACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wDAC5D,aAAY;wDACZ,KAAI;wDACJ,WAAU;wDACV,QAAQ;;;;;;kEAEV,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAG5C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,YAAY,QAAQ;wDAC3B,UAAU,CAAC,IAAM,mBAAmB,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;wDACvE,WAAU;wDACV,cAAW;kEAEV;4DAAC;4DAAG;4DAAG;4DAAG;4DAAG;4DAAG;yDAAE,CAAC,GAAG,CAAC,CAAA,oBACtB,8OAAC;gEAAiB,OAAO;gEAAK,WAAU;;oEAA0B;oEAAI;;+DAAzD;;;;;;;;;;kEAGjB,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;kDAK9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC;4CACX,WAAU;;gDACX;8DAEC,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ9E,gBAAgB,6BACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAoC,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC3F,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;kDAQhD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC5E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAO3C,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,cAAc,gBAAgB;gDACrC,UAAU,CAAC,IAAM,sBAAsB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACzE,aAAY;gDACZ,KAAI;gDACJ,MAAK;gDACL,WAAU;gDACV,QAAQ;;;;;;0DAEV,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAM5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACtE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;oDACjE;;;;;;;0DAGR,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC;gDACX,WAAU;;oDACX;kEAEC,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACtE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ9E,gBAAgB,0BACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAA6B,YAAY,IAAI,IAAI;;;;;;;;;;;;kFAEnE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAA6B,YAAY,MAAM,IAAI;;;;;;;;;;;;kFAErE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAA6B,YAAY,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kEAKzE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;;oFAA6B,cAAc,gBAAgB;oFAAC;;;;;;;;;;;;;kFAE9E,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;;oEAA6B,aAAa,aAAa;oEAAC;;;;;;;;;;;;;kEAE1E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;;oEAA6B,aAAa,QAAQ;oEAAC;;;;;;;;;;;;;kEAErE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;;oEAA6B,aAAa,YAAY;oEAAC;;;;;;;;;;;;;kEAEzE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;;oEAA6B,cAAc,gBAAgB;oEAAC;;;;;;;;;;;;;kEAE9E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAA2B;;;;;;0EAC3C,8OAAC;gEAAK,WAAU;;oEACb,CAAC,aAAa,KAAK,GAAG,WAAW,cAAc,gBAAgB,IAAI,IAAI,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;kDAO7F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACtE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;oDACjE;;;;;;;0DAGR,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACtE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;oDACjE;;;;;;;;;;;;;;;;;;;4BAQb,gBAAgB,4BACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;kDAGnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;oDAA+C;;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;oDAA8D;;;;;;;0DAG/E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;oDAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnF", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/TokenLaunch/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Head<PERSON> } from \"@/components/Header\";\nimport { TokenLaunchModal } from \"@/components/TokenLaunchModal\";\n\nexport default function Home() {\n  return (\n    <>\n      <Header />\n\n      {/* Main Content - Token Creation Focus */}\n      <main className=\"relative overflow-hidden bg-black min-h-screen\">\n        {/* Animated background elements */}\n        <div className=\"absolute inset-0\">\n          {/* Grid pattern overlay */}\n          <div className=\"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]\"></div>\n\n          {/* Floating orbs */}\n          <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse\"></div>\n          <div className=\"absolute bottom-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-to-r from-cyan-500/20 to-green-500/20 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse delay-500\"></div>\n\n          {/* Additional floating elements */}\n          <div className=\"absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-full blur-2xl animate-pulse delay-2000\"></div>\n          <div className=\"absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-full blur-2xl animate-pulse delay-3000\"></div>\n        </div>\n\n        <div className=\"relative container mx-auto px-4 py-8\">\n          {/* Simplified header section */}\n          <div className=\"text-center mb-12\">\n            <div className=\"inline-flex items-center bg-gray-900/50 border border-purple-500/30 rounded-full px-6 py-3 mb-6\">\n              <span className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse mr-3\"></span>\n              <span className=\"text-gray-300 text-sm font-medium\">Live on Solana Mainnet</span>\n            </div>\n\n            <h1 className=\"text-4xl lg:text-6xl font-black text-white mb-4 leading-tight tracking-tight\">\n              Launch Your\n              <span className=\"block bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent\">\n                Solana Token\n              </span>\n            </h1>\n\n            <p className=\"text-lg lg:text-xl text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed\">\n              Create, deploy, and add liquidity to your Solana token in minutes with our\n              <span className=\"text-purple-400 font-semibold\"> professional-grade platform</span>.\n            </p>\n          </div>\n\n          {/* Quick feature highlights */}\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-3xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">~0.15 SOL</div>\n              <div className=\"text-gray-400 text-sm\">Total Cost</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">&lt; 2 min</div>\n              <div className=\"text-gray-400 text-sm\">Deploy Time</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">3 Steps</div>\n              <div className=\"text-gray-400 text-sm\">Simple Process</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-white\">DEX Ready</div>\n              <div className=\"text-gray-400 text-sm\">Instant Trading</div>\n            </div>\n          </div>\n\n          {/* Main Token Creation Form */}\n          <div className=\"max-w-5xl mx-auto\">\n            <TokenLaunchModal\n              isOpen={true}\n              onClose={() => {}}\n            />\n          </div>\n\n          {/* Trust indicators */}\n          <div className=\"mt-16 pt-8 border-t border-gray-800/30 text-center\">\n            <p className=\"text-gray-500 text-sm mb-6\">Built with industry-leading protocols</p>\n            <div className=\"flex flex-wrap justify-center items-center gap-6\">\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium\">Solana</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium\">Raydium</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium\">Metaplex</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-gray-900/40 border border-gray-700/30 rounded-xl px-4 py-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg\"></div>\n                <span className=\"text-gray-300 font-medium\">SPL Token</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE;;0BACE,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAGP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAGtD,8OAAC;wCAAG,WAAU;;4CAA+E;0DAE3F,8OAAC;gDAAK,WAAU;0DAAgG;;;;;;;;;;;;kDAKlH,8OAAC;wCAAE,WAAU;;4CAA0E;0DAErF,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;4CAAmC;;;;;;;;;;;;;0CAKvF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sIAAA,CAAA,mBAAgB;oCACf,QAAQ;oCACR,SAAS,KAAO;;;;;;;;;;;0CAKpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5D", "debugId": null}}]}