/**
 * AI Image Generation Service
 * Generates token artwork using OpenAI DALL-E based on tweet content
 */

import { EnrichedTweet } from '@/types/twitter';

export interface TokenArtworkRequest {
  tweet: EnrichedTweet;
  tokenName: string;
  tokenSymbol: string;
  style?: 'meme' | 'abstract' | 'cartoon' | 'minimalist' | 'cyberpunk';
  size?: '256x256' | '512x512' | '1024x1024';
}

export interface TokenArtworkResult {
  success: boolean;
  imageUrl?: string;
  prompt?: string;
  error?: string;
  generationTime?: number;
}

class AIImageService {
  private apiKey: string | null = null;
  private baseUrl = 'https://api.openai.com/v1/images/generations';
  private useMockData = process.env.NODE_ENV === 'development';

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || null;
  }

  /**
   * Generate token artwork based on tweet content
   */
  async generateTokenArtwork(request: TokenArtworkRequest): Promise<TokenArtworkResult> {
    if (this.useMockData || !this.apiKey) {
      console.log('Using mock image generation for development');
      return this.getMockArtwork(request);
    }

    try {
      const startTime = Date.now();
      const prompt = this.createArtworkPrompt(request);

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'dall-e-3',
          prompt: prompt,
          n: 1,
          size: request.size || '1024x1024',
          quality: 'standard',
          style: 'vivid'
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const generationTime = Date.now() - startTime;

      if (data.data && data.data.length > 0) {
        return {
          success: true,
          imageUrl: data.data[0].url,
          prompt: prompt,
          generationTime
        };
      } else {
        throw new Error('No image generated');
      }
    } catch (error) {
      console.error('Error generating artwork:', error);
      // Fallback to mock data on error
      return this.getMockArtwork(request);
    }
  }

  /**
   * Create an optimized prompt for memecoin artwork generation
   */
  private createArtworkPrompt(request: TokenArtworkRequest): string {
    const { tweet, tokenName, tokenSymbol, style = 'meme' } = request;

    // Extract key themes and concepts from tweet
    const tweetText = tweet.text.toLowerCase();
    const themes = this.extractThemes(tweetText);

    // Build memecoin-focused prompt
    let prompt = '';

    // Style-specific memecoin prompt beginnings
    switch (style) {
      case 'meme':
        prompt = `Create a hilarious meme-style cryptocurrency token logo for "${tokenName}" ($${tokenSymbol}). `;
        prompt += `This should look like a popular internet meme coin with bold, eye-catching design. `;
        break;
      case 'abstract':
        prompt = `Design an abstract memecoin logo for "${tokenName}" ($${tokenSymbol}) with geometric shapes and vibrant colors. `;
        break;
      case 'cartoon':
        prompt = `Illustrate a cute, cartoon mascot character for the memecoin "${tokenName}" ($${tokenSymbol}). `;
        prompt += `Make it adorable and shareable, perfect for social media. `;
        break;
      case 'minimalist':
        prompt = `Create a clean, minimalist memecoin symbol for "${tokenName}" ($${tokenSymbol}) with simple but memorable design. `;
        break;
      case 'cyberpunk':
        prompt = `Design a futuristic cyberpunk memecoin logo for "${tokenName}" ($${tokenSymbol}) with neon colors and digital aesthetics. `;
        break;
    }

    // Incorporate the actual tweet content as the core concept
    const tweetConcept = this.sanitizeTweetForPrompt(tweet.text);
    if (tweetConcept) {
      prompt += `The design is directly inspired by this viral tweet: "${tweetConcept}". `;
      prompt += `Capture the essence and humor of this tweet in the visual design. `;
    }

    // Add meme-specific elements found in the tweet
    const tweetMemeElements = this.extractMemeElements(tweet.text);
    if (tweetMemeElements.length > 0) {
      prompt += `Include visual references to: ${tweetMemeElements.join(', ')}. `;
    }

    // Add theme-based elements for additional context
    if (themes.length > 0) {
      prompt += `Incorporate thematic elements: ${themes.join(', ')}. `;
    }

    // Memecoin-specific technical requirements
    prompt += `This is a MEMECOIN logo, so make it: `;
    prompt += `- Instantly recognizable and memorable `;
    prompt += `- Perfect for social media sharing `;
    prompt += `- Funny, relatable, or endearing `;
    prompt += `- Bold and vibrant colors `;
    prompt += `- Clear at small sizes (profile pictures) `;
    prompt += `- Include "$${tokenSymbol}" text prominently `;

    // Final style specifications
    switch (style) {
      case 'meme':
        prompt += `Style: internet meme aesthetic, bold outlines, bright colors, comic-style, viral-ready design. `;
        break;
      case 'cartoon':
        prompt += `Style: cute cartoon character, friendly expression, rounded shapes, appealing to wide audience. `;
        break;
      case 'abstract':
        prompt += `Style: modern abstract art, geometric patterns, gradient colors, contemporary feel. `;
        break;
      case 'minimalist':
        prompt += `Style: clean lines, simple shapes, limited color palette, elegant simplicity. `;
        break;
      case 'cyberpunk':
        prompt += `Style: neon colors, digital glitch effects, futuristic typography, tech-inspired. `;
        break;
    }

    prompt += `Background: solid color or simple gradient. No complex backgrounds. `;
    prompt += `Format: circular token logo design, suitable for cryptocurrency branding.`;

    return prompt;
  }

  /**
   * Extract meme-specific elements from tweet text
   */
  private extractMemeElements(text: string): string[] {
    const elements: string[] = [];

    // Common meme patterns and references
    const memePatterns = {
      'POV': /POV:/i,
      'when you': /when you/i,
      'me explaining': /me explaining/i,
      'nobody asked but': /nobody.*asked/i,
      'normalize': /normalize/i,
      'shoutout to': /shoutout to/i,
      'breaking news': /breaking/i,
      'local man': /local (man|woman)/i,
      'four stages': /stages/i,
      'just one more': /just one more/i,
      'mom': /mom|mother/i,
      'dad': /dad|father/i,
      'netflix': /netflix/i,
      'tiktok': /tiktok/i,
      'amazon': /amazon/i,
      'online shopping': /shopping|buy/i,
      'procrastination': /procrastinat|lazy/i,
      'productivity': /productive/i,
      'relatable': /relatable/i,
      'mood': /mood/i,
      'vibe': /vibe/i,
      'energy': /energy/i
    };

    for (const [element, pattern] of Object.entries(memePatterns)) {
      if (pattern.test(text)) {
        elements.push(element);
      }
    }

    return elements.slice(0, 4); // Limit to top 4 elements
  }

  /**
   * Extract themes from tweet text for artwork inspiration
   */
  private extractThemes(text: string): string[] {
    const themes: string[] = [];
    
    // Common meme/viral themes
    const themePatterns = {
      'cats': /cat|kitten|feline/i,
      'dogs': /dog|puppy|canine|woof/i,
      'food': /food|eating|hungry|pizza|burger/i,
      'money': /money|rich|broke|expensive|cheap/i,
      'technology': /phone|computer|app|internet|wifi/i,
      'work': /work|job|boss|office|meeting/i,
      'relationships': /love|dating|relationship|crush/i,
      'entertainment': /netflix|movie|tv|show|binge/i,
      'shopping': /shopping|buy|purchase|amazon|store/i,
      'time': /time|late|early|schedule|deadline/i,
      'social media': /twitter|instagram|tiktok|social|viral/i,
      'emotions': /happy|sad|angry|excited|tired/i,
      'space': /space|moon|star|planet|rocket/i,
      'nature': /tree|flower|ocean|mountain|sun/i,
      'gaming': /game|gaming|player|console|pc/i
    };

    for (const [theme, pattern] of Object.entries(themePatterns)) {
      if (pattern.test(text)) {
        themes.push(theme);
      }
    }

    return themes.slice(0, 3); // Limit to top 3 themes
  }

  /**
   * Sanitize tweet text for use in AI prompts
   */
  private sanitizeTweetForPrompt(text: string): string {
    // Remove URLs, mentions, and hashtags
    let sanitized = text
      .replace(/https?:\/\/[^\s]+/g, '')
      .replace(/@\w+/g, '')
      .replace(/#\w+/g, '')
      .replace(/\n+/g, ' ')
      .trim();

    // Limit length and clean up
    if (sanitized.length > 100) {
      sanitized = sanitized.substring(0, 100) + '...';
    }

    return sanitized;
  }

  /**
   * Generate mock artwork for development
   */
  private getMockArtwork(request: TokenArtworkRequest): Promise<TokenArtworkResult> {
    return new Promise((resolve) => {
      // Simulate API delay
      setTimeout(() => {
        const mockImages = [
          'https://picsum.photos/1024/1024?random=1',
          'https://picsum.photos/1024/1024?random=2',
          'https://picsum.photos/1024/1024?random=3',
          'https://picsum.photos/1024/1024?random=4',
          'https://picsum.photos/1024/1024?random=5',
        ];

        const randomImage = mockImages[Math.floor(Math.random() * mockImages.length)];
        const prompt = this.createArtworkPrompt(request);

        resolve({
          success: true,
          imageUrl: randomImage,
          prompt: prompt,
          generationTime: 2000 + Math.random() * 3000 // 2-5 seconds
        });
      }, 1000 + Math.random() * 2000); // 1-3 second delay
    });
  }

  /**
   * Generate multiple artwork variations
   */
  async generateArtworkVariations(
    request: TokenArtworkRequest, 
    count: number = 3
  ): Promise<TokenArtworkResult[]> {
    const styles: Array<TokenArtworkRequest['style']> = ['meme', 'abstract', 'cartoon'];
    const promises = styles.slice(0, count).map(style => 
      this.generateTokenArtwork({ ...request, style })
    );

    try {
      return await Promise.all(promises);
    } catch (error) {
      console.error('Error generating artwork variations:', error);
      return [];
    }
  }

  /**
   * Validate image URL and ensure it's accessible
   */
  async validateImageUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Get artwork generation cost estimate
   */
  getGenerationCost(size: string = '1024x1024'): number {
    // OpenAI DALL-E 3 pricing (as of 2024)
    const costs = {
      '256x256': 0.016,
      '512x512': 0.018,
      '1024x1024': 0.020
    };
    return costs[size as keyof typeof costs] || 0.020;
  }

  /**
   * Check if API key is configured
   */
  isConfigured(): boolean {
    return !!this.apiKey && !this.useMockData;
  }

  /**
   * Get service status
   */
  getStatus(): { configured: boolean; mockMode: boolean; apiKey: boolean } {
    return {
      configured: this.isConfigured(),
      mockMode: this.useMockData,
      apiKey: !!this.apiKey
    };
  }
}

export const aiImageService = new AIImageService();
