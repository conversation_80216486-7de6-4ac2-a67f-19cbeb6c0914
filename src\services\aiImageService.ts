/**
 * AI Image Generation Service
 * Generates token artwork using OpenAI DALL-E based on tweet content
 */

import { EnrichedTweet } from '@/types/twitter';

export interface TokenArtworkRequest {
  tweet: EnrichedTweet;
  tokenName: string;
  tokenSymbol: string;
  style?: 'meme' | 'abstract' | 'cartoon' | 'minimalist' | 'cyberpunk';
  size?: '256x256' | '512x512' | '1024x1024';
}

export interface TokenArtworkResult {
  success: boolean;
  imageUrl?: string;
  prompt?: string;
  error?: string;
  generationTime?: number;
}

class AIImageService {
  private apiKey: string | null = null;
  private baseUrl = 'https://api.openai.com/v1/images/generations';
  private useMockData = process.env.NODE_ENV === 'development';

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || null;
  }

  /**
   * Generate token artwork based on tweet content
   */
  async generateTokenArtwork(request: TokenArtworkRequest): Promise<TokenArtworkResult> {
    if (this.useMockData || !this.apiKey) {
      console.log('Using mock image generation for development');
      return this.getMockArtwork(request);
    }

    try {
      const startTime = Date.now();
      const prompt = this.createArtworkPrompt(request);

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'dall-e-3',
          prompt: prompt,
          n: 1,
          size: request.size || '1024x1024',
          quality: 'standard',
          style: 'vivid'
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const generationTime = Date.now() - startTime;

      if (data.data && data.data.length > 0) {
        return {
          success: true,
          imageUrl: data.data[0].url,
          prompt: prompt,
          generationTime
        };
      } else {
        throw new Error('No image generated');
      }
    } catch (error) {
      console.error('Error generating artwork:', error);
      // Fallback to mock data on error
      return this.getMockArtwork(request);
    }
  }

  /**
   * Create an optimized prompt for token artwork generation
   */
  private createArtworkPrompt(request: TokenArtworkRequest): string {
    const { tweet, tokenName, tokenSymbol, style = 'meme' } = request;
    
    // Extract key themes from tweet text
    const tweetText = tweet.text.toLowerCase();
    const themes = this.extractThemes(tweetText);
    
    // Base prompt structure
    let prompt = '';

    // Style-specific prompt beginnings
    switch (style) {
      case 'meme':
        prompt = `Create a vibrant meme-style cryptocurrency token logo for "${tokenName}" ($${tokenSymbol}). `;
        break;
      case 'abstract':
        prompt = `Design an abstract, modern cryptocurrency token logo for "${tokenName}" ($${tokenSymbol}). `;
        break;
      case 'cartoon':
        prompt = `Illustrate a fun, cartoon-style cryptocurrency token mascot for "${tokenName}" ($${tokenSymbol}). `;
        break;
      case 'minimalist':
        prompt = `Create a clean, minimalist cryptocurrency token symbol for "${tokenName}" ($${tokenSymbol}). `;
        break;
      case 'cyberpunk':
        prompt = `Design a futuristic cyberpunk cryptocurrency token logo for "${tokenName}" ($${tokenSymbol}). `;
        break;
    }

    // Add theme-based elements
    if (themes.length > 0) {
      prompt += `Incorporate elements related to: ${themes.join(', ')}. `;
    }

    // Add tweet context (sanitized)
    const context = this.sanitizeTweetForPrompt(tweet.text);
    if (context) {
      prompt += `Inspired by the concept: "${context}". `;
    }

    // Add technical requirements
    prompt += `The design should be suitable for a cryptocurrency token, with bold colors, clear visibility at small sizes, and a professional yet playful appearance. `;
    prompt += `Include the symbol "$${tokenSymbol}" prominently. `;
    prompt += `Style: modern, digital art, high contrast, suitable for crypto/DeFi branding. `;
    prompt += `No text other than the symbol. Clean background.`;

    return prompt;
  }

  /**
   * Extract themes from tweet text for artwork inspiration
   */
  private extractThemes(text: string): string[] {
    const themes: string[] = [];
    
    // Common meme/viral themes
    const themePatterns = {
      'cats': /cat|kitten|feline/i,
      'dogs': /dog|puppy|canine|woof/i,
      'food': /food|eating|hungry|pizza|burger/i,
      'money': /money|rich|broke|expensive|cheap/i,
      'technology': /phone|computer|app|internet|wifi/i,
      'work': /work|job|boss|office|meeting/i,
      'relationships': /love|dating|relationship|crush/i,
      'entertainment': /netflix|movie|tv|show|binge/i,
      'shopping': /shopping|buy|purchase|amazon|store/i,
      'time': /time|late|early|schedule|deadline/i,
      'social media': /twitter|instagram|tiktok|social|viral/i,
      'emotions': /happy|sad|angry|excited|tired/i,
      'space': /space|moon|star|planet|rocket/i,
      'nature': /tree|flower|ocean|mountain|sun/i,
      'gaming': /game|gaming|player|console|pc/i
    };

    for (const [theme, pattern] of Object.entries(themePatterns)) {
      if (pattern.test(text)) {
        themes.push(theme);
      }
    }

    return themes.slice(0, 3); // Limit to top 3 themes
  }

  /**
   * Sanitize tweet text for use in AI prompts
   */
  private sanitizeTweetForPrompt(text: string): string {
    // Remove URLs, mentions, and hashtags
    let sanitized = text
      .replace(/https?:\/\/[^\s]+/g, '')
      .replace(/@\w+/g, '')
      .replace(/#\w+/g, '')
      .replace(/\n+/g, ' ')
      .trim();

    // Limit length and clean up
    if (sanitized.length > 100) {
      sanitized = sanitized.substring(0, 100) + '...';
    }

    return sanitized;
  }

  /**
   * Generate mock artwork for development
   */
  private getMockArtwork(request: TokenArtworkRequest): Promise<TokenArtworkResult> {
    return new Promise((resolve) => {
      // Simulate API delay
      setTimeout(() => {
        const mockImages = [
          'https://picsum.photos/1024/1024?random=1',
          'https://picsum.photos/1024/1024?random=2',
          'https://picsum.photos/1024/1024?random=3',
          'https://picsum.photos/1024/1024?random=4',
          'https://picsum.photos/1024/1024?random=5',
        ];

        const randomImage = mockImages[Math.floor(Math.random() * mockImages.length)];
        const prompt = this.createArtworkPrompt(request);

        resolve({
          success: true,
          imageUrl: randomImage,
          prompt: prompt,
          generationTime: 2000 + Math.random() * 3000 // 2-5 seconds
        });
      }, 1000 + Math.random() * 2000); // 1-3 second delay
    });
  }

  /**
   * Generate multiple artwork variations
   */
  async generateArtworkVariations(
    request: TokenArtworkRequest, 
    count: number = 3
  ): Promise<TokenArtworkResult[]> {
    const styles: Array<TokenArtworkRequest['style']> = ['meme', 'abstract', 'cartoon'];
    const promises = styles.slice(0, count).map(style => 
      this.generateTokenArtwork({ ...request, style })
    );

    try {
      return await Promise.all(promises);
    } catch (error) {
      console.error('Error generating artwork variations:', error);
      return [];
    }
  }

  /**
   * Validate image URL and ensure it's accessible
   */
  async validateImageUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Get artwork generation cost estimate
   */
  getGenerationCost(size: string = '1024x1024'): number {
    // OpenAI DALL-E 3 pricing (as of 2024)
    const costs = {
      '256x256': 0.016,
      '512x512': 0.018,
      '1024x1024': 0.020
    };
    return costs[size as keyof typeof costs] || 0.020;
  }

  /**
   * Check if API key is configured
   */
  isConfigured(): boolean {
    return !!this.apiKey && !this.useMockData;
  }

  /**
   * Get service status
   */
  getStatus(): { configured: boolean; mockMode: boolean; apiKey: boolean } {
    return {
      configured: this.isConfigured(),
      mockMode: this.useMockData,
      apiKey: !!this.apiKey
    };
  }
}

export const aiImageService = new AIImageService();
